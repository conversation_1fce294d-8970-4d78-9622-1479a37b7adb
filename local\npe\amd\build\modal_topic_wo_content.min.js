define("local_npe/modal_topic_wo_content",["exports","jquery","core/notification","core/custom_interaction_events","core/modal","core/modal_registry","core/str"],(function(_exports,_jquery,_notification,_custom_interaction_events,_modal,_modal_registry,_str){function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _defineProperty(e,r,t){return(r=function(t){var i=function(t,r){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var i=e.call(t,r||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==typeof i?i:i+""}(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_notification=_interopRequireDefault(_notification),_custom_interaction_events=_interopRequireDefault(_custom_interaction_events),_modal=_interopRequireDefault(_modal),_modal_registry=_interopRequireDefault(_modal_registry),_str=_interopRequireDefault(_str);const SELECTORS_MODALBOX=".modal_text",SELECTORS_HIDE=".close",SELECTORS_CONFIRM='[data-action="confirm"]';let registered=!1;class ModalTopicWoContent extends _modal.default{constructor(root){super(root)}setData(text){"single"===text?_str.default.get_string("conflict_message","local_npe").done((string=>{this.getRoot().find(SELECTORS_MODALBOX).html(string)})).fail(_notification.default.exception):"multiple"===text&&_str.default.get_string("conflict_several_teams_message","local_npe").done((string=>{this.getRoot().find(SELECTORS_MODALBOX).html(string)})).fail(_notification.default.exception)}registerEventListeners(){super.registerEventListeners(this),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_CONFIRM,(()=>{this.getRoot().removeClass("show"),(0,_jquery.default)("body").removeClass("modal-open"),(0,_jquery.default)(".modal-backdrop").remove()})),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_HIDE,(()=>{this.getRoot().removeClass("show"),(0,_jquery.default)("body").removeClass("modal-open"),(0,_jquery.default)(".modal-backdrop").remove()}))}}return _exports.default=ModalTopicWoContent,_defineProperty(ModalTopicWoContent,"TYPE","local_npe/modal_topic_wo_content"),_defineProperty(ModalTopicWoContent,"TEMPLATE","local_npe/courses/modal_topic_wo_content"),registered||(_modal_registry.default.register(ModalTopicWoContent.TYPE,ModalTopicWoContent,ModalTopicWoContent.TEMPLATE),registered=!0),_exports.default}));

//# sourceMappingURL=modal_topic_wo_content.min.js.map
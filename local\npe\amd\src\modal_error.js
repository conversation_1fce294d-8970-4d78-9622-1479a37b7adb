import $ from 'jquery';
import Notification from 'core/notification';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';
import Str from 'core/str';

const SELECTORS = {
    MODALTITLE: '.error-title',
    MODALDESCRIPTION: '.error-description',
    HIDE: '.close',
    FINISH: '[data-action="finish"]',
};

let registered = false;

export default class ModalError extends Modal {

    static TYPE = 'local_npe/modal_error';
    static TEMPLATE = 'local_npe/commons/modal_error';

    constructor(root) {
        super(root);
    }

    setData(titleKey, descriptionKey, descriptionParam = null) {
        const description = descriptionParam !== null
            ? {key: descriptionKey, component: 'local_npe', param: JSON.parse(descriptionParam)}
            : {key: descriptionKey, component: 'local_npe'};

        let requeststrings;
        if (titleKey === null) {
            requeststrings = [description];
        } else {
            const title = {key: titleKey, component: 'local_npe'};
            if (descriptionKey === null) {
                requeststrings = [title];
            } else {
                requeststrings = [title, description];
            }
        }

        Str.get_strings(requeststrings).done((strings) => {
            let descriptionstring = '';
            let titlestring = strings[0];
            if (strings.length == 2) {
                titlestring = strings[0];
                descriptionstring = strings[1];
            }
            this.getRoot().find(SELECTORS.MODALTITLE).html(titlestring);
            this.getRoot().find(SELECTORS.MODALDESCRIPTION).html(descriptionstring);
        }).fail(Notification.exception);
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.success-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.success-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
        });
    }
}

if (!registered) {
    ModalRegistry.register(ModalError.TYPE, ModalError, ModalError.TEMPLATE);
    registered = true;
}

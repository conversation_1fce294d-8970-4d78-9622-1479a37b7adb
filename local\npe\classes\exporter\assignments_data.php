<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_npe\exporter;

use local_npe\activity\activity;
use local_npe\app;
use local_npe\constants;
use local_npe\core\persistent\activity_category;
use local_npe\core\persistent\activity_section;
use local_npe\core\persistent\product_topic;
use local_npe\course;
use local_npe\DTO\activity_dto;
use local_npe\exporter\assignments_data\activity_data;
use local_npe\exporter\assignments_data\category_data;
use local_npe\exporter\assignments_data\filter_data;
use local_npe\exporter\mycourse_data\course_section_data;
use local_npe\exporter_data;
use local_npe\filter\assignment_filter;
use local_npe\helper\breadcrumbs;
use local_npe\helper\session;
use local_npe\helper\json;
use local_npe\helper\url;
use local_npe\product;
use local_npe\product\megamenu;
use local_npe\traits\array_filter;
use local_npe\user_factory;

/**
 * Class assignments_data
 *
 * @package local_npe\exporter
 */
class assignments_data extends exporter_data {

    use array_filter;

    public $name;
    public $level;
    public $group;
    public $isteacher;
    public $hasstudents;
    public $isresourcesaved;
    public $isactivitysaved;
    public $isexamsaved;
    /** @var course_section_data[] */
    public $sections = [];

    public $filter;

    public bool $showhelp;

    public $sidemenu;
    public $itemssection1 = [];
    public $itemssection3 = [];
    public $emptyarea3 = true;

    public $floatingroup;
    public $floatingrouptitle;
    public $floatingrouptab0;
    public $floatingrouptab1;
    public $itemstab0;
    public $itemstab1;
    public $tab0type;
    public $tab1type;
    public $idtab0;
    public $idtab1;
    public $istabletst0;
    public $istabletst1;
    public $breadcrumbs;
    public $assignmentsurl;
    public $urltoviewer;
    public $isassignmentfilter;
    public $txtSequence;
    public $headerhelp;
    public $texthelp;
    public $issmauser;
    public $isdefaulttheme;
    public $isnuancestheme;
    public $chatboturl;
    public $resourcetitle;
    public $unittitle;
    public $subcattitle;

    // Parámetros no exportables.
    /** @var course  */
    private $course;
    /** @var product  */
    private $product;
    /** @var assignment_filter */
    private $assignmentfilter;
    /** @var category_data */
    private $mockcategorydata;
    /** @var filter_data */
    private $mockfilterdata;
    /** @var session */
    private $sessionhelper;
    private breadcrumbs $breadcrumbshelper;
    private json $jsonhelper;
    private url $urlhelper;

    public function __construct(course $course, product $product, assignment_filter $assignmentfilter) {
        $this->course = $course;
        $this->product = $product;
        $this->assignmentfilter = $assignmentfilter;
    }

    /**
     * Establecer los parámetros a exportar.
     *
     * @throws \JsonException
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\activity_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\user_exception
     * @throws \moodle_exception
     */
    public function set_params() :void {
        $this->courseid = $this->course->get_id();
        $this->name = $this->product->get_name();
        $this->level = $this->product->get_level();
        $this->group = $this->course->get_course_data()->get_fullname();
        $this->isteacher = app::get_userfactory()->get_current_user()->is_teacher();
        $this->hasstudents = $this->isteacher ? $this->course->has_students() : false;
        $this->isresourcesaved = $this->get_session_helper()->get_session('resourcesaved', true);
        $this->isactivitysaved = $this->get_session_helper()->get_session('activitysaved', true);
        $this->isexamsaved = $this->get_session_helper()->get_session('examsaved', true);

        // Menu Lateral.
        $this->set_side_menu();
        // Titulos biblioteca
        $productdata = $this->product->get_product_data();
        $this->resourcetitle = $productdata->get_json_labels('resourceTitle') ?? get_string('resourcename', 'local_npe');
        $this->unittitle = $productdata->get_json_labels('sdaName') ?? get_string('unit', 'local_npe');
        $this->subcattitle = $productdata->get_json_labels('subcatName');

        $this->set_filter();
        $this->set_sections();
        // Ayuda - assignments.
        $this->showhelp = $this->course->get_product()->can_show_help();

        // JSON - floatingGroup.
        $this->set_floatingroup_items(false);

        // Chatbot URL
        $this->chatboturl = app::get_chatbot()->get_chatbot_url();

        $this->assignmentsurl = $this->get_url_helper()->get_homework_url($this->course->get_id());

        if (!is_null($this->assignmentfilter->get_topicid())) {
            $this->isassignmentfilter = true;
            $this->urltoviewer = app::get_url_helper()->get_viewer_url(
                $this->course->get_course_data()->get_id(),
                $this->product->get_topic_by_packerid($this->assignmentfilter->get_topicid())->get_id(),
                ''
            );
            $this->txtSequence = $this->product->get_product_data()->get_json_labels('txtSequence');
        }
        // Breadcrumbs.
        // Enviamos miga de pan completa y en el caso de que se acceda desde la secuencia o se navegue
        // por las secciones, se controla desde el JS
        $this->breadcrumbs = $this->get_breadcrumb_helper()->get_assignments_breadcrumb($this->course);
        $this->isassignmentfilter = false;
    }

    /**
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\activity_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \JsonException
     */
    private function set_sections() {
        $product = $this->get_product();
        $theme = $this->product->get_product_data()->get_theme();
        $this->isdefaulttheme = $theme === 'defaultTheme';
        $this->isnuancestheme = $theme === 'nuancesTheme';
        $sections = $product->get_activity_sections();
        $user = app::get_userfactory()->get_current_user();
        $groupByCategories = true;
        foreach ($sections as $section) {
            $activities = $this->product->get_activities_and_resources_by_section($section->get_id(), $user, $this->course);
            switch ($section->get_section()) {
                case 'actAndRec':
                    $headerhelp = 'headerhelplibrarysequenceview';
                    $texthelp = 'texthelplibrarysequenceview' . ($this->isteacher ? 'teacher' : 'student');
                    $groupByCategories = false;
                    break;
                case 'actAndResAsi':
                    $headerhelp = 'headerhelplibrarylistview';
                    $texthelp = $this->isteacher ? 'texthelplibrarylistviewteacher' : '';
                    $groupByCategories = true;
                    break;
                case 'docTeacher':
                case 'docStudent':
                    $headerhelp = 'headerhelplibrarypilview';
                    $texthelp = 'texthelplibrarypilview' . ($this->isteacher ? 'teacher' : 'student');
                    $groupByCategories = true;
                    break;
                default: break;
            }

            $hashelppanel = false;
            $helppanellink = '';
            $helppanellinklabel = '';
            $this->issmauser = $user->get_user_data()->is_sma_user();
            if($this->issmauser){
                $helppanel = $product->get_product_data()->get_helppanel($section->get_section());
                if($helppanel){
                    $hashelppanel = true;
                    $helppanellink = $helppanel->link;
                    $helppanellinklabel = $helppanel->linkLabel;
                }
            }

            $showsubcategoriesname = $section->get_idview() === 'listSubcategories';

            $sectiondata = [
                'id' => $section->get_id(),
                'idView' => $section->get_idview(),
                'section' => $section->get_section(),
                'extendedDropDown' => $section->get_idview() === 'tablet',
                'filters' => $this->get_section_filters($section->get_section(), $activities),
                'headerhelp' => $headerhelp != '' ? get_string($headerhelp, 'local_npe') : '',
                'texthelp' => $texthelp != '' ? get_string($texthelp, 'local_npe') : '',
                'hashelppanel' => $hashelppanel,
                'helppanellink' => $helppanellink,
                'helppanellinklabel' => $helppanellinklabel,
                'showsubcategoriesname' => $showsubcategoriesname,
            ];
            $categories = $product->get_categories_by_section($section->get_id());
            if (!empty($categories) && $groupByCategories) {
                foreach ($categories as $category) {
                    $categorydata = $this->get_category_data();
                    $categorydata->set_category_dto($category);
                    $categorydata->set_section_id($section->get_id());
                    $categorydata->set_params();
                    $sectiondata['categories'][] = $categorydata;
                }
            } else {
                $categorydata = $this->get_category_data();
                $categorydata->set_section_id($section->get_id());
                $categorydata->set_params();
                $sectiondata['categories'][] = $categorydata;
            }
            if ($sectiondata['idView'] === 'tablet') {
                foreach ($sectiondata['categories'] as $section) {
                    // Re-order activities based on idView.
                    usort($section->items, function($a, $b) {
                        return ($a->name < $b->name) ? -1 : 1;
                    });
                }
            }
            $this->sections[] = $sectiondata;
        }
    }

    /**
     * @throws \dml_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\course_exception
     * @throws \coding_exception
     */
    private function set_filter() {
        $filterdata = $this->get_filter_data();
        $filterdata->set_params();

        foreach ($filterdata->filtertopic as $key => $val) {
            if ($val->sdtype == 'noIndex') {
                $item = $filterdata->filtertopic[$key];
                unset($filterdata->filtertopic[$key]);
                array_unshift($filterdata->filtertopic, $item);
                break;
            }
        }
        $this->filter = $filterdata;
    }

    /**
     * @param $section
     * @param $activities
     * @return array
     * @throws \JsonException
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    private function get_section_filters($section, $activities) {
        if ( !$this->isteacher ) {
            $this->course->get_customization_by_producttopicpackerid();
        }

        $sections = $this->get_product()->get_product_data()->get_repository_json();
        //$sections = current($sections);
        $is_student = app::get_instance()->get(user_factory::class)->get_current_user()->is_student();
        $arrStdClass =  get_object_vars($sections);
        $sections = $arrStdClass['sections'];
        $key = array_search($section, array_column($sections, 'section'));
        $this->persovisible = json_encode($this->product->get_perso_visible());
        $persovisible = json_decode($this->persovisible);
        $filters = [];
        foreach ($sections[$key]->sectionFilters as $filter) {
            $options = [];
            if ((($filter->profile === app::get_menu_helper()::PROFILEALLPROFILES ||
                        $filter->profile == app::get_menu_helper()::PROFILETEACHER)
                    && $this->isteacher) ||
                (($filter->profile === app::get_menu_helper()::PROFILEALLPROFILES ||
                        $filter->profile == app::get_menu_helper()::PROFILESTUDENT)
                    && !$this->isteacher)) {


                if ($filter->filter === 'sequenceId' && $filter->values) {
                    $opt = $this->filters_filtered($filter, $activities, $sections[$key]->section);
                    // Construímos el nombre de manera diferente
                    foreach ($opt as $single) {
                        $topicinfo = product::get_instance($this->get_product()->get_codproduct())->get_topic_by_packerid($single->key);
                        $ishidden = ($is_student &&
                        $topicinfo->get_topic_data()->get_visibilityn2() !== null && $topicinfo->get_topic_data()->get_visibilityn2() === 'onlyTeacher') ||
                                    ($persovisible !== null && in_array($single->key, $persovisible,true));
                        $ccaa = product_topic::check_ccaa_variation($this->course->get_ccaa());
                        if ($ishidden || (null !== $ccaa &&
                            $topicinfo->available_for_ccaa($ccaa) === false)) {
                            continue;
                        }
                        $options[] = [
                            'key' => $single->key,
                            'value' => $topicinfo->get_sdanumber() ?
                                $topicinfo->get_sdanumber() . ' - ' . $single->value : $single->value,
                            'blockOrder' => $single->unitOrder ?? '',
                            'sdatype' => $topicinfo->get_sdatype()
                        ];
                    }
                    usort($options, fn($a, $b) => $a['blockOrder'] <=> $b['blockOrder']);
                    foreach ($options as $index => $value) {
                        if ($value['sdatype'] == 'noIndex') {
                            $temp = $value;
                            unset($options[$index]);
                            array_unshift($options, $temp);
                            break;
                        }
                    }
                } else {
                    $options = isset($filter->values) ? $this->filters_filtered($filter, $activities, $sections[$key]->section) : '';
                    if ($options) {
                        usort($options, function($a, $b) {
                            return isset($a->blockOrder) ? $a->blockOrder <=> $b->blockOrder : 0;
                        });
                    }
                }

                if ($filter->filter === 'categoryId' && $filter->values) {
                    if (!$this->filters_filtered($filter, $activities, $sections[$key]->section)) {
                        return;
                    }

                    $options = isset($filter->values) ? $this->filters_filtered($filter, $activities, $sections[$key]->section) : '';
                    if ($options) {
                        usort($options, function($a, $b) {
                            return isset($a->blockOrder) ? $a->blockOrder <=> $b->blockOrder : 0;
                        });
                        $options = json_decode(json_encode($options), true);
                        // Find the matching element in itemssection1 and add filters
                        foreach ($this->itemssection1 as $index => $item) {
                            if (isset($item['section']) && $item['section'] === $section) {
                                $this->sidemenu['itemssection1'][$index]['filters'] = $options;
                                $this->itemssection1[$index]['filters'] = $options;
                                $this->sidemenu['itemssection1'][$index]['hasfilters'] = true;
                                $this->itemssection1[$index]['hasfilters'] = true;
                                break;
                            }
                        }
                    } else {
                        $this->itemssection1[$index]['hasfilters'] = false;
                        $this->sidemenu['itemssection1'][$index]['hasfilters'] = false;
                    }
                }
                // Comprobamos si el filtro tiene resultados antes de pintarlo
                $filtercolvalue = isset($filter->filterCols) && (int) $filter->filterCols >= 2;
                $filters[] = [
                    'filterId' => $section . '-' . $filter->filter,
                    'filterName' => $filter->filter,
                    'filterLabel' => $filter->filterLabel,
                    'filterCols' => $filtercolvalue,
                    'hasoptions' => isset($filter->values),
                    'options' => $options
                ];
            }
        }
        return $filters;
    }

    /**
     * @param $filter
     * @param $activities
     * @param $section
     * @return array|void
     * @throws \dml_exception
     */
    private function filters_filtered($filter, $activities, $section) {
        $options = [];
        foreach ($filter->values as $filteritem) {
            $search = $filter->filter === 'recommendedGroupingId'
                ? $this->map_delivery($filteritem->key)
                : $filteritem->key;
            $found = array_filter($activities,function(activity $obj) use ($search, $section, $filter) {
                return (
                    $obj->get_activity_data()->get_activity_section() === $section &&
                    $this->map_function($filter->filter) &&
                    $obj->get_activity_data()->{$this->map_function($filter->filter)}() == $search
                );
            },ARRAY_FILTER_USE_BOTH);
            if ($found) {
                $options[] = $filteritem;
            }
        }
        return $options;
    }

    /**
     * @param $key
     * @return string
     */
    private function map_delivery($key): string {
        $mapping = [
            'individual' => 'single',
            'team' => 'group',
            'bigGroup' => 'grandgroup',
            'convertTeam' => 'convertTeam'
        ];
        return $mapping[$key];
    }

    /**
     * @param $typename
     * @return string
     */
    private function map_function($typename): string {
        $method = [
            'blockId' => 'get_product_topic_block_packerid',
            'sequenceId' => 'get_product_topic_packerid',
            'activityTypeId' => 'get_type_id',
            'competenceId' => 'get_competence',
            'criterionId' => 'get_criterion',
            'themeId' => 'get_theme',
            'transverseKeyId' => 'get_transversekey',
            'keyEvidenceId' => 'get_key_evidence',
            'difficultyLevelId' => 'get_difficultylevel',
            'categoryId' => 'get_activity_category_category',
            'recommendedGroupingId' => 'get_default_delivery',
            'skillsId' => 'get_skill',
            'pedagogicalPurposesId' => 'get_pedagogical_purposes',
            'assessmentId' => 'get_assessment_id',
            'learningLevelId' => 'get_learning_level_id',
            'trackingActivitiesId' => 'get_tracking_activities_id',
            'challengesId' => 'get_challenges_id',
            'inContextId' => 'get_in_context_id',
            'typeOfActivityId' => 'get_type_of_activity_id',
            'presentationResourcesId' => 'get_presentation_resources_id',
            'subcategoryId' => 'get_activity_subcategory'
        ];
        return $method[$typename] ?? '';
    }

    /**
     * @return megamenu\seccolums[]
     * @throws \local_npe\exception\npe_exception
     */
    private function get_all_filters() {
        $megamenu = megamenu::get_instance($this->product->get_codproduct());
        /** @var megamenu\block_menu $blockmenu */
        $blockmenu = $this->array_filter($megamenu->get_blockmenu(), 'idblocks', '3');
        /** @var megamenu\areas_sections $areasections */
        $areasections = $this->array_filter($blockmenu->get_areassections(), 'areaid', '1');
        return $areasections->get_columsmenus()[0]->get_seccolums();
    }

    /**
     * @return category_data
     */
    private function get_category_data() {
        return $this->mockcategorydata ?: new category_data($this->course, $this->product, $this->assignmentfilter);
    }

    /**
     * Se emplea para Test Unitarios.
     *
     * @param category_data $sectiondata
     */
    public function set_mock_category_data(category_data $sectiondata) {
        $this->mockcategorydata = $sectiondata;
    }

    /**
     * @return filter_data
     */
    private function get_filter_data() {
        return $this->mockfilterdata ?: new filter_data($this->course, $this->product, $this->assignmentfilter);
    }

    /**
     * Se emplea para Test Unitarios.
     *
     * @param filter_data $filterdata
     */
    public function set_mock_filter_data(filter_data $filterdata) {
        $this->mockfilterdata = $filterdata;
    }

    /**
     * @return session
     */
    private function get_session_helper() {
        return $this->sessionhelper ?? $this->set_session_helper();
    }

    /**
     * Setea el url helper.
     * Costura para Test Unitarios.
     *
     * @param session|null $sessionhelper |null $urlhelper
     * @return session $session
     */
    private function set_session_helper(session $sessionhelper = null) {
        return $this->sessionhelper = $sessionhelper ?? new session();
    }

    /**
     * @return json
     */
    private function get_json_helper() {
        return $this->jsonhelper ?? $this->set_json_helper();
    }

    /**
     * Setea el json helper.
     * Costura para Test Unitarios.
     *
     * @param json|null $jsonhelper
     * @return json
     */
    private function set_json_helper(): json {
        $this->jsonhelper = new json();
        return $this->jsonhelper;
    }

    /**
     * @throws \Exception
     */
    private function set_sidemenu_items(bool $isstudent = false, $data = []) {
        $product = $this->product->get_codproduct();
        $this->sidemenu = $this->get_json_helper()->get_side_menu_items($data, $product, $isstudent);
        if ($this->sidemenu) {
            $this->itemssection1 = $this->sidemenu['itemssection1'];
            $this->itemssection3 = $this->sidemenu['itemssection3'];
        }
        $this->emptyarea3 = empty($this->itemssection3);
    }

    private function set_side_menu() {
        $section = substr(static::class, strripos(static::class, '\\') + 1,
            strripos(static::class, '_') - strripos(static::class, '\\') - 1);
        $data = (object)
        [
            'courseid' => $this->course->get_course_data()->get_id(),
            'section' => $section,
            'category' => $this->course->get_course_data()->get_category(),
        ];
        $this->set_sidemenu_items(app::get_instance()->get(user_factory::class)->get_current_user()->is_student(), $data);
    }

    private function set_floatingroup_items(bool $isstudent) {
        $this->floatingroup = $this->get_json_helper()->get_floating_group($this->course->get_codproduct(), $isstudent);
        if ($this->floatingroup) {
            $this->floatingrouptitle = $this->floatingroup['title'];
            $this->floatingrouptab0 = $this->floatingroup['tab0'];
            $this->floatingrouptab1 = $this->floatingroup['tab1'];
            $this->itemstab0 = $this->floatingroup['itemstab0'];
            $this->itemstab1 = $this->floatingroup['itemstab1'];
            if ($this->floatingroup['tab0id'] === 'txtClassroom') {
                $this->istabletst0 = true;
                $this->idtab0 = 'nav-aula';
                $this->idtab1 = 'nav-area';
            } else {
                $this->istabletst1 = true;
                $this->idtab0 = 'nav-area';
                $this->idtab1 = 'nav-aula';
            }
            if ($this->floatingroup['tab0view'] === 'tablet') {
                $this->tab0type = 'tablets';
                $this->tab1type = 'lis';
            } else {
                $this->tab0type = 'lis';
                $this->tab1type = 'tablets';
            }
        }
    }

    /**
     * @return breadcrumbs
     */
    private function get_breadcrumb_helper() {
        return $this->breadcrumbhelper ?? $this->set_breadcrumb_helper();
    }

    /**
     * Setea el breadcrumbs helper.
     * Costura para Test Unitarios.
     *
     * @param breadcrumbs|null $breadcrumbs
     * @return breadcrumbs
     */
    public function set_breadcrumb_helper(breadcrumbs $breadcrumbs = null) {
        return $this->breadcrumbshelper = $breadcrumbs ?? new breadcrumbs();
    }

    /**
     * @return product
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     */
    public function get_product() {
        return product::get_instance($this->course->get_product()->get_codproduct());
    }

    /**
     * @return url
     */
    private function get_url_helper() {
        return $this->urlhelper ?? $this->set_url_helper();
    }

    /**
     * Setea el url helper.
     * Costura para Test Unitarios.
     *
     * @param url|null $urlhelper
     * @return url
     */
    private function set_url_helper(): url{
        $this->urlhelper = new url();
        return $this->urlhelper;
    }
}

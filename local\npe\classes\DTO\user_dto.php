<?php

namespace local_npe\DTO;

use local_npe\app;
use local_npe\base\stdclass_transform;

/**
 * Class UserDTO
 *
 * @package local_npe\DTO
 */
class user_dto extends stdclass_transform {

    protected int $id;
    protected string $auth;
    protected int $confirmed = 1;
    protected int $policyagreed;
    protected int $deleted;
    protected int $mnethostid = 1;
    protected string $username;
    protected string $idnumber = '';
    protected string $fullname;
    protected string $firstname;
    protected string $lastname;
    protected string $email;
    protected ?int $icq;
    protected string $institution;
    protected string $department;
    protected string $address;
    protected string $city;
    protected string $country;
    protected string $lang;
    protected string $picture;
    protected ?string $timezone;
    protected ?int $lastaccess;
    protected ?int $lastlogin;
    protected ?int $currentlogin;
    protected int $autosubscribe = 0;
    protected int $trackforums = 1;
    protected int $suspended = 0;
    protected ?string $phone1;

    // Atributos del Profile del usuario (usado por Educamos).
    protected ?string $profile_field_nombre; // Nombre del tutor
    protected ?string $profile_field_apellidos; // Apellido del tutor
    protected ?string $profile_field_email; // Correo del tutor.
    protected ?string $profile_field_identorno;
    protected ?string $profile_field_idproyectoci;
    protected ?string $profile_field_idcalendario;
    protected ?string $profile_field_sistemaexterno;

    // Los atributos privados NO se devuelven con un getData().
    private bool $iseducamosuser;
    private bool $ismarsupialuser;
    private bool $isexternaluser;
    private bool $issenecauser;
    private string $educamosguid;
    /** @var callable $picturecallback */
    private $picturecallback;


    /**
     * Alias que relaciona los campos que devuelve CI con los atributos de la clase.
     *
     * @return array
     */
    protected function get_alias(): array {
        return [
            'Guid' => 'idnumber',
            'Login' => 'username',
            'IdRol' => 'icq',
            'Nombre' => 'firstname',
            'Apellidos' => 'lastname',
            'CodPais' => 'country',
            'ComunidadAutonoma' => 'institution',
            'Idioma' => 'lang',
            'CodCentroSAP' => 'department',
            'CodCategoriaEVA' => 'address',
            'NombreTutorLegal' => 'profile_field_nombre',
            'ApellidosTutorLegal' => 'profile_field_apellidos',
            'EmailTutorLegal' => 'profile_field_email',
            'IdTipoCentro' => 'profile_field_identorno',
            'IdProyecto' => 'profile_field_idproyectoci',
            'IdCalendario' => 'profile_field_idcalendario',
            'SistemasExternos' => 'profile_field_sistemaexterno',
            'TextosLegales' => 'policyagreed',
            'EsDirector' => 'phone1'
        ];
    }

    /**
     * Si el icq tiene valor convertir a entero.
     * Si viene como cadena vacía dejarlo como null.
     *
     * Nota: Sin este método, la conversión de cadena vacía que hace el core es a 0 en lugar de null.
     *
     * @param $value
     * @return int|null
     */
    protected function _prepare_icq($value) {
        return empty($value) === false ? (int) $value : null;
    }

    /**
     * @param $value
     * @return false|mixed|string|string[]|null
     */
    protected function _prepare_username($value) {
        return mb_strtolower($value, 'UTF-8');
    }

    /**
     * @param $value
     * @return int
     */
    protected function _prepare_policyagreed($value) {
        $policyagreed = $value;
        if(is_array($value)) {
            $textos = array_filter($value, static function($texto) {
                return (isset($texto->Tipo) && $texto->Tipo === 'PP');
            });
            $textolegale = current($textos);
            $policyagreed = (isset($textolegale->Aceptado) && $textolegale->Aceptado === true) ? 1 : 0;
        }

        return $policyagreed;
    }

    /**
     * @param $value
     * @return string
     */
    protected function _prepare_profile_field_sistemaexterno($value) {
        $sistemaexterno = $value;
        if(is_array($value)) {
            $sistemaexterno = $sistemaexterno[0]->Sistema;
        }
        return $sistemaexterno;
    }

    /**
     * @return int
     */
    public function get_id() :int {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function set_id($id): void {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function get_auth() {
        return $this->auth;
    }

    /**
     * @param mixed $auth
     */
    public function set_auth($auth): void {
        $this->auth = $auth;
    }

    /**
     * @return mixed
     */
    public function get_username() {
        return $this->username;
    }

    /**
     * @param mixed $username
     */
    public function set_username($username): void {
        $this->username = $username;
    }

    /**
     * @return mixed
     */
    public function get_idnumber() {
        return $this->idnumber;
    }

    /**
     * @param mixed $idnumber
     */
    public function set_idnumber($idnumber): void {
        $this->idnumber = $idnumber;
    }

    /**
     * @return mixed
     */
    public function get_fullname() {
        return $this->fullname;
    }

    /**
     * @param mixed $fullname
     */
    public function set_fullname($fullname): void {
        $this->fullname = $fullname;
    }

    /**
     * @return mixed
     */
    public function get_firstname() {
        return $this->firstname;
    }

    /**
     * @param mixed $firstname
     */
    public function set_firstname($firstname): void {
        $this->firstname = $firstname;
    }

    /**
     * @return mixed
     */
    public function get_lastname() {
        return $this->lastname;
    }

    /**
     * @param mixed $lastname
     */
    public function set_lastname($lastname): void {
        $this->lastname = $lastname;
    }

    /**
     * @return mixed
     */
    public function get_email() {
        return $this->email;
    }

    /**
     * @param mixed $email
     */
    public function set_email($email): void {
        $this->email = $email;
    }

    /**
     * @return mixed
     */
    public function get_icq() {
        return $this->icq;
    }

    /**
     * @param mixed $icq
     */
    public function set_icq($icq): void {
        $this->icq = $icq;
    }

    /**
     * @return mixed
     */
    public function get_institution() {
        return $this->institution;
    }

    /**
     * @param mixed $institution
     */
    public function set_institution($institution): void {
        $this->institution = $institution;
    }

    /**
     * @return mixed
     */
    public function get_address() {
        return $this->address;
    }

    /**
     * @param mixed $address
     */
    public function set_address($address): void {
        $this->address = $address;
    }

    /**
     * @return mixed
     */
    public function get_city() {
        return $this->city;
    }

    /**
     * @param mixed $city
     */
    public function set_city($city): void {
        $this->city = $city;
    }

    /**
     * @return mixed
     */
    public function get_country() {
        return $this->country;
    }

    /**
     * @param mixed $country
     */
    public function set_country($country): void {
        $this->country = $country;
    }

    /**
     * @return mixed
     */
    public function get_lang() {
        return $this->lang;
    }

    /**
     * @param mixed $lang
     */
    public function set_lang($lang): void {
        $this->lang = $lang;
    }

    /**
     * @return bool
     */
    public function is_sma_user(): bool {
        return (bool) (!$this->ismarsupialuser && !$this->iseducamosuser && !$this->isexternaluser && !$this->issenecauser);
    }

    /**
     * @return mixed
     */
    public function is_marsupial_user(): bool {
        return $this->ismarsupialuser;
    }

    /**
     * @return mixed
     */
    public function is_educamos_user() {
        return $this->iseducamosuser;
    }

   /**
     * @return mixed
     */
    public function is_external_user(): bool {
        return $this->isexternaluser;
    }

     /**
     * @return mixed
     */
    public function is_seneca_user(): bool {
        return $this->issenecauser;
    }

    /**
     * @param mixed $iseducamosuser
     */
    public function set_iseducamosuser($iseducamosuser): void {
        $this->iseducamosuser = $iseducamosuser;
    }

    /**
     * @param mixed $ismarsupialuser
     */
    public function set_ismarsupialuser($ismarsupialuser): void {
        $this->ismarsupialuser = $ismarsupialuser;
    }

    /**
     * @param mixed $isexternaluser
     */
    public function set_isexternaluser($isexternaluser): void {
        $this->isexternaluser = $isexternaluser;
    }

     /**
     * @param mixed $issenecauser
     */
    public function set_issenecauser($issenecauser): void {
        $this->issenecauser = $issenecauser;
    }

    /**
     * @return mixed
     */
    public function get_educamosguid() {
        return $this->educamosguid;
    }

    /**
     * @param mixed $educamosguid
     */
    public function set_educamosguid($educamosguid): void {
        $this->educamosguid = $educamosguid;
    }

    /**
     * @return string|null
     */
    public function get_tutor_name(): ?string {
        return $this->profile_field_nombre;
    }

    /**
     * @param string|null $nombre
     */
    public function set_tutor_name(?string $nombre): void {
        $this->profile_field_nombre = $nombre;
    }

    /**
     * @return string|null
     */
    public function get_tutor_lastname(): ?string {
        return $this->profile_field_apellidos;
    }

    /**
     * @param string|null $apellidos
     */
    public function set_tutor_lastname(?string $apellidos): void {
        $this->profile_field_apellidos = $apellidos;
    }

    /**
     * @return string|null
     */
    public function get_tutor_email(): ?string {
        return $this->profile_field_email;
    }

    /**
     * @param string|null $emailtutor
     */
    public function set_tutor_email(?string $emailtutor): void {
        $this->profile_field_email = $emailtutor;
    }

    /**
     * @return string|null
     */
    public function get_idcalendario(): ?string {
        return $this->profile_field_idcalendario;
    }

    /**
     * @param string|null $idcalendario
     */
    public function set_idcalendario(?string $idcalendario): void {
        $this->profile_field_idcalendario = $idcalendario;
    }


    /**
     * @return mixed
     */
    public function get_picture() {
        return $this->picture ?: $this->picture = call_user_func($this->picturecallback);
    }

    /**
     * @param mixed $picture
     */
    public function set_picture($picture): void {
        $this->picture = $picture;
    }

    /**
     * @param callable $callback
     */
    public function set_picture_callback(callable $callback): void {
        $this->picturecallback = $callback;
    }

    /**
     * @return string|null
     */
    public function get_timezone(): ?string {
        return $this->timezone;
    }

    /**
     * @return mixed
     */
    public function get_lastaccess() {
        return $this->lastaccess;
    }

    /**
     * @param mixed $lastaccess
     */
    public function set_lastaccess($lastaccess): void {
        $this->lastaccess = $lastaccess;
    }

    /**
     * @return mixed
     */
    public function get_department() {
        return $this->department;
    }

    /**
     * @param mixed $department
     */
    public function set_department($department): void {
        $this->department = $department;
    }

    /**
     * @return int
     */
    public function get_confirmed(): int {
        return $this->confirmed;
    }

    /**
     * @param int $confirmed
     */
    public function set_confirmed(int $confirmed): void {
        $this->confirmed = $confirmed;
    }

    /**
     * @return int
     */
    public function get_autosubscribe(): int {
        return $this->autosubscribe;
    }

    /**
     * @param int $autosubscribe
     */
    public function set_autosubscribe(int $autosubscribe): void {
        $this->autosubscribe = $autosubscribe;
    }

    /**
     * @return mixed
     */
    public function get_lastlogin() {
        return $this->lastlogin;
    }

    /**
     * @param mixed $lastlogin
     */
    public function set_lastlogin($lastlogin): void {
        $this->lastlogin = $lastlogin;
    }

    /**
     * @return int
     */
    public function get_mnethostid(): int {
        return $this->mnethostid;
    }

    /**
     * @param int $mnethostid
     */
    public function set_mnethostid(int $mnethostid): void {
        $this->mnethostid = $mnethostid;
    }

    /**
     * @return mixed
     */
    public function get_currentlogin() {
        return $this->currentlogin;
    }

    /**
     * @param mixed $currentlogin
     */
    public function set_currentlogin($currentlogin): void {
        $this->currentlogin = $currentlogin;
    }

    /**
     * @return mixed
     */
    public function get_policyagreed() {
        return $this->policyagreed;
    }

    /**
     * @param mixed $policyagreed
     */
    public function set_policyagreed($policyagreed): void {
        $this->policyagreed = $policyagreed;
    }

    /**
     * @return mixed
     */
    public function get_profile_field_sistemaexterno() {
        return $this->profile_field_sistemaexterno;
    }

    /**
     * @param mixed $profile_field_sistemaexterno
     */
    public function set_profile_field_sistemaexterno($profile_field_sistemaexterno): void {
        $this->profile_field_sistemaexterno = $profile_field_sistemaexterno;
    }

    /**
     * @return mixed
     */
    public function get_deleted() {
        return $this->deleted;
    }

    /**
     * @param mixed $deleted
     */
    public function set_deleted($deleted): void {
        $this->deleted = $deleted;
    }


    /**
     * @return int
     */
    public function get_suspended(): int {
        return $this->suspended;
    }

    /**
     * @param int $suspended
     */
    public function set_suspended(int $suspended): void {
        $this->suspended = $suspended;
    }

    /**
     * @return int
     */
    public function get_phone1(): string {
        return $this->phone1;
    }

    /**
     * @param int $phone1
     */
    public function set_phone1(int $phone1): void {
        $this->phone1 = $phone1;
    }
}

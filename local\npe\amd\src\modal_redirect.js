import $ from 'jquery';
import Notification from 'core/notification';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';
import Str from 'core/str';

const SELECTORS = {
    MODALTITLE: '.modal-title',
    MODALDESCRIPTION: '.modal-description',
    HIDE: '.close',
    CANCEL: '[data-action="hide"]',
    FINISH: '[data-action="finish"]',
};

let registered = false;

export default class ModalRedirect extends Modal {

    static TYPE = 'local_npe/modal_redirect';
    static TEMPLATE = 'local_npe/commons/modal_redirect';

    constructor(root) {
        super(root);
    }

    setData(redirectUrl, titleButtonKey, titleKey, descriptionKey, descriptionParam = null) {
        this.getRoot().find(SELECTORS.FINISH).attr('data-url', redirectUrl);

        var description = descriptionParam !== null
            ? {key: description<PERSON>ey, component: 'local_npe', param: JSON.parse(descriptionParam)}
            : {key: descriptionKey, component: 'local_npe'};

        var titleButton = {key: titleButtonKey, component: 'local_npe'};
        var title = {key: titleKey, component: 'local_npe'};
        var requeststrings = [titleButton, title, description];

        Str.get_strings(requeststrings).done((strings) => {
            let titlebuttonstring = strings[0];
            let titlestring = strings[1];
            let descriptionstring = strings[2];
            this.getRoot().find(SELECTORS.FINISH).html(titlebuttonstring);
            this.getRoot().find(SELECTORS.MODALTITLE).html(titlestring);
            if (descriptionKey) {
                this.getRoot().find(SELECTORS.MODALDESCRIPTION).html(descriptionstring);
            }
        }).fail(Notification.exception);
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.CANCEL, () => {
            this.closeModal();
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {
            this.closeModal();
            window.location.href = this.getRoot().find(SELECTORS.FINISH).data('url');
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {
            this.closeModal();
        });
    }

    closeModal() {
        this.getRoot().removeClass('show');
        $('body').removeClass('modal-open');
        $('.success-modal').remove();
        $('.modal-backdrop').removeClass('show').addClass('hide');
    }
}

if (!registered) {
    ModalRegistry.register(ModalRedirect.TYPE, ModalRedirect, ModalRedirect.TEMPLATE);
    registered = true;
}

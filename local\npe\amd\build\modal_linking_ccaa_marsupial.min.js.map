{"version": 3, "file": "modal_linking_ccaa_marsupial.min.js", "sources": ["../src/modal_linking_ccaa_marsupial.js"], "sourcesContent": ["import $ from 'jquery';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\nimport Ajax from 'core/ajax';\nimport ModalLinkingCCAASuccessApi from 'local_npe/modal_linking_success_api';\nimport ModalLinkingCCAAFailApi from 'local_npe/modal_linking_fail_api';\nimport ModalFactory from 'core/modal_factory';\n\nconst SELECTORS = {\n    MODAL: '.linking-ccaa-modal',\n    MODALBACKDROP: '.modal-backdrop',\n    CCAA_TITLE: '#title_ccaa',\n    SELECT_CCAA_TEXT: '#linkccaa_text',\n    SELECT_CCAA: '#linkccaa',\n    CHOOSE_CCAA_DROPDOWN: '#linkccaa_dropdown',\n    CHOOSE_CCAA_DROPDOWN_CONTENT: '#linkccaa_dropdown_content',\n    CHOOSE_CCAA_OPTION: '.dropdown_ccaa_item',\n    CHOOSE_CCAA_ICO: '#dropdown_ico',\n    CONFIRM: '[data-action=\"confirm\"]',\n    CCAAQUESTION: '#caaquestion',\n    CCAAINFO: '#ccaainfo'\n};\n\nconst SERVICES = {\n    LINKGROUPCCAA: 'local_npe_link_group_ccaa'\n};\n\nlet registered = false;\n\nexport default class ModalLinkCCAAMarsupial extends Modal {\n\n    static TYPE = 'local_npe/modal_link_ccaa_marsupial';\n    static TEMPLATE = 'local_npe/courses/modal_linking_ccaa_marsupial';\n\n    #textccaainfo = true;\n\n    constructor(root) {\n        super(root);\n    }\n\n    setData(ccaaavailable) {\n        if (Object.keys(ccaaavailable).length > 0) {\n            let first = true;\n            for (const ccaaKey in ccaaavailable) {\n                if (first) {\n                    first = false;\n                    this.getRoot().find(SELECTORS.SELECT_CCAA_TEXT).val(ccaaavailable[ccaaKey]['name']);\n                    this.getRoot().find(SELECTORS.SELECT_CCAA).val(ccaaavailable[ccaaKey]['id']);\n                } else {\n                    var sep_opt = document.createElement('div');\n                    sep_opt.classList.add('separator');\n                    this.getRoot().find(SELECTORS.CHOOSE_CCAA_DROPDOWN_CONTENT).append(sep_opt);\n                }\n                var newOpt = document.createElement('div');\n                var text = document.createTextNode(ccaaavailable[ccaaKey]['name']);\n                newOpt.appendChild(text);\n                newOpt.setAttribute('data-value', ccaaavailable[ccaaKey]['id']);\n                newOpt.classList.add('dropdown_ccaa_item');\n                this.getRoot().find(SELECTORS.CHOOSE_CCAA_DROPDOWN_CONTENT).append(newOpt);\n            }\n            this.getRoot().find(SELECTORS.SELECT_CCAA_TEXT).removeClass('d-none');\n            this.getRoot().find(SELECTORS.CCAA_TITLE).removeClass('d-none');\n            this.getRoot().find(SELECTORS.CHOOSE_CCAA_ICO).removeClass('d-none');\n        }\n    }\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.SELECT_CCAA_TEXT, () => {\n            if ($(SELECTORS.CHOOSE_CCAA_DROPDOWN).hasClass('d-none')) {\n                $(SELECTORS.CHOOSE_CCAA_ICO).css({'transform': 'rotate(270deg) translateX(7px)'});\n            } else {\n                $(SELECTORS.CHOOSE_CCAA_ICO).css({'transform': 'rotate(90deg)'});\n            }\n            $(SELECTORS.CHOOSE_CCAA_DROPDOWN).toggleClass('d-none');\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CHOOSE_CCAA_OPTION, (e) => {\n            this.getRoot().find(SELECTORS.SELECT_CCAA_TEXT).val(e.target.innerHTML);\n            $(SELECTORS.CHOOSE_CCAA_OPTION).removeClass('selected');\n            e.target.classList.add('selected');\n            this.getRoot().find(SELECTORS.SELECT_CCAA).val(e.target.getAttribute('data-value'));\n            $(SELECTORS.CHOOSE_CCAA_DROPDOWN).addClass('d-none');\n            $(SELECTORS.CHOOSE_CCAA_ICO).css({'transform': 'rotate(90deg)'});\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CONFIRM, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $(SELECTORS.MODAL).removeClass('show').addClass('hide');\n            $(SELECTORS.MODALBACKDROP).removeClass('show').addClass('hide');\n            let searchParams = new URLSearchParams(window.location.search);\n            let courseid = searchParams.get('id') ? searchParams.get('id') : null;\n            let ccaaid = this.getRoot().find(SELECTORS.SELECT_CCAA).val() ? this.getRoot().find(SELECTORS.SELECT_CCAA).val() : null;\n            this.ajaxCall(courseid, ccaaid);\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CCAAQUESTION, () => {\n            this.showtext();\n        });\n\n        $(window).on('keydown', (event) => {\n            if (event.key === 'Escape') {\n                $(SELECTORS.MODALBACKDROP).removeClass('hide').addClass('show').css('z-index', '1051');\n                $(SELECTORS.MODAL).css('z-index', '1052');\n            }\n        });\n    }\n\n    showtext() {\n        if (this.#textccaainfo) {\n            $(SELECTORS.CCAAINFO).show();\n            this.#textccaainfo = false;\n        } else {\n            $(SELECTORS.CCAAINFO).hide();\n            this.#textccaainfo = true;\n        }\n    }\n\n    ajaxCall(courseid, ccaaid) {\n        const promises = Ajax.call([{\n            methodname: SERVICES.LINKGROUPCCAA,\n            args: {\n                courseid: courseid,\n                ccaaid: ccaaid\n            }\n        }]);\n\n        promises[0].done((response) => {\n            this.ajaxResult(response);\n        }).fail((ex) => {\n            this.ajaxError(ex);\n        });\n    }\n\n    ajaxResult() {\n        ModalFactory.create({type: ModalLinkingCCAASuccessApi.TYPE}).done((modal) => {\n            modal.show();\n        });\n    }\n\n    ajaxError(ex) {\n        ModalFactory.create({type: ModalLinkingCCAAFailApi.TYPE}).done((modal) => {\n            modal.show();\n        });\n        window.console.log(ex);\n    }\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalLinkCCAAMarsupial.TYPE, ModalLinkCCAAMarsupial, ModalLinkCCAAMarsupial.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_classPrivateFieldInitSpec", "t", "a", "has", "TypeError", "_checkPrivateRedeclaration", "set", "_defineProperty", "r", "i", "Symbol", "toPrimitive", "call", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_classPrivateFieldSet", "s", "_assert<PERSON>lassBrand", "n", "arguments", "length", "_j<PERSON>y", "_custom_interaction_events", "_modal", "_modal_registry", "_ajax", "_modal_linking_success_api", "_modal_linking_fail_api", "_modal_factory", "SELECTORS", "SERVICES", "registered", "_textccaainfo", "WeakMap", "ModalLinkCCAAMarsupial", "Modal", "constructor", "root", "super", "this", "setData", "ccaaavailable", "keys", "first", "ccaaKey", "getRoot", "find", "val", "sep_opt", "document", "createElement", "classList", "add", "append", "newOpt", "text", "createTextNode", "append<PERSON><PERSON><PERSON>", "setAttribute", "removeClass", "registerEventListeners", "getModal", "on", "CustomEvents", "events", "activate", "$", "hasClass", "css", "transform", "toggleClass", "target", "innerHTML", "getAttribute", "addClass", "searchParams", "URLSearchParams", "window", "location", "search", "courseid", "get", "ccaaid", "ajaxCall", "showtext", "event", "key", "show", "hide", "Ajax", "methodname", "args", "done", "response", "ajaxResult", "fail", "ex", "ajaxError", "ModalFactory", "create", "type", "ModalLinkingCCAASuccessApi", "TYPE", "modal", "ModalLinkingCCAAFailApi", "console", "log", "_exports", "ModalRegistry", "register", "TEMPLATE"], "mappings": "yYAO8C,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,2BAAAH,EAAAI,EAAAC,IAAA,SAAAL,EAAAI,GAAA,GAAAA,EAAAE,IAAAN,GAAA,MAAA,IAAAO,UAAA,iEAAA,EAAAC,CAAAR,EAAAI,GAAAA,EAAAK,IAAAT,EAAAK,EAAA,CAAA,SAAAK,gBAAAV,EAAAW,EAAAP,GAAAO,OAAAA,EAAA,SAAAP,GAAAQ,IAAAA,EAAA,SAAAR,EAAAO,GAAAP,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAJ,IAAAA,EAAAI,EAAAS,OAAAC,yBAAAd,EAAA,CAAA,IAAAY,EAAAZ,EAAAe,KAAAX,EAAAO,kCAAAC,EAAA,OAAAA,EAAAL,MAAAA,IAAAA,4EAAAI,EAAAK,OAAAC,QAAAb,EAAA,CAAAc,CAAAd,EAAAQ,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAO,CAAAR,MAAAX,EAAAoB,OAAAC,eAAArB,EAAAW,EAAAW,CAAAA,MAAAlB,EAAAmB,YAAAC,EAAAA,cAAAC,EAAAA,cAAAzB,EAAAW,GAAAP,EAAAJ,CAAA,CAAA,SAAA0B,sBAAAC,EAAAtB,EAAAM,UAAAgB,EAAAlB,IAAAmB,kBAAAD,EAAAtB,GAAAM,GAAAA,CAAA,CAAA,SAAAiB,kBAAA5B,EAAAI,EAAAyB,GAAA,GAAA,mBAAA7B,EAAAA,IAAAI,EAAAJ,EAAAM,IAAAF,GAAA,OAAA0B,UAAAC,OAAA,EAAA3B,EAAAyB,EAAA,MAAA,IAAAtB,UAAA,gDAAA,iFAP9CyB,QAAAjC,uBAAAiC,SACAC,2BAAAlC,uBAAAkC,4BACAC,OAAAnC,uBAAAmC,QACAC,gBAAApC,uBAAAoC,iBACAC,MAAArC,uBAAAqC,OACAC,2BAAAtC,uBAAAsC,4BACAC,wBAAAvC,uBAAAuC,yBACAC,eAAAxC,uBAAAwC,gBAEA,MAAMC,gBACK,sBADLA,wBAEa,kBAFbA,qBAGU,cAHVA,2BAIgB,iBAJhBA,sBAKW,YALXA,+BAMoB,qBANpBA,uCAO4B,6BAP5BA,6BAQkB,sBARlBA,0BASe,gBATfA,kBAUO,0BAVPA,uBAWY,eAXZA,mBAYQ,YAGRC,uBACa,4BAGnB,IAAIC,YAAa,EAAM,IAAAC,kBAAAC,QAER,MAAMC,+BAA+BC,OAAAA,QAOhDC,WAAAA,CAAYC,MACRC,MAAMD,MAHV7C,2BAAA+C,KAAAP,eAAgB,EAIhB,CAEAQ,OAAAA,CAAQC,eACJ,GAAIhC,OAAOiC,KAAKD,eAAerB,OAAS,EAAG,CACvC,IAAIuB,OAAQ,EACZ,IAAK,MAAMC,WAAWH,cAAe,CACjC,GAAIE,MACAA,OAAQ,EACRJ,KAAKM,UAAUC,KAAKjB,4BAA4BkB,IAAIN,cAAcG,SAAe,MACjFL,KAAKM,UAAUC,KAAKjB,uBAAuBkB,IAAIN,cAAcG,SAAa,QACvE,CACH,IAAII,QAAUC,SAASC,cAAc,OACrCF,QAAQG,UAAUC,IAAI,aACtBb,KAAKM,UAAUC,KAAKjB,wCAAwCwB,OAAOL,QACvE,CACA,IAAIM,OAASL,SAASC,cAAc,OAChCK,KAAON,SAASO,eAAef,cAAcG,SAAe,MAChEU,OAAOG,YAAYF,MACnBD,OAAOI,aAAa,aAAcjB,cAAcG,SAAa,IAC7DU,OAAOH,UAAUC,IAAI,sBACrBb,KAAKM,UAAUC,KAAKjB,wCAAwCwB,OAAOC,OACvE,CACAf,KAAKM,UAAUC,KAAKjB,4BAA4B8B,YAAY,UAC5DpB,KAAKM,UAAUC,KAAKjB,sBAAsB8B,YAAY,UACtDpB,KAAKM,UAAUC,KAAKjB,2BAA2B8B,YAAY,SAC/D,CACJ,CAEAC,sBAAAA,GACItB,MAAMsB,uBAAuBrB,MAE7BA,KAAKsB,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUpC,4BAA4B,MACrE,EAAAqC,QAAAA,SAAErC,gCAAgCsC,SAAS,WAC3C,EAAAD,QAAAA,SAAErC,2BAA2BuC,IAAI,CAACC,UAAa,oCAE/C,EAAAH,QAAAA,SAAErC,2BAA2BuC,IAAI,CAACC,UAAa,mBAEnD,EAAAH,QAAAA,SAAErC,gCAAgCyC,YAAY,SAAS,IAG3D/B,KAAKsB,WAAWC,GAAGC,2BAAYxE,QAACyE,OAAOC,SAAUpC,8BAA+BxC,IAC5EkD,KAAKM,UAAUC,KAAKjB,4BAA4BkB,IAAI1D,EAAEkF,OAAOC,YAC7D,EAAAN,QAAAA,SAAErC,8BAA8B8B,YAAY,YAC5CtE,EAAEkF,OAAOpB,UAAUC,IAAI,YACvBb,KAAKM,UAAUC,KAAKjB,uBAAuBkB,IAAI1D,EAAEkF,OAAOE,aAAa,gBACrE,EAAAP,QAAAA,SAAErC,gCAAgC6C,SAAS,WAC3C,EAAAR,QAAAA,SAAErC,2BAA2BuC,IAAI,CAACC,UAAa,iBAAiB,IAGpE9B,KAAKsB,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUpC,mBAAmB,KAChEU,KAAKM,UAAUc,YAAY,SAC3B,EAAAO,QAAAA,SAAE,QAAQP,YAAY,eACtB,EAAAO,QAAC3E,SAACsC,iBAAiB8B,YAAY,QAAQe,SAAS,SAChD,EAAAR,QAAC3E,SAACsC,yBAAyB8B,YAAY,QAAQe,SAAS,QACxD,IAAIC,aAAe,IAAIC,gBAAgBC,OAAOC,SAASC,QACnDC,SAAWL,aAAaM,IAAI,MAAQN,aAAaM,IAAI,MAAQ,KAC7DC,OAAS3C,KAAKM,UAAUC,KAAKjB,uBAAuBkB,MAAQR,KAAKM,UAAUC,KAAKjB,uBAAuBkB,MAAQ,KACnHR,KAAK4C,SAASH,SAAUE,OAAO,IAGnC3C,KAAKsB,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUpC,wBAAwB,KACrEU,KAAK6C,UAAU,KAGnB,EAAAlB,QAAAA,SAAEW,QAAQf,GAAG,WAAYuB,QACH,WAAdA,MAAMC,OACN,EAAApB,QAAAA,SAAErC,yBAAyB8B,YAAY,QAAQe,SAAS,QAAQN,IAAI,UAAW,SAC/E,EAAAF,QAAC3E,SAACsC,iBAAiBuC,IAAI,UAAW,QACtC,GAER,CAEAgB,QAAAA,GAxG0C,IAAApE,EAAAtB,IAyGlC6C,MAzGkCvB,EAyG7BgB,eAzG6BiD,IAAAhE,kBAAAD,EAAAtB,MA0GlC,EAAAwE,QAAAA,SAAErC,oBAAoB0D,OACtBxE,sBAAKiB,cAALO,MAAqB,MAErB,EAAA2B,QAAAA,SAAErC,oBAAoB2D,OACtBzE,sBAAKiB,cAALO,MAAqB,GAE7B,CAEA4C,QAAAA,CAASH,SAAUE,QACEO,MAAAA,QAAKrF,KAAK,CAAC,CACxBsF,WAAY5D,uBACZ6D,KAAM,CACFX,SAAUA,SACVE,OAAQA,WAIP,GAAGU,MAAMC,WACdtD,KAAKuD,WAAWD,SAAS,IAC1BE,MAAMC,KACLzD,KAAK0D,UAAUD,GAAG,GAE1B,CAEAF,UAAAA,GACII,eAAY3G,QAAC4G,OAAO,CAACC,KAAMC,mCAA2BC,OAAOV,MAAMW,QAC/DA,MAAMhB,MAAM,GAEpB,CAEAU,SAAAA,CAAUD,IACNE,eAAY3G,QAAC4G,OAAO,CAACC,KAAMI,gCAAwBF,OAAOV,MAAMW,QAC5DA,MAAMhB,MAAM,IAEhBV,OAAO4B,QAAQC,IAAIV,GACvB,EAMH,OALAW,SAAApH,QAAA2C,uBAAAnC,gBAvHoBmC,uBAAsB,OAEzB,uCAAqCnC,gBAFlCmC,uBAAsB,WAGrB,kDAsHjBH,aACD6E,gBAAAA,QAAcC,SAAS3E,uBAAuBoE,KAAMpE,uBAAwBA,uBAAuB4E,UACnG/E,YAAa,GAChB4E,SAAApH,OAAA"}
<?php

namespace local_npe\product;

use core_course\search\section;
use local_npe\app;
use local_npe\product\megamenu\block_menu;
use local_npe\course;
use local_npe\helper\menu;
use local_npe\product\megamenu\seccolums;
use local_npe\traits\cache_url;

defined('MOODLE_INTERNAL') || die();

/**
 * Class megamenu
 *
 * @package local_npe\product
 */
class megamenu extends base {

    use cache_url;

    const PROFILESTUDENT = 'student';
    const PROFILETEACHER = 'teacher';
    const PROFILEALLPROFILES = 'allProfiles';

    const MEGAMENUAREA1 = '1';
    const MEGAMENUAREA2 = '2';
    const MEGAMENUAREA3 = '3';

    /** @var block_menu[]  */
    protected $blockmenu = [];

    /**
     * @throws \dml_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     */
    protected function load_data() {
        $productconfiguration = $this->get_product_configuration();
        foreach ($productconfiguration->megaMenu as $block) {
            /** @var block_menu $blockmenu */
            $blockmenu = clone app::get_instance()->get(block_menu::class);
            $blockmenu->set_data($block);
            $this->blockmenu[] = $blockmenu;
        }
    }

    /**
     * @return block_menu[]
     */
    public function get_blockmenu(): array {
        return $this->blockmenu;
    }

    /**
     * @param $courseid
     * @param $codproduct
     * @return mixed
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\user_exception
     * @throws \moodle_exception
     */
    public function blocks($courseid, $codproduct) {
        $allblocks = $this->get_blockmenu();
        /** @var menu $menu */
        $menu = app::get_instance()->get(menu::class);
        $blocks = [];
        $course = app::get_course($courseid);
        $hasteachers = $course->is_educamos_course() ? $course->has_educamos_teachers() : $course->has_teachers();

        foreach ($allblocks as $block) {
            $blocks[$block->idblocks] = new \stdClass();
            $blocks[$block->idblocks]->id = $block->idblockss;
            $blocks[$block->idblocks]->blocktitle = $block->blocktitle;
            $blocks[$block->idblocks]->hasteacher = $hasteachers;
            $blocks[$block->idblocks]->isstudent = $menu->is_student();
            $blocks[$block->idblocks]->icon = $this->get_url_content($block->blockicon);
            $blocks[$block->idblocks]->blockimage = $block->blockimage;

            if ($block->idblocks == 1) {
                $blocks[$block->idblocks]->url = $menu->build_url(null, null, null, $courseid);
            }

            /*
            * blocks -> areasSections
            */
            $blocks[$block->idblocks]->areassections1 = new \stdClass();
            $blocks[$block->idblocks]->areassections2 = new \stdClass();
            $blocks[$block->idblocks]->areassections3 = new \stdClass();

            if (isset($block->areassections)) {
                foreach ((array)$block->areassections as $section) {
                    if ($section->areaid == self::MEGAMENUAREA1) {
                        $blocks[$block->idblocks]->areassections1->areaid = isset($section->areaid) ? $section->areaid : null;

                        /*
                        * blocks -> columsMenus
                        */
                        foreach ($section->columsmenus as $area1) {
                            $blocks[$block->idblocks]->areassections1->columsmenus = new \stdClass();
                            $blocks[$block->idblocks]->areassections1->columsmenus->title =
                                $menu->get_item_title($area1);

                            /*
                            * blocks -> seccolums
                            */
                            $blocks[$block->idblocks]->areassections1->columsmenus->seccolums = array();
                            $count = 0;

                            foreach ($area1->seccolums ?? [] as $colum) {
                                if (($colum->locationitems == $menu::LOCATIONITEMSBOTH ||
                                        $colum->locationitems == $menu::LOCATIONITEMSONLYMENU) &&
                                    $menu->have_profile($colum->profile)
                                ) {

                                    $idsso = $colum->idsso ?? null;
                                    if (
                                        $colum->sectiontype === 'sso' &&
                                        app::get_panel()->exist_panel_in_config($idsso)
                                    ) {
                                        $hide_panel = !app::get_config()->has_panel_active();
                                        // Si se va a ocultar el panel de informes no continuar.
                                        if ($hide_panel) {
                                            continue;
                                        }
                                    }

                                    $blocks[$block->idblocks]->areassections1->columsmenus->seccolums[$count] = new \stdClass();
                                    $blocks[$block->idblocks]->areassections1->columsmenus->seccolums[$count]->locationitems =
                                        $colum->locationitems ?? null;
                                    $blocks[$block->idblocks]->areassections1->columsmenus->seccolums[$count]->license =
                                        $colum->license ?? null;
                                    $blocks[$block->idblocks]->areassections1->columsmenus->seccolums[$count]->profile =
                                        $colum->profile ?? null;
                                    $blocks[$block->idblocks]->areassections1->columsmenus->seccolums[$count]->title =
                                        $menu->get_item_title($colum);
                                    $blocks[$block->idblocks]->areassections1->columsmenus->seccolums[$count]->class = $colum->section;
                                    if (isset($colum->url)) {
                                        $blocks[$block->idblocks]->areassections1->columsmenus->seccolums[$count]->url =
                                            $colum->url ?? $colum->url;
                                    } else {
                                        $blocks[$block->idblocks]->areassections1->columsmenus->seccolums[$count]->url =
                                            $menu->build_url(
                                                $colum->sectiontype ?? null,
                                                $colum->section ?? null,
                                                $codproduct ?? null,
                                                $courseid ?? null,
                                                null,
                                                $idsso
                                            );

                                        $blocks[$block->idblocks]->areassections1->columsmenus->seccolums[$count]->opennewwin =
                                            ($colum->sectiontype === 'sso' && app::get_panel()->exist_panel_in_config($idsso))
                                            ? '_blank'
                                            : '';
                                    }

                                    $count++;
                                }
                            }
                        }
                    }

                    if ($section->areaid == self::MEGAMENUAREA2) {
                        $blocks[$block->idblocks]->areassections2->areaid = $section->areaid ?? null;
                        /*
                        * blocks -> columsMenus
                        */
                        $columnum = 0;
                        foreach ($section->columsmenus as $area2) {
                            $blocks[$block->idblocks]->areassections2->columsmenus[$columnum] = new \stdClass();
                            $blocks[$block->idblocks]->areassections2->columsmenus[$columnum]->title =
                                $menu->get_item_title($area2);

                            /*
                            * blocks -> secColums
                            */
                            $count = 0;
                            if (isset($area2->seccolums)) {
                                foreach ($area2->seccolums as $colum) {
                                    if (!$this->can_show_link($colum, $menu)) {
                                        continue;
                                    }
                                    $blocks[$block->idblocks]->areassections2->columsmenus[$columnum]->seccolums[$count]
                                        = new \stdClass();
                                    $blocks[$block->idblocks]->areassections2->columsmenus[$columnum]->seccolums[$count]->locationitems = $colum->locationitems ?? null;
                                    $blocks[$block->idblocks]->areassections2->columsmenus[$columnum]->seccolums[$count]->license =
                                        $colum->license ?? null;
                                    $blocks[$block->idblocks]->areassections2->columsmenus[$columnum]->seccolums[$count]->profile =
                                        $colum->profile ?? null;
                                    $blocks[$block->idblocks]->areassections2->columsmenus[$columnum]->seccolums[$count]->title =
                                        $menu->get_item_title($colum);
                                    $blocks[$block->idblocks]->areassections2->columsmenus[$columnum]->seccolums[$count]->sectiontype = $colum->sectiontype ?? null;
                                    $blocks[$block->idblocks]->areassections2->columsmenus[$columnum]->seccolums[$count]->section =
                                        $colum->section ?? null;
                                    $blocks[$block->idblocks]->areassections2->columsmenus[$columnum]->seccolums[$count]->id =
                                        $colum->id ?? null;
                                    if ($menu->have_profile($colum->profile)) {
                                        $blocks[$block->idblocks]->areassections2->columsmenus[$columnum]->seccolums[$count]->haveprofile = true;
                                        $blocks[$block->idblocks]->areassections2->data = true;
                                        if ($resource = $colum->get_resource()) {
                                            $blocks[$block->idblocks]->areassections2->columsmenus[$columnum]->seccolums[$count]->url = $resource[0]->cm_url;
                                        } else if (!is_null($colum->url)) {
                                            $blocks[$block->idblocks]->areassections2->columsmenus[$columnum]->seccolums[$count]->url = $colum->url;
                                        } else {
                                            $blocks[$block->idblocks]->areassections2->columsmenus[$columnum]->seccolums[$count]->url =
                                                $menu->build_url(
                                                    $colum->get_sectiontype(),
                                                    $colum->get_section(),
                                                    $codproduct,
                                                    $courseid,
                                                    $colum->get_sectiontype() == 'functionalSection'
                                                        ? null : ['itemid' => $colum->id, 'origin' => 'megaMenu'],
                                                    $colum->get_idsso()
                                                );
                                                $blocks[$block->idblocks]->areassections2->columsmenus[$columnum]->seccolums[$count]->opennewwin =
                                            $colum->sectiontype == 'sso' && (int) get_config('local_externalconnect', 'openssonewwin') ?
                                            "_blank"  :
                                            "";
                                        }
                                    }
                                    $count++;
                                }
                            }
                            $columnum++;
                        }
                    }

                    if ($section->areaid == self::MEGAMENUAREA3) {
                        $blocks[$block->idblocks]->areassections3->areaid = $section->areaid ?? null;

                        /*
                        * blocks -> columsMenus
                        */
                        foreach ($section->columsmenus as $area3) {
                            $blocks[$block->idblocks]->areassections3->columsmenus = new \stdClass();
                            $blocks[$block->idblocks]->areassections3->columsmenus->id = $area3->id ?? null;
                        }

                        /*
                            * blocks -> seccolums
                            */
                        $blocks[$block->idblocks]->areassections3->columsmenus->seccolums = array();
                        $count = 0;
                        if ($area3->seccolums) {
                            foreach ($area3->seccolums as $colum) {
                                if (!$this->can_show_link($colum, $menu)) {
                                    continue;
                                }
                                if (($colum->locationitems == $menu::LOCATIONITEMSBOTH ||
                                        $colum->locationitems == $menu::LOCATIONITEMSONLYMENU) &&
                                    $menu->have_profile($colum->profile)
                                ) {
                                    $blocks[$block->idblocks]->areassections3->columsmenus->seccolums[$count] = new \stdClass();
                                    $blocks[$block->idblocks]->areassections3->columsmenus->seccolums[$count]->locationitems =
                                        $colum->locationitems ?? null;
                                    $blocks[$block->idblocks]->areassections3->columsmenus->seccolums[$count]->license =
                                        $colum->license ?? null;
                                    $blocks[$block->idblocks]->areassections3->columsmenus->seccolums[$count]->profile =
                                        $colum->profile ?? null;
                                    $blocks[$block->idblocks]->areassections3->columsmenus->seccolums[$count]->idsso =
                                        $colum->idsso ?? null;
                                    // Solo mostrar la gestión de grupos cuando no es un curso de Educamos o Marsupial.
                                    if (isset($colum->section) && $colum->section === 'groups' &&
                                        ($course->is_educamos_course() || $course->is_marsupial_course())) {
                                        $blocks[$block->idblocks]->areassections3->columsmenus->seccolums[$count]->title = null;
                                    } else {
                                        $blocks[$block->idblocks]->areassections3->columsmenus->seccolums[$count]->title =
                                            $menu->get_item_title($colum);
                                    }
                                    $blocks[$block->idblocks]->areassections3->columsmenus->seccolums[$count]->section =
                                        $colum->section ?? null;
                                    /*
                                    * blocks -> Icon
                                    */
                                    if (isset($colum->url)) {
                                        $blocks[$block->idblocks]->areassections3->columsmenus->seccolums[$count]->url =
                                            $colum->url ?? null;
                                    } else {
                                        $blocks[$block->idblocks]->areassections3->columsmenus->seccolums[$count]->url =
                                            $menu->build_url(
                                                $colum->sectiontype ?? null,
                                                $colum->section ?? null,
                                                $codproduct ?? null,
                                                $courseid ?? null,
                                                null,
                                                $colum->idsso ?? null,
                                            );
                                        $blocks[$block->idblocks]->areassections3->columsmenus->seccolums[$count]->opennewwin =
                                            $colum->sectiontype == 'sso' && (int) get_config('local_externalconnect', 'openssonewwin') ?
                                            "_blank"  :
                                            "";
                                    }
                                    $blocks[$block->idblocks]->areassections3->columsmenus->seccolums[$count]->hasurl =
                                        isset($blocks[$block->idblocks]->areassections3->columsmenus->seccolums[$count]->url);
                                }

                                $count++;
                            }
                        }
                    }
                }
            }
        }
        return $blocks;
    }


    /**
     * @param $blockid
     * @param $areaid
     * @param $sectionalia
     * @return string
     */
    public function get_section_icon($blockid, $areaid, $sectionalia) {
        $section = $this->get_section($blockid, $areaid, $sectionalia);
        $icon = $section != null ?  $this->get_url_content($section->get_icon()) :  '';

        return $icon;
    }

    /**
     * @param $blockid
     * @param $areaid
     * @param $sectionalia
     * @return section object|| null
     * @throws \local_npe\exception\npe_exception
     */
    public function get_section($blockid, $areaid, $sectionalia) {
        try {
            $blocks = $this->get_blockmenu();
            $blockmenu = $this->array_filter($blocks, 'idblocks', $blockid);
            $areas = $blockmenu->get_areassections();
            $areasection = $this->array_filter($areas, 'areaid', $areaid);
            $cols = $areasection->get_columsmenus()[0]->get_seccolums();
            /** @var section $section */
            $section = $this->array_filter($cols, 'section', $sectionalia);
        } catch (\Exception $e) {

            $section = null;
        }
        return $section;
    }
    /**
     * @param $blockid
     * @return blocktitle string || null
     * @throws \local_npe\exception\npe_exception
     */
    public function get_block_title($blockid) {
        try {
            $blocks = $this->get_blockmenu();
            $blockmenu = $this->array_filter($blocks, 'idblocks', $blockid);
            $blocktitle = $blockmenu->blocktitle;
        } catch (\Exception $e) {

            $blocktitle = null;
        }
        return $blocktitle;
    }

    /**
     * 
     */
    private function can_show_link($column, $menu) {
        // Comprobamos primero si el usuario puede verlo
        switch($column->idsso) {
            case 'ssoSimplifica':
                if (!get_config('local_externalconnect', 'enablesimplifica')) {
                    return false;
                }
                break;
            case 'ssoGame':
                if (!get_config('local_externalconnect', 'enablegame')) {
                    return false;
                }
                break;
            case 'ssoPlanno':
                if (!get_config('local_externalconnect', 'enableplanno')) {
                    return false;
                }
                break;
        }
        
        // En caso de que pueda verlo, comprobamos si esta el setting activado.
        return $menu->have_profile($column->profile);
    }
}

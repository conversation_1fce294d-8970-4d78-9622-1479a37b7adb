version: 0.2

phases:
  install:
    commands:
      - composer -q
  build:
    commands:
      - composer install --no-dev
  post_build:
    commands:
      - mkdir -p /tmp/build/moodle
      - mv aws/* -t /tmp/build/
      - rm -Rf aws config .git
      - rm -f sonar-project.properties bitbucket-pipelines.yml config.php config-dist.php composer.lock composer.json
      - mv ./* /tmp/build/moodle
artifacts:
  base-directory: /tmp/build
  files:
    - '**/*'

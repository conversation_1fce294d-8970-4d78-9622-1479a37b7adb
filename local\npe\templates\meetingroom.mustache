<div class="main_container assignments">
    <div class="d-flex" id="wrapper">
        {{> local_npe/commons/sidebar_menu }}

        <div id="page-content-wrapper" class="content-course comunication-content">
            <div class="comunication-meeting-room">
                <div class="top-bar">
                    {{> local_npe/commons/breadcrumbs }}
                    {{# showhelp }}
                        {{< local_npe/commons/info_help }}
                            {{$ headerhelp }}
                                {{# str }} headerhelpcommunicationvideoconferences, local_npe {{/ str }}
                            {{/ headerhelp }}
                            {{$ buttonhelppanel }}
                                {{# hashelppanel }}
                                    {{# isteacher }}
                                        {{# issmauser }}
                                            <a id="linkhelppanel" class="btn npe-button-primary"
                                               href="{{helppanellink}}" target="_blank">{{helppanellinklabel}}</a>
                                        {{/issmauser}}
                                    {{/isteacher}}
                                {{/hashelppanel}}
                            {{/buttonhelppanel}}
                            {{$ texthelp }}
                                {{^ isstudent }}
                                    {{# str }} texthelpcommunicationvideoconferencesteacher, local_npe {{/ str }}
                                {{/ isstudent }}
                            {{/ texthelp }}
                        {{/ local_npe/commons/info_help }}
                    {{/ showhelp }}
                    {{< local_npe/commons/page_title }}
                        {{$npe-moreatributes-heading}}
                            class="extra-padding-after-npe-page-header"
                        {{/npe-moreatributes-heading}}
                        {{$npe-icon-page-header}}
                            {{> local_npe/icons/meetingroom}}
                        {{/npe-icon-page-header}}
                        {{$npe-title-page-header}}{{title}}{{/npe-title-page-header}}
                    {{/ local_npe/commons/page_title }}
                </div>
                <div class="content-activities">
                    <div id="meeting-room">

                        <div class="panel-body">
                            {{^ urlmeetingroom }}
                                <div class="first-line">
                                    <span class="panel-title">{{# str}}conferencenosessiontitle, local_npe{{/ str}}</span>
                                </div>
                                <div class="second-line">
                                    <span class="moment">
                                        {{# isstudent }}{{# str}}conferencenosessiontextstudent, local_npe{{/ str}}{{/ isstudent }}
                                        {{# isteacher }}{{# str}}conferencenosessiontextteacher, local_npe{{/ str}}{{/ isteacher }}
                                    </span>
                                </div>
                            {{/ urlmeetingroom }}
                            {{# urlmeetingroom }}
                                <div class="dropdown">
                                    <div class="npe-dropdown-container">
                                                {{# isteacher }}{{> local_npe/assignments/assignments_dropdown_edit_n_delete }}{{/ isteacher }}
                                    </div>
                                </div>
                                <div class="first-line">
                                    <span class="panel-title">{{# str}}conferencesessiontitle, local_npe{{/ str}}</span>
                                </div>
                                <div class="second-line">
                                    <span class="moment">{{ datemeetingroom }} {{ timemeetingroom }}</span>
                                </div>
                                <div class="join-button{{^ validmomment }} disabled{{/ validmomment }}" >
                                    <a target="_blank" {{^ validmomment }}data-toggle="tooltip" data-placement="bottom" title="{{# str }} conferenceinfotooltip, local_npe {{/ str }}"{{/ validmomment }} href="{{# validmomment }}{{ urlmeetingroom }}{{/ validmomment }}">{{# str}}conferencejoin, local_npe{{/ str}}</a>
                                    <button class="arrow-right-white"></button>
                                </div>
                            {{/ urlmeetingroom }}
                        </div>

                        {{# isteacher }}
                            <div class="body-meeting-room {{^ urlmeetingroom }}visible{{/ urlmeetingroom }}">
                                <h5>{{# str}}conferencecreatestepone, local_npe{{/ str}}</h5>
                                <ul class="meeting-room-apps-links">
                                    <li class="zoom-icon"><span>{{# str}}zoomtitle, local_npe{{/ str}}</span></li>
                                    <li class="msteams-icon"><span>{{# str}}msteamstitle, local_npe{{/ str}}</span></li>
                                    <li class="gmeet-icon"><span>{{# str}}gmeettitle, local_npe{{/ str}}</span></li>
                                </ul>
                                <h5>{{# str}}conferencecreatesteptwo, local_npe{{/ str}}</h5>

                                <div class="grid">
                                    <span>{{#str}} conferenceurl, local_npe {{/str}}</span>
                                    <div class="meeting-room-input url-meeting-room-input">
                                        <input type="url" id="url-meeting-room" class="form-control" placeholder="{{^ urlmeetingroom }}{{#str}} conferenceplaceholderurl, local_npe {{/str}}{{/ urlmeetingroom }}{{# urlmeetingroom }}{{ urlmeetingroom }}{{/ urlmeetingroom }}" name="url" maxlength="85" value="{{ urlmeetingroom }}">
                                    </div>
                                </div>
                                <div class="flex">
                                    <div class="grid">
                                        <span>{{#str}} conferencedate, local_npe {{/str}}</span>
                                        <div class="meeting-room-input date-meeting-room-input">
                                            <input id="date-meeting-room" type="text" name="date" class="form-control date" readonly="readonly" maxlength="10" placeholder="{{^ datemeetingroom }}{{#str}} date, local_npe {{/str}}{{/ datemeetingroom }}{{# datemeetingroom }}{{ datemeetingroom }}{{/ datemeetingroom }}"/>
                                            <input type="hidden" name="date" id="date" value="{{ datemeetingroom }}"/>
                                            <span class="clean"></span>
                                        </div>
                                    </div>
                                    <div class="grid time">
                                        <span>{{#str}} myassignments_hourbegining, local_npe {{/str}}</span>
                                        <div class="meeting-room-input time-meeting-room-input">
                                            <input id="time-meeting-room" type="time" name="time" class="form-control hour hour-begining" maxlength="4" placeholder="{{^ timemeetingroom }}00:00 h{{/ timemeetingroom }}{{# timemeetingroom }}{{ timemeetingroom }}{{/ timemeetingroom }}" value="{{ timemeetingroom }}"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="buttons">
                                    {{# urlmeetingroom}}
                                    <a href="{{ backurl }}" class="btn cancel npe-button-secondary">{{#str}} cancel, local_npe {{/str}}</a>
                                    <button type="submit" class="btn add npe-button-primary">
                                        {{#str}} savechanges, local_npe {{/str}}
                                    </button>
                                    {{/ urlmeetingroom}}
                                    {{^ urlmeetingroom}}
                                    <button type="submit" class="btn add npe-button-primary">
                                        {{#str}} conferencesessionprogram, local_npe {{/str}}
                                    </button>
                                    {{/ urlmeetingroom}}
                                </div>
                            </div>
                        {{/ isteacher }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal-meeting-room">
    <button type="button" class="close" aria-label="Close"><span aria-hidden="true"></span></button>
    <div class="panel">
        <div class="error-new">
            <i class="error-icon"></i>
            <div class="text-title">{{# str}}conferencesessionprogramkotext, local_npe{{/ str}}</div>
        </div>
        <div class="success-new">
            <i class="success-icon"></i>
            <h5 class="sucess-title">{{# str}}conferencesessionprogramoktitle, local_npe{{/ str}}</h5>
            <div class="text-title">{{# str}}conferencetextstudentaccesstext, local_npe{{/ str}}</div>
        </div>
        <div class="error-edit">
            <i class="error-icon"></i>
            <div class="text-title">{{# str}}conferenceerroredit, local_npe{{/ str}}</div>
        </div>
        <div class="error-delete">
            <i class="error-icon"></i>
            <div class="text-title">{{# str}}conferenceerrordelete, local_npe{{/ str}}</div>
        </div>
        <div class="question-delete">
            <i class="error-icon"></i>
            <div class="text-title">{{# str}}conferencetitledeleteok, local_npe{{/ str}}</div>
            <div class="buttons">
                <a class="btn cancel npe-button-secondary">{{#str}} cancel, local_npe {{/str}}</a>
                <button type="submit" class="btn delete npe-button-primary">
                    {{#str}} confirmdelete, local_npe {{/str}}
                </button>
            </div>
        </div>
        <div class="understand-button bg-c">
            <a href="">{{# str}}understood, local_npe{{/ str}}</a>
        </div>
    </div>
</div>
{{> local_npe/commons/floating_tools_menu }}
<!-- Menu Flotante de Chatbox -->
{{> local_npe/commons/floating_chatbot_menu }}
{{# js }}
    require(['local_npe/meetingroom', 'local_npe/prepare_ui', 'local_npe/datepicker'],
        function(MeetingRoom, PrepareUI, DatePicker) {
            new MeetingRoom();
            new PrepareUI();
            DatePicker.calendar('.date');
        }
    );
{{/ js }}
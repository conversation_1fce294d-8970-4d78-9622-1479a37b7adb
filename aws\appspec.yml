version: 0.0
os: linux
files:
  - source: /moodle
    destination: /webs/www/moodle
  - source: /config.php
    destination: /webs/www/moodle
file_exists_behavior: OVERWRITE
permissions:
  - object: /webs/www/moodle
    owner: root
    group: apache
    mode: 640
    type:
      - file
  - object: /webs/www/moodle
    owner: root
    group: apache
    mode: 750
    type:
      - directory
hooks:
  BeforeInstall:
    - location: 0_misc.sh
      timeout: 90
      runas: root
    - location: 1_set_vhost_macro.sh
      timeout: 90
      runas: root
    - location: 2_set_vhost_list.sh
      timeout: 90
      runas: root
    - location: 3_set_efs.sh
      timeout: 90
      runas: root
  AfterInstall:
    - location: 4_set_config.sh
      timeout: 90
      runas: root
    - location: 5_upgrade_moodle.sh
      timeout: 600
      runas: root
  ApplicationStart:
    - location: 6_start_server.sh
      timeout: 90
      runas: root
    - location: 7_set_cron.sh
      timeout: 90
      runas: root

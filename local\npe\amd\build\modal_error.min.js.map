{"version": 3, "file": "modal_error.min.js", "sources": ["../src/modal_error.js"], "sourcesContent": ["import $ from 'jquery';\nimport Notification from 'core/notification';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\nimport Str from 'core/str';\n\nconst SELECTORS = {\n    MODALTITLE: '.error-title',\n    MODALDESCRIPTION: '.error-description',\n    HIDE: '.close',\n    FINISH: '[data-action=\"finish\"]',\n};\n\nlet registered = false;\n\nexport default class ModalError extends Modal {\n\n    static TYPE = 'local_npe/modal_error';\n    static TEMPLATE = 'local_npe/commons/modal_error';\n\n    constructor(root) {\n        super(root);\n    }\n\n    setData(titleKey, descriptionKey, descriptionParam = null) {\n        const description = descriptionParam !== null\n            ? {key: descriptionKey, component: 'local_npe', param: JSON.parse(descriptionParam)}\n            : {key: descriptionKey, component: 'local_npe'};\n\n        let requeststrings;\n        if (titleKey === null) {\n            requeststrings = [description];\n        } else {\n            const title = {key: titleKey, component: 'local_npe'};\n            if (descriptionKey === null) {\n                requeststrings = [title];\n            } else {\n                requeststrings = [title, description];\n            }\n        }\n\n        Str.get_strings(requeststrings).done((strings) => {\n            let descriptionstring = '';\n            let titlestring = strings[0];\n            if (strings.length == 2) {\n                titlestring = strings[0];\n                descriptionstring = strings[1];\n            }\n            this.getRoot().find(SELECTORS.MODALTITLE).html(titlestring);\n            this.getRoot().find(SELECTORS.MODALDESCRIPTION).html(descriptionstring);\n        }).fail(Notification.exception);\n    }\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.success-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.success-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n        });\n    }\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalError.TYPE, ModalError, ModalError.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_defineProperty", "r", "t", "i", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_j<PERSON>y", "_notification", "_custom_interaction_events", "_modal", "_modal_registry", "_str", "SELECTORS", "registered", "ModalError", "Modal", "constructor", "root", "super", "setData", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "descriptionParam", "arguments", "length", "undefined", "description", "key", "component", "param", "JSON", "parse", "requeststrings", "title", "Str", "get_strings", "done", "strings", "descriptionstring", "titlestring", "this", "getRoot", "find", "html", "fail", "Notification", "exception", "registerEventListeners", "getModal", "on", "CustomEvents", "events", "activate", "removeClass", "$", "remove", "addClass", "_exports", "ModalRegistry", "register", "TYPE", "TEMPLATE"], "mappings": "wPAK2B,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,gBAAAH,EAAAI,EAAAC,GAAAD,OAAAA,EAAA,SAAAC,GAAAC,IAAAA,EAAA,SAAAD,EAAAD,GAAAC,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAL,IAAAA,EAAAK,EAAAE,OAAAC,yBAAAR,EAAA,CAAA,IAAAM,EAAAN,EAAAS,KAAAJ,EAAAD,kCAAAE,EAAA,OAAAA,EAAAI,MAAAA,IAAAA,4EAAAN,EAAAO,OAAAC,QAAAP,EAAA,CAAAQ,CAAAR,EAAAC,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAQ,CAAAV,MAAAJ,EAAAe,OAAAC,eAAAhB,EAAAI,EAAAa,CAAAA,MAAAZ,EAAAa,YAAAC,EAAAA,cAAAC,EAAAA,cAAApB,EAAAI,GAAAC,EAAAL,CAAA,iFAL3BqB,QAAAtB,uBAAAsB,SACAC,cAAAvB,uBAAAuB,eACAC,2BAAAxB,uBAAAwB,4BACAC,OAAAzB,uBAAAyB,QACAC,gBAAA1B,uBAAA0B,iBACAC,KAAA3B,uBAAA2B,MAEA,MAAMC,qBACU,eADVA,2BAEgB,qBAFhBA,eAGI,SAHJA,iBAIM,yBAGZ,IAAIC,YAAa,EAEF,MAAMC,mBAAmBC,OAAAA,QAKpCC,WAAAA,CAAYC,MACRC,MAAMD,KACV,CAEAE,OAAAA,CAAQC,SAAUC,gBAAyC,IAAzBC,iBAAgBC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,KACjD,MAAMG,YAAmC,OAArBJ,iBACd,CAACK,IAAKN,eAAgBO,UAAW,YAAaC,MAAOC,KAAKC,MAAMT,mBAChE,CAACK,IAAKN,eAAgBO,UAAW,aAEvC,IAAII,eACJ,GAAiB,OAAbZ,SACAY,eAAiB,CAACN,iBACf,CACH,MAAMO,MAAQ,CAACN,IAAKP,SAAUQ,UAAW,aAErCI,eADmB,OAAnBX,eACiB,CAACY,OAED,CAACA,MAAOP,YAEjC,CAEAQ,KAAG/C,QAACgD,YAAYH,gBAAgBI,MAAMC,UAClC,IAAIC,kBAAoB,GACpBC,YAAcF,QAAQ,GACJ,GAAlBA,QAAQb,SACRe,YAAcF,QAAQ,GACtBC,kBAAoBD,QAAQ,IAEhCG,KAAKC,UAAUC,KAAK9B,sBAAsB+B,KAAKJ,aAC/CC,KAAKC,UAAUC,KAAK9B,4BAA4B+B,KAAKL,kBAAkB,IACxEM,KAAKC,cAAY1D,QAAC2D,UACzB,CAEAC,sBAAAA,GACI7B,MAAM6B,uBAAuBP,MAE7BA,KAAKQ,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUxC,kBAAkB,KAC/D4B,KAAKC,UAAUY,YAAY,SAC3B,EAAAC,QAAAA,SAAE,QAAQD,YAAY,eACtB,EAAAC,iBAAE,kBAAkBC,UACpB,EAAAD,QAACnE,SAAC,mBAAmBkE,YAAY,QAAQG,SAAS,OAAO,IAG7DhB,KAAKQ,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUxC,gBAAgB,KAC7D4B,KAAKC,UAAUY,YAAY,SAC3B,EAAAC,QAAAA,SAAE,QAAQD,YAAY,eACtB,EAAAC,iBAAE,kBAAkBC,UACpB,EAAAD,QAACnE,SAAC,mBAAmBkE,YAAY,QAAQG,SAAS,OAAO,GAEjE,EAMH,OALAC,SAAAtE,QAAA2B,WAAA1B,gBAvDoB0B,WAAU,OAEb,yBAAuB1B,gBAFpB0B,WAAU,WAGT,iCAsDjBD,aACD6C,gBAAAA,QAAcC,SAAS7C,WAAW8C,KAAM9C,WAAYA,WAAW+C,UAC/DhD,YAAa,GAChB4C,SAAAtE,OAAA"}
{"version": 3, "file": "sidebarmenu.min.js", "sources": ["../src/sidebarmenu.js"], "sourcesContent": ["define(['jquery'],\n    function ($) {\n        let SELECTORS = {\n            LIST_GROUP: '.list-group-flush',\n            AREA3: '#area3',\n            DROP_SIDEMENU: '#drop-side-menu',\n            ICON: '.icon_menu',\n            ICON_SECTION_FIRST: '.icon_menu.section a:first',\n            ICON_SECTION_SVG: '.icon_menu.section svg',\n            ICON_SECTION_ACTIVE: '.icon_menu.active',\n            ICON_SECTION: '.icon_menu.section',\n            ICON_SECTION_A: '.icon_menu.section a',\n            SIDEMENU: '#selector-side-menu',\n            SECTION_SIDEMENU: '.select-section-sidemenu',\n            PLUS_MENU: '#plus_menu',\n            COLLAPSE_ICON: '#collapse-icon',\n            SIDEBAR: '.sidebar-menu',\n            SIDEBAR_DROP: '.sidebar-menu .dropdown-menu',\n            SIDEBAR_PLUS: '.sidebar-menu .plus_menu',\n            SEPARATOR: '#separator',\n            SIDEBAR_GROUP_DROP: '.sidebar-menu .list-group .dropdown-menu-left',\n            ICON_SECTION_COLLAPSE_RIGHT: '.icon_menu.section.collapse-right',\n            MENU_GROUP_SHARE: '.sidebar-menu .list-group.list-group-flush.share',\n            LIST_GROUP_SHARE: '.list-group.list-group-flush.share',\n            ITEMS_SECTION: '.li-section',\n            ACTION_NOTIFICATIONS: '.action.notifications',\n            NOTEBOOKBUTTON: '#class-notebook-button',\n            JOINGROUPBUTTON: '#join-group-button',\n            DROPWDOWN_ITEM: 'a.dropdown-item',\n            HELP_PANEL: '.help_panel',\n            HELP_TOOLTIP: '.tooltip-help',\n        };\n\n        /**\n         *\n         * @param {*} library\n         */\n        function SideBarMenu(library = false) {\n            SideBarMenu.prototype.constructor = SideBarMenu;\n            SideBarMenu.prototype.library = library;\n            this.init();\n        }\n\n        SideBarMenu.prototype.init = function () {\n\n            if ($(SELECTORS.DROPWDOWN_ITEM).attr('href') == $('.action.notifications').attr('href')) {\n                $(SELECTORS.DROPWDOWN_ITEM).addClass('action notifications');\n            }\n            if ($(SELECTORS.LIST_GROUP).length > 0) {\n                $(SELECTORS.LIST_GROUP).hide();\n                $(SELECTORS.AREA3).after($(SELECTORS.LIST_GROUP));\n                var str = $(SELECTORS.LIST_GROUP).html();\n                str = str.replace(/<\\/?span[^>]*>/g, \"\");\n                $(SELECTORS.DROP_SIDEMENU).append(str);\n                $(SELECTORS.DROP_SIDEMENU).find('a').addClass('dropdown-item');\n                $(SELECTORS.LIST_GROUP).fadeIn(500);\n            }\n\n            var item = '';\n            if ($(SELECTORS.ITEMS_SECTION).length) {\n                var urlParams = new URLSearchParams(window.location.search);\n                var section = urlParams.get('section');\n                if (section) {\n                    if ($(SELECTORS.HELP_PANEL).length) {\n                        $(SELECTORS.HELP_TOOLTIP + '.' + section).show();\n                        $(SELECTORS.HELP_PANEL + '.' + section).show();\n                    }\n                    item = $(SELECTORS.ICON_SECTION).find('[data-section = \"' + section + '\"]').parent();\n                } else {\n                    if ($('body').attr('id') == 'page-local-npe-courses-classes-index'\n                        && $(SELECTORS.HELP_PANEL).length) {\n                        $(SELECTORS.HELP_TOOLTIP).hide();\n                        $(SELECTORS.HELP_PANEL).hide();\n                        $(SELECTORS.HELP_TOOLTIP).first().show();\n                        $(SELECTORS.HELP_PANEL).first().show();\n                    }\n                    item = $(SELECTORS.ICON_SECTION_FIRST).parent();\n                }\n                this.positionSelector(item);\n            } else {\n                $(SELECTORS.SIDEMENU).hide();\n            }\n\n            this.prepareView();\n            this.sessionStateMenuController();\n        };\n\n        SideBarMenu.prototype.prepareView = function () {\n            var that = this;\n\n            $(SELECTORS.ACTION_NOTIFICATIONS).on('click', function (e) {\n                e.preventDefault();\n                e.stopImmediatePropagation();\n                $(SELECTORS.NOTEBOOKBUTTON).trigger('click');\n            });\n\n            $(SELECTORS.ICON_SECTION_SVG).bind('click', function (e) {\n                e.preventDefault();\n                $(this).parent().find('a').trigger('click');\n            });\n\n            $(SELECTORS.ICON_SECTION_A).bind('click', function (e) {\n                e.preventDefault();\n                if ($(this).attr('href').indexOf('#') === 0) {\n                    that.positionSelector($(this).parent());\n                    that.setHelpPanel(this);\n\n                    const section = $(this).data('section');\n                    const url = new URL(window.location.href);\n                    url.searchParams.set('section', section);\n                    window.history.replaceState(null, null, url);\n\n                    // Notificación a navigation_history.js de cambio de url.\n                    $(window).trigger('OnHistoryUrlStateUpdated', [url.href]);\n                } else {\n                    if (window.location.href.indexOf('http') === 0) {\n                        window.open($(this).attr('href'), $(this).attr('target'));\n                    }\n                }\n                if ($(this).attr('target') !== '_blank') {\n                    that.positionSelector($(this).parent());\n                }\n                that.desplazarSelectorActivo($(this));\n            });\n\n            $(SELECTORS.PLUS_MENU).bind('click', function (e) {\n                e.stopImmediatePropagation();\n                if ($(this).hasClass('rotate-right')) {\n                    $(this).removeClass('rotate-right').addClass('rotate-left');\n                } else {\n                    $(this).removeClass('rotate-left').addClass('rotate-right');\n                }\n                if ($(this).hasClass('rotate-right')) {\n                    $(SELECTORS.DROP_SIDEMENU).hide();\n                } else {\n                    $(SELECTORS.DROP_SIDEMENU).show();\n                }\n            });\n\n            $(SELECTORS.COLLAPSE_ICON).bind('click', function (e) {\n                e.stopImmediatePropagation();\n                $(this).parent().trigger('click');\n            });\n\n            $(SELECTORS.COLLAPSE_ICON).parent().bind('click', function () {\n                if ($(this).hasClass('collapse-right')) {\n                    that.desplazarMenuDerecha();\n                } else {\n                    that.desplazarMenuIzquierda();\n                }\n            });\n        };\n\n        SideBarMenu.prototype.desplazarMenuDerecha = function () {\n            if ($(SELECTORS.SIDEBAR).hasClass('collapse-width-no-animated')) {\n                $(SELECTORS.SIDEBAR).removeClass('collapse-width-no-animated');\n            }\n            $(SELECTORS.COLLAPSE_ICON).parent().removeClass('collapse-right');\n            $(SELECTORS.SIDEBAR).removeClass('collapse-width');\n            $(SELECTORS.DROP_SIDEMENU).hide();\n            $(SELECTORS.SIDEBAR_DROP).fadeOut();\n            $(SELECTORS.SIDEBAR_PLUS).removeClass('rotate-right').addClass('rotate-left');\n            $(SELECTORS.SIDEBAR_PLUS).hide();\n            $(SELECTORS.ICON_SECTION_COLLAPSE_RIGHT).unbind('mouseenter');\n            $(SELECTORS.ICON_SECTION_COLLAPSE_RIGHT).unbind('mouseleave');\n            $(SELECTORS.ICON).removeClass('collapse-right');\n            if ($(SELECTORS.LIST_GROUP).length > 0) {\n                $(SELECTORS.MENU_GROUP_SHARE).fadeIn(500);\n            }\n            $(SELECTORS.SEPARATOR).removeClass('separator-collapse').addClass('separator');\n            sessionStorage.sidemenuexpanded = true;\n        };\n\n        SideBarMenu.prototype.desplazarMenuIzquierda = function () {\n            if ($(SELECTORS.SIDEBAR).hasClass('collapse-width-no-animated')) {\n                $(SELECTORS.SIDEBAR).removeClass('collapse-width-no-animated');\n            }\n            $(SELECTORS.SIDEBAR_PLUS).removeClass('rotate-left').addClass('rotate-right');\n            if ($(SELECTORS.LIST_GROUP).length > 0) {\n                $(SELECTORS.LIST_GROUP_SHARE).hide();\n            }\n            $(SELECTORS.COLLAPSE_ICON).parent().addClass('collapse-right');\n            $(SELECTORS.SIDEBAR).addClass('collapse-width');\n            $(SELECTORS.ICON).addClass('collapse-right');\n            $(SELECTORS.SEPARATOR).removeClass('separator').addClass('separator-collapse');\n            if ($(SELECTORS.SIDEBAR_GROUP_DROP).html().trim().length >= 1) {\n                $('.plus_menu').show();\n            }\n            this.toolTipBinder();\n            sessionStorage.sidemenuexpanded = false;\n        };\n\n        SideBarMenu.prototype.desplazarSelectorActivo = function (dest) {\n            if (!this.library) {\n                $(SELECTORS.SIDEMENU).animate({ top: dest.position().top + 14 }, \"slow\");\n                return;\n            }\n\n            if (!dest || !dest.length) {\n                return;\n            }\n\n            // Siempre ocultar el selector cuando hay un acordeón involucrado\n            $(SELECTORS.SIDEMENU).hide();\n\n            // Verificar si hay acordeón en la seccion que abrimos\n            const $targetAccordion = dest.closest('.accordion-item').find('.icon_menu.section');\n\n            // Calculamos la posición y mostramos el selector\n            const positionAndShowSelector = () => {\n                // Recalculamos la posición del destino aquí\n                // ya que puede haber cambiado después de que el acordeón se desplegó\n                const currentDestOffset = dest.offset();\n                const parentOffset = $(SELECTORS.SIDEBAR).offset();\n\n                if (currentDestOffset && parentOffset) {\n                    const relativeTop = currentDestOffset.top - parentOffset.top + 9;\n                    $(SELECTORS.SIDEMENU).css({ top: relativeTop }).fadeIn(1000);\n                }\n            };\n\n            // Si hay un acordeón involucrado, esperamos a que se despliegue completamente\n            if ($targetAccordion.length > 0) {\n                // Observer para detectar cambios en los acordeones\n                const observer = new MutationObserver(() => {\n                    // Verificar si hay algún acordeón en transición\n                    const anyAccordionTransitioning = $('.accordion-item .collapse.collapsing').length > 0;\n\n                    // Si no hay acordeones en transición, calcular la posición\n                    if (!anyAccordionTransitioning) {\n                        // Desconectar el observer\n                        observer.disconnect();\n\n                        // Esperar un poco para asegurar que el DOM está completamente actualizado\n                        setTimeout(positionAndShowSelector, 200);\n                    }\n                });\n\n                // Configurar el observer para detectar cambios en las clases de los acordeones\n                observer.observe(document.querySelector('.sidebar-menu'), {\n                    subtree: true,\n                    attributes: true,\n                    attributeFilter: ['class']\n                });\n\n                // Si el acordeón ya está desplegado y no hay transiciones en curso\n                if ($targetAccordion.hasClass('show') && $('.accordion-item .collapse.collapsing').length === 0) {\n                    // Desconectar el observer\n                    observer.disconnect();\n\n                    // Calcular la posición inmediatamente\n                    setTimeout(positionAndShowSelector, 50);\n                }\n            } else {\n                // Si no hay acordeón de destino, calcular la posición inmediatamente\n                setTimeout(positionAndShowSelector, 50);\n            }\n        };\n\n        SideBarMenu.prototype.setHelpPanel = function (item) {\n            if (item.className.indexOf('section') === 0) {\n                let section = item.className.replace('section', '').replace('active', '').trim();\n                if (section != '' && $(SELECTORS.HELP_PANEL).length) {\n                    $(SELECTORS.HELP_PANEL).hide();\n                    $(SELECTORS.HELP_TOOLTIP).hide();\n                    $(SELECTORS.HELP_TOOLTIP + '.' + section).show();\n                    $(SELECTORS.HELP_PANEL + '.' + section).show();\n                }\n            }\n        };\n\n        SideBarMenu.prototype.positionSelector = function (item) {\n            if ($(SELECTORS.SIDEMENU).is(':visible')) {\n                $(SELECTORS.ICON_SECTION_ACTIVE).removeClass('active');\n                this.desplazarSelectorActivo($(item));\n                $(SELECTORS.SECTION_SIDEMENU).removeClass('active');\n                $(item).addClass('active');\n            }\n        };\n\n        SideBarMenu.prototype.sessionStateMenuController = function() {\n            let sessioncid = M.cfg.sesskey + '_' + M.cfg.courseId;\n            if (!sessionStorage.hasOwnProperty('sessioncid') ||\n                sessionStorage.sessioncid !== sessioncid) {\n                sessionStorage.sidemenuexpanded = true;\n                sessionStorage.sessioncid = sessioncid;\n            }\n            if (!sessionStorage.hasOwnProperty('sidemenuexpanded')) {\n                sessionStorage.sidemenuexpanded = true;\n                sessionStorage.sessioncid = sessioncid;\n            } else {\n                if (sessionStorage.sidemenuexpanded === 'false'\n                    && $(SELECTORS.SIDEBAR).hasClass('collapse-width-no-animated')) {\n                    this.toolTipBinder();\n                }\n                if(sessionStorage.sidemenuexpanded === 'true'\n                    && $(SELECTORS.SIDEBAR).hasClass('collapse-width-no-animated')) {\n                    this.desplazarMenuDerecha();\n                }\n            }\n\n            if (document.body.clientWidth <= 1024) {\n                this.desplazarMenuIzquierda();\n            }\n\n            var that = this;\n            $(window).resize(function () {\n                if (document.body.clientWidth <= 1024) {\n                    if (!$(SELECTORS.COLLAPSE_ICON).parent().hasClass('collapse-right')) {\n                        that.desplazarMenuIzquierda();\n                    }\n                }\n            });\n        };\n\n        SideBarMenu.prototype.toolTipBinder = function() {\n            $(SELECTORS.ICON_SECTION_COLLAPSE_RIGHT).bind('mouseenter', function (e) {\n                e.preventDefault();\n                e.stopImmediatePropagation();\n                if ($(this).parent().find('.tooltip-sidemenu-section').is(':hidden')) {\n                    $(this).parent().find('.tooltip-sidemenu-section').show(100, function () {\n                        var top = $(this).parent().find('svg').offset().top - 83;\n                        $(this).css({ top: top });\n                    });\n                }\n            });\n            $(SELECTORS.ICON_SECTION_COLLAPSE_RIGHT).bind('mouseleave', function (e) {\n                e.preventDefault();\n                e.stopImmediatePropagation();\n                $(this).parent().find('.tooltip-sidemenu-section').hide(100);\n            });\n        };\n\n        return SideBarMenu;\n    });\n\n\n"], "names": ["define", "$", "SELECTORS", "SideBarMenu", "library", "arguments", "length", "undefined", "prototype", "constructor", "this", "init", "attr", "addClass", "hide", "after", "str", "html", "replace", "append", "find", "fadeIn", "item", "section", "URLSearchParams", "window", "location", "search", "get", "show", "parent", "first", "positionSelector", "<PERSON><PERSON><PERSON><PERSON>", "sessionStateMenuController", "that", "on", "e", "preventDefault", "stopImmediatePropagation", "trigger", "bind", "indexOf", "setHelpPanel", "data", "url", "URL", "href", "searchParams", "set", "history", "replaceState", "open", "desplazarSelectorActivo", "hasClass", "removeClass", "desplazarMenuDerecha", "desplazarMenuIzquierda", "fadeOut", "unbind", "sessionStorage", "sidemenuexpanded", "trim", "toolTipBinder", "dest", "animate", "top", "position", "$targetAccordion", "closest", "positionAndShowSelector", "currentDestOffset", "offset", "parentOffset", "relativeTop", "css", "observer", "MutationObserver", "disconnect", "setTimeout", "observe", "document", "querySelector", "subtree", "attributes", "attributeFilter", "className", "is", "sessioncid", "M", "cfg", "sesskey", "courseId", "hasOwnProperty", "body", "clientWidth", "resize"], "mappings": "AAAAA,+BAAO,CAAC,WACJ,SAAUC,GACN,IAAIC,qBACY,oBADZA,gBAEO,SAFPA,wBAGe,kBAHfA,eAIM,aAJNA,6BAKoB,6BALpBA,2BAMkB,yBANlBA,8BAOqB,oBAPrBA,uBAQc,qBARdA,yBASgB,uBAThBA,mBAUU,sBAVVA,2BAWkB,2BAXlBA,oBAYW,aAZXA,wBAae,iBAbfA,kBAcS,gBAdTA,uBAec,+BAfdA,uBAgBc,2BAhBdA,oBAiBW,aAjBXA,6BAkBoB,gDAlBpBA,sCAmB6B,oCAnB7BA,2BAoBkB,mDApBlBA,2BAqBkB,qCArBlBA,wBAsBe,cAtBfA,+BAuBsB,wBAvBtBA,yBAwBgB,yBAxBhBA,yBA0BgB,kBA1BhBA,qBA2BY,cA3BZA,uBA4Bc,gBAOlB,SAASC,cAA6B,IAAjBC,QAAOC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,IAAAA,UAAA,GACxBF,YAAYK,UAAUC,YAAcN,YACpCA,YAAYK,UAAUJ,QAAUA,QAChCM,KAAKC,MACT,CAoSA,OAlSAR,YAAYK,UAAUG,KAAO,WAKzB,GAHIV,EAAEC,0BAA0BU,KAAK,SAAWX,EAAE,yBAAyBW,KAAK,SAC5EX,EAAEC,0BAA0BW,SAAS,wBAErCZ,EAAEC,sBAAsBI,OAAS,EAAG,CACpCL,EAAEC,sBAAsBY,OACxBb,EAAEC,iBAAiBa,MAAMd,EAAEC,uBAC3B,IAAIc,IAAMf,EAAEC,sBAAsBe,OAClCD,IAAMA,IAAIE,QAAQ,kBAAmB,IACrCjB,EAAEC,yBAAyBiB,OAAOH,KAClCf,EAAEC,yBAAyBkB,KAAK,KAAKP,SAAS,iBAC9CZ,EAAEC,sBAAsBmB,OAAO,IACnC,CAEA,IAAIC,KAAO,GACX,GAAIrB,EAAEC,yBAAyBI,OAAQ,CACnC,IACIiB,QADY,IAAIC,gBAAgBC,OAAOC,SAASC,QAC5BC,IAAI,WACxBL,SACItB,EAAEC,sBAAsBI,SACxBL,EAAEC,uBAAyB,IAAMqB,SAASM,OAC1C5B,EAAEC,qBAAuB,IAAMqB,SAASM,QAE5CP,KAAOrB,EAAEC,wBAAwBkB,KAAK,oBAAsBG,QAAU,MAAMO,WAEhD,wCAAxB7B,EAAE,QAAQW,KAAK,OACZX,EAAEC,sBAAsBI,SAC3BL,EAAEC,wBAAwBY,OAC1Bb,EAAEC,sBAAsBY,OACxBb,EAAEC,wBAAwB6B,QAAQF,OAClC5B,EAAEC,sBAAsB6B,QAAQF,QAEpCP,KAAOrB,EAAEC,8BAA8B4B,UAE3CpB,KAAKsB,iBAAiBV,KAC1B,MACIrB,EAAEC,oBAAoBY,OAG1BJ,KAAKuB,cACLvB,KAAKwB,8BAGT/B,YAAYK,UAAUyB,YAAc,WAChC,IAAIE,KAAOzB,KAEXT,EAAEC,gCAAgCkC,GAAG,SAAS,SAAUC,GACpDA,EAAEC,iBACFD,EAAEE,2BACFtC,EAAEC,0BAA0BsC,QAAQ,QACxC,IAEAvC,EAAEC,4BAA4BuC,KAAK,SAAS,SAAUJ,GAClDA,EAAEC,iBACFrC,EAAES,MAAMoB,SAASV,KAAK,KAAKoB,QAAQ,QACvC,IAEAvC,EAAEC,0BAA0BuC,KAAK,SAAS,SAAUJ,GAEhD,GADAA,EAAEC,iBACwC,IAAtCrC,EAAES,MAAME,KAAK,QAAQ8B,QAAQ,KAAY,CACzCP,KAAKH,iBAAiB/B,EAAES,MAAMoB,UAC9BK,KAAKQ,aAAajC,MAElB,MAAMa,QAAUtB,EAAES,MAAMkC,KAAK,WACvBC,IAAM,IAAIC,IAAIrB,OAAOC,SAASqB,MACpCF,IAAIG,aAAaC,IAAI,UAAW1B,SAChCE,OAAOyB,QAAQC,aAAa,KAAM,KAAMN,KAGxC5C,EAAEwB,QAAQe,QAAQ,2BAA4B,CAACK,IAAIE,MACvD,MACiD,IAAzCtB,OAAOC,SAASqB,KAAKL,QAAQ,SAC7BjB,OAAO2B,KAAKnD,EAAES,MAAME,KAAK,QAASX,EAAES,MAAME,KAAK,WAGxB,WAA3BX,EAAES,MAAME,KAAK,WACbuB,KAAKH,iBAAiB/B,EAAES,MAAMoB,UAElCK,KAAKkB,wBAAwBpD,EAAES,MACnC,IAEAT,EAAEC,qBAAqBuC,KAAK,SAAS,SAAUJ,GAC3CA,EAAEE,2BACEtC,EAAES,MAAM4C,SAAS,gBACjBrD,EAAES,MAAM6C,YAAY,gBAAgB1C,SAAS,eAE7CZ,EAAES,MAAM6C,YAAY,eAAe1C,SAAS,gBAE5CZ,EAAES,MAAM4C,SAAS,gBACjBrD,EAAEC,yBAAyBY,OAE3Bb,EAAEC,yBAAyB2B,MAEnC,IAEA5B,EAAEC,yBAAyBuC,KAAK,SAAS,SAAUJ,GAC/CA,EAAEE,2BACFtC,EAAES,MAAMoB,SAASU,QAAQ,QAC7B,IAEAvC,EAAEC,yBAAyB4B,SAASW,KAAK,SAAS,WAC1CxC,EAAES,MAAM4C,SAAS,kBACjBnB,KAAKqB,uBAELrB,KAAKsB,wBAEb,KAGJtD,YAAYK,UAAUgD,qBAAuB,WACrCvD,EAAEC,mBAAmBoD,SAAS,+BAC9BrD,EAAEC,mBAAmBqD,YAAY,8BAErCtD,EAAEC,yBAAyB4B,SAASyB,YAAY,kBAChDtD,EAAEC,mBAAmBqD,YAAY,kBACjCtD,EAAEC,yBAAyBY,OAC3Bb,EAAEC,wBAAwBwD,UAC1BzD,EAAEC,wBAAwBqD,YAAY,gBAAgB1C,SAAS,eAC/DZ,EAAEC,wBAAwBY,OAC1Bb,EAAEC,uCAAuCyD,OAAO,cAChD1D,EAAEC,uCAAuCyD,OAAO,cAChD1D,EAAEC,gBAAgBqD,YAAY,kBAC1BtD,EAAEC,sBAAsBI,OAAS,GACjCL,EAAEC,4BAA4BmB,OAAO,KAEzCpB,EAAEC,qBAAqBqD,YAAY,sBAAsB1C,SAAS,aAClE+C,eAAeC,kBAAmB,GAGtC1D,YAAYK,UAAUiD,uBAAyB,WACvCxD,EAAEC,mBAAmBoD,SAAS,+BAC9BrD,EAAEC,mBAAmBqD,YAAY,8BAErCtD,EAAEC,wBAAwBqD,YAAY,eAAe1C,SAAS,gBAC1DZ,EAAEC,sBAAsBI,OAAS,GACjCL,EAAEC,4BAA4BY,OAElCb,EAAEC,yBAAyB4B,SAASjB,SAAS,kBAC7CZ,EAAEC,mBAAmBW,SAAS,kBAC9BZ,EAAEC,gBAAgBW,SAAS,kBAC3BZ,EAAEC,qBAAqBqD,YAAY,aAAa1C,SAAS,sBACrDZ,EAAEC,8BAA8Be,OAAO6C,OAAOxD,QAAU,GACxDL,EAAE,cAAc4B,OAEpBnB,KAAKqD,gBACLH,eAAeC,kBAAmB,GAGtC1D,YAAYK,UAAU6C,wBAA0B,SAAUW,MACtD,IAAKtD,KAAKN,QAEN,YADAH,EAAEC,oBAAoB+D,QAAQ,CAAEC,IAAKF,KAAKG,WAAWD,IAAM,IAAM,QAIrE,IAAKF,OAASA,KAAK1D,OACf,OAIJL,EAAEC,oBAAoBY,OAGtB,MAAMsD,iBAAmBJ,KAAKK,QAAQ,mBAAmBjD,KAAK,sBAGxDkD,wBAA0BA,KAG5B,MAAMC,kBAAoBP,KAAKQ,SACzBC,aAAexE,EAAEC,mBAAmBsE,SAE1C,GAAID,mBAAqBE,aAAc,CACnC,MAAMC,YAAcH,kBAAkBL,IAAMO,aAAaP,IAAM,EAC/DjE,EAAEC,oBAAoByE,IAAI,CAAET,IAAKQ,cAAerD,OAAO,IAC3D,GAIJ,GAAI+C,iBAAiB9D,OAAS,EAAG,CAE7B,MAAMsE,SAAW,IAAIC,kBAAiB,KAEA5E,EAAE,wCAAwCK,OAAS,IAKjFsE,SAASE,aAGTC,WAAWT,wBAAyB,KACxC,IAIJM,SAASI,QAAQC,SAASC,cAAc,iBAAkB,CACtDC,SAAS,EACTC,YAAY,EACZC,gBAAiB,CAAC,WAIlBjB,iBAAiBd,SAAS,SAAgE,IAArDrD,EAAE,wCAAwCK,SAE/EsE,SAASE,aAGTC,WAAWT,wBAAyB,IAE5C,MAEIS,WAAWT,wBAAyB,KAI5CnE,YAAYK,UAAUmC,aAAe,SAAUrB,MAC3C,GAA0C,IAAtCA,KAAKgE,UAAU5C,QAAQ,WAAkB,CACzC,IAAInB,QAAUD,KAAKgE,UAAUpE,QAAQ,UAAW,IAAIA,QAAQ,SAAU,IAAI4C,OAC3D,IAAXvC,SAAiBtB,EAAEC,sBAAsBI,SACzCL,EAAEC,sBAAsBY,OACxBb,EAAEC,wBAAwBY,OAC1Bb,EAAEC,uBAAyB,IAAMqB,SAASM,OAC1C5B,EAAEC,qBAAuB,IAAMqB,SAASM,OAEhD,GAGJ1B,YAAYK,UAAUwB,iBAAmB,SAAUV,MAC3CrB,EAAEC,oBAAoBqF,GAAG,cACzBtF,EAAEC,+BAA+BqD,YAAY,UAC7C7C,KAAK2C,wBAAwBpD,EAAEqB,OAC/BrB,EAAEC,4BAA4BqD,YAAY,UAC1CtD,EAAEqB,MAAMT,SAAS,YAIzBV,YAAYK,UAAU0B,2BAA6B,WAC/C,IAAIsD,WAAaC,EAAEC,IAAIC,QAAU,IAAMF,EAAEC,IAAIE,SACxChC,eAAeiC,eAAe,eAC/BjC,eAAe4B,aAAeA,aAC9B5B,eAAeC,kBAAmB,EAClCD,eAAe4B,WAAaA,YAE3B5B,eAAeiC,eAAe,qBAIS,UAApCjC,eAAeC,kBACZ5D,EAAEC,mBAAmBoD,SAAS,+BACjC5C,KAAKqD,gBAE8B,SAApCH,eAAeC,kBACX5D,EAAEC,mBAAmBoD,SAAS,+BACjC5C,KAAK8C,yBATTI,eAAeC,kBAAmB,EAClCD,eAAe4B,WAAaA,YAY5BP,SAASa,KAAKC,aAAe,MAC7BrF,KAAK+C,yBAGT,IAAItB,KAAOzB,KACXT,EAAEwB,QAAQuE,QAAO,WACTf,SAASa,KAAKC,aAAe,OACxB9F,EAAEC,yBAAyB4B,SAASwB,SAAS,mBAC9CnB,KAAKsB,yBAGjB,KAGJtD,YAAYK,UAAUuD,cAAgB,WAClC9D,EAAEC,uCAAuCuC,KAAK,cAAc,SAAUJ,GAClEA,EAAEC,iBACFD,EAAEE,2BACEtC,EAAES,MAAMoB,SAASV,KAAK,6BAA6BmE,GAAG,YACtDtF,EAAES,MAAMoB,SAASV,KAAK,6BAA6BS,KAAK,KAAK,WACzD,IAAIqC,IAAMjE,EAAES,MAAMoB,SAASV,KAAK,OAAOoD,SAASN,IAAM,GACtDjE,EAAES,MAAMiE,IAAI,CAAET,IAAKA,KACvB,GAER,IACAjE,EAAEC,uCAAuCuC,KAAK,cAAc,SAAUJ,GAClEA,EAAEC,iBACFD,EAAEE,2BACFtC,EAAES,MAAMoB,SAASV,KAAK,6BAA6BN,KAAK,IAC5D,KAGGX,WACX"}
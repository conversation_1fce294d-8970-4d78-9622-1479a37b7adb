import $ from 'jquery';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';
import Ajax from 'core/ajax';
import ModalLinkingCCAASuccessApi from 'local_npe/modal_linking_success_api';
import ModalLinkingCCAAFailApi from 'local_npe/modal_linking_fail_api';
import ModalFactory from 'core/modal_factory';

const SELECTORS = {
    MODAL: '.linking-ccaa-modal',
    MODALBACKDROP: '.modal-backdrop',
    CCAA_TITLE: '#title_ccaa',
    SELECT_CCAA_TEXT: '#linkccaa_text',
    SELECT_CCAA: '#linkccaa',
    CHOOSE_CCAA_DROPDOWN: '#linkccaa_dropdown',
    CHOOSE_CCAA_DROPDOWN_CONTENT: '#linkccaa_dropdown_content',
    CHOOSE_CCAA_OPTION: '.dropdown_ccaa_item',
    CHOOSE_CCAA_ICO: '#dropdown_ico',
    CONFIRM: '[data-action="confirm"]',
    CCAAQUESTION: '#caaquestion',
    CCAAINFO: '#ccaainfo'
};

const SERVICES = {
    LINKGROUPCCAA: 'local_npe_link_group_ccaa'
};

let registered = false;

export default class ModalLinkCCAAMarsupial extends Modal {

    static TYPE = 'local_npe/modal_link_ccaa_marsupial';
    static TEMPLATE = 'local_npe/courses/modal_linking_ccaa_marsupial';

    #textccaainfo = true;

    constructor(root) {
        super(root);
    }

    setData(ccaaavailable) {
        if (Object.keys(ccaaavailable).length > 0) {
            let first = true;
            for (const ccaaKey in ccaaavailable) {
                if (first) {
                    first = false;
                    this.getRoot().find(SELECTORS.SELECT_CCAA_TEXT).val(ccaaavailable[ccaaKey]['name']);
                    this.getRoot().find(SELECTORS.SELECT_CCAA).val(ccaaavailable[ccaaKey]['id']);
                } else {
                    var sep_opt = document.createElement('div');
                    sep_opt.classList.add('separator');
                    this.getRoot().find(SELECTORS.CHOOSE_CCAA_DROPDOWN_CONTENT).append(sep_opt);
                }
                var newOpt = document.createElement('div');
                var text = document.createTextNode(ccaaavailable[ccaaKey]['name']);
                newOpt.appendChild(text);
                newOpt.setAttribute('data-value', ccaaavailable[ccaaKey]['id']);
                newOpt.classList.add('dropdown_ccaa_item');
                this.getRoot().find(SELECTORS.CHOOSE_CCAA_DROPDOWN_CONTENT).append(newOpt);
            }
            this.getRoot().find(SELECTORS.SELECT_CCAA_TEXT).removeClass('d-none');
            this.getRoot().find(SELECTORS.CCAA_TITLE).removeClass('d-none');
            this.getRoot().find(SELECTORS.CHOOSE_CCAA_ICO).removeClass('d-none');
        }
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.SELECT_CCAA_TEXT, () => {
            if ($(SELECTORS.CHOOSE_CCAA_DROPDOWN).hasClass('d-none')) {
                $(SELECTORS.CHOOSE_CCAA_ICO).css({'transform': 'rotate(270deg) translateX(7px)'});
            } else {
                $(SELECTORS.CHOOSE_CCAA_ICO).css({'transform': 'rotate(90deg)'});
            }
            $(SELECTORS.CHOOSE_CCAA_DROPDOWN).toggleClass('d-none');
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.CHOOSE_CCAA_OPTION, (e) => {
            this.getRoot().find(SELECTORS.SELECT_CCAA_TEXT).val(e.target.innerHTML);
            $(SELECTORS.CHOOSE_CCAA_OPTION).removeClass('selected');
            e.target.classList.add('selected');
            this.getRoot().find(SELECTORS.SELECT_CCAA).val(e.target.getAttribute('data-value'));
            $(SELECTORS.CHOOSE_CCAA_DROPDOWN).addClass('d-none');
            $(SELECTORS.CHOOSE_CCAA_ICO).css({'transform': 'rotate(90deg)'});
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.CONFIRM, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $(SELECTORS.MODAL).removeClass('show').addClass('hide');
            $(SELECTORS.MODALBACKDROP).removeClass('show').addClass('hide');
            let searchParams = new URLSearchParams(window.location.search);
            let courseid = searchParams.get('id') ? searchParams.get('id') : null;
            let ccaaid = this.getRoot().find(SELECTORS.SELECT_CCAA).val() ? this.getRoot().find(SELECTORS.SELECT_CCAA).val() : null;
            this.ajaxCall(courseid, ccaaid);
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.CCAAQUESTION, () => {
            this.showtext();
        });

        $(window).on('keydown', (event) => {
            if (event.key === 'Escape') {
                $(SELECTORS.MODALBACKDROP).removeClass('hide').addClass('show').css('z-index', '1051');
                $(SELECTORS.MODAL).css('z-index', '1052');
            }
        });
    }

    showtext() {
        if (this.#textccaainfo) {
            $(SELECTORS.CCAAINFO).show();
            this.#textccaainfo = false;
        } else {
            $(SELECTORS.CCAAINFO).hide();
            this.#textccaainfo = true;
        }
    }

    ajaxCall(courseid, ccaaid) {
        const promises = Ajax.call([{
            methodname: SERVICES.LINKGROUPCCAA,
            args: {
                courseid: courseid,
                ccaaid: ccaaid
            }
        }]);

        promises[0].done((response) => {
            this.ajaxResult(response);
        }).fail((ex) => {
            this.ajaxError(ex);
        });
    }

    ajaxResult() {
        ModalFactory.create({type: ModalLinkingCCAASuccessApi.TYPE}).done((modal) => {
            modal.show();
        });
    }

    ajaxError(ex) {
        ModalFactory.create({type: ModalLinkingCCAAFailApi.TYPE}).done((modal) => {
            modal.show();
        });
        window.console.log(ex);
    }
}

if (!registered) {
    ModalRegistry.register(ModalLinkCCAAMarsupial.TYPE, ModalLinkCCAAMarsupial, ModalLinkCCAAMarsupial.TEMPLATE);
    registered = true;
}

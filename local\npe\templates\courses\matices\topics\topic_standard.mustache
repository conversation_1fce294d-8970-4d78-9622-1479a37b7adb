<div class="topic_card card_counter {{ sdatype }}
{{# hidden }}
    npe_topic_black
    {{# visibility_only_teacher }} npe_topic_black_only_teacher  {{/ visibility_only_teacher }}
{{/ hidden }}
{{^ hidden }}
    {{# visibility_only_teacher }}
        npe_topic_black_only_teacher npe_topic_white
    {{/ visibility_only_teacher }}
    {{^ visibility_only_teacher }}
        npe_topic_white
    {{/ visibility_only_teacher }}
{{/ hidden }}" 
id="{{idtopic}}" idblock="{{id}}" idpacker="{{packerid}}">
    <div class="wrapper_topic">
        <a
            {{# hascontent }}
                class="group-card-link" href="{{ mainurl }}"
            {{/ hascontent }}
            {{^ hascontent }}
                class="btn-topic-wo-content"
            {{/ hascontent }}>
            <div class='row npe-card-row'>
                <div class="npe-card-row-md5 topic-col-img 
                {{# hidden }}npe_topic_black{{/ hidden }}
                {{^ hidden }}border-hover-card-b{{/ hidden }}">
                    {{# sdaimage }}<img class="img-topics-grid" src="{{ sdaimage }}" alt="">{{/ sdaimage }}
                    {{^ sdaimage }}
                        <div class="pix-topics-grid">{{# pix }}default_img_topics, theme_npe {{/ pix }}</div>
                    {{/ sdaimage }}
                </div>
                <div class="npe-card-row-md5 topic-col-info">
                    <div class="col-md-12 topic-col-info-text {{ sdatype }} pl-0 ellipsys 
                        {{^ areaname }}h90 
                            {{# visibility_only_teacher }}only-teacher{{/ visibility_only_teacher }}
                        {{/ areaname }}
                        {{# visibility_only_teacher }}
                            {{# areaname }} only-teacher {{/ areaname }}
                        {{/ visibility_only_teacher }}">
                        <div class="title-flex">
                            <div class="topic_name text-left">
                                <span title="{{ name }}" class="align-middle info-text-title">
                                {{# sdanumber }}<span class="sdanumber">{{ sdanumber }}. </span>{{/ sdanumber }}
                                {{ name }}
                            </div>
                            {{# unittitle }}
                            <div class="topic_name_sub text-left">
                                <span title="{{ unittitle }}" class="align-middle info-text-subtitle">{{ unittitle }}</span>
                            </div>
                            {{/ unittitle }}
                        </div>
                        {{# showmenu }}
                            {{< local_npe/commons/dotsmenu }}
                                {{$menudotsclass}}matices{{/menudotsclass}}
                                {{$dotsoptionsclass}}dropdown-course-index topic-options{{/dotsoptionsclass}}
                                {{$dotsoptions}}
                                    {{#hascontent}}
                                        {{#hassequence}}
                                            <button class="dropdown-item dropdown-course-index-item" tabindex="0" data-href="{{ url }}">{{#str}}viewerview, local_npe{{/str}} {{txtSequence}}</button>
                                        {{/hassequence}}
                                        {{#haspdf}}
                                            <button class="dropdown-item dropdown-course-index-item" tabindex="0" data-href="{{ urlpdf }}">{{#str}}viewerview, local_npe{{/str}} {{txtBook}}</button>
                                        {{/haspdf}}
                                        {{#isteacher}}
                                            <button class="dropdown-item dropdown-course-index-item share-url" tabindex="0" data-url="{{ urltoshare }}">{{#str}}url_to_share, local_npe{{/str}}</button>
                                        {{/isteacher}}
                                    {{/hascontent}}
                                {{/dotsoptions}}
                            {{/local_npe/commons/dotsmenu}}
                        {{/ showmenu }}
                    </div>
                </div>
                {{# topic_filter }}
                <div class="topic-col-filter" data-href="{{ url }}">
                    <span class="topic-filter-text" title="{{ title }}">{{ title }}</span>
                    {{# icon }}
                    <div class="topic-filter-icon">
                        {{{ icon }}}
                    </div>
                    {{/ icon }}
                </div>
                {{/ topic_filter }}
            </div>
        </a>
        <div class="row view-move-topic view-move-topic-low
        {{# visibility_only_teacher }}black{{/ visibility_only_teacher }}
        {{# visibility_teacher }}visibility_teacher{{/ visibility_teacher }}">
            <div class="item-index-into-topic content-icon-move-topic text-right col-2">
                <i class="icon-fullscream-topic {{# visibility_only_teacher }}black{{/ visibility_only_teacher }}"></i>
            </div>
            {{^ visibility_only_teacher }}
                <div class="item-index-into-topic content-icon-eye-open-topic toggleEyeTopic" id="{{ idtopic }}" idblock="{{ id }}" data-packerid={{packerid}}><i class="icon-eye-open-topic"></i></div>
            {{/ visibility_only_teacher }}
        </div>
        
        {{# visibility_only_teacher }}
            {{> local_npe/courses/matices/topics/infolabel/visibility_only_teacher }}
        {{/ visibility_only_teacher }}
        {{^ visibility_only_teacher }}
            {{> local_npe/courses/matices/topics/infolabel/visibility_teacher }}
        {{/ visibility_only_teacher }}
    </div>
</div>
{{# js }}
    {{# sdanumber }}
        $('.title-flex').each(function(){
            var width = $(this).find('.sdanumber').width();
            $(this).find('.info-text-subtitle').css('margin-left', width);
        })
    {{/ sdanumber }}
{{/ js }}
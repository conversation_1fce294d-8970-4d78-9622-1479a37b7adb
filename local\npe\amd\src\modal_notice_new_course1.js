import $ from 'jquery';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';
import ModalNoticeNewCourse2 from 'local_npe/modal_notice_new_course2';
import ModalFactory from 'core/modal_factory';

const SELECTORS = {
    USERNAMETEACHER: '#usernameteacher',
    GOON_BUTTON: '[data-action="go-on"]',
    NOTNOW_BUTTON: '[data-action="not-now"]'
};

let registered = false;

export default class ModalNoticeNewCourse1 extends Modal {

    static TYPE = 'local_npe/modal_notice_new_course_1';
    static TEMPLATE = 'local_npe/noticenewcourse/noticenewcourse_modal01_hi';

    constructor(root) {
        super(root);
    }

    setData(username) {
        this.getRoot().find(SELECTORS.USERNAMETEACHER).text(username);
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.GOON_BUTTON, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.notice-new-course-hi-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
            ModalFactory.create({type: ModalNoticeNewCourse2.TYPE}).done((modal2) => {
                modal2.show();
            });
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.NOTNOW_BUTTON, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.notice-new-course-hi-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
            $('#wizard-noticenewcourse').show();
            $('#screen-megamenu').hide();
        });
    }
}

if (!registered) {
    ModalRegistry.register(ModalNoticeNewCourse1.TYPE, ModalNoticeNewCourse1, ModalNoticeNewCourse1.TEMPLATE);
    registered = true;
}

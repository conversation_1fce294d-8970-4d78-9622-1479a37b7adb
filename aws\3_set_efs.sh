#!/bin/bash
# SM Set vhost script

# Set params

ENTORNO=QA
if [[ $DEPLOYMENT_GROUP_NAME = "Moodle-Staging" || $DEPLOYMENT_GROUP_NAME = "Cron-Staging" ]]; then
  ENTORNO=STG
elif [[ $DEPLOYMENT_GROUP_NAME = "Moodle" || $DEPLOYMENT_GROUP_NAME = "MoodleCron" ]]; then
  ENTORNO=PRO
fi

FSTABFILE=/etc/fstab
EFS=\#__EFS__

#Check if fstab exists
if [ ! -f "$FSTABFILE" ]; then
    echo "$FSTABFILE not found!"
    exit 1
fi

# Exec if fstab has not been built
if grep -q $EFS $FSTABFILE; then
  # Get AWS params
  AWSDATA=$( (aws ssm get-parameters --names /"$ENTORNO"/NPE/EFS --query Parameters[0].Value) | sed 's/"//g')

  # Add PARAMS
  sedParams=(-e "s|$EFS|$AWSDATA|g")
  sed -i "${sedParams[@]}" $FSTABFILE

  mount -a
else
  echo "$FSTABFILE already configured"
fi

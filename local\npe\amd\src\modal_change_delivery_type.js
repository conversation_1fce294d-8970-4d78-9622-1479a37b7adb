import $ from 'jquery';
import Notification from 'core/notification';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';
import Ajax from 'core/ajax';

const SELECTORS = {
    MODAL: '.change-delivery-type-modal',
    MODALBACKDROP: '.modal-backdrop',
    CONFIRMBUTTON: '[data-action="confirm"]',
    CANCELBUTTON: '[data-action="cancel"]',
    HIDE: '.close'
};

const SERVICES = {
    REMOVEASSIGN: 'local_npe_remove_assign'
};

let registered = false;

export default class ModalChangeDeliveryType extends Modal {

    static TYPE = 'local_npe/modal_change_delivery_type';
    static TEMPLATE = 'local_npe/assignments/modal_change_delivery_type';

    constructor(root) {
        super(root);
    }

    setData(assignurl, assignid, activityid, courseid) {
        this.assignurl = assignurl;
        this.assignid = assignid;
        this.courseid = courseid;
        this.activityid = activityid;
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.CONFIRMBUTTON, () => {
            this.ajaxCall();
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE + ', ' + SELECTORS.CANCELBUTTON, () => {
            $(SELECTORS.MODAL).remove();
            $(SELECTORS.MODALBACKDROP).remove();
            $('body').removeClass('modal-open');
        });
    }

    ajaxCall() {
        const promises = Ajax.call([{
            methodname: SERVICES.REMOVEASSIGN,
            args: {
                assign: [{'id': this.activityid}],
                teamid: null,
                userid: null,
                activityid: this.activityid,
                changedelivery: true,
                courseid: this.courseid
            }
        }]);

        promises[0].done((response) => {
            this.ajaxResult(response);
        }).fail((ex) => {
            this.ajaxError(ex);
        });
    }

    ajaxError(ex) {
        Notification.exception({message: ex});
    }

    ajaxResult(response) {
        if (response.success === true) {
            window.location.href = this.assignurl;
        }
    }

}

if (!registered) {
    ModalRegistry.register(ModalChangeDeliveryType.TYPE, ModalChangeDeliveryType, ModalChangeDeliveryType.TEMPLATE);
    registered = true;
}

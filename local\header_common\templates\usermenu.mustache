{{!

    User menu

    Context variables required for this template:

    * img: profile picture img block
    * user_name: user name
    * notifications_counter: notifications counter,
    * basicdownloadurl: app basic download url,
    * logouturl: logout url,
    * mylicensesurl: my licenses url,
    * mynotificationsurl: my notifications url,
    * isdirector: if user is director,
    * reportsurl: reports url
    * extralinks: add extra links to menu
    * showgoogleclassroomlink: show google classroom link
    * showmicrosoftteamslink: show microsoft teams link

    Example context (json):
    {
        "img": "img src="http://evasm/pluginfile.php/2973/user/icon/webbook/f1?rev=1658basicdownloadurl"
                alt="Imagen de ADRI PROFE SM" title="Imagen de ADRI PROFE SM" class="userpicture" />",
        "user_name": "ADRI PROFEe SM",
        "notifications_counter": 1,
        "basicdownloadurl": "http:\/\/savia.descargas.grupo-sm.com",
        "logouturl": moodle_url,
        "mylicensesurl": "http:\/\/evasm\/local\/webbook\/mylicenses.php",
        "mynotificationsurl": "http:\/\/evasm\/local\/notifications\/mynotifications.php",
        "isdirector": true,
        "reportsurl": "http:\/\/evasm\/local\/webbook\/activities\/pentaho\/view.php",
        "extralinks": true,
        "showgoogleclassroomlink": 1,
        "showmicrosoftteamslink": 0
    }

}}
<div class="header__right-wrapper">
    {{^ isseneca }}
        {{# helpurl }}
            <div class="header__help">
                <a href="{{ helpurl }}" target="_blank"
                    {{# ismobile }}
                   id="help_user_header"
                   data-mobsso="true"
                    {{/ismobile}}
                >
                    <span class="tooltiptext">{{  userlang.help }}</span>
                    <img src="{{ logohelp }}" alt="{{ userlang.help }}" />
                </a>
            </div>
        {{/ helpurl }}
    {{/ isseneca}}

    <div class="header__mynotifications">
        <a href="{{ mynotificationsurl }}"
            {{# notifications_counter }}
           class="withcounter"
            {{/ notifications_counter}}
            {{#ismobile}}
           data-args="{{ mynotificationsargs }}"
           data-sendextra="1"
            {{/ ismobile}}
        >
            <span class="tooltiptext">{{  userlang.notifications }}</span>
            <img src="{{ logonotification }}" alt="{{ userlang.notifications }}" />
            {{# notifications_counter }}
                <span class="header__mynotifications__counter" data-region="unread-notifications">
                    {{ notifications_counter }}
                </span>
            {{/ notifications_counter }}
        </a>
    </div>

    <div class="header__profile">
        <div class="header__profile__photo js_header__profile__photo" data-action="user-menu">
            <img src="{{ img }}" tabindex="0" class="userpicture" width="64" height="64" alt="profile_image" title="profile_image">
        </div>
        {{! <span class="header__profile__more icon-down-open"></span>}}
    </div>

    <div class="header__user-area__wrapper">
        <div class="header__user-area" id="header__user-data">
            <div class="header__user-area__name js_header__user-area__name">
                {{ userlang.hello_start }} <span id="js_menu__user_name">{{ user_name }}</span>!
            </div>
            <div class="header__user-area__edit">
                <span class="thumbnail">
                    <img src="{{{ img }}}">
                </span>
                <span class="myprofile">
                    <a href="{{ myprofilelink }}" title="{{ userlang.title_link_my_profile }}">
                        {{ userlang.edit_profile }}
                    </a>
                </span>
            </div>
            <ul>
                {{# ismobile }}
                {{! ESCP-7335 Se eliminan las opciones de trabajar sin conexión / conectar }}
                    <li>
                        <a id="delete_user" href="#">{{ userlang.deleteuser }}</a>
                    </li>
                    <li>
                        <a id="reset_settings" href="#">{{ userlang.resetsettings }}</a>
                    </li>
                    {{# showhelponusermenu }}
                        {{# helpurl }}
                            <li>
                                <a
                                        id="help_user"
                                        target="_blank"
                                        href="{{{ helpurl }}}"
                                        data-mobsso="true">{{ userlang.help }}</a>
                            </li>
                        {{/ helpurl }}
                    {{/showhelponusermenu}}
                {{/ ismobile }}
                {{^ ismobile }}
                    <!--WEB OPTIONS-->
                    <li><a href="{{ mylicensesurl }}">{{ userlang.mylicenses }}</a></li>
                    {{^ isseneca}}
                        {{# basicdownloadurl }}
                            <li><a href="{{ basicdownloadurl }}" target="_blank" rel="noopener">{{ userlang.download_app }}</a></li>
                        {{/ basicdownloadurl }}
                        {{# isdirector }}
                            <li>
                                {{# isroldirectoractive }}
                                    <form name="mydirectorformmenu" id="mydirectorformmenu" method="get" action="{{{ reportsurl }}}" target="_blank" class="mydirectorform">
                                        <input type="hidden" name="AuthorizationJwt" id="AuthorizationJwt" value="{{ roldirector_url }}">
                                    </form>
                                    <a id="mydirectorlink" href="#" data-action="mydirectorlink" data-form="mydirectorformmenu">
                                        {{ userlang.reports }}
                                    </a>
                                {{/ isroldirectoractive }}
                                {{^ isroldirectoractive }}
                                    <a target="_blank" href="{{{ reportsurl }}}">{{ userlang.reports }}</a>
                                {{/ isroldirectoractive }}
                            </li>
                        {{/isdirector}}
                        {{# showhelponusermenu }}
                            {{# helpurl }}
                                <li><a href="{{{ helpurl }}}" target="_blank">{{ userlang.help }}</a></li>
                            {{/ helpurl }}
                        {{/showhelponusermenu}}
                    {{/ isseneca}}
                    {{# showcontacturl }}
                        <li><a href="{{{ contacturl }}}" target="_blank">{{ userlang.headercontact }}</a></li>
                    {{/showcontacturl}}
                {{/ ismobile }}
            </ul>
            {{^ isseneca}}
                {{# extralinks }}
                    <hr>
                    <ul class="classrooms">
                        {{# showgoogleclassroomlink }}
                            <li>
                                <img src="{{ logogoogleclassroom }}" alt="Google Classroom" />
                                <a href="https://classroom.google.com/" title="{{ userlang.googleclassroom }}" target="_blank">
                                    {{ userlang.googleclassroom }}
                                </a>
                            </li>
                        {{/ showgoogleclassroomlink }}
                        {{# showmicrosoftteamslink }}
                            <li>
                                <img src="{{ logomicrosoftteam }}" alt="Microsoft Teams" />
                                <a href="https://teams.microsoft.com/" title="{{ userlang.microsoftteams }}" target="_blank">
                                    {{ userlang.microsoftteams }}
                                </a>
                            </li>
                        {{/ showmicrosoftteamslink }}
                    </ul>
                {{/ extralinks }}
            {{/ isseneca}}
        </div>
         {{^ isseneca}}
            <div class="header__user-area__close js_nd_integration_data" data-editprofileiframe="{{{ editprofileiframe }}}">
                <a href="{{ logouturl }}" id="logout_link">
                    <span>{{ userlang.logout }}</span>
                </a>
            </div>
        {{/ isseneca}}
    </div>

</div>

{{#js}}
    require(['jquery', 'local_header_common/nd_usermenu'], function ($, UserMenu) {
        $('#mydirectorlink').on('click', function(){
            $('#mydirectorformmenu').submit();
        });
        new UserMenu();
    });
{{/js}}

<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

use local_npe\activity\activity;
use local_npe\app;
use local_npe\constants;
use local_npe\core\activity_core;
use local_npe\core\persistent\topic_teacher_perso;
use local_npe\course;
use local_npe\DTO\activity_dto;
use nperepository_teacher\DTO\repository_uses_dto;
use nperepository_teacher\persistent\repository_uses;

defined('MOODLE_INTERNAL') || die;

require_once($CFG->libdir . "/externallib.php");
require_once($CFG->dirroot . "/webservice/lib.php");
require_once($CFG->dirroot . "/course/format/lib.php");

/**
 * Create external functions
 *
 * @package    local_npe
 * @category   external
 */
class local_npe_create_group_external extends external_api {

    /**
     * Valida datos de entrada
     *
     * @return external_function_parameters
     */
    public static function create_group_parameters() {
        return new external_function_parameters(
            array(
                'name' => new external_value(PARAM_TEXT, 'some name group'),
                'ccaa' => new external_value(PARAM_INT, 'group ccaa'),
                'courseid' => new external_value(PARAM_INT, 'course id', false)
            )
        );
    }

    /**
     * Crea el nuevo grupo (curso)
     *
     * @param $name
     * @param $ccaa
     * @param $currentcourseid // Este parametro solo llega cuando se está duplicando un curso
     * @return array|bool|mixed
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\user_exception
     * @throws coding_exception
     * @throws dml_exception
     * @throws invalid_parameter_exception
     * @throws moodle_exception
     */
    public static function create_group($name, $ccaa, $currentcourseid = null) {
        $params = self::validate_parameters(
            self::create_group_parameters(),
            array(
                'name' => $name,
                'ccaa' => $ccaa,
                'courseid' => $currentcourseid
            )
        );

        /** @var \local_npe\helper\session $session */
        $session = \local_npe\app::get_instance()->get(\local_npe\helper\session::class);
        $codproduct = $session->get_session('codproduct');

        if (empty($codproduct)) {
            throw new \local_npe\exception\npe_exception('Falta el codproduct');
        }

        $isexternal = app::get_userfactory()->get_current_user()->get_user_data()->is_external_user();
        // Creación del Grupo de clase.
        if (!$isexternal){
            $courseid = \local_npe\course::create_teacher_course(
                $params['name'],
                $codproduct,
                $ccaa
            );
        } else {
            $courseid = \local_npe\course::create_external_teacher_course(
                $params['name'],
                $codproduct,
                $ccaa,
                $params['name']
            );
        }

        // Creación del Equipo General.
        $teammanager = \local_npe\general_team_manager::get_instance($courseid);
        $teammanager->create_team('Equipo General');

        $params['courseid'] = $courseid;
        $params['enrolmentkey'] = \local_npe\npe_enrolment_manager::generate_enrolment_key($courseid);
        $params['error'] = 0;

        // Si llega id de curso al servicio, tenemos que duplicar el curso y cualquier recurso del repositorio del profesor.
        if ($currentcourseid) {
            $params['error'] = self::duplicate_course($currentcourseid, $courseid, $codproduct);
        }

        return $params;
    }

    /**
     * @param $currentcourseid // Curso que duplico
     * @param $courseid // Curso nuevo (duplicado)
     * @param $codproduct
     * @return int
     * @throws \core\invalid_persistent_exception
     * @throws \local_npe\exception\activity_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\user_exception
     * @throws coding_exception
     * @throws dml_exception
     * @throws file_exception
     * @throws stored_file_creation_exception
     */
    private static function duplicate_course($currentcourseid, $courseid, $codproduct) {
        global $USER;
        $currentuserid = $USER->id;
        $course = app::get_course($courseid);

        // Personalización
        try {
            $useridnumber = \local_npe\app::get_userfactory()->get_current_user()->get_idnumber();
            $userid = \local_npe\app::get_userfactory()->get_current_user()->get_id();
            $customizations = app::get_message_manager()->get_customizations(
                $useridnumber,
                -1,
                $codproduct,
                $currentcourseid
            );
            $dataperso = json_decode($customizations);

            if(!empty($dataperso)){
                current($dataperso)->idCourse = $courseid;
            }
            // Si se ha recuperado bien la informacion, se guarda en mensajería con el nuevo id de curso - GENERAL
            app::get_message_manager()->save_raw_messages(json_encode(['personalizaciones' => $dataperso]));

            // Perso por topic - ESPECIFICA DE UNIDAD
            $product_topics = app::get_product($codproduct)->get_topics();
            foreach ($product_topics as $topic) {
                $topicpackerid = $topic->get_topic_data()->get_packerid();
                $persotopic = app::get_message_manager()->get_customizations(
                    $useridnumber,
                    $topicpackerid,
                    $codproduct,
                    $currentcourseid
                );
                if (empty($persotopic)) {
                    // Pasamos a la siguiente unidad
                    continue;
                }
                $datatosend = json_decode($persotopic);
                app::get_message_manager()->save_messages(
                    current($datatosend)->codCentro,
                    $useridnumber,
                    $codproduct,
                    $courseid,
                    $topicpackerid,
                    null,
                    current($datatosend)->data
                );
            }

            // Duplicas todo para un curso y un usuario dentro de la table topic_teacher_perso
            $topics = topic_teacher_perso::get_records(
                [
                    'userid' => $userid, 
                    'courseid' => $currentcourseid
                ]
            );
            foreach ($topics as $topic) {
                // Duplicamos los registros con el nuevo curso
                $record = new topic_teacher_perso();
                    $record->set('userid', $userid);
                    $record->set('courseid', $courseid);
                    $record->set('packerid', $topic->get('packerid'));
                    $record->set('custom', false);
                    $record->save();
            }

        } catch (Exception $e) {
            \local_npe\course::delete_course($courseid);
            return 1; // error de mensajería
        }

        // Actividades del repo del profesor
        $uses = \nperepository_teacher\persistent\repository_uses::get_resources_by_course_id($currentcourseid);
        foreach ($uses as $use) {
            if (null === $use->activityid) {
                // Si hay algún error en el uso, lo descartamos.
                continue;
            }
            // Creamos la actividad
            /** @var activity_dto $newactivitydto */
            $newactivitydto = app::get_product($codproduct)->get_activity_core()->get_activity($use->activityid);
            if ($newactivitydto) {
                $newactivitydto->set_id(0);
                $newactivitydto->set_idnumber(sha1('repo' . microtime()));
                app::get_instance()->get(activity_core::class)->save_activity($newactivitydto);

                // Generamos la actividad de tipo assign.
                if ($newactivitydto->get_type() === 'assign') {
                    $activity = activity::get_activity($courseid, $newactivitydto->get_id());
                    $activity->create_moodle_activity($course);
                }
            }

            // Si contiene adjuntos (comprorbar tipo de recurso que analizamos), clonamos el archivos antes de guardar.
            $fs = get_file_storage();
            $newcontextid = \context_user::instance($currentuserid)->id;
            $files = $fs->get_area_files(
                $newcontextid,
                'nperepository_teacher',
                'attachment',
                $use->data
            );
            // Duplicamos archivo y borrador.
            $newdata = abs(crc32(uniqid())); // Por si te lo estabas preguntando, esto genera un rand int postivo.
            foreach ($files as $file) {
                $value = clean_param($file->get_filename(), PARAM_FILE);
                if ($value === '') {
                    continue;
                }
                $fileinfodraft = array(
                    'contextid' => $newcontextid,
                    'component' => 'user',
                    'filearea' => 'draft',
                    'itemid' => $newdata,
                    'filepath' => '/',
                    'filename' => $courseid . '_' . $file->get_source()
                );
                $fileinfo = array(
                    'contextid' => $newcontextid,
                    'component' => 'nperepository_teacher',
                    'filearea' => 'attachment',
                    'itemid' => $newdata,
                    'filepath' => '/',
                    'filename' => $courseid . '_' . $file->get_source()
                );
                try {
                    $fs->create_file_from_storedfile($fileinfodraft, $file);
                    $fs->create_file_from_storedfile($fileinfo, $file);
                } catch (Exception $e) {
                    \local_npe\course::delete_course($courseid);
                    return 2; // error de creacion de archivos duplicados
                }
            }

            // Creamos el uso con los datos de la nueva actividad
            try {
                /** @var repository_uses_dto $repositoryusesdto */
                $repositoryusesdto = app::get_instance()->make(repository_uses_dto::class);
                $repositoryusesdto->repositoryid = $use->repositoryid;
                $repositoryusesdto->courseid = $courseid;
                $repositoryusesdto->codproduct = $codproduct;
                $repositoryusesdto->activityid = $newactivitydto->get_id();
                $repositoryusesdto->title = $use->title;
                $repositoryusesdto->description = $use->description;
                $repositoryusesdto->data = $newdata;
                $repositoryusesdto->visibility = $use->visibility;
                $repositoryuses = new repository_uses(0, $repositoryusesdto->get_data());
                $repositoryuses->create();
            } catch (Exception $e) {
                \local_npe\course::delete_course($courseid);
                return 3; // error en el duplicado de los usos
            }
        }
        return 0; // ha salido perfecto

    }

    /**
     * Valida formato de salida
     *
     * @return external_function_parameters
     */
    public static function create_group_returns() {
        return new external_function_parameters(
            array(
                'courseid' => new external_value(PARAM_INT, 'courseid'),
                'name' => new external_value(PARAM_TEXT, 'name string unique'),
                'enrolmentkey' => new external_value(PARAM_TEXT, 'enrolmentkey unique'),
                'error' => new external_value(PARAM_RAW, 'error message'),
            )
        );
    }
}
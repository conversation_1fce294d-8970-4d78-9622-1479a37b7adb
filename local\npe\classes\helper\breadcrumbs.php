<?php

namespace local_npe\helper;

use local_npe\activity\activity;
use local_npe\app;
use local_npe\constants;
use local_npe\course;
use local_npe\product_topic;
use local_npe\teacher_team_manager;
use local_npe\general_team_manager;
use function get_string;
use local_npe\product\megamenu;


class breadcrumbs {
    protected url $urlhelper;
    /**
     * set breadcrumb
     *
     * @param string $title breadcrumb title
     * @param string|null $url
     * @return array
     */
    private function add_breadcrumb(?string $title, ?string $url) {
        return [
            'title' => $title,
            'url' => $url
        ];
    }

    /**
     * Breadcrumb for assign resource view
     * @throws \coding_exception
     */
    public function get_mygroups_breadcrumb() {
        /** @var session $sessionhelper*/
        $sessionhelper = app::get_instance()->get(session::class);

        return $this->add_breadcrumb(get_string('mysubjects', 'local_npe'),
            $sessionhelper->get_session('backurlevasm'));
    }

    /**
     * @param $megamenu
     * @param $isteacher
     * @return mixed|string
     */
    private function get_title_portfolio_monitoring($megamenu, $isteacher) {
        $portfoliomonitoring = $megamenu->get_section( constants::BLOCK_SECTION_EVATUATION,
            constants::AREA_TRACKINGTASKS, constants::PORTFOLIOMONITORING_SECTION);
        return $isteacher ? $portfoliomonitoring->get_teachertitle() : '';
    }

    /**
     * Breadcrumb for portfolio monitoring view
     * @param course $course
     * @param $isteacher
     * @param $megamenu
     * @param bool $isstudentview
     * @param string|null $url
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    public function get_portfolio_breadcrumb(course $course, $isteacher, $megamenu, bool $isstudentview = false, string $url = null) {
        $titleportfoliomonitoring = $this->get_title_portfolio_monitoring($megamenu, $isteacher);

        $portfolio = $megamenu->get_section( constants::BLOCK_SECTION_EVATUATION,
            constants::AREA_TRACKINGTASKS, constants::PORTFOLIO_SECTION);
        $titleportfolio = $isteacher ? $portfolio->get_teachertitle() : $portfolio->get_studenttitle();

        $breadcrumb = [];
        if ($isteacher) {
            $breadcrumb[] = $this->add_breadcrumb(
                $course->get_product()->get_name(),
                $this->get_url_helper()->get_groups_url($course->get_codproduct()));
        }
        $breadcrumb[] = $this->add_breadcrumb($course->get_fullname(), $this->get_url_helper()->get_course_url($course->get_id()));
        if ($isteacher && $isstudentview) {
            $breadcrumb[] = $this->add_breadcrumb(app::get_instance()->get(json::class)->get_block_title('myassesment',
                $course->get_codproduct()), $this->get_url_helper()->get_homework_url($course->get_id()));
            $breadcrumb[] = $this->add_breadcrumb($titleportfoliomonitoring,
                $this->get_url_helper()->get_portfolio_url($course->get_id()));
        } else {
            $breadcrumb[] = $this->add_breadcrumb(app::get_instance()->get(json::class)->get_block_title('myassesment',
                    $course->get_codproduct()), null);
        }

        $breadcrumb[] = $this->add_breadcrumb($titleportfolio, $url);
        return $breadcrumb;
    }

    /**
     * Breadcrumb for portfolio monitoring view
     * @param course $course
     * @param $isteacher
     * @param $megamenu
     * @param null $url
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    public function get_monitoring_breadcrumb(course $course, $isteacher, $megamenu, $url = null) {
        $titleportfoliomonitoring = $this->get_title_portfolio_monitoring($megamenu, $isteacher);

        $portfolio = $megamenu->get_section( constants::BLOCK_SECTION_EVATUATION,
            constants::AREA_TRACKINGTASKS, constants::MONITORING_SECTION);
        $titlemonitoring = $isteacher ? $portfolio->get_teachertitle() : $portfolio->get_studenttitle();

        $breadcrumb = [];
        if ($isteacher) {
            $breadcrumb[] = $this->add_breadcrumb(
                $course->get_product()->get_name(),
                $this->get_url_helper()->get_groups_url($course->get_codproduct()));
        }
        $breadcrumb[] = $this->add_breadcrumb($course->get_fullname(), $this->get_url_helper()->get_course_url($course->get_id()));
        if ($isteacher) {
            $breadcrumb[] = $this->add_breadcrumb(app::get_instance()->get(json::class)->get_block_title('myassesment',
                $course->get_codproduct()), $this->get_url_helper()->get_homework_url($course->get_id()));
            $breadcrumb[] = $this->add_breadcrumb($titleportfoliomonitoring,
                $this->get_url_helper()->get_portfolio_url($course->get_id()));
            $breadcrumb[] = $this->add_breadcrumb($titlemonitoring, $url);
        } else {
            $breadcrumb[] = $this->add_breadcrumb(app::get_instance()->get(json::class)->get_block_title('myassesment',
                $course->get_codproduct()), $url);
        }

        return $breadcrumb;
    }

    /**
     * Breadcrumb for portfolio monitoring view
     * @param course $course
     * @param $isteacher
     * @param $megamenu
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    public function get_portfolio_monitoring_breadcrumb(course $course, $isteacher, $megamenu, $url = null) {
        $titleportfoliomonitoring = $this->get_title_portfolio_monitoring($megamenu, $isteacher);
        $breadcrumb = [];
        if ($isteacher) {
            $breadcrumb[] = $this->add_breadcrumb(
                $course->get_product()->get_name(),
                $this->get_url_helper()->get_groups_url($course->get_codproduct()));
        }
        $breadcrumb[] = $this->add_breadcrumb($course->get_fullname(), $this->get_url_helper()->get_course_url($course->get_id()));
        if ($isteacher) {
            $breadcrumb[] = $this->add_breadcrumb(app::get_instance()->get(json::class)->get_block_title('myassesment',
                $course->get_codproduct()), $this->get_url_helper()->get_homework_url($course->get_id()));
        } else {
            $breadcrumb[] = $this->add_breadcrumb(app::get_instance()->get(json::class)->get_block_title('myassesment',
                $course->get_codproduct()), null);
        }
        $breadcrumb[] = $this->add_breadcrumb($titleportfoliomonitoring, $url);
        return $breadcrumb;
    }

    /**
     * Breadcrumb for feedbacklist view
     * @throws \coding_exception
     */
    public function get_feedbacklist_breadcrumb() {
        $title = 'closecomentreturn';
        $breadcrumb [] = $this->add_breadcrumb(get_string($title, 'local_npe'), null);
        return $breadcrumb;
    }

    /**
     * @param course $course
     * @param null $url
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    public function get_grade_table_breadcrumb(course $course, $url = null) {
        $breadcrumb = [];
        $breadcrumb [] = $this->add_breadcrumb(
            $course->get_product()->get_name(),
            $this->get_url_helper()->get_groups_url($course->get_codproduct()));
        $breadcrumb [] = $this->add_breadcrumb($course->get_fullname(), $this->get_url_helper()->get_course_url($course->get_id()));

        $breadcrumb [] = $this->add_breadcrumb(app::get_instance()->get(json::class)->get_block_title('myassesment',
            $course->get_codproduct()), null);
        $breadcrumb [] = $this->add_breadcrumb(get_string('gradetable', 'local_npe'), $url);

        return $breadcrumb;
    }

    /**
     * @param course $course
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    private function get_init_breadcrumb_course(course $course) {
        return [
            $this->add_breadcrumb($course->get_product()->get_name(),
                $this->get_url_helper()->get_groups_url($course->get_codproduct())),
            $this->add_breadcrumb($course->get_fullname(),
                $this->get_url_helper()->get_course_url($course->get_id())),
        ];
    }

    /**
     * Breadcrumbs for myclass view
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \moodle_exception
     * @throws \local_npe\exception\product_exception
     */
    public function get_myclass_breadcrumb(course $course) {
        $breadcrumb = $this->get_init_breadcrumb_course($course);
        $breadcrumb[] = $this->add_breadcrumb(get_string('myclass', 'local_npe'),
            $this->get_url_helper()->get_myclass_url($course->get_id()));
         //JSON - myclass_participants.
         $megamenu = megamenu::get_instance($course->get_codproduct());
         $section = $megamenu->get_section( constants::BLOCK_SECTION_MYCLASS,
            constants::AREA_MYCLASS,  constants::PARTICIPANTS_SECTION);
         $currentuser = app::get_userfactory()->get_current_user();
         $isteacher = $currentuser->is_teacher();
         $this->myclass_participants = $section != null? ($isteacher ? $section->get_teachertitle() :
             $section->get_studenttitle()): '';
         $breadcrumb[] = $this->add_breadcrumb($this->myclass_participants, null);

        return $breadcrumb;
    }

    /**
     * Breadcrumbs for myteams view
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \moodle_exception
     * @throws \local_npe\exception\product_exception
     */
    public function get_myteams_breadcrumb(course $course) {
        $breadcrumb = $this->get_init_breadcrumb_course($course);
        $breadcrumb[] = $this->add_breadcrumb(get_string('myclass', 'local_npe'),
            $this->get_url_helper()->get_myclass_url($course->get_id()));
        $breadcrumb[] = $this->add_breadcrumb(get_string('myclass_teams', 'local_npe'), null);

        return $breadcrumb;
    }

    /**
     * Breadcrumbs for myteam view/edit/add
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\product_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws \coding_exception
     * @throws \local_npe\exception\course_exception
     * @throws \Exception
     */
    public function get_myteam_breadcrumb(course $course, ?int $teamid) {
        $generalmanager = general_team_manager::get_instance($course->get_id());
        $generalteam = $generalmanager->get_team();
        $teammanager = teacher_team_manager::get_instance($course->get_id());
        $team = $teammanager->get_team($teamid);
        $teamname = $team ? $team->get_name() : ($generalmanager ? $generalteam->get_name() : get_string('createteam', 'local_npe'));

        $breadcrumb = $this->get_init_breadcrumb_course($course);
        $breadcrumb[] = $this->add_breadcrumb(get_string('myclass', 'local_npe'),
            $this->get_url_helper()->get_myclass_url($course->get_id()));
        $breadcrumb[] = $this->add_breadcrumb(get_string('myclass_teams', 'local_npe'),
            $this->get_url_helper()->get_myclass_url($course->get_id(), 'teams'));
        $breadcrumb[] = $this->add_breadcrumb($teamname, null);

        return $breadcrumb;
    }

    /**
     * Breadcrumb for assignmets view
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\product_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws \coding_exception
     * @throws \local_npe\exception\course_exception
     */
    public function get_assignments_breadcrumb(course $course, $title = '', $url = '') {
        if ($title == '' && $url == '') {
            $breadcrumb = $this->get_init_breadcrumb_course($course);
            $breadcrumb[] = $this->add_breadcrumb(get_string('titlepage_activitiesresources', 'local_npe'), null);
            $breadcrumb[] = $this->add_breadcrumb("",null);
        } else {
            $breadcrumb = $this->add_breadcrumb($title, $url);
        }
        return $breadcrumb;
    }

    /**
     * Breadcrumb mod/assign/view.php assignmets for ActAndRec
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\product_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws \coding_exception
     * @throws \local_npe\exception\course_exception
     */
    public function get_view_assignments_library_breadcrumb(course $course, activity $activity, $isteacher, $url = '') {
        $breadcrumb = $this->get_library_activities_resources_breadcrumb($course, $activity, $isteacher, $url);
        $breadcrumb[] = $this->add_breadcrumb($activity->get_activity_data()->get_name(), null);
        return $breadcrumb;
    }


    public function get_library_activities_resources_breadcrumb(course $course, activity $activity, $isteacher, $url = '') {
        $breadcrumb = $this->get_init_breadcrumb_course($course);
        $breadcrumb[] = $this->add_breadcrumb(get_string('titlepage_activitiesresources', 'local_npe'), $url);
        $megamenu = megamenu::get_instance($course->get_codproduct());
        $section = $megamenu->get_section(
            constants::BLOCK_RESOURCE_LIBRARY,
            constants::AREA_RESOURCE_LIBRARY,
            constants::ACTIVITIES_AND_RESOURCES_SECTION);
        $sectiontitle = $section != null ? ($isteacher ? $section->get_teachertitle() : $section->get_studenttitle()): '';
        $breadcrumb[] = $this->add_breadcrumb($sectiontitle, $url);
        return $breadcrumb;
    }

    /**
     * Breadcrumb mod/assign/view.php assignmets for ActAndResAsi
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\product_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws \coding_exception
     * @throws \local_npe\exception\course_exception
     */
    public function get_view_assign_actAndResAsi_breadcrumb(course $course, activity $activity, $url = '') {
        $breadcrumb = $this->get_library_activities_resources_assign_breadcrumb($course, $url);
        $breadcrumb[] = $this->add_breadcrumb($activity->get_activity_data()->get_name(), null);
        return $breadcrumb;
    }

    /**
     * @param course $course
     * @param string $url
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exceptionS
     */
    public function get_library_activities_resources_assign_breadcrumb(course $course, $url = '') {
        $breadcrumb = $this->get_init_breadcrumb_course($course);
        $breadcrumb[] = $this->add_breadcrumb(get_string('titlepage_activitiesresources', 'local_npe'), null);
        $megamenu = megamenu::get_instance($course->get_codproduct());
        $section = $megamenu->get_section(
            constants::BLOCK_RESOURCE_LIBRARY,
            constants::AREA_RESOURCE_LIBRARY,
            constants::ACTIVITIES_AND_RESOURCES_ASSIGN_SECTION);
        $breadcrumb[] = $this->add_breadcrumb($section->get_teachertitle(), $url);
        return $breadcrumb;
    }

    /**
     * Breadcrumb mod/assign/view.php assignmets for docTeacher
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\product_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws \coding_exception
     * @throws \local_npe\exception\course_exception
     */
    public function get_view_assign_docTeacher_breadcrumb(course $course, activity $activity, $url = '') {
        $breadcrumb = $this->get_init_breadcrumb_course($course);
        $breadcrumb[] = $this->add_breadcrumb(get_string('titlepage_activitiesresources', 'local_npe'), null);
        $megamenu = megamenu::get_instance($course->get_codproduct());
        $section = $megamenu->get_section(
            constants::BLOCK_RESOURCE_LIBRARY,
            constants::AREA_RESOURCE_LIBRARY,
            constants::TEACHER_DOCUMENTATION_SECTION);
        $breadcrumb[] = $this->add_breadcrumb($section->get_teachertitle(), $url);
        $breadcrumb[] = $this->add_breadcrumb($activity->get_activity_data()->get_name(), null);
        return $breadcrumb;
    }

    /**
     * Breadcrumb mod/assign/view.php assignmets for docStudent
     * @param course $course
     * @param activity $activity
     * @param string $url
     * @param $isteacher
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    public function get_view_assign_docStudent_breadcrumb(course $course, activity $activity, $isteacher, $url = '') {
        $breadcrumb = $this->get_init_breadcrumb_course($course);
        $breadcrumb[] = $this->add_breadcrumb(get_string('titlepage_activitiesresources', 'local_npe'), null);
        $megamenu = megamenu::get_instance($course->get_codproduct());
        $section = $megamenu->get_section(
            constants::BLOCK_RESOURCE_LIBRARY,
            constants::AREA_RESOURCE_LIBRARY,
            constants::STUDENT_DOCUMENTATION_SECTION);
        $breadcrumb[] = $this->add_breadcrumb($isteacher? $section->get_teachertitle():$section->get_studenttitle(), $url);
        $breadcrumb[] = $this->add_breadcrumb($activity->get_activity_data()->get_name(), null);
        return $breadcrumb;
    }

    /**
     * Breadcrumb mod/assign/view.php assignmets for GradeTable
     * @param course $course
     * @param activity $activity
     * @param string $url
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    public function get_view_assign_grade_table_breadcrumb(course $course, activity $activity, $isteacher, $url = '') {
        $breadcrumb = $this->get_grade_table_breadcrumb($course, $url);
        $breadcrumb[] = $this->add_breadcrumb($activity->get_activity_data()->get_name(), null);
        return $breadcrumb;
    }

    /**
     * Breadcrumb mod/assign/view.php assignmets for taskMonitoring
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\product_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws \coding_exception
     * @throws \local_npe\exception\course_exception
     */
    public function get_view_assignments_task_monitoring_breadcrumb(course $course, activity $activity, $isteacher, $url = '') {
        $breadcrumb = $this->get_myhomework_breadcrumb($course, $isteacher, $url);
        $breadcrumb[] = $this->add_breadcrumb($activity->get_activity_data()->get_name(), null);
        return $breadcrumb;
    }

    /**
     * Breadcrumb mod/assign/view.php assignmets for secuence
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\product_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws \coding_exception
     * @throws \local_npe\exception\course_exception
     */
    public function get_view_assignments_secuence_breadcrumb(course $course, activity $activity) {
        $txtsequence = app::get_product($course->get_codproduct())->get_product_data()->get_json_labels('txtSequence');
        return $this->add_breadcrumb(
            get_string('backtosequence', 'local_npe', array('txtSequence' => $txtsequence)),
            app::get_url_helper()->get_viewer_url($course->get_id(), $activity->get_activity_data()->get_product_topic_id()));
    }


    /**
     * @param course $course
     * @param activity $activity
     * @param string $url
     * @param $isteacher
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    public function get_view_assign_porfolio_breadcrumb(course $course, activity $activity, $isteacher, $url = '') {
        $megamenu = megamenu::get_instance($course->get_codproduct());
        $breadcrumb = $this->get_portfolio_breadcrumb($course, $isteacher, $megamenu, true, $url);
        $breadcrumb[] = $this->add_breadcrumb($activity->get_activity_data()->get_name(), null);
        return $breadcrumb;
    }

    /**
     * @param course $course
     * @param activity $activity
     * @param string $url
     * @param $isteacher
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    public function get_view_assign_monitoring_breadcrumb(course $course, activity $activity, $isteacher, $url = '') {
        $megamenu = megamenu::get_instance($course->get_codproduct());
        $breadcrumb = $this->get_monitoring_breadcrumb($course, $isteacher, $megamenu,  $url);
        $breadcrumb[] = $this->add_breadcrumb($activity->get_activity_data()->get_name(), null);
        return $breadcrumb;
    }

    /**
     * Breadcrumb for familyreseources view
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\product_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws \coding_exception
     * @throws \local_npe\exception\course_exception
     */
    public function get_familyreseources_breadcrumb(course $course, $isteacher) {

        if ($isteacher) {
            $breadcrumb[] = $this->add_breadcrumb($course->get_product()->get_name(),
                $this->get_url_helper()->get_groups_url($course->get_codproduct()));
        }
        $breadcrumb[] = $this->add_breadcrumb($course->get_fullname(),
            $this->get_url_helper()->get_course_url($course->get_id()));

        $codproduct = $course->get_product()->get_codproduct();
        $productdata = app::get_product($codproduct)->get_product_data();
        $title = $productdata->get_json_labels(constants::TXT_FAMILY_LINK);
        $breadcrumb[] = $this->add_breadcrumb($title, null);

        return $breadcrumb;
    }


    /**
     * Breadcrumb for resource assign
     * @param course $course
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    public function get_resource_assign_breadcrumb(course $course) {
        $actandresasiurl = $this->get_url_helper()->get_resources_list_url($course->get_id(), constants::ACTIVITIES_AND_RESOURCES_ASSIGN_SECTION);
        $breadcrumb = $this->get_library_activities_resources_assign_breadcrumb($course, $actandresasiurl);
        $breadcrumb[] = $this->add_breadcrumb(get_string('dropdown-assign', 'local_npe'), null);

        return $breadcrumb;
    }

    /**
     * @param course $course
     * @param activity $activity
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    public function get_assign_exam_breadcrumb(course $course, activity $activity) {
        $actandresasiurl = $this->get_url_helper()->get_resources_list_url($course->get_id(), constants::ACTIVITIES_AND_RESOURCES_ASSIGN_SECTION);
        $breadcrumb = $this->get_library_activities_resources_assign_breadcrumb($course, $actandresasiurl);
        $breadcrumb[] = $this->add_breadcrumb(get_string('dropdown-assign', 'local_npe'), null);
        $breadcrumb[] = $this->add_breadcrumb($activity->get_activity_data()->get_name(), null);
        return $breadcrumb;
    }

    /**
     * Breadcrumb for edit assigment
     * @param course $course
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    public function get_edit_assign_activity_breadcrumb(course $course) {
        $section = optional_param('section', '', PARAM_TEXT);
        if (!is_null($section) && !empty($section)) {
            if ($section === constants::TASKMONITORING_SECTION) {
                $params = ['section' => constants::TASKMONITORING_SECTION];
                $taskmonitoringurl = $this->get_url_helper()->get_homework_url($course->get_id(), $params);
                $breadcrumb = $this->get_myhomework_breadcrumb($course, true, $taskmonitoringurl);
            } else if ($section === constants::ACTIVITIES_AND_RESOURCES_ASSIGN_SECTION) {
                $actandresasiurl = $this->get_url_helper()->get_resources_list_url($course->get_id(), constants::ACTIVITIES_AND_RESOURCES_ASSIGN_SECTION);
                $breadcrumb = $this->get_library_activities_resources_assign_breadcrumb($course,  $actandresasiurl);
            }
        }
        else {
            $breadcrumb = $this->get_init_breadcrumb_course($course);
            $breadcrumb[] = $this->add_breadcrumb(get_string('titlepage_activitiesresources', 'local_npe'),
                $this->get_url_helper()->get_resources_list_url($course->get_id()));
        }
        $breadcrumb[] = $this->add_breadcrumb(get_string('dropdown-assign-edit', 'local_npe'), null);

        return $breadcrumb;
    }

    /**
     * Breadcrumb for followup view
     * @param course $course
     * @param activity $activity
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    public function get_followup_activity_breadcrumb(course $course, activity $activity) {
        $breadcrumb = $this->get_init_breadcrumb_course($course);
        $megamenu = megamenu::get_instance($course->get_codproduct());
        $breadcrumb[] = $this->add_breadcrumb($megamenu->get_block_title(constants::BLOCK_SECTION_EVATUATION),
            $this->get_url_helper()->get_homework_url($course->get_id()));
        $breadcrumb[] = $this->add_breadcrumb($activity->get_activity_data()->get_name(), null);
        return $breadcrumb;
    }

    /**
     * Breadcrumb for forum view
     * @param course $course
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    public function get_forum_breadcrumb(course $course) {
        $breadcrumb = $this->get_init_breadcrumb_course($course);
        $breadcrumb[] = $this->add_breadcrumb(app::get_instance()->get(json::class)->get_block_title('myassesment',
            $course->get_codproduct()), null);
        $breadcrumb[] = $this->add_breadcrumb(get_string('forum', 'local_npe'), null);

        return $breadcrumb;
    }

    /**
     * Breadcrumb for general forum view
     *
     * @param course $course
     * @param \stdClass|null $headermenuexport
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    public function get_generalforum_breadcrumb(course $course, \stdClass $headermenuexport=null) {
        if ($headermenuexport) {
            foreach ($headermenuexport->comunication['areassections1']['columsmenus']['seccolums'] as $seccolum) {
                if ($seccolum['class'] === 'forum') {
                    $generalforumtitle = $seccolum['title'];
                }
            }
        }
        $breadcrumb = $this->get_init_breadcrumb_course($course);
        $codproduct = $course->get_codproduct();
        $breadcrumb[] = $this->add_breadcrumb(app::get_instance()->get(json::class)->get_block_title('mycomunication', $codproduct),
            null);
        $breadcrumb[] = $this->add_breadcrumb($generalforumtitle ?? '', null);
        return $breadcrumb;
    }

    /**
     * Breadcrumb for activities mod/assign/view
     * @param course $course
     * @param activity $activity
     * @param bool $fromviewer
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\user_exception
     * @throws \moodle_exception
     */
    public function get_activity_breadcrumb(course $course, activity $activity, $fromviewer = false) {

        $currentuser = app::get_userfactory()->get_current_user();
        $isteacher = $currentuser->is_teacher();

        $actandrecurl = $this->get_url_helper()->get_resources_list_url($course->get_id(),'actAndRec');

        if ($fromviewer) {
            $breadcrumb = $this->get_view_assignments_library_breadcrumb($course, $activity, $isteacher, $actandrecurl);
        } else {
            $section = $this->get_breadcrumb_section();
            if (!is_null($section) && !empty($section)) {
                if ($section === constants::ACTIVITIES_AND_RESOURCES_SECTION) {
                    $breadcrumb = $this->get_view_assignments_library_breadcrumb($course, $activity, $isteacher, $actandrecurl);
                } else if ($section === constants::TASKMONITORING_SECTION) {
                    $params = ['section' => constants::TASKMONITORING_SECTION];
                    $taskmonitoringurl = $this->get_url_helper()->get_homework_url($course->get_id(), $params);
                    $breadcrumb = $this->get_view_assignments_task_monitoring_breadcrumb($course, $activity, $isteacher, $taskmonitoringurl);
                } else if ($section === constants::ACTIVITIES_AND_RESOURCES_ASSIGN_SECTION && $isteacher) {
                    $actandresasiurl = $this->get_url_helper()->get_resources_list_url($course->get_id(), constants::ACTIVITIES_AND_RESOURCES_ASSIGN_SECTION);
                    $breadcrumb = $this->get_view_assign_actAndResAsi_breadcrumb($course, $activity, $actandresasiurl);
                } else if ($section === constants::TEACHER_DOCUMENTATION_SECTION && $isteacher) {
                    $docteacherurl = $this->get_url_helper()->get_resources_list_url($course->get_id(), constants::TEACHER_DOCUMENTATION_SECTION);
                    $breadcrumb = $this->get_view_assign_docTeacher_breadcrumb($course, $activity, $docteacherurl);
                } else if ($section === constants::STUDENT_DOCUMENTATION_SECTION) {
                    $docstudenturl = $this->get_url_helper()->get_resources_list_url($course->get_id(), constants::STUDENT_DOCUMENTATION_SECTION);
                    $breadcrumb = $this->get_view_assign_docStudent_breadcrumb($course, $activity, $isteacher, $docstudenturl);
                } else if ($section === constants::GRADES_SECTION) {
                    $gradetableurl = $this->get_url_helper()->get_grade_table_url($course->get_id());
                    $breadcrumb = $this->get_view_assign_grade_table_breadcrumb($course, $activity, $isteacher, $gradetableurl);
                } else if ($section === constants::PORTFOLIOMONITORING_SECTION) {
                    $userid = optional_param('userid', 0, PARAM_INT);
                    $porfoliourl = $this->get_url_helper()->get_student_portfolio_url($course->get_id(), $userid);
                    $breadcrumb = $this->get_view_assign_porfolio_breadcrumb($course, $activity, $isteacher, $porfoliourl);
                } else if ($section === constants::STUDENT_TASKMONITORING_SECTION) {
                    $userid = optional_param('userid', 0, PARAM_INT);
                    $monitoringurl =  $this->get_url_helper()->get_student_monitoring_url($course->get_id(), $userid);
                    $breadcrumb = $this->get_view_assign_monitoring_breadcrumb($course, $activity, $isteacher, $monitoringurl);
                }

            } else {
                $breadcrumb = $this->get_view_assignments_library_breadcrumb($course, $activity, $isteacher, $actandrecurl);
            }
        }
        return $breadcrumb;
    }


    /**
     * @return string
     */
    private function get_breadcrumb_section() : string  {
        $section = optional_param('section', '', PARAM_TEXT);
        $referer = $_SERVER['HTTP_REFERER'] ?? '';
        if ($referer && empty($section)){
            parse_str(parse_url($referer)['query'], $url_params);
            $section = isset($url_params['section']) ? trim($url_params['section']) : '';
        }

        if (strstr($referer, 'npe/courses/index.php') ||
            strstr($referer, 'courses/feedbacklist.php')) {
            $section = constants::TASKMONITORING_SECTION;
        }
        if ($section == constants::TASKMONITORING_SECTION &&
            strstr($_SERVER['HTTP_REFERER'], url::MYPORTFOLIO_PATH)) {
           $section = constants::STUDENT_TASKMONITORING_SECTION;
        }

        return  $section;
    }

    /**
     * Breadcrumb for Meeting view
     * @param course $course
     * @param $meeting_label
     * @return array
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     */
    public function get_meetingroom(course $course, $meeting_label, $isteacher) {
        if ($isteacher){
            $breadcrumb[] = $this->add_breadcrumb($course->get_product()->get_name(),
                $this->get_url_helper()->get_groups_url($course->get_codproduct()));
        }
        $breadcrumb[] = $this->add_breadcrumb($course->get_fullname(),
            $this->get_url_helper()->get_course_url($course->get_id()));
        $breadcrumb[] = $this->add_breadcrumb(app::get_instance()->get(json::class)->get_block_title('mycomunication',
            $course->get_codproduct()), null);
        if ( $meeting_label ) {
            $breadcrumb[] = $this->add_breadcrumb($meeting_label, null);
        }

        return $breadcrumb;
    }

    /**
     * @param course $course
     * @param $isteacher
     * @param null $url
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */

    public function get_myhomework_breadcrumb(course $course, $isteacher, $url = null) {
        //JSON - txtTrackingActivitie.
        $megamenu = megamenu::get_instance($course->get_codproduct());
        $section = $megamenu->get_section(
            constants::BLOCK_SECTION_EVATUATION,
            constants::AREA_TRACKINGTASKS,
            constants::TASKMONITORING_SECTION);
        $this->txtTrackingActivitie = $section != null?( $isteacher ? $section->get_teachertitle() : $section->get_studenttitle()):'';
        return [
            $this->add_breadcrumb($course->get_product()->get_name(),$this->get_url_helper()->get_course_url($course->get_id())),
            $this->add_breadcrumb($course->get_fullname(), null),
            $this->add_breadcrumb(app::get_instance()->get(json::class)->get_block_title('myassesment',
                $course->get_codproduct()), null),

            $this->add_breadcrumb($this->txtTrackingActivitie, $url)
        ];
    }

    /**
     * Breadcrumb for activity view
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\product_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws \coding_exception
     * @throws \local_npe\exception\course_exception
     */
    public function get_view_activity_breadcrumb(course $course, activity $activity) {
        $breadcrumb = $this->get_init_breadcrumb_course($course);
        $breadcrumb[] = $this->add_breadcrumb(get_string('titlepage_activitiesresources', 'local_npe'),
            $this->get_url_helper()->get_resources_list_url($course->get_id()));
        $breadcrumb[] = $this->add_breadcrumb($activity->get_activity_data()->get_name(), null);

        return $breadcrumb;
    }

    /**
     * Breadcrumbs for pdfviewer view
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \moodle_exception
     * @throws \local_npe\exception\product_exception
     */
    public function get_pdfviewer_breadcrumb(course $course, product_topic $topic) {
        return [
        $this->add_breadcrumb($course->get_title(),
            $this->get_url_helper()->get_course_url($course->get_id())),
        $this->add_breadcrumb($topic->get_name(), null)
        ];
    }

    /**
     * @return url
     */
    private function get_url_helper() {
        return $this->urlhelper ?? $this->set_url_helper();
    }

    /**
     * Setea el url helper.
     * Costura para Test Unitarios.
     *
     * @param url|null $urlhelper
     *
     * @return url
     */
    private function set_url_helper(): url{
        $this->urlhelper = new url();
        return $this->urlhelper;
    }

    /**
     * @param \local_npe\course $course
     * @return array
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    public function get_myrepository_breadcrumb(course $course): array {
        $breadcrumb = [];
        $breadcrumb[] = $this->add_breadcrumb($course->get_product()->get_name() . ' ' .
            $course->get_product()->get_stage() . ' ' . $course->get_fullname(),
            $this->get_url_helper()->get_course_url($course->get_id()));
        $breadcrumb[] = $this->add_breadcrumb(get_string('viewpageheading', 'local_npe'),
            $this->get_url_helper()->get_myrepository_url($course->get_id()));
        return $breadcrumb;
    }

    /**
     * @param \local_npe\course $course
     * @return array
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    public function get_newresource_breadcrumb(course $course): array {
        $breadcrumb = $this->get_myrepository_breadcrumb($course);
        $breadcrumb[] = $this->add_breadcrumb(get_string('createnew'), null);
        return $breadcrumb;
    }

    /**
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     * @throws \local_npe\exception\npe_exception
     */
    public function get_editesource_breadcrumb(course $course): array {
        $breadcrumb = $this->get_myrepository_breadcrumb($course);
        $breadcrumb[] = $this->add_breadcrumb(get_string('edit_dd', 'local_npe'), null);
        return $breadcrumb;
    }

    /**
     * @param \local_npe\course $course
     * @return array
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     * @throws \local_npe\exception\npe_exception
     */
    public function get_editesource_library_breadcrumb(course $course): array {
        $breadcrumb = $this->get_init_breadcrumb_course($course);
        $breadcrumb[] = $this->add_breadcrumb(get_string('titlepage_activitiesresources', 'local_npe'),
            $this->get_url_helper()->get_resources_list_url($course->get_id()));
        $breadcrumb[] = $this->add_breadcrumb(get_string('edit_dd', 'local_npe'), null);
        return $breadcrumb;
    }

    /**
     * @param \local_npe\course $course
     * @return array
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    public function get_assignresource_breadcrumb(course $course): array {
        $breadcrumb = $this->get_myrepository_breadcrumb($course);
        $breadcrumb[] = $this->add_breadcrumb(get_string('viewpagebuttoncopy', 'nperepository_teacher'), null);
        return $breadcrumb;
    }

    /**
     * Breadcrumb for portfolio monitoring view
     * @param course $course
     * @param null $evidencetitle
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    public function get_portfolio_monitoring_evidence(course $course, $evidencetitle = null, $topicid = null) {
        $breadcrumb = [];
        $breadcrumb [] = $this->add_breadcrumb($course->get_fullname(), $this->get_url_helper()->get_course_url($course->get_id()));
        $breadcrumb [] = $this->add_breadcrumb(app::get_instance()->get(json::class)->get_block_title('myassesment',
            $course->get_codproduct()), null);
        $breadcrumb [] = $this->add_breadcrumb(get_string('portfolio', 'local_npe'),
        $this->get_url_helper()->get_portfolio_url($course->get_id(), $topicid));
        if ($evidencetitle != null) {
            $breadcrumb [] = $this->add_breadcrumb(get_string('porfolioevidenceedit', 'local_npe'), null);
        }
        else {
            $breadcrumb [] = $this->add_breadcrumb(get_string('porfolioevidencenew', 'local_npe'), null);
        }

        return $breadcrumb;
    }

}

.backbutton,
.backlinkbutton {
    content: "";
    width: 30px;
    height: 30px;
    background-image: url([[pix:theme|arrow-white]]);
    -webkit-background-size: 16px auto;
    background-repeat: no-repeat;
    background-size: 16px auto;
    background-position-x: 7px;
    background-position-y: 6px;
    display: block;
    border-radius: 50px;
    cursor: pointer;
}
.npe-breadcrumb {
    ul li {
        list-style: none;
        display: inline;
    }
    ul,
    li {
        padding: 0;
        margin: 0;
        &.breadcrumb-item::before {
            float: none;
        }
    }
    margin-left: 20px;
    margin-top: 20px;
    margin-bottom: 20px;
    background: $basic-color-w;
    font-size: 12px;
    color: $basic-color-d;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    border-radius: 50px;
    border: 2px solid $basic-color-w;
    align-items: center;
    padding-right: 5px;
    .npe-backcontainer {
        padding-right: 5px;
    }
    .npe-linkcontainer {
        font-family: "Work Sans SemiBold";
        font-weight: bolder;
    }
}

.underline {
    text-decoration: underline;
}
{"version": 3, "file": "modal_input.min.js", "sources": ["../src/modal_input.js"], "sourcesContent": ["import $ from 'jquery';\nimport Notification from 'core/notification';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\nimport Ajax from 'core/ajax';\nimport EventListener from 'local_npe/event_listener';\nimport Str from 'core/str';\nimport Templates from 'core/templates';\nimport Events from 'local_npe/project_events';\nimport Services from 'local_npe/project_services';\n\nconst SELECTORS = {\n    ACCEPT_BUTTON: '[data-action=\"accept\"]',\n    CANCEL_BUTTON: '[data-action=\"cancel\"]',\n    INPUT_COURSE_NAME: '[data-modal=\"input-value\"]',\n    MESSAGE_ALERT: '.message_alert',\n};\n\nlet registered = false;\n\n// TODO esta modal parece que no se usa, los eventos que la lanzan no se disparan nunca. revisar y borrar.\nexport default class ModalInput extends Modal {\n\n    static TYPE = 'local_npe/modal_input';\n    static TEMPLATE = 'local_npe/modal_input';\n\n    #identifier;\n\n    constructor(root) {\n        super(root);\n\n        if (!this.getFooter().find(SELECTORS.ACCEPT_BUTTON).length) {\n            Notification.exception({message: 'No accept button found'});\n        }\n\n        if (!this.getFooter().find(SELECTORS.CANCEL_BUTTON).length) {\n            Notification.exception({message: 'No cancel button found'});\n        }\n    }\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.ACCEPT_BUTTON, () => {\n            EventListener.shoutEvent(Events.BUTTONS_MODAL_INPUT.SUBMIT, {\n                'identifier': this.#identifier,\n                'inputval': $(SELECTORS.INPUT_COURSE_NAME).val()\n            });\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CANCEL_BUTTON, () => {\n            EventListener.shoutEvent(Events.RELOAD.PROJECT, {\n                'identifier': this.#identifier\n            });\n            this.hide();\n            this.destroy();\n        });\n\n        this.getModal().on('focus', SELECTORS.INPUT_COURSE_NAME, () => {\n            $(SELECTORS.MESSAGE_ALERT).html('');\n            $(SELECTORS.ACCEPT_BUTTON).removeClass('disabled');\n        });\n    }\n\n    setIntro(string, lang) {\n        Str.get_strings(\n            [\n                {key: 'save', component: 'local_npe', lang: lang},\n                {key: 'cancel', component: 'local_npe', lang: lang},\n            ]\n        ).done((strs) => {\n            this.setBody(Templates.render('local_npe/modal_input_body', {intro: string}));\n            this.setFooter(Templates.render('local_npe/modal_input_footer', {save: strs[0], cancel: strs[1]}));\n        });\n    }\n\n    setId(string) {\n        this.#identifier = string;\n    }\n\n    showError(dataError) {\n        Templates.render('local_npe/modal_input_error_message', dataError).done((html) => {\n            $('[data-region=\"modal_input_error_message\"]').replaceWith(html);\n            $(SELECTORS.ACCEPT_BUTTON).addClass('disabled');\n        });\n    }\n\n    setSucces(courseid, coursename, namecreator, picture) {\n        switch (this.#identifier) {\n            case 0:\n                // Join course.\n                var arr = {name: coursename, namecreator: namecreator, picture: picture};\n                this.setBody(Templates.render('local_npe/modal_input_succes_join', arr));\n                this.setFooter(Templates.render('local_npe/modal_input_footer_button_continue', ''));\n                Str.get_string('welcome', 'local_npe').done((welcome) => {\n                    this.setTitle(welcome);\n                });\n                break;\n            case 1:\n                // Create course.\n                var getTeacherKey = Ajax.call([{methodname: Services.GET_STUDENT_KEY, args: {courseid: courseid}}], true, true);\n                getTeacherKey[0].done((response) => {\n                    var arr = {name: coursename, studentcode: response.password};\n                    this.setBody(Templates.render('local_npe/modal_input_succes_create', arr));\n                    this.setFooter(Templates.render('local_npe/modal_input_footer_button_continue', ''));\n                    Str.get_string('welcome', 'local_npe').done((welcome) => {\n                        this.setTitle(welcome);\n                    });\n                });\n                break;\n            case 2:\n                // Edit name.\n                this.hide();\n                this.destroy();\n                break;\n        }\n    }\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalInput.TYPE, ModalInput, ModalInput.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_classPrivateFieldInitSpec", "t", "a", "has", "TypeError", "_checkPrivateRedeclaration", "set", "_defineProperty", "r", "i", "Symbol", "toPrimitive", "call", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_classPrivateFieldGet", "s", "get", "_assert<PERSON>lassBrand", "n", "arguments", "length", "_j<PERSON>y", "_notification", "_custom_interaction_events", "_modal", "_modal_registry", "_ajax", "_event_listener", "_str", "_templates", "_project_events", "_project_services", "SELECTORS", "registered", "_identifier", "WeakMap", "ModalInput", "Modal", "constructor", "root", "super", "this", "getFooter", "find", "Notification", "exception", "message", "registerEventListeners", "getModal", "on", "CustomEvents", "events", "activate", "EventListener", "shoutEvent", "Events", "BUTTONS_MODAL_INPUT", "SUBMIT", "identifier", "inputval", "$", "val", "RELOAD", "PROJECT", "hide", "destroy", "html", "removeClass", "setIntro", "string", "lang", "Str", "get_strings", "key", "component", "done", "strs", "setBody", "Templates", "render", "intro", "setFooter", "save", "cancel", "setId", "showError", "dataError", "replaceWith", "addClass", "setSucces", "courseid", "coursename", "namecreator", "picture", "arr", "name", "get_string", "welcome", "setTitle", "Ajax", "methodname", "Services", "GET_STUDENT_KEY", "args", "response", "studentcode", "password", "_exports", "ModalRegistry", "register", "TYPE", "TEMPLATE"], "mappings": "2aAUkD,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,2BAAAH,EAAAI,EAAAC,IAAA,SAAAL,EAAAI,GAAA,GAAAA,EAAAE,IAAAN,GAAA,MAAA,IAAAO,UAAA,iEAAA,EAAAC,CAAAR,EAAAI,GAAAA,EAAAK,IAAAT,EAAAK,EAAA,CAAA,SAAAK,gBAAAV,EAAAW,EAAAP,GAAAO,OAAAA,EAAA,SAAAP,GAAAQ,IAAAA,EAAA,SAAAR,EAAAO,GAAAP,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAJ,IAAAA,EAAAI,EAAAS,OAAAC,yBAAAd,EAAA,CAAA,IAAAY,EAAAZ,EAAAe,KAAAX,EAAAO,kCAAAC,EAAA,OAAAA,EAAAL,MAAAA,IAAAA,4EAAAI,EAAAK,OAAAC,QAAAb,EAAA,CAAAc,CAAAd,EAAAQ,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAO,CAAAR,MAAAX,EAAAoB,OAAAC,eAAArB,EAAAW,EAAAW,CAAAA,MAAAlB,EAAAmB,YAAAC,EAAAA,cAAAC,EAAAA,cAAAzB,EAAAW,GAAAP,EAAAJ,CAAA,CAAA,SAAA0B,sBAAAC,EAAAtB,GAAAsB,OAAAA,EAAAC,IAAAC,kBAAAF,EAAAtB,GAAA,CAAA,SAAAwB,kBAAA7B,EAAAI,EAAA0B,GAAA,GAAA,mBAAA9B,EAAAA,IAAAI,EAAAJ,EAAAM,IAAAF,GAAA,OAAA2B,UAAAC,OAAA,EAAA5B,EAAA0B,EAAA,MAAA,IAAAvB,UAAA,gDAAA,iFAVlD0B,QAAAlC,uBAAAkC,SACAC,cAAAnC,uBAAAmC,eACAC,2BAAApC,uBAAAoC,4BACAC,OAAArC,uBAAAqC,QACAC,gBAAAtC,uBAAAsC,iBACAC,MAAAvC,uBAAAuC,OACAC,gBAAAxC,uBAAAwC,iBACAC,KAAAzC,uBAAAyC,MACAC,WAAA1C,uBAAA0C,YACAC,gBAAA3C,uBAAA2C,iBACAC,kBAAA5C,uBAAA4C,mBAEA,MAAMC,wBACa,yBADbA,wBAEa,yBAFbA,4BAGiB,6BAHjBA,wBAIa,iBAGnB,IAAIC,YAAa,EAAM,IAAAC,gBAAAC,QAGR,MAAMC,mBAAmBC,OAAAA,QAOpCC,WAAAA,CAAYC,MACRC,MAAMD,MAHVhD,gCAAA2C,iBAAW,GAKFO,KAAKC,YAAYC,KAAKX,yBAAyBZ,QAChDwB,cAAYtD,QAACuD,UAAU,CAACC,QAAS,2BAGhCL,KAAKC,YAAYC,KAAKX,yBAAyBZ,QAChDwB,cAAYtD,QAACuD,UAAU,CAACC,QAAS,0BAEzC,CAEAC,sBAAAA,GACIP,MAAMO,uBAAuBN,MAE7BA,KAAKO,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUpB,yBAAyB,KACtEqB,gBAAa/D,QAACgE,WAAWC,gBAAAA,QAAOC,oBAAoBC,OAAQ,CACxDC,WAAc5C,sBAAKoB,YAALO,MACdkB,UAAY,EAAAC,QAAAA,SAAE5B,6BAA6B6B,OAC7C,IAGNpB,KAAKO,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUpB,yBAAyB,KACtEqB,gBAAa/D,QAACgE,WAAWC,gBAAAA,QAAOO,OAAOC,QAAS,CAC5CL,WAAc5C,sBAAKoB,YAALO,QAElBA,KAAKuB,OACLvB,KAAKwB,SAAS,IAGlBxB,KAAKO,WAAWC,GAAG,QAASjB,6BAA6B,MACrD,EAAA4B,QAAAA,SAAE5B,yBAAyBkC,KAAK,KAChC,EAAAN,QAAAA,SAAE5B,yBAAyBmC,YAAY,WAAW,GAE1D,CAEAC,QAAAA,CAASC,OAAQC,MACbC,KAAGjF,QAACkF,YACA,CACI,CAACC,IAAK,OAAQC,UAAW,YAAaJ,KAAMA,MAC5C,CAACG,IAAK,SAAUC,UAAW,YAAaJ,KAAMA,QAEpDK,MAAMC,OACJnC,KAAKoC,QAAQC,WAAAA,QAAUC,OAAO,6BAA8B,CAACC,MAAOX,UACpE5B,KAAKwC,UAAUH,WAAAA,QAAUC,OAAO,+BAAgC,CAACG,KAAMN,KAAK,GAAIO,OAAQP,KAAK,KAAK,GAE1G,CAEAQ,KAAAA,CAAMf,QAnEwC,IAAAtD,EAAAtB,EAAAM,EAAAN,EAoE1CgD,KApE0C1C,EAoEvBsE,QApEuBtD,EAoErCmB,aApEqCrC,IAAAoB,kBAAAF,EAAAtB,GAAAM,EAqE9C,CAEAsF,SAAAA,CAAUC,WACNR,WAASxF,QAACyF,OAAO,sCAAuCO,WAAWX,MAAMT,QACrE,EAAAN,QAAAA,SAAE,6CAA6C2B,YAAYrB,OAC3D,EAAAN,QAAAA,SAAE5B,yBAAyBwD,SAAS,WAAW,GAEvD,CAEAC,SAAAA,CAAUC,SAAUC,WAAYC,YAAaC,SACzC,OAAQ/E,sBAAKoB,YAALO,OACJ,KAAK,EAED,IAAIqD,IAAM,CAACC,KAAMJ,WAAYC,YAAaA,YAAaC,QAASA,SAChEpD,KAAKoC,QAAQC,WAASxF,QAACyF,OAAO,oCAAqCe,MACnErD,KAAKwC,UAAUH,WAASxF,QAACyF,OAAO,+CAAgD,KAChFR,KAAGjF,QAAC0G,WAAW,UAAW,aAAarB,MAAMsB,UACzCxD,KAAKyD,SAASD,QAAQ,IAE1B,MACJ,KAAK,EAEmBE,MAAAA,QAAKhG,KAAK,CAAC,CAACiG,WAAYC,kBAAQ/G,QAACgH,gBAAiBC,KAAM,CAACb,SAAUA,aAAa,GAAM,GAC5F,GAAGf,MAAM6B,WACnB,IAAIV,IAAM,CAACC,KAAMJ,WAAYc,YAAaD,SAASE,UACnDjE,KAAKoC,QAAQC,WAASxF,QAACyF,OAAO,sCAAuCe,MACrErD,KAAKwC,UAAUH,WAASxF,QAACyF,OAAO,+CAAgD,KAChFR,KAAGjF,QAAC0G,WAAW,UAAW,aAAarB,MAAMsB,UACzCxD,KAAKyD,SAASD,QAAQ,GACxB,IAEN,MACJ,KAAK,EAEDxD,KAAKuB,OACLvB,KAAKwB,UAGjB,EAMH,OALA0C,SAAArH,QAAA8C,WAAAtC,gBAhGoBsC,WAAU,OAEb,yBAAuBtC,gBAFpBsC,WAAU,WAGT,yBA+FjBH,aACD2E,gBAAAA,QAAcC,SAASzE,WAAW0E,KAAM1E,WAAYA,WAAW2E,UAC/D9E,YAAa,GAChB0E,SAAArH,OAAA"}
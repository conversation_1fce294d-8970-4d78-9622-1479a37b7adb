define("local_npe/modal_change_other_groups",["exports","jquery","core/custom_interaction_events","core/modal","core/modal_registry"],(function(_exports,_jquery,_custom_interaction_events,_modal,_modal_registry){function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _defineProperty(e,r,t){return(r=function(t){var i=function(t,r){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var i=e.call(t,r||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==typeof i?i:i+""}(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_custom_interaction_events=_interopRequireDefault(_custom_interaction_events),_modal=_interopRequireDefault(_modal),_modal_registry=_interopRequireDefault(_modal_registry);const SELECTORS={HEADER:'[data-region="header"]',NAMEGROUPTOPICS:"#namegrouptopics",BUTTONCOPYTOPICS:".buttoncopytopics"};let registered=!1;class ModalChangeOtherGroups extends _modal.default{constructor(root){super(root)}setData(nameGroup,havemoregroups){1===havemoregroups&&this.getRoot().find(SELECTORS.BUTTONCOPYTOPICS).hide(),this.getRoot().find(SELECTORS.NAMEGROUPTOPICS).text(nameGroup)}registerEventListeners(){super.registerEventListeners(this),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS.FINISH,(()=>{this.getRoot().removeClass("show"),(0,_jquery.default)(".change-other-groups-modal").remove(),(0,_jquery.default)(".modal-backdrop").removeClass("show").addClass("hide")})),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS.HIDE,(()=>{this.getRoot().find(SELECTORS.HEADER).show()})),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS.CANCELBUTTON,(()=>{(0,_jquery.default)(".change-other-groups-modal").remove(),(0,_jquery.default)(".modal-backdrop").removeClass("show").addClass("hide"),(0,_jquery.default)("body").removeClass("modal-open")}))}}return _exports.default=ModalChangeOtherGroups,_defineProperty(ModalChangeOtherGroups,"TYPE","local_npe/Modal_Change_Other_Groups"),_defineProperty(ModalChangeOtherGroups,"TEMPLATE","local_npe/courses/modal_change_other_groups"),registered||(_modal_registry.default.register(ModalChangeOtherGroups.TYPE,ModalChangeOtherGroups,ModalChangeOtherGroups.TEMPLATE),registered=!0),_exports.default}));

//# sourceMappingURL=modal_change_other_groups.min.js.map
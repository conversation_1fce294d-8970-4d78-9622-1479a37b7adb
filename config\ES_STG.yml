alfrescourl: https://moodlep:<EMAIL>/alfresco/service/sm/consulta-curso?codSAP=
alfrescourlpieces: https://moodlep:<EMAIL>/alfresco/service/sm/consulta-pieza?uuid=
alfrescourltoken: https://alfresco-es.smwebtools.net/alfresco/service/api/login?u=moodlep&pw=moodlep

urls3: https://evasm-moodledata-dev.s3-eu-west-1.amazonaws.com
keys3: ********************
secrets3: 1e44DKRU3lHBsX+jm0x2wHN7iR3bDYqvkyWnSWiR
buckets3: evasm-moodledata-dev

# CI - STG.
ciurl: https://apici-stg.smapplications.net/2/
citoken: sv@evasm_esp@eva_savia


# CI - PRO
# ciurl: https://api.grupo-sm.com/2/
# citoken: e568062f729440d99d0dfaa1f91e51ad

# CI - QA
# ciurl: https://apiciv3-qa.smapplications.net/v3/
# citoken: sv@evasm_esp@eva_savia

apismeducamosauthorization: sv@educamos_evasm_savia@eva_edu_savia
#apismeducamosauthorization: 121e5378e81d4f5db0526ebdd6e0d1ac
evainstance: s00
apismtimeout: 1
apismbatchtimeout: 60
apismretries: 2
maxusersblock: 40
codcountry: ES

local_npe:
  viewerurl: https://s3-packer-qa.smapplications.net/engines/viewer-premium/index.html?appType=WEB 
  apismtimeout: 30
  viewer_template_path:  https://s3-packer-qa.smapplications.net/engines/templates-premium
  annotationsserviceurl: https://waevaqaneuanotapi01.azurewebsites.net/
  viewerpdftronurl: https://s3-packer-qa.smapplications.net/engines/ldtron/index.html
  api_chatbot_url: https://apichatsma-qa.smapplications.net
  panel_url: https://panelesrevuela-qa.smapplications.net
  panel_key: LBMHK$KyVJo@a*@ZBGRNWmQBOe*HHGCinqro7GXZ
  api_imagenes_clientid: moodlesma
  api_imagenes_clientsecret: secretoMoodle
  api_imagenes_scope: saltonpe
  api_imagenes_urlauth: https://loginsma-qa.smapplications.net/connect/token
  api_imagenes_url: https://api-imagenes-qa.smapplications.net
  
  #PRO
  mindmap_url: https://mindmap.educamos.sm/
  mindmap_salt: 498379843279847329safg798gd809xXX

  #QA
  # mindmap_url: https://wsevaqaneuammaps01.azurewebsites.net/

#PRO
#mindmap_salt: 498379843279847329safg798gd809xXX
#QA
mindmap_salt:

assignsubmission_file:
  filetypes: archive,audio,document,image,presentation,spreadsheet,video,.zip

lang: es
langlist: ca,es,ca_valencia,eu,es_mx

# REVUELA QA
revuela_clientid: moodlesma
revuela_clientsecret: secretoMoodle
revuela_scope: saltonpe
revuela_url: http://nuevoproyectoeditorial41.smapplications.net
revuela_urlauth: https://loginsma-qa.smapplications.net/connect/token
revuela_jwkurl: https://loginsma-qa.smapplications.net/.well-known/openid-configuration/jwks
evasm_url: http://moodle41.ismeduca.com/local/dashboard/login/index.php
revuela_jwtkey: c5d5c246f2864d46af52af2f73b1138061544103

# PACKER QA
# packer_clientid: 57994de1-dbd7-4a80-8631-f18db370c3f9
# packer_clientsecret: **********************************
# packer_scope: bec8464c-bc59-40eb-a280-6b0124ae411e/.default
# packer_urlauth: https://login.microsoftonline.com/2f9bb83a-962e-4a68-b0a9-ff147c241a96/oauth2/v2.0/token
# packer_urlapi: https://api-packer-qa.smapplications.net

# PACKER PRO
packer_clientid: bb3534de-29d5-4094-a725-52da23c58b19
packer_clientsecret: ****************************************
packer_scope: 4d0289e5-61b1-4d18-a7b2-a36bf57020d4/.default
packer_urlauth: https://login.microsoftonline.com/0dca57f1-8ca8-4d39-b81d-345f03cbb5e7/oauth2/v2.0/token
packer_urlapi: https://api-packer-es.smaprendizaje.com

# Api Gestión Mensajes QA
message_manager_clientid: moodlesma
message_manager_clientsecret: secretoMoodle
message_manager_scope: Mensajes
message_manager_urlauth: https://loginsma-qa.smapplications.net/connect/token
message_manager_urlapi: https://waevaqaneunpe01.azurewebsites.net/MensajeriaApi

# Api Gestión Mensajes PRO
# message_manager_clientid: moodle
# message_manager_clientsecret: secretoMoodlePr0
# message_manager_scope: mensajes
# message_manager_urlauth: https://loginsma.smaprendizaje.com/connect/token
# message_manager_urlapi: https://waevaexneunpe01.azurewebsites.net/MensajeriaApi

# API Catálogo
pcm_url: https://apicatalogo-qa.smapplications.net/v2/
pcm_urlauth: https://loginsma-qa.smapplications.net/connect/token
pcm_clientid: capaintermedia.client
pcm_clientsecret: capaintermediaclientsecret
pcm_scope: catalogo

# IDS
ids_clientid: moodlesma
ids_clientsecret: secretoMoodle
ids_urlauth: https://loginsma-qa.smapplications.net/connect/token
ids_jwkurl: https://loginsma-qa.smapplications.net/.well-known/openid-configuration/jwks
ids_jwtkey: c5d5c246f2864d46af52af2f73b1138061544103

##Simplificaglobal
# local_simplificaglobal:
#   apismauthorization: 7fb6bd71a5ef457a92de66ba0470df1e
#   apismauthorizationcenters: e568062f729440d99d0dfaa1f91e51ad
#   apismdominio: api.grupo-sm.com
#   apismprefijo: /2/
#   apismprotocol: https
#   enable: 1
#   enablesimplifica: 1
#   openssonewwin: 1
#   simplificaglobal_auth_url: https://simplifica.smaprendizagem.com/auth
#   sso_key_pro: 81f38428423c8dc07c2b004e6aca8fe0f30
#   sso_key_qa: 014c6a016563e4e84bdbdfb562fe0133d43
#   sso_url_qa: https://dev-simplifica.smbrasil.com.br/evasso/sso
#   sso_url_pro: https://simplifica.smaprendizaje.com/evasso/sso
#   login_qa: https://dev-simplifica.smbrasil.com.br/auth?token={jwt}
#   login_pro: https://simplifica.smaprendizaje.com/auth?token={jwt}

#   version: 2020102200
#   logevents: 1
#   sso_url: https://sso.smbrasil.com.br/sma/
#   sso_key: 81f38428423c8dc07c2b004e6aca8fe0f30
#   login: https://simplifica.smaprendizaje.com/auth?token={jwt}
#   enableplanner: 1
#   planner_auth_url: https://planner.smaprendizagem.com/auth
#   enablekindergarten: 1
#   kindergarten_auth_url: https://diariodeaprendizagem.smbrasil.com.br/auth
#   moveblocktomultipiece: 0
#   moveblocktomultipiecepos: 1-1
#   block_id: B3_SSO
#   subtipo_json: planeador
#   include_in_next_multipiece: 0

# External Connect
local_externalconnect:
  # apismauthorization: 7fb6bd71a5ef457a92de66ba0470df1e
  apismauthorization: c9c66b54ae8c42208940c1a44b26d8e7 #PE
  # apismauthorizationcenters: 3037a0fe58884309adbf-0b29742dec97 #MX
  # apismauthorizationcenters: 69b4fd9f9b074809a30a555f0e545f88 #EC
  apismauthorizationcenters: e43735486ec0408c87d9e781bccb98f9 #PE
  apismdominio: api.grupo-sm.com
  apismprefijo: /2/
  apismprotocol: https
  enablesimplifica: 1
  openssonewwin: 1
  # simplifica_sso_url: https://simplifica.smaprendizagem.com/evasso/sso
  # simplifica_sso_key: 81f38428423c8dc07c2b004e6aca8fe0f30
  # simplifica_auth_url: https://simplifica.smaprendizagem.com/auth
  # simplifica_sso_url: https://dev-simplifica.smbrasil.com.br/evasso/sso #QA
  # simplifica_sso_key: 014c6a016563e4e84bdbdfb562fe0133d43 #QA
  # simplifica_auth_url: https://dev-simplifica.smbrasil.com.br/auth?token={jwt} #QA
  simplifica_sso_url: https://simplifica.smaprendizaje.com/evasso/sso #PE
  simplifica_sso_key: 81f38428423c8dc07c2b004e6aca8fe0f30 #PE
  simplifica_auth_url: https://simplifica.smaprendizaje.com/auth?token={jwt} #PE
  sso_key_pro: 81f38428423c8dc07c2b004e6aca8fe0f30
  sso_key_qa: 014c6a016563e4e84bdbdfb562fe0133d43
  sso_url_qa: https://dev-simplifica.smbrasil.com.br/evasso/sso
  sso_url_pro: https://simplifica.smaprendizaje.com/evasso/sso
  version: 2020102200
  logevents: 1
  # login: https://simplifica.smaprendizaje.com/auth?token={jwt}
  login: https://dev-simplifica.smbrasil.com.br/auth?token={jwt} #QA
  # login: https://simplifica.smaprendizaje.com/auth?token={jwt} #PRO

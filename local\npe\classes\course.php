<?php

namespace local_npe;

use local_npe\activity\activity;
use local_npe\activity\generalforum;
use local_npe\base\user\iuser;
use local_npe\core\course_core;
use local_npe\helper\transform;
use local_npe\core\persistent\assign_activity;
use local_npe\core\persistent\assign_activity_class;
use local_npe\core\persistent\assign_activity_student;
use local_npe\core\persistent\assign_activity_team;
use local_npe\course\create_course;
use local_npe\DTO\course_dto;
use local_npe\DTO\product_topic_block_dto;
use local_npe\DTO\product_topic_area_dto;
use local_npe\exception\course_exception;
use local_npe\exception\stdclass_transform_exception;
use local_npe\exception\user_exception;
use local_npe\product\calification;
use local_npe\task\generate_course_activities_adhoc;
use local_npe\task\delete_evasm_notifications_adhoc;
use local_npe\traits\adhoc;

defined('MOODLE_INTERNAL') || die();

/**
 * Class Course
 * Clase para manejar el curso dado (Grupo de clase).
 *
 * @package local_npe
 */
class course {

    use adhoc;

    /** @var int */
    private $courseid;
    /** @var course_dto */
    private $coursedto;
    /** @var iuser[] Alumnos del curso */
    private $students;
    /** @var iuser[] Profesores del curso */
    private $teachers;
    /** @var iuser[] Profesores invitados del curso */
    private $guestteachers;
    /** @var iuser[] Profesores educamos del curso */
    private $educamosteachers;
    /** @var iuser Profesor creador del curso */
    private $creator;
    /** @var iuser[] Todos los usuarios registrados en el curso (profes y alumnos). */
    private $enroledusers;
    /** @var course_core */
    private $coursecore;
    /** @var user_factory */
    private $userfactory;

    public $customsproducttopicpackerid;

    /** @var static[] */
    private static $instances;
    /** @var [] */
    private $customization = [];

    private function __construct(int $courseid) {
        $this->courseid = $courseid;
    }

    private function __clone() {
    }

    public function __wakeup() {
    }

    /**
     * Devuelve una instancia del curso.
     *
     * @param int $courseid
     * @return course
     */
    public static function get_instance(int $courseid): course {
        if (isset(self::$instances[$courseid]) === false) {
            self::$instances[$courseid] = new static($courseid);
        }
        return self::$instances[$courseid];
    }

    /**
     * Devuelve una instancia del curso.
     *
     * @param string $courseidnumber
     * @return course
     * @throws \dml_exception
     */
    public static function get_instance_by_idnumber(string $courseidnumber): ?course {
        /** @var \local_npe\core\course_core $coursecore */
        $coursecore = app::get_instance()->get(course_core::class);
        $course = $coursecore->get_courseid_by_idnumber($courseidnumber);

        return $course ? self::get_instance($course) : null;
    }

    /**
     * Id del curso.
     *
     * @return int
     */
    public function get_id(): int {
        return $this->courseid;
    }

    /**
     * @return string
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_idnumber(): string {
        return $this->get_course_data()->get_idnumber();
    }

    /**
     * Devuelve el nombre del curso.
     *
     * @return string
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_fullname(): string {
        return $this->get_course_data()->get_fullname();
    }

    /**
     * Devuelve el titulo del curso.
     *
     * @return string
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_title(): string {
        return $this->get_course_data()->get_title();
    }

    /**
     * Cambiar el nombre del curso.
     *
     * @param string $fullname
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @todo Ver si es necesario cambiar el shortname y si el shortname se va a usar para algo.
     *
     */
    public function set_fullname(string $fullname): void {
        $this->get_course_data()->set_fullname($fullname);
        $this->coursecore->update_course($this->get_course_data());
    }

    /**
     * @return course_dto
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_course_data(): course_dto {
        return $this->coursedto ?? $this->load_course_dto();
    }

    /**
     * @return course_dto
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    private function load_course_dto(): course_dto {
        $coursedto = $this->get_course_core()->get_course($this->courseid);
        $product = $this->get_product();
        $coursedto->set_title($product->get_name());
        $coursedto->set_level($product->get_level());
        return $this->coursedto = $coursedto;
    }

    /**
     * Devuelve los temas/unidades del curso.
     *
     * @return product_topic[]
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_topics(): array {
        return $this->get_product()->get_topics($this->get_ccaa());
    }

    /**
     * Devuelve los temas/unidades del curso.
     *
     * @return product_topic[]
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws course_exception
     * @throws exception\npe_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public function get_topics_by_student(): array {
        return $this->get_product()->get_topics_by_student($this->get_ccaa());
    }

    /**
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws \dml_exception
     * @throws course_exception
     */
    public function get_topics_by_block(int $blockid): array {
        return $this->get_product()->get_topics_by_block($blockid, $this->get_ccaa());
    }

    /**
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws \dml_exception
     * @throws course_exception
     */
    public function get_topics_by_area(int $blockid): array {
        return $this->get_product()->get_topics_by_area($blockid, $this->get_ccaa());
    }

    /**
     * @param int $blockid
     * @param $packerid
     * @return product_topic[]
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_topics_by_block_with_perso(int $blockid, $packerid): array {
        return $this->get_product()->get_topics_by_block_with_perso($blockid, $packerid, $this->get_ccaa());
    }

    /**
     * @param int $blockid
     * @param $packerid
     * @param $item_move
     * @return array
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_topics_by_block_with_originalperso(int $blockid, $packerid, $item_move): array {
        return $this->get_product()->get_topics_by_block_with_originalperso($blockid, $packerid, $item_move, $this->get_ccaa());
    }

    /**
     * @param int $areaid
     * @param $packerid
     * @param $item_move
     * @return array
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_topics_by_area_with_originalperso(int $areaid, $packerid, $item_move): array {
        return $this->get_product()->get_topics_by_area_with_originalperso($areaid, $packerid, $item_move, $this->get_ccaa());
    }

    /**
     * @param int $areaid
     * @param $packerid
     * @return array
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_topics_by_area_with_perso(int $areaid, $packerid): array {
        return $this->get_product()->get_topics_by_area_with_perso($areaid, $packerid, $this->get_ccaa());
    }

    /**
     * Devuelve la lista de bloques que tienen los topics.
     *
     * @return product_topic_block_dto[]
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_topics_blocks(): array {
        return $this->get_product()->get_topics_blocks($this->get_ccaa());
    }

    /**
     * Devuelve la lista de areas que tienen los topics.
     *
     * @return product_topic_area_dto[]
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_topics_areas(): array {
        return $this->get_product()->get_topics_areas($this->get_ccaa());
    }

    /**
     * @return mixed
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_topics_blocks_with_perso() {
        return $this->get_product()->get_topics_blocks_with_perso($this->get_ccaa());
    }

    /**
     * @return mixed
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_topics_areas_with_perso() {
        return $this->get_product()->get_topics_areas_with_perso($this->get_ccaa());
    }

    /**
     * @param $item_move
     * @return mixed
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_topics_blocks_with_originalperso($item_move) {
        return $this->get_product()->get_topics_blocks_with_originalperso($item_move, $this->get_ccaa());
    }

    /**
     * @param $item_move
     * @return mixed
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_topics_areas_with_originalperso($item_move) {
        return $this->get_product()->get_topics_areas_with_originalperso($item_move, $this->get_ccaa());
    }

    /**
     * Devuelve los temas/unidades del porfolio del usuario en el curso.
     *
     * @param int $userid
     * @return array
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_user_porfolio_topics(int $userid): array {
        return $this->get_product()->get_user_porfolio_topics($userid, $this->get_ccaa());
    }

    /**
     * Devuelve un tema/unidad del curso.
     *
     * @param int $topicid
     * @return product_topic
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_topic(int $topicid): product_topic {
        return $this->get_product()->get_topic($topicid);
    }

    /**
     * Valida si es un curso de estudiantes (Niños perdidos).
     *
     * @return bool
     */
    public function is_student_course() {
        return $this->get_course_core()->course_format_data()->get_student_course($this->get_id());
    }

    /**
     * Valida si es un curso de tipo educamos.
     *
     * @return bool
     */
    public function is_educamos_course(): bool {
        return $this->get_course_core()->course_format_data()->get_educamos_course($this->get_id());
    }

    /**
     * Devuelve si es un curso de Marsupial.
     *
     * @return bool
     */
    public function is_marsupial_course(): bool {
        return $this->get_course_core()->course_format_data()->get_marsupial_course($this->get_id());
    }

    /**
     * @return string|null
     */
    public function get_marsupial_groupname(): ?string {
        return $this->get_course_core()->course_format_data()->get_marsupial_groupname($this->get_id());
    }

    /**
     * @return string|null
     */
    public function get_external_groupname(): ?string {
        return $this->get_course_core()->course_format_data()->get_external_groupname($this->get_id());
    }

    /**
     * Devuelve si es un curso Externo (LTI).
     *
     * @return bool
     */
    public function is_external_course(): bool {
        return $this->get_course_core()->course_format_data()->get_external_course($this->get_id());
    }

    /**
     * @return int
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public function get_total_students(): int {
        return count($this->get_students());
    }

    /**
     * @return bool
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public function has_students(): bool {
        return $this->hasstudents ?? $this->set_has_students();
    }
    
    private function set_has_students(): bool {
        global $DB;
        $sql = 'SELECT * FROM mdl_user u
        JOIN mdl_user_enrolments ue ON u.id = ue.userid
        JOIN mdl_enrol e ON e.id = ue.enrolid
        WHERE e.courseid = :courseid
        AND u.icq = 4
        AND u.deleted = 0';
        $this->hasstudents = $DB->record_exists_sql($sql,['courseid' => $this->get_id()]);
        return $this->hasstudents;
    }

    /**
     * @param $userid
     * @return bool
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public function has_student($userid) {
        return array_key_exists($userid, $this->get_students());
    }

    /**
     * @return bool
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public function has_teachers(): bool {
        return (bool) $this->get_teachers();
    }

    /**
     * @return bool
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public function has_educamos_teachers(): bool {
        return (bool) $this->get_educamos_teachers();
    }

    /**
     * @param $userid
     * @return bool
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public function has_teacher($userid) {
        return array_key_exists($userid, $this->get_teachers());
    }

    /**
     * @return iuser[]
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public function get_students() {
        return $this->students ?? $this->set_students();
    }

    /**
     * @return iuser[]
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    private function set_students() {
        $users = $this->get_registered_users();
        $this->students = array_filter($users, static function(iuser $user) {
            return $user->is_student();
        });

        $order = app::get_session_helper()->get_session('orderButton', true);
        if (!empty($order)){
            $this->students = $this->get_transform_helper()->change_user_list_order($this->students, $order);
        }
        return $this->students;
    }

    private function get_transform_helper() {
        return $this->transformhelper ?? $this->set_transform_helper();
    }

    private function set_transform_helper(transform $transform = null) {
        return $this->transformhelper = $transform ?? new transform();
    }

    /**
     * @return int
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public function get_total_teachers(): int {
        if ($this->is_educamos_course()) {
            return count($this->get_educamos_teachers());
        }
        return count($this->get_teachers());
    }

    /**
     * @return int
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public function get_total_guest_teachers(): int {
        return count($this->get_guest_teachers());
    }

    /**
     * Devuelve los profesores de un curso.
     *
     * @return iuser[]
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public function get_teachers() {
        if ($this->is_student_course()) {
            return [];
        }
        return $this->teachers ?? $this->set_teachers();
    }

    /**
     * Devuelve los profesores de un curso Educamos.
     *
     * @return iuser[]
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public function get_educamos_teachers() {
        if ($this->is_student_course()) {
            return [];
        }
        return $this->educamosteachers ?? $this->set_educamos_teachers();
    }

    /**
     * Establece los profesores de un curso.
     *
     * @return iuser[]
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    private function set_teachers() {
        $creator = $this->get_creator();
        $creatorarray = [];
        // Añadir profesor creador al inicio del array de profesores.
        if ($creator !== null) {
            $creatorarray = [$creator->get_id() => $creator];
        }
        $guestteachers = $this->get_guest_teachers() === null ? [] : $this->get_guest_teachers();
        $this->teachers = $creatorarray + $guestteachers;
        return $this->teachers;
    }

    /**
     * Devuelve los profesores de un curso.
     *
     * @return iuser[]
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public function get_guest_teachers() {
        if ($this->is_student_course()) {
            return [];
        }
        return $this->guestteachers ?? $this->set_guest_teachers();
    }

    /**
     * Establece los profesores invitados de un curso.
     *
     * @return iuser[]
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    private function set_guest_teachers() {
        $users = $this->get_registered_users();
        if ($this->is_educamos_course()) {
            foreach ($users as $user) {
                if ($user->is_teacher()) {
                    $this->guestteachers[] = $user;
                }
            }
        } else {
            if ($this->is_student_course()) {
                return [];
            }

            $creator = $this->get_creator();
            $creatorid = $creator !== null ? $creator->get_id() : 0;
            $this->guestteachers = array_filter($users, static function(iuser $user) use ($creatorid) {
                return ($user->is_teacher() && $user->get_id() !== $creatorid);
            });
        }
        return $this->guestteachers;
    }

    /**
     * Establece los profesores invitados de un curso.
     *
     * @return iuser[]
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    private function set_educamos_teachers() {
        $users = $this->get_registered_users();
        $this->educamosteachers = array_filter($users, static function(iuser $user) {
            return ($user->get_user_data()->is_educamos_user() && $user->is_teacher());
        });
        return $this->educamosteachers;
    }

    /**
     * Devuelve si el usuario actual es el creador del curso o no.
     *
     * @return bool
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public function is_current_user_creator() {
        return ($this->get_user_factory()->get_current_user()->get_id() === $this->get_creator()->get_id());
    }

    /**
     * Devuelve si el usuario es el creador de curso.
     *
     * @param iuser $user
     * @return bool
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public function is_creator(iuser $user): bool {
        if (null === $this->get_creator()) {
            return false;
        }
        return ($user->get_id() === $this->get_creator()->get_id());
    }

    /**
     * Devuelve el usuario creador del curso.
     *
     * @return iuser|null
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public function get_creator() {
        return $this->creator ?? $this->set_creator();
    }

    /**
     * @return iuser|null
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    private function set_creator() {
        $ownerid = $this->get_course_core()->course_format_data()->get_creator_id($this->get_id());
        $creator = null;
        if ($ownerid > 0) {
            $owner = $this->get_user_factory()->get_enrolled_user($ownerid, $this->get_id());
            $creator = $owner->is_teacher() ? $owner : null;
        }
        return $this->creator = $creator;
    }

    /**
     * Devuelve el identificador de Comunidad Autónoma asociado al curso.
     *
     * @return int|null
     */
    public function get_ccaa() {
        return $this->get_course_core()->course_format_data()->get_ccaa($this->get_id());
    }

    /**
     * Devuelve el código de producto asociado al curso.
     *
     * @return string
     */
    public function get_codproduct(): string {
        return $this->get_course_core()->course_format_data()->get_codproduct($this->get_id());
    }

    /**
     * Devuelve el producto asociado al curso.
     *
     * @return product
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_product(): product {
        return product::get_instance($this->get_codproduct());
    }

    /**
     * Devuelve el manejador de equipos del profe.
     *
     * @return teacher_team_manager
     */
    public function get_teacher_team_manager() {
        return teacher_team_manager::get_instance($this->get_id());
    }

    /**
     * Devuelve el manejador del equipo general.
     *
     * @return general_team_manager
     */
    public function get_general_team_manager() {
        return general_team_manager::get_instance($this->get_id());
    }

    /**
     * Devuelve el manejador de la configuración de calificaciones.
     *
     * @return calification
     */
    public function get_calification() {
        return calification::get_instance($this->get_codproduct());
    }

    /**
     * @return iuser[]
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    private function get_registered_users() {
        return $this->enroledusers ?? $this->set_registered_users();
    }

    /**
     * @return iuser[]
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    private function set_registered_users() {
        return $this->enroledusers = $this->load_registered_users();
    }

    /**
     * Devuelve los usuarios registrados en un curso.
     *
     * @return iuser[]
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    private function load_registered_users() {
        /** @var iuser[] $users */
        $users = [];
        $ids = $this->get_course_core()->course_enrollment()->get_enrolled_users_id($this->get_id());
        foreach ($ids as $userid) {
            $users[$userid] = $this->get_user_factory()->get_enrolled_user($userid, $this->get_id());
        }
        return $users;
    }

    /**
     * Registrar un alumno en el curso y agregarlo an equipo general.
     *
     * @param int $userid
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\npe_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     * @throws \Exception
     */
    public function enrol_student(int $userid): void {
        $this->get_course_core()->course_enrollment()->enrol_student($userid, $this->get_id());
        $manager = general_team_manager::get_instance($this->get_id());
        $generalteam = $manager->get_team();
        if ($generalteam === null) {
            $generalteam = $manager->create_team('Equipo General');
        }
        $generalteam->add_students($userid);
    }

    /**
     * Registrar un alumno en el curso de educamos.
     *
     * @param int $userid
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws user_exception
     */
    public function enrol_educamos_student(int $userid): void {
        $this->get_course_core()->course_enrollment()->enrol_student($userid, $this->get_id());
    }

    /**
     * Registrar un profesor en el curso.
     *
     * @param int $userid
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws user_exception
     */
    public function enrol_teacher(int $userid): void {
        $this->get_course_core()->course_enrollment()->enrol_teacher($userid, $this->get_id());
    }

    /**
     * Desmatricular usuario del curso.
     *
     * @param int $userid
     * @throws \coding_exception
     * @throws course_exception
     * @throws \dml_transaction_exception
     */
    public function unenrol_user(int $userid): void {
        // Se desmatricula al usuario del curso
        $this->get_course_core()->course_enrollment()->unenrol_user($userid, $this->get_id());

        // Borrado físico de todos los datos usuario asociado al curso del que se ha desmatriculado.
        $this->delete_course_records_by_userid($userid);
    }

    /**
     * @return user_factory
     */
    private function get_user_factory() {
        return $this->userfactory ?? $this->set_user_factory();
    }

    /**
     * Setear el UserFactory.
     *
     * Nota:
     * Se setea de esta forma y no en el contructor para evitar error por inyección de dependencias circulares con el CourseFactory.
     *
     * @param user_factory|null $userfactory
     * @return user_factory
     */
    public function set_user_factory(user_factory $userfactory = null): user_factory {
        return $this->userfactory = $userfactory ?? app::get_instance()->get(user_factory::class);
    }

    /**
     * Obtener la clase fachada que permite el intercambio con el core.
     *
     * @return course_core
     */
    private function get_course_core(): course_core {
        return $this->coursecore ?? $this->set_course_core();
    }

    /**
     * Setear el UserFactory.
     *
     * Nota:
     * Se setea de esta forma y no en el contructor para evitar error por inyección de dependencias circulares con el CourseFactory.
     *
     * @param course_core|null $coursecore
     * @return course_core
     */
    public function set_course_core(course_core $coursecore = null): course_core {
        return $this->coursecore = $coursecore ?? app::get_instance()->get(course_core::class);
    }

    /**
     * Crear un nuevo curso de profesor para el producto dado.
     *
     * @param string $name
     * @param string $codproduct
     * @param $ccaa
     * @param bool $ispreview
     * @return int
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws course_exception
     * @throws exception\npe_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public static function create_teacher_course(string $name, string $codproduct, $ccaa, $ispreview = false): int {
        /** @var create_course $createcourse */
        $createcourse = app::get_instance()->get(create_course::class);
        return $createcourse->create_teacher_course($name, $codproduct, $ccaa, $ispreview);
    }

    /**
     * Crear un nuevo curso de profesor para el producto dado.
     *
     * @param string $name
     * @param string $codproduct
     * @param $ccaa
     * @param string $groupname
     * @return int
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public static function create_marsupial_teacher_course(string $name, string $codproduct, $ccaa, string $groupname): int {
        /** @var create_course $createcourse */
        $createcourse = app::get_instance()->get(create_course::class);
        return $createcourse->create_marsupial_teacher_course($name, $codproduct, $ccaa, $groupname);
    }

    /**
     * Crear un nuevo curso de profesor para el producto dado.
     *
     * @param string $name
     * @param string $codproduct
     * @param $ccaa
     * @param string $groupname
     * @return int
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public static function create_external_teacher_course(string $name, string $codproduct, $ccaa, string $groupname): int {
        /** @var create_course $createcourse */
        $createcourse = app::get_instance()->get(create_course::class);
        return $createcourse->create_external_teacher_course($name, $codproduct, $ccaa, $groupname);
    }

    /**
     * @param string $codproduct
     * @param $ccaa
     * @param string $groupname
     * @return int|null
     * @throws \dml_exception
     */
    public static function get_marsupial_teacher_courseid(string $codproduct, $ccaa, string $groupname) {
        /** @var course_core $coursecore */
        $coursecore = app::get_instance()->get(course_core::class);
        $ids = $coursecore->get_marsupial_teacher_course_ids($codproduct, $ccaa, $groupname);
        return empty($ids) === false ? (int) current($ids) : null;
    }

    /**
     * @param string $codproduct
     * @param $ccaa
     * @param string $groupname
     * @return int|null
     * @throws \dml_exception
     */
    public static function get_external_teacher_courseid(string $codproduct, $ccaa, string $groupname) {
        /** @var course_core $coursecore */
        $coursecore = app::get_instance()->get(course_core::class);
        $ids = $coursecore->get_external_teacher_course_ids($codproduct, $ccaa, $groupname);
        return empty($ids) === false ? (int) current($ids) : null;
    }

    /**
     * @param string $codproduct
     * @param $ccaa
     * @return int|null
     * @throws \dml_exception
     */
    public static function get_marsupial_student_courseid(string $codproduct, $ccaa) {
        /** @var course_core $coursecore */
        $coursecore = app::get_instance()->get(course_core::class);
        $ids = $coursecore->get_marsupial_student_course_ids($codproduct, $ccaa);
        return empty($ids) === false ? (int) current($ids) : null;
    }

    /**
     * @param string $codproduct
     * @param $ccaa
     * @return int|null
     * @throws \dml_exception
     */
    public static function get_external_student_courseid(string $codproduct, $ccaa) {
        /** @var course_core $coursecore */
        $coursecore = app::get_instance()->get(course_core::class);
        $ids = $coursecore->get_external_student_course_ids($codproduct, $ccaa);
        return empty($ids) === false ? (int) current($ids) : null;
    }

    /**
     * Crear un nuevo curso de profesor para el producto dado.
     *
     * @param string $codproduct
     * @param string $idnumber
     * @param int $categoryid
     * @param $idperiodteachers
     * @param $idperiodstudents
     * @param $licensetypeperiodteachers
     * @param $licensetypeperiodstudents
     * @param $groupname
     * @param $ccaa
     * @return int
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public static function create_educamos_teacher_course(string $codproduct, string $idnumber, int $categoryid,
        $idperiodteachers, $idperiodstudents, $licensetypeperiodteachers, $licensetypeperiodstudents, $groupname, $ccaa): int {
        /** @var create_course $createcourse */
        $createcourse = app::get_instance()->get(create_course::class);
        return $createcourse->create_educamos_teacher_course($codproduct, $idnumber, $categoryid,
            $idperiodteachers, $idperiodstudents, $licensetypeperiodteachers, $licensetypeperiodstudents, $groupname, $ccaa);
    }

    /**
     * Crear curso de estudiantes para el producto dado.
     *
     * @param string $name
     * @param string $codproduct
     * @param $ccaa
     * @return int
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public static function create_student_course(string $name, string $codproduct, $ccaa): int {
        /** @var create_course $createcourse */
        $createcourse = app::get_instance()->get(create_course::class);
        return $createcourse->create_student_course($name, $codproduct, $ccaa);
    }

    /**
     * Marsupial - Crear curso de estudiantes para el producto dado.
     *
     * @param string $name
     * @param string $codproduct
     * @param $ccaa
     * @return int
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public static function create_marsupial_student_course(string $name, string $codproduct, $ccaa): int {
        /** @var create_course $createcourse */
        $createcourse = app::get_instance()->get(create_course::class);
        return $createcourse->create_marsupial_student_course($name, $codproduct, $ccaa);
    }

    /**
     * External - Crear curso de estudiantes para el producto dado.
     *
     * @param string $name
     * @param string $codproduct
     * @param $ccaa
     * @return int
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public static function create_external_student_course(string $name, string $codproduct, $ccaa): int {
        /** @var create_course $createcourse */
        $createcourse = app::get_instance()->get(create_course::class);
        return $createcourse->create_external_student_course($name, $codproduct, $ccaa);
    }

    /**
     * @param string $codproduct
     * @param $ccaa
     * @return bool
     * @throws \dml_exception
     */
    public static function has_student_course(string $codproduct, $ccaa) {
        /** @var course_core $coursecore */
        $coursecore = app::get_instance()->get(course_core::class);
        return $coursecore->has_student_course($codproduct, $ccaa);
    }

    /**
     * @param string $codproduct
     * @param $ccaa
     * @return bool
     * @throws \dml_exception
     */
    public static function has_marsupial_student_course(string $codproduct, $ccaa) {
        /** @var course_core $coursecore */
        $coursecore = app::get_instance()->get(course_core::class);
        return $coursecore->has_marsupial_student_course($codproduct, $ccaa);
    }

    /**
     * @param string $codproduct
     * @param $ccaa
     * @return bool
     * @throws \dml_exception
     */
    public static function has_external_student_course(string $codproduct, $ccaa) {
        /** @var course_core $coursecore */
        $coursecore = app::get_instance()->get(course_core::class);
        return $coursecore->has_external_student_course($codproduct, $ccaa);
    }

    /**
     * @param string $codproduct
     * @param $ccaa
     * @param string $groupname
     * @return bool
     * @throws \dml_exception
     */
    public static function has_marsupial_teacher_course(string $codproduct, $ccaa, string $groupname) {
        /** @var course_core $coursecore */
        $coursecore = app::get_instance()->get(course_core::class);
        return $coursecore->has_marsupial_teacher_course($codproduct, $ccaa, $groupname);
    }

    /**
     * @param string $codproduct
     * @param $ccaa
     * @param string $groupname
     * @return bool
     * @throws \dml_exception
     */
    public static function has_external_teacher_course(string $codproduct, $ccaa, string $groupname) {
        /** @var course_core $coursecore */
        $coursecore = app::get_instance()->get(course_core::class);
        return $coursecore->has_marsupial_teacher_course($codproduct, $ccaa, $groupname);
    }

    /**
     * @param string $codproduct
     * @param $ccaa
     * @return mixed|null
     * @throws \dml_exception
     */
    public static function get_student_courseid(string $codproduct, $ccaa) {
        /** @var course_core $coursecore */
        $coursecore = app::get_instance()->get(course_core::class);
        $ids_codproduct = $coursecore->get_student_course_ids_by_codproduct($codproduct, $ccaa);
        $ids_idnumber = $coursecore->get_student_course_ids_by_idnumber($codproduct, $ccaa);
        if (!empty($ids_codproduct)) {
            return current($ids_codproduct);
        }
        else {
            return empty($ids_idnumber) === false ? current($ids_idnumber) : null;
        }
    }

    /**
     * Eliminar el curso.
     *
     * @param $courseid
     * @return bool
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public static function delete_course($courseid): bool {
        $course = self::get_instance($courseid);
        if ($course->has_students()) {
            return false;
        }
        /** @var course_core $coursecore */
        $coursecore = app::get_instance()->get(course_core::class);
        return $coursecore->delete_course($courseid);
    }

    /**
     * Ocultar el curso.
     *
     * @param $courseid
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public static function hide_course($courseid) {
        $course = self::get_instance($courseid)->get_course_data();
        $course->set_visible(0);
        /** @var course_core $coursecore */
        $coursecore = app::get_instance()->get(course_core::class);
        $coursecore->update_course($course);
    }

    /**
     * Crea los "course modules" de las actividades para este curso.
     *
     * @param activity ...$activities
     * @return void
     */
    public function create_moodle_activities(activity ...$activities) {
        foreach ($activities as $activity) {
            $activity->create_moodle_activity($this);
        }
    }

    /**
     * Actualiza los "course modules" de las actividades para este curso.
     *
     * @param activity ...$activities
     * @return void
     */
    public function update_moodle_activities(activity ...$activities) {
        foreach ($activities as $activity) {
            $activity->update_moodle_activity($this);
        }
    }

    /**
     * Setear tarea adhoc para generar actividades de Moodle.
     *
     * @return void
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws stdclass_transform_exception
     */
    public function create_moodle_activities_adhoc() {
        $this->set_adhoc(generate_course_activities_adhoc::class,
            [
                'codproduct' => $this->get_codproduct(),
                'courseids' => [$this->get_id()]
            ]
        );
    }

    /**
     * Eliminar todas las actividades de Moodle para este curso.
     *
     * SOLO PARA PRUEBAS EN LOCAL.
     *
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function remove_moodle_activities() {
        $this->get_course_core()->remove_moodle_activities($this->get_id());
    }

    /**
     * Eliminar todas las asignaciones de este curso.
     *
     * @throws \dml_exception
     */
    public function delete_all_assignments() {
        assign_activity_class::delete_by_courseid($this->get_id());
        assign_activity_team::delete_by_courseid($this->get_id());
        assign_activity_student::delete_by_courseid($this->get_id());
        assign_activity::delete_by_courseid($this->get_id());
    }

    /**
     * Devuelve todas las actividades del curso.
     *
     * @return activity[]
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_activities() {
        return $this->get_product()->get_activities($this->get_ccaa(), $this->get_id());
    }

    /**
     * @param $studentid
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_activities_by_student($studentid) {
        $activities = [];
        foreach ($this->get_activities() as $activity) {
            if ($activity->has_assigned_to_student($this->get_id(), $studentid)) {
                $activities [] = $activity;
            }
        }
        return $activities;
    }

    /**
     * Devuelve una actividad.
     *
     * @param $activityid
     * @return activity
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\activity_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_activity($activityid) {
        return $this->get_product()->get_activity($activityid);
    }

    /**
     * Devuelve una actividad.
     *
     * @param string $idnumber
     * @return activity
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\activity_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_activity_by_idnumber(string $idnumber) {
        return $this->get_product()->get_activity_by_idnumber($idnumber);
    }

    /**
     * Devuelve una actividad.
     *
     * @param int $cmid
     * @return activity
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws course_exception
     * @throws exception\activity_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_activity_by_cmid(int $cmid) {
        return $this->get_product()->get_activity_by_cmid($cmid, $this->get_id());
    }

    /**
     * Devuelve una actividad de tipo foro general.
     *
     * @return generalforum|false
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_activity_general_forum() {
        return $this->get_product()->get_activity_general_forum($this->get_ccaa());
    }

    /**
     * Devuelve una actividad de tipo meetingroom.
     *
     * @return meetingroom|false
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     */
    public function get_activity_meetingroom() {
        return $this->get_product()->get_activity_meetingroom($this->get_ccaa());
    }

    /**
     * @param $customization
     * @return void
     */
    public function set_customizations_by_producttopicpackerid() {
        $product = $this->get_product();
        $activities = $product->get_activities($this->get_ccaa());
        $producttopicpackerids = [];
        $oldtopicpackerid = 0;

        $currentuser = app::get_userfactory()->get_current_user();
        if ($currentuser->get_user_data()->is_educamos_user() || null === $this->get_creator()) {
            $idnumber = $this->get_idnumber();
            $codcentro = $currentuser->get_user_data()->get_department();
        } else {
            $idnumber = $this->get_creator()->get_user_data()->get_idnumber();
            $codcentro = $this->get_creator()->get_user_data()->get_department();
        }
        $product->prepare_perso($idnumber, $codcentro, $this->get_id());
        $this->persovisible = $product->get_perso_visible();
        $this->set_customization_topics();
        foreach ($activities as $activity) {
            $activitydata = $activity->get_activity_data();
            $topicpackerid = $activitydata->get_product_topic_packerid();
            if ($topicpackerid != null && ($this->persovisible == null || !in_array($topicpackerid, $this->persovisible))) {
                if (($oldtopicpackerid === 0 || $topicpackerid !== $oldtopicpackerid)) {
                    if (!in_array($topicpackerid, $producttopicpackerids)) {
                        $producttopicpackerids[$topicpackerid] = new \stdClass;
                    }
                    $producttopicpackerids[$topicpackerid]->ids = [];
                    $producttopicpackerids[$topicpackerid]->ids[] = $activitydata->get_id();
                    $producttopicpackerids[$topicpackerid]->customization = $this->get_customization_by_topic($topicpackerid);
                    $oldtopicpackerid = $topicpackerid;
                } else {
                    $producttopicpackerids[$topicpackerid]->ids[] = $activitydata->get_id();
                }
            }
        }

        $this->customsproducttopicpackerid = $producttopicpackerids;
    }

    /**
     * @return void
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\npe_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public function set_customization_topics() {
        $iseducamosuser = app::get_userfactory()->get_current_user()->get_user_data()->is_educamos_user();
        $idnumber = ($iseducamosuser || null === $this->get_creator())
            ? $this->get_idnumber()
            : $this->get_creator()->get_user_data()->get_idnumber();
        $this->customization = app::get_message_manager()->get_customizations(
            $idnumber,
            null,
            $this->get_codproduct(),
            $this->get_id()
        );
        $this->customization = $this->customization ? json_decode ($this->customization) : null;
    }

    /**
     * @param $id
     * @return array|mixed
     */
    public function get_customization_by_topic($id) {
        if ($this->customization) {
            foreach ($this->customization as $val) {
                if ($id === $val->idUnit) {
                    return json_decode($val->data);
                }
            }
        }
        return [];
    }

    /**
     * @param $producttopicpackerid
     * @return mixed
     */
    public function get_customization_by_producttopicpackerid($producttopicpackerid = null) {
        if (!$this->customsproducttopicpackerid) {
            $this->set_customizations_by_producttopicpackerid();
        }
        return $producttopicpackerid ?
            $this->customsproducttopicpackerid[$producttopicpackerid] : $this->customsproducttopicpackerid;
    }

    /**
     * Devuelve si el curso ha tenido interacciones o no
     *
     * @return bool
     */
    public function has_interactions(): bool {
        return (bool) $this->get_course_core()->course_format_data()->get_has_interactions($this->get_id());
    }

    /**
     * @param $userid
     * @return bool|void
     * @throws \dml_transaction_exception
     */
    private function delete_course_records_by_userid($userid) {
        global $DB;

        /** ADHOC PARA BORRAR DE NOTIFICACIONES EN EVASM */
        $codproduct = $this->get_codproduct();
        $user = app::get_userfactory()->get_user($userid);
        $userguid = $user->get_user_data()->get_idnumber();
        $this->set_adhoc(delete_evasm_notifications_adhoc::class,
            [
                'codproduct' => $codproduct,
                'userguid' => $userguid,
            ]
        );
         
        try {
            $transaction = $DB->start_delegated_transaction();
            /********** BORRADO DE ASIGNACIONES */
            $DB->delete_records_subquery('npe_assign_activity_student', 'id', 'id',
                "SELECT aa.id FROM {npe_assign_activity_student} aa
                    LEFT JOIN {npe_assign_activity} asig ON asig.id = aa.assignactivityid
                    WHERE asig.courseid = :courseid AND aa.userid = :userid", [
                    'courseid' => $this->courseid,
                    'userid' => $userid
                ]);

            /********* BORRADO DE EVIDENCIAS Y OTROS DATOS */
            $DB->delete_records_subquery('npe_activity', 'id', 'id',
                "SELECT a.id FROM {npe_activity} a
                    INNER JOIN {npe_student_evidence} stev ON stev.activityid = a.id AND stev.userid = :userid
                    WHERE a.iscustom = 1 AND stev.courseid = :courseid", ['courseid' => $this->courseid, 'userid' => $userid]);
            $DB->delete_records('npe_student_evidence', ['courseid' => $this->courseid, 'userid' => $userid]);
            $DB->delete_records('npe_student_journalothinking', ['courseid' => $this->courseid, 'userid' => $userid]);
            $DB->delete_records('npe_student_objective', ['courseid' => $this->courseid, 'userid' => $userid]);
            $DB->delete_records('npe_student_plan', ['courseid' => $this->courseid, 'userid' => $userid]);

            /****** BORRADO DE FEEDBACK */
            $DB->delete_records('npe_activity_feedback_stud', ['courseid' => $this->courseid, 'fromuserid' => $userid]);
            $DB->delete_records('npe_activity_feedback_team', ['courseid' => $this->courseid, 'fromuserid' => $userid]);
            $DB->delete_records('npe_activity_feedback_class', ['courseid' => $this->courseid, 'fromuserid' => $userid]);
            $DB->delete_records('npe_portfolio_feedback', ['courseid' => $this->courseid, 'fromuserid' => $userid]);

            /***** BORRADO DE GRADES */
            $DB->delete_records('npe_activity_grade_student', ['courseid' => $this->courseid, 'userid' => $userid]);

            /** BORRADO DE OTROS DATOS */
            $DB->delete_records_subquery('npe_notifications_user', 'id', 'id',
                "SELECT nu.id
                          FROM {npe_notifications_user} nu
                          INNER JOIN {npe_notifications} n ON nu.notificationid = n.id
                          WHERE n.courseid = :courseid AND nu.userid = :userid",
                ['courseid' => $this->courseid, 'userid' => $userid]);

            $sql = "SELECT n.id FROM {npe_notifications} n
                    WHERE n.courseid = $this->courseid AND n.id NOT IN (
                        SELECT DISTINCT nu.notificationid
                        FROM {npe_notifications_user} nu 
                        INNER JOIN {npe_notifications} n ON n.id = nu.notificationid
                        WHERE n.courseid = $this->courseid)";
            $nrecords = $DB->get_records_sql($sql);
            if ($nrecords) {
                $ids = [];
                foreach ($nrecords as $key => $value) {
                    $ids[] = $key;
                }
                $DB->delete_records_list('npe_notifications', 'id', $ids);
            };

            $transaction->allow_commit();

        } catch (\Exception $e) {
            mtrace('Error: ' . $e->getMessage());
            $transaction->rollback($e);
            throwException($e);
        }
    }

    /**
     * @return mixed
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws course_exception
     * @throws exception\product_exception
     * @throws stdclass_transform_exception
     * @throws user_exception
     */
    public function get_first_expiring_date() {
        $students = $this->get_students();
        $firstexpiring = [];
        foreach ($students as $student) {
            $firstexpiring[] = $student->get_license_expiration($this->get_codproduct());
        }
        return min($firstexpiring);
    }
}
<?php // Moodle configuration file

unset($CFG);
global $CFG;
$CFG = new stdClass();

$debugmode = __DEBUGMODE__;
$performancemode = false;
$dbdebug = false;
$CFG->debugwsci = false;

$instances = __INSTANCIAS__;

if (isset($_SERVER['SERVER_PORT']) && '7357' === $_SERVER['SERVER_PORT']) {
    $CFG->currentinstance = array_key_first($instances);
    $CFG->wwwroot = 'http://' . $_SERVER['HTTP_HOST'];
    $CFG->sslproxy = 0;
} elseif (defined('CLI_SCRIPT') && CLI_SCRIPT) {
    require_once(__DIR__ . '/lib/clilib.php'); // CLI only functions.
    [$options, $unrecognized] = cli_get_params(
        ['instance' => ''],
        ['i' => 'instance']
    );
    $CFG->currentinstance = $options['instance'];
    $instanceurl = $instances[$CFG->currentinstance] ?? exit('Instance code required');
    $CFG->wwwroot = 'https://' . $instanceurl;
} else {
    $CFG->currentinstance = array_search($_SERVER['HTTP_HOST'], $instances, true) ?? error_config();
    $CFG->wwwroot = 'https://' . $_SERVER['HTTP_HOST'];
    $CFG->sslproxy = 1;
}

$CFG->dbtype = 'mysqli';
$CFG->dblibrary = 'native';
$CFG->dbhost = '__DBHOST__';
$CFG->dbname = 'moodle_npe_' . $CFG->currentinstance;
$CFG->dbuser = '__DBUSER__';
$CFG->dbpass = '__DBPASS__';
$CFG->prefix = 'mdl_';
$CFG->dboptions = [
    __DBOPTIONS__
];

$CFG->dataroot = '__DATAROOT__' . $CFG->currentinstance;
$CFG->localcachedir = '__LOCALCACHE__' . $CFG->currentinstance;

$CFG->admin = 'admin';
$CFG->directorypermissions = 02700;
$CFG->customscripts = __DIR__ . '/local/npe/customscripts';
$CFG->disableupdateautodeploy = true;
$CFG->disableupdatenotifications = true;
$CFG->disableonclickaddoninstall = true;
$CFG->preventfilelocking = true;

$CFG->enable_read_only_sessions = true;
$CFG->session_handler_class = \core\session\redis::class;
$CFG->session_redis_host = '__REDISENDPOINT__';
$CFG->session_redis_prefix = 'sess_npe_'. $CFG->currentinstance;
$CFG->session_redis_acquire_lock_timeout = 60;
$CFG->session_redis_lock_expire = 60;
$CFG->session_redis_serializer_use_igbinary = true;

$CFG->maxtimelimit = __MAXTIMELIMIT__;

if (true === $debugmode) {
    @error_reporting(E_ALL);
    @ini_set('display_errors', '1');
    $CFG->debug = (E_ALL);
    $CFG->debugdisplay = 1;
    $CFG->cachejs = true;
    $CFG->debugstringids = true;
    $CFG->themedesignermode = false;
    $CFG->showcrondebugging = true;
    $CFG->showcronsql = false;
}

if (true === $performancemode) {
    define('MDL_PERF', true);
    define('MDL_PERFDB', true);
    define('MDL_PERFTOLOG', true);
    define('MDL_PERFTOFOOT', true);
    $CFG->earlyprofilingenabled = true;
}

require_once(__DIR__ . '/vendor/autoload.php');
require_once(__DIR__ . '/lib/setup.php');

global $DB;
if (isset($DB)) {
    $DB->set_debug($dbdebug);
}

if (class_exists(\local_npe\access_control::class)) {
    \local_npe\access_control::check();
}

function error_config() {
    header('HTTP/1.0 404 Not Found');
    echo '<h1>404 Not Found</h1>';
    echo 'The page you have requested could not be found. <div style="display:none">Trz1:<div>';
    die();
}

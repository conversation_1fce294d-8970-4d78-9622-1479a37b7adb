{{!
    @template theme_webbook/header

    Header

    Context variables required for this template:

    * output_user_menu | output.user_menu : HTML strinfied that represents the header
    * params:
          homeurl: site home URL


    Example context (json):
    {
        output_user_menu | output.user_menu : "a example HTML stringfied"
        params {
          'homeurl': 'http://evasm/my'
        }
    }

}}
{{#smheader}}
<header class="header__wrapper header_common" id="nd_header_wrapper">
    <div class="header {{#ismarsupial}}marsupial{{/ismarsupial}}">
        <a href="{{{ homeurl }}}" data-args="#">
            {{! ESCP-7202 App requiere la extensión del archivo }}
            {{#ismobile}}
                <img
                        class="icon" alt="Logo SM"
                        src="{{logoformobile}}">
                        tabindex="0"
            {{/ismobile}}
            {{^ismobile}}
                <img
                        class="icon"
                        src="{{ logourl }}"
                        alt="Logo SM"
                        tabindex="0">

            {{/ismobile}}
        </a>
        {{>local_header_common/usermenu}}
    </div>
</header>
{{> theme_npe/megamenu }}
<div id="npe-helpdock">
</div>
{{/smheader}}
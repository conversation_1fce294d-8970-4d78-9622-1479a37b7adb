<?php

namespace local_npe\core;

use core\session\manager;
use local_npe\app;
use local_npe\base\user\user;
use local_npe\core\persistent\user_license;
use local_npe\core\traits\database;
use local_npe\core\traits\page_context;
use local_npe\DTO\user_dto;
use local_npe\educamos\manage_images;
use local_npe\exception\user_exception;
use local_npe\external\user_ci;
use local_npe\images_manager;
use function is_array;

/**
 * Class user_core
 *
 * @package local_npe\core
 */
class user_core {

    use page_context;
    use database;

    /** @var user_dto */
    private $userdto;
    /** @var user_ci */
    private $userci;
    /**
     * @var array
     */
    private array $profile;

    /**
     * User constructor.
     *
     * @param user_dto $userdto
     */
    public function __construct(user_dto $userdto, user_ci $userci) {
        // UserDTO vacío.
        $this->userdto = $userdto;
        $this->userci = $userci;
    }

    /**
     * Crear usuario con los datos de CI.
     *
     * @param string $idnumber
     * @return int
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     * @throws user_exception
     */
    public function create_user_ci(string $idnumber): int {
        global $CFG;
        require_once($CFG->dirroot . '/user/lib.php');
        $userdto = $this->userci->get_user_dto($idnumber);
        return user_create_user($userdto->get_data(), false, false);
    }

    /**
     * Crear usuario a partir del dto dado.
     *
     * @param user_dto $userdto
     * @return int
     * @throws \moodle_exception
     */
    public function create_user_by_dto(user_dto $userdto): int {
        global $CFG;
        require_once($CFG->dirroot . '/user/lib.php');
        return user_create_user($userdto->get_data(), false, false);
    }

    /**
     * Actualizar usuario a partir del dto dado.
     *
     * @param user_dto $userdto
     * @throws \moodle_exception
     */
    public function update_user_by_dto(user_dto $userdto): void {
        global $CFG;
        require_once($CFG->dirroot . '/user/lib.php');
        user_update_user($userdto->get_data());
    }

    /**
     * @param int $userid
     * @throws \dml_exception
     */
    public function login(int $userid): void {
        manager::restart_with_write_lock(true);
        $userdata = \get_complete_user_data('id', $userid);
        \complete_user_login($userdata);
    }

    /**
     * @param $user
     */
    public function save_user_extra_data($user): void {
        global $CFG;
        require_once($CFG->dirroot . '/user/profile/lib.php');
        profile_save_data($user);
    }

    /**
     * Verifica si existe usuario autenticado.
     *
     * @return bool
     */
    public function has_user_logged(): bool {
        global $USER;
        return (!empty($USER->id));
    }

    /**
     * Devuelve el id del usuario actual.
     *
     * @return int
     * @throws user_exception
     */
    public function get_current_user_id(): int {
        global $USER;
        if ($USER->id === 0) {
            throw new user_exception('No se puede determinar el usuario actual, porque no existe usuario logado.');
        }
        return (int) $USER->id;
    }

    /**
     * Devuelve el userid para el idnumber solicitado (si existe).
     *
     * @param string $idnumber
     * @return int|null
     * @throws \dml_exception
     */
    public function get_userid_by_idnumber(string $idnumber): ?int {
        $userid = $this->get_db()->get_field(db_tables_core::USER_TABLE, 'id', array('idnumber' => $idnumber));
        return $userid !== false ? (int) $userid : null;
    }

    /**
     * Devuelve el UserDTO para el id solicitado.
     *
     * @param int $userid
     * @return user_dto
     * @throws user_exception
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     */
    public function get_user(int $userid): user_dto {
        $userdto = clone $this->userdto;
        $user = \core_user::get_user($userid);
        if (($user instanceof \stdClass) === false) {
            throw new user_exception('No se encuentra usuario con el ID: ' . $userid);
        }

        $userdto->set_data($user);
        $fullname = preg_replace("/[\r\n|\n|\r]+/", " ", fullname($user));
        $userdto->set_fullname($fullname);

        // Se pone como callback para que solo recupere la foto si se solicita ($user->get_user_data()->get_picture()).
        $closure = static function() use ($user) {
            $userobj = app::get_userfactory()->get_user($user->id);
            if (!$userobj->get_user_data()->is_educamos_user()) {
                $images_manager = new images_manager();
                $avatar = $images_manager->get_image_from_cache($user->idnumber);

                if ($avatar) {
                    return $avatar;
                } else {
                    global $PAGE;
                    $picture = new \user_picture($user);
                    $picture->size = 1; // Size f1.
                    return $picture->get_url($PAGE)->out();
                }
            } else {
                return manage_images::get_educamos_user_image_from_cache($user->id);
            }
        };
        $userdto->set_picture_callback($closure);

        $this->set_profile_data($userdto);
        return $userdto;
    }

    /**
     * Busca usuario por el campo especificado.
     * Primero busca el usuario en la tabla user_info_field y luego se busca la información relacionada del usuario
     * si existiese.
     * Si el usuario no existe o si existe pero no tiene idnumber relacionado, se retorna un []
     * si no, se retorna la información del usuario.
     *
     * @param string $field
     * @param string $value
     * @return \stdClass
     * @throws \dml_exception
     * @throws \moodle_exception
     */
    public function get_users_by_custom_field(string $field, string $value): ?\stdClass {
        global $DB;
        $customfield = $DB->get_field('user_info_field', 'id', array('shortname' => $field));
        if (!$customfield) {
            throw new \moodle_exception('wsfieldnotfound', 'educamos_api', '', $field);
        }
        $select = 'fieldid = :customfield AND ' . $DB->sql_compare_text('data') . ' = :value';
        $selectvalues = array('customfield' => $customfield, 'value' => $value);
        $userfield = $DB->get_record_select('user_info_data', $select, $selectvalues, 'userid');
        if (!$userfield) {
            return null;
        }

        return $DB->get_record('user', array('id' => $userfield->userid));
    }

    /**
     * Busca usuario por el campo especificado.
     * Primero busca el usuario en la tabla user_info_field y luego se busca la información relacionada del usuario
     * si existiese.
     * Si el usuario no existe o si existe pero no tiene idnumber relacionado, se retorna un array()
     * si no, se retorna la información del usuario.
     *
     * @param string $field
     * @param array $value
     * @return array
     * @throws \dml_exception
     * @throws \moodle_exception
     */
    public static function get_users_by_custom_fields(string $field, array $value): array {
        global $DB;
        $existingusers = [];
        $customfield = $DB->get_field('user_info_field', 'id', array('shortname' => $field));
        if (!$customfield) {
            throw new \moodle_exception('wsfieldnotfound', 'educamos_api', '', $field);
        }
        $select = "fieldid = :customfield AND data IN ('" . implode("','", $value) . "')";
        $selectvalues = array('customfield' => $customfield);
        $userfield = $DB->get_records_select('user_info_data', $select, $selectvalues, '', 'userid, data');

        if (!$userfield) {
            return array();
        }

        foreach ($userfield as $userdata) {
            $userdetails['id'] = $userdata->userid;
            $userdetails['guid'] = explode('_', $userdata->data)[1];
            $existingusers[] = $userdetails;
        }

        if (empty($existingusers)) {
            return array();
        }

        return $existingusers;
    }

    /**
     * @param \local_npe\DTO\user_dto $userdto
     * @return void
     * @throws \dml_exception
     */
    private function set_profile_data(user_dto $userdto): void {
        $userdto->set_iseducamosuser(
            $this->is_educamos_user($userdto->get_id())
        );
        $userdto->set_ismarsupialuser(
            $this->is_marsupial_user($userdto->get_id())
        );
        $userdto->set_isexternaluser(
            $this->is_external_user($userdto->get_id())
        );
        $userdto->set_issenecauser(
            $this->is_seneca_user($userdto->get_id())
        );
        $userdto->set_educamosguid(
            $this->get_educamosguid($userdto->get_id())
        );
        $userdto->set_tutor_name(
            $this->get_tutor_name($userdto->get_id())
        );
        $userdto->set_tutor_lastname(
            $this->get_tutor_lastname($userdto->get_id())
        );
        $userdto->set_tutor_email(
            $this->get_tutor_email($userdto->get_id())
        );
        $userdto->set_idcalendario(
            $this->get_idcalendario($userdto->get_id())
        );
    }

    /**
     * @param int $userid
     * @return bool
     * @throws \dml_exception
     */
    private function is_educamos_user(int $userid): bool {
        $field = $this->find_user_profile_data($userid, 'educamosguid');
        return !empty($field);
    }

     /**
     * @param int $userid
     * @return bool
     * @throws \dml_exception
     */
    private function is_marsupial_user(int $userid): bool {
        $field = $this->find_user_profile_data($userid, 'sistemaexterno');
        return (isset($field) && $field === "MSPL");
        
    }

     /**
     * @param int $userid
     * @return bool
     * @throws \dml_exception
     */
    private function is_external_user(int $userid): bool {
        $field = $this->find_user_profile_data($userid, 'sistemaexterno');
        return (isset($field) && ($field === "LTI" || $field === "SEN"));
    }

     /**
     * @param int $userid
     * @return bool
     * @throws \dml_exception
     */
    private function is_seneca_user(int $userid): bool {
        $field = $this->find_user_profile_data($userid, 'sistemaexterno');
        return (isset($field) && $field === "SEN");
    }


    /**
     * @param int $userid
     * @return string
     * @throws \dml_exception
     */
    private function get_educamosguid(int $userid): string {
        if (!$this->is_educamos_user($userid)) {
            return '';
        }
        $field = $this->find_user_profile_data($userid, 'educamosguid');
        $userguid = \explode('_', $field);

        return is_array($userguid) ? $userguid[1] : '';
    }

    /**
     * @param int $userid
     * @return mixed|string|null
     * @throws \dml_exception
     */
    private function get_tutor_name(int $userid) {
        return $this->find_user_profile_data($userid, 'nombre');
    }

    /**
     * @param int $userid
     * @return mixed|string|null
     * @throws \dml_exception
     */
    private function get_tutor_lastname(int $userid) {
        return $this->find_user_profile_data($userid, 'apellidos');
    }

    /**
     * @param int $userid
     * @return mixed|string|null
     * @throws \dml_exception
     */
    private function get_tutor_email(int $userid) {
        return $this->find_user_profile_data($userid, 'email');
    }

    /**
     * @param int $userid
     * @return mixed|string|null
     * @throws \dml_exception
     */
    private function get_idcalendario(int $userid) {
        return $this->find_user_profile_data($userid, 'idcalendario');
    }

    /**
     * @param int $userid
     * @param string $inputname
     * @return mixed|null
     * @throws \dml_exception
     */
    private function find_user_profile_data(int $userid, string $inputname) {
        $fields = $this->get_profile($userid);
        return $fields[$inputname] ?? '';
    }

    /**
     * @param int $userid
     * @return array
     * @throws \dml_exception
     */
    private function get_profile(int $userid): array {
        return $this->profile[$userid] ?? $this->set_profile($userid);
    }

    /**
     * @param int $userid
     * @return array
     * @throws \dml_exception
     */
    private function set_profile(int $userid): array {
        global $USER;
        if ($userid === $USER->id) {
            $this->profile[$userid] = $USER->profile;
        } else {
            global $DB;
            $sql = 'SELECT uif.name, uid.data
                    FROM {user_info_field} uif
                    LEFT JOIN {user_info_data} uid
                    ON uif.id = uid.fieldid AND uid.userid = :userid';
            $this->profile[$userid] = $DB->get_records_sql_menu($sql, ['userid' => $userid]);
        }
        return $this->profile[$userid];
    }

    /**
     * Devuelve la fecha de expiración de la licencia que tiene el usuario para el producto dado.
     *
     * @param int $userid
     * @param int $productid
     * @return int
     * @throws \coding_exception
     */
    public function get_license_expiration(int $userid, int $productid) {
        $userlicense = $this->get_user_license($userid, $productid);
        if (!$userlicense) {
            return false;
        }
        return (int) $userlicense->get('licenseexpiration');
    }

    /**
     * Devuelve si el usuario pasó por el onboarding para un producto dado.
     *
     * @param int $userid
     * @param int $productid
     * @return bool
     * @throws \coding_exception
     */
    public function get_product_onboarding(int $userid, int $productid): bool {
        $userlicense = $this->get_user_license($userid, $productid);
        if (!$userlicense) {
            return false;
        }
        return (bool) $userlicense->get('onboarding');
    }

    /**
     * Devuelve si el usuario pasó por el noticenewcourse para un producto dado.
     *
     * @param int $userid
     * @param int $productid
     * @return bool
     * @throws \coding_exception
     */
    public function get_product_noticenewcourse(int $userid, int $productid): bool {
        $userlicense = $this->get_user_license($userid, $productid);
        if (!$userlicense) {
            return false;
        }
        return (bool) $userlicense->get('noticenewcourse');
    }

    /**
     * Devuelve el persistente con los datos de la licencia.
     *
     * @param int $userid
     * @param int $productid
     * @return user_license
     */
    private function get_user_license(int $userid, int $productid): ?user_license {
        $userlicense = user_license::get_record(['userid' => $userid, 'productid' => $productid]);
        if ($userlicense === false) {
            return null;
        }
        return $userlicense;
    }

    /**
     * Crear/Actualizar datos de la licencia del usuario.
     *
     * @param int $userid
     * @param int $productid
     * @param int $licenseexpiration
     * @param int $cu
     * @throws \coding_exception
     */
    public function update_license(int $userid, int $productid, int $licenseexpiration, int $cu = 1): void {
        $record = user_license::get_record(['userid' => $userid, 'productid' => $productid]);
        if ($record === false) {
            $record = new user_license();
            $record->set('userid', $userid);
            $record->set('productid', $productid);
            $record->set('onboarding', 0);
            $record->set('cu', $cu);
            $record->set('noticenewcourse', 0);
        }
        $record->set('licenseexpiration', $licenseexpiration);
        $record->set('cu', $cu);
        $record->save();
    }

    /**
     * Crear/Actualizar datos de la licencia del usuario.
     *
     * @param int $userid
     * @param int $productid
     * @param int $cu
     * @throws \coding_exception
     */
    public function update_cu_license(int $userid, int $productid, int $cu = 1): void {
        $record = user_license::get_record(['userid' => $userid, 'productid' => $productid]);
        $record->set('cu', $cu);
        $record->save();
    }

    /**
     * Actualiza el onboarding del usuario.
     *
     * @param int $userid
     * @param int $productid
     * @throws \coding_exception
     */
    public function update_onboarding(int $userid, int $productid): void {
        $record = user_license::get_record(['userid' => $userid, 'productid' => $productid]);
        $record->set('onboarding', 1);
        $record->save();
    }

    /**
     * Actualiza el noticenewcourse del usuario.
     *
     * @param int $userid
     * @param int $productid
     * @throws \coding_exception
     */
    public function update_noticenewcourse(int $userid, int $productid): void {
        $record = user_license::get_record(['userid' => $userid, 'productid' => $productid]);
        $record->set('noticenewcourse', 1);
        $record->save();
    }

    /**
     * Actualiza el onboarding de grupos del usuario.
     *
     * @param int $userid
     * @param int $productid
     * @throws \coding_exception
     */
    public function update_onboarding_groups(int $userid, int $productid): void {
        $record = user_license::get_record(['userid' => $userid, 'productid' => $productid]);
        $record->set('onboardinggroups', 1);
        $record->save();
    }

    /**
     * Recoge el onboarding de grupos del usuario.
     *
     * @param int $userid
     * @param int $productid
     * @throws \coding_exception
     */
    /**
     * @param int $userid
     * @param int $productid
     * @return void
     */
    public function get_onboarding_groups(int $userid, int $productid) {
        $record = user_license::get_record(['userid' => $userid, 'productid' => $productid]);
        return (bool) $record->get('onboardinggroups');
    }

    /**
     * Obtiene si la licencia tiene las condiciones de uso aceptadas para un producto
     *
     * @param int $userid
     * @param int $productid
     * @return bool
     * @throws \coding_exception
     */
    public function get_cu_license(int $userid, int $productid): bool {
        $userlicense = $this->get_user_license($userid, $productid);
        if (!$userlicense) {
            return false;
        }
        return (bool) $userlicense->get('cu');
    }

    /**
     * Devuelve el persistente con los datos de la licencia.
     *
     * @param int $userid
     * @param int $productid
     * @return bool
     */
    public function user_has_license(int $userid, int $productid): bool {
        $userlicense = user_license::get_record(['userid' => $userid, 'productid' => $productid]);
        return $userlicense !== false;
    }

    /**
     * Eliminar el registro de licencia de un producto para el usuario dado.
     *
     * @param int $userid
     * @param int $productid
     * @throws \coding_exception
     */
    public function delete_license(int $userid, int $productid): void {
        $userlicense = $this->get_user_license($userid, $productid);
        if ($userlicense) {
            $userlicense->delete();
        }
    }
}

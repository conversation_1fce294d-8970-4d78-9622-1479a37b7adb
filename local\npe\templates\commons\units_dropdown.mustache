<div class="accordion md-accordion unitsdropdown" id="unitsdropdown">
    <div class="units-dropdown">
        <!-- header -->

            <div class="list-header" id="heading-units-dropdown">
                <a id="a-combo" data-toggle="collapse" data-parent="#unitsdropdown" href="#collapse-units-dropdown" aria-expanded="true"
                    aria-controls="collapse-units-dropdown">
                    <div class="tooltipcollapse">
                    </div>
                    {{# woheader }}
                        <h5>{{ firsttopictitle }}</h5>
                    {{/ woheader }}
                    {{^ woheader }}
                        <h5>{{# str }}units_dropdown, local_npe, {{sdaNamePlural}}{{/ str }}</h5>
                    {{/ woheader }}
                    <i class="fas fa-angle-down rotate"></i>
                </a>
            </div>


        <div id="collapse-units-dropdown" class="collapse" role="tabpanel" aria-labelledby="heading-units-dropdown"
             data-parent="#unitsdropdown">
            <div class="list-body">
                <ul class="itemslist">
                    {{^ woheader }}
                        <li class="topic-item" data-topicid="all">{{# str }}units_dropdown, local_npe, {{sdaNamePlural}}{{/ str }}</li>
                    {{/ woheader }}
                   {{# unitsdropdown}}
                       <li class="topic-item {{hide}}" data-topicid="{{ topicid }}"
                       {{# portfolio }}
                           data-evidences="{{ hasevidences }}"
                           data-goals="{{ hasgoals }}"
                           data-plans="{{ hasplans }}"
                           data-diaries="{{ hasdiaries }}"
                       {{/ portfolio }}
                       >{{ title }}</li>
                   {{/ unitsdropdown}}
                </ul>
            </div>
        </div>
    </div>
</div>

{{# js }}
    require(['local_npe/units_dropdown'],
        function(UnitsDropdown) {
            new UnitsDropdown({{{isstudent}}});
        }
    );
{{/ js }}
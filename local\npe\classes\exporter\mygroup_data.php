<?php

namespace local_npe\exporter;

use local_npe\base\user\iuser;
use local_npe\base\user\user_sma;
use local_npe\config;
use local_npe\constants;
use local_npe\course;
use local_npe\helper\breadcrumbs;
use local_npe\helper\session;
use local_npe\product;
use local_npe\exporter\mygroup_data\group_data;
use local_npe\exporter_data;
use local_npe\helper\url;
use local_npe\helper\json;
use local_npe\app;
use local_npe\traits\cache_url;
use local_npe\traits\check_course_status;

class mygroup_data extends exporter_data {

    use cache_url;
    use check_course_status;

    public $title;
    public $level;
    public $stage;
    public $ccaa;
    public $username;
    public $iseducamos;
    public $issma;
    public $isteacher;
    public $hasgroups;
    public $hasmultiplegroups;
    public $limitreached;
    public $limitgroup;
    public $welcomeurl;
    /** @var group_data[] */
    public $groups = [];
    public bool $showhelp;
    public $noticenewcourse;
    public $noticenewcourse_intime;

    public $floatingroup;
    public $floatingrouptitle;
    public $floatingrouptab0;
    public $floatingrouptab1;
    public $itemstab0;
    public $itemstab1;
    public $tab0type;
    public $tab1type;
    public $idtab0;
    public $idtab1;
    public $istabletst0;
    public $istabletst1;
    public $breadcrumbs;
    public $isurlshared;
    public $expiration;
    public $helppanellink;
    public $helppanellinklabel;
    public $issmauser;
    public $hashelppanel;
    public $isdefaulttheme;
    public $isexternal;
    public $isnuancestheme;
    public $chatboturl;
    public string $logo;

    // Parámetros no exportables.
    /** @var iuser  */
    private $currentuser;
    /** @var course[]  */
    private $courses;
    /** @var product  */
    private $product;
    /** @var group_data */
    private $mockgroupdata;
    /** @var config $config */
    private $config;
    private url $urlhelper;
    private breadcrumbs $breadcrumbshelper;
    private $sessionhelper;
    /**
     * @var \local_npe\helper\json
     */
    private json $jsonhelper;

    public function __construct(iuser $currentuser, product $product, config $config, course ...$courses) {
        $this->currentuser = $currentuser;
        $this->product = $product;
        $this->config = $config;
        $this->courses = $courses;
    }

    /**
     * Establecer los parámetros a exportar.
     *
     * @throws \dml_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     * @throws \Exception
     */
    public function set_params() :void {
        $this->title = $this->product->get_name();
        $this->ccaa = $this->product->get_ccaa();
        $this->level = $this->product->get_level();
        $this->stage = $this->product->get_stage();
        $this->username = $this->currentuser->get_user_data()->get_firstname();
        $this->iseducamos = $this->currentuser->get_user_data()->is_educamos_user();
        $this->issma = ($this->currentuser instanceof user_sma);
        $this->isteacher = $this->currentuser->is_teacher();
        $this->limitreached = count($this->courses) >= $this->config->get_max_groups_by_product();
        $this->limitgroup = $this->config->get_max_groups_by_product();
        $this->welcomeurl = $this->get_url_helper()->get_welcome_url($this->product->get_codproduct());
        $this->set_groups();
        $this->hasgroups = (bool) $this->groups;
        $this->hasmultiplegroups = count($this->courses) > 1;
        // JSON - floatingGroup.
        $this->set_floatingroup_items($this->currentuser->is_student());
        // Chatbot URL
        $this->chatboturl = app::get_chatbot()->get_chatbot_url();

        // Ayuda - Gestión de grupos.
        $this->showhelp = $this->product->can_show_help();
        // Enlace a videotutoriales
        $this->hashelppanel = false;
        $this->issmauser = $this->currentuser->get_user_data()->is_sma_user();
        if ($this->issmauser) {
            $helppanel = $this->product->get_product_data()->get_helppanel(constants::GROUP_SECTION);
            if ($helppanel) {
                $this->hashelppanel = true;
                $this->helppanellink = $helppanel->link;
                $this->helppanellinklabel = $helppanel->linkLabel;
            }
        }

        //logo Revuela
        $this->logo = $this->get_url_content($this->product->get_product_data()->get_icon());

        // Breadcrumbs.
        $this->breadcrumbs = $this->get_breadcrumb_helper()->get_mygroups_breadcrumb();
        $this->isurlshared = $this->get_session_helper()->get_session('data') !== '' ||
            $this->get_session_helper()->get_session('topicid');

        // Establece si el curso esta dentro periodo de tiempo y tiene interacciones para aviso de nuevo curso escolar
        $codproduct = $this->product->get_product_data()->get_codproduct();
        $this->noticenewcourse_intime = $this::is_notice_new_course_intime($codproduct);
        // Establece si el curso ya mostro la modal de aviso de nuevo curso escolar
        $this->noticenewcourse = $this->currentuser->get_noticenewcourse($codproduct);
        if (!$this->noticenewcourse && $this->noticenewcourse_intime) {
            // Actualiza el aviso de nuevo curso escolar noticenewcourse en BD para que no se vuelva a mostrar al usuario
            $this->currentuser->update_noticenewcourse($codproduct);
        }
        $theme = $this->product->get_product_data()->get_theme();
        $this->isdefaulttheme = $theme === 'defaultTheme';
        $this->isexternal = app::get_userfactory()->get_current_user()->get_user_data()->is_external_user();
        $this->isnuancestheme = $theme === 'nuancesTheme';
    }

    /**
     * @return void
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\user_exception
     */
    private function set_groups() {
        $this->order_groups();
        foreach ($this->courses as $course) {
            $groupdata = $this->get_group_data();
            $groupdata->set_course($course);
            $groupdata->set_currentuser($this->currentuser);
            $groupdata->set_params();
            $groupdata->totalunreadnotifications = app::get_notification_helper()
                ->get_totalunreadnotifications_by_course($course->get_id(), $this->currentuser->get_id());
            $this->groups[] = $groupdata;
        }
    }

    /**
     * Ordenar grupos alfabeticamente.
     */
    private function order_groups() {
        uasort($this->courses, function (course $a, course $b) {
            return strnatcasecmp($a->get_fullname(), $b->get_fullname());
        });
    }

    private function get_group_data() {
        return $this->mockgroupdata ?: new group_data();
    }

    /**
     * Se emplea para Test Unitarios.
     *
     * @param group_data $groupdata
     */
    public function set_mock_group_data(group_data $groupdata) {
        $this->mockgroupdata = $groupdata;
    }

    /**
     * @return url
     */
    private function get_url_helper() {
        return $this->urlhelper ?? $this->set_url_helper();
    }

    /**
     * Setea el url helper.
     * Costura para Test Unitarios.
     *
     * @param url|null $urlhelper
     * @return url
     */
    private function set_url_helper(): url{
        $this->urlhelper = new url();
        return $this->urlhelper;
    }

    /**
     * @return json
     */
    private function get_json_helper() {
        return $this->jsonhelper ?? $this->set_json_helper();
    }

    /**
     * Setea el json helper.
     * Costura para Test Unitarios.
     *
     * @param json $jsonhelper
     * @return json
     */
    private function set_json_helper(): json {
        $this->jsonhelper = new json();
        return $this->jsonhelper;
    }

    /**
     * @throws \Exception
     */
    private function set_floatingroup_items(bool $isstudent){
        $this->floatingroup = $this->get_json_helper()->get_floating_group($this->product->get_codproduct(), $isstudent);
        if ( $this->floatingroup ) {
            $this->floatingrouptitle = $this->floatingroup['title'];
            $this->floatingrouptab0 = $this->floatingroup['tab0'];
            $this->floatingrouptab1 = $this->floatingroup['tab1'];
            $this->itemstab0 = $this->floatingroup['itemstab0'];
            $this->itemstab1 = $this->floatingroup['itemstab1'];
            if ($this->floatingroup['tab0id'] === 'txtClassroom') {
                $this->istabletst0 = true;
                $this->idtab0 = 'nav-aula';
                $this->idtab1 = 'nav-area';
            } else {
                $this->istabletst1 = true;
                $this->idtab0 = 'nav-area';
                $this->idtab1 = 'nav-aula';
            }
            if ($this->floatingroup['tab0view'] === 'tablet') {
                $this->tab0type = 'tablets';
                $this->tab1type = 'lis';
            } else {
                $this->tab0type = 'lis';
                $this->tab1type = 'tablets';
            }
        }
    }

    /**
     * @return breadcrumbs
     */
    private function get_breadcrumb_helper() {
        return $this->breadcrumbhelper ?? $this->set_breadcrumb_helper();
    }

    /**
     * Setea el breadcrumbs helper.
     * Costura para Test Unitarios.
     *
     * @param breadcrumbs|null $breadcrumbs
     * @return breadcrumbs
     */
    public function set_breadcrumb_helper(breadcrumbs $breadcrumbs = null) {
        return $this->breadcrumbshelper = $breadcrumbs ?? new breadcrumbs();
    }

    /**
     * @return session
     */
    private function get_session_helper() {
        return $this->sessionhelper ?? $this->set_session_helper();
    }

    /**
     * Setea el url helper.
     * Costura para Test Unitarios.
     *
     * @param session|null $sessionhelper |null $urlhelper
     * @return session $session
     */
    private function set_session_helper(session $sessionhelper = null) {
        return $this->sessionhelper = $sessionhelper ?? new session();
    }
}

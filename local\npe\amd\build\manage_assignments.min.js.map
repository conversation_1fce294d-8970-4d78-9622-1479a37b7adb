{"version": 3, "file": "manage_assignments.min.js", "sources": ["../src/manage_assignments.js"], "sourcesContent": ["define(['jquery', 'jqueryui', 'local_npe/datepicker', 'local_npe/event_listener', 'local_npe/date_validator', \"core/str\"],\n    function ($, jqueryui, DatePicker, EventListener, DateValidator, Str) {\n\n        let SELECTORS = {\n            ROOT: '.myassignments',\n            ADD_BUTTON: '.add',\n            CANCEL_BUTTON: '.cancel',\n            NAME_ERROR: '.invalid-feedback',\n            ACTIVITYLIST: '.activities-list',\n            INFOBUTTON: '.info_drop_button',\n            SEARCH: '#search',\n            CLEAN: '.clean',\n            ICON_SECTION: '.icon_menu.section',\n            HOUR: '.hour-begining',\n            HOUREND: '.hour-end',\n            ERROR_HOUR: '#errorhourend',\n            BACKTOSEQUENCE: 'ul.npe-linkcontainer li'\n        };\n\n        let EVENTS = {\n            LISTCHANGE: 'list-changed',\n            TEAMWITHCONFLICT: 'team-with-conflict',\n            TEAMSWITHCONFLICTS: 'teams-with-conflicts',\n        };\n\n        /**\n         *\n         * @param {*} resourcesaved\n         * @param {*} activitysaved\n         * @param {*} examsaved\n         * @param {*} assignmentsurl\n         * @param {*} urltoviewer\n         * @param {*} isassignmentfilter\n         * @param {*} txtSequence\n         */\n        function ManageAssignment(resourcesaved = '',\n            activitysaved = '',\n            examsaved = '',\n            assignmentsurl = '',\n            urltoviewer = '',\n            isassignmentfilter = false,\n            txtSequence = '') {\n            ManageAssignment.prototype.constructor = ManageAssignment;\n            ManageAssignment.prototype.root = null;\n            ManageAssignment.prototype.inputdescription = null;\n            ManageAssignment.prototype.form = null;\n            ManageAssignment.prototype.resourcesaved = resourcesaved;\n            ManageAssignment.prototype.activitysaved = activitysaved;\n            ManageAssignment.prototype.examsaved = examsaved;\n            ManageAssignment.prototype.assignmentsurl = assignmentsurl;\n            ManageAssignment.prototype.urltoviewer = urltoviewer;\n            ManageAssignment.prototype.isassignmentfilter = isassignmentfilter;\n            ManageAssignment.prototype.txtSequence = txtSequence;\n\n            // Prepare view first.\n            setTemplate(txtSequence);\n\n            this.init();\n            this.isAssignmentSaved();\n            this.changeView();\n        }\n\n        ManageAssignment.prototype.init = function () {\n            this.root = $(SELECTORS.ROOT);\n\n            EventListener.hearEvent(EVENTS.LISTCHANGE, function (e) {\n                if (e.detail.data.response.haselements) {\n                    $(SELECTORS.ADD_BUTTON).prop('disabled', false);\n                } else {\n                    $(SELECTORS.ADD_BUTTON).prop('disabled', true);\n                }\n            }.bind(this));\n\n            EventListener.hearEvent(EVENTS.TEAMWITHCONFLICT, function () {\n                require(['local_npe/modal_team_conflict', 'core/modal_factory'],\n                    function (ModalTeamConflict, ModalFactory) {\n                        ModalFactory.create({ type: ModalTeamConflict.TYPE }, $(\"#success\")).done(function (modal) {\n                            modal.setData('single');\n                            modal.show();\n                        });\n                    });\n            }.bind(this));\n\n            EventListener.hearEvent(EVENTS.TEAMSWITHCONFLICTS, function () {\n                require(['local_npe/modal_team_conflict', 'core/modal_factory'],\n                    function (ModalTeamConflict, ModalFactory) {\n                        ModalFactory.create({ type: ModalTeamConflict.TYPE }).done(function (modal) {\n                            modal.setData('multiple');\n                            modal.show();\n                        });\n                    });\n            }.bind(this));\n\n            if ($(SELECTORS.HOUR).length) {\n                $(SELECTORS.ROOT).ready(function () {\n                    DateValidator.setFields(\n                        '#userdate[name=\"user_date\"]',\n                        SELECTORS.HOUR,\n                        SELECTORS.HOUREND,\n                        SELECTORS.ADD_BUTTON\n                    );\n                });\n            }\n\n            this.prepareView();\n            this.searchInList();\n        };\n\n        ManageAssignment.prototype.prepareView = function () {\n            $('#collapse-icon').on('click', function () {\n                if ($(this).hasClass('collapse-right')) {\n                    $('.list-group-item').show();\n                    $('#collapse-icon').removeClass('collapse-right').addClass('collapse-left');\n                    $('.assignments-sidebar').removeClass('collapse-width');\n                    $('#separator').removeClass('separator-collapse').addClass('separator');\n                } else {\n                    $('.list-group-item').hide();\n                    $('#collapse-icon').removeClass('collapse-left').addClass('collapse-right');\n                    $('.assignments-sidebar').addClass('collapse-width');\n                    $('#separator').removeClass('separator').addClass('separator-collapse');\n                }\n            });\n        };\n\n        ManageAssignment.prototype.isAssignmentSaved = function () {\n            if (window.performance && window.performance.navigation.type === window.performance.navigation.TYPE_BACK_FORWARD) {\n                this.resourcesaved = '';\n                this.activitysaved = '';\n                this.examsaved = '';\n            }\n            var that = this;\n\n            if (this.resourcesaved !== '' && this.resourcesaved !== '0') {\n                require(['local_npe/modal_success_assign', 'core/modal_factory'],\n                    function (ModalSuccessAssign, ModalFactory) {\n                        ModalFactory.create({ type: ModalSuccessAssign.TYPE }, $(\"#success\")).done(function (modal) {\n                            if (that.isassignmentfilter) {\n                                modal.setData('newresourceassignment', that.urltoviewer, that.isassignmentfilter, that.txtSequence);\n                                modal.show();\n                            } else {\n                                modal.setData('newresourceassignment', that.assignmentsurl);\n                                modal.show();\n                            }\n                        });\n                    });\n            }\n\n            if (this.activitysaved !== '' && this.resourcesaved !== '0') {\n                require(['local_npe/modal_success_assign', 'core/modal_factory'],\n                    function (ModalSuccessAssign, ModalFactory) {\n                        ModalFactory.create({ type: ModalSuccessAssign.TYPE }, $(\"#success\")).done(function (modal) {\n                            if (that.isassignmentfilter) {\n                                modal.setData('newactivityassignment', that.urltoviewer, that.isassignmentfilter, that.txtSequence);\n                                modal.show();\n                            } else {\n                                modal.setData('newactivityassignment', that.assignmentsurl);\n                                modal.show();\n                            }\n                        });\n                    });\n            }\n\n            if (this.examsaved !== '' && this.resourcesaved !== '0') {\n                require(['local_npe/modal_success_assign', 'core/modal_factory'],\n                    function (ModalSuccessAssign, ModalFactory) {\n                        ModalFactory.create({ type: ModalSuccessAssign.TYPE }, $(\"#success\")).done(function (modal) {\n                            if (that.isassignmentfilter) {\n                                modal.setData('newactivityassignment', that.urltoviewer, that.isassignmentfilter, that.txtSequence);\n                                modal.show();\n                            } else {\n                                modal.setData('newactivityassignment', that.assignmentsurl);\n                                modal.show();\n                            }\n                        });\n                    });\n            }\n        };\n\n        ManageAssignment.prototype.searchInList = function () {\n            $('.icon-lupa').on('click', function() {\n                $('.icon-lupa').toggleClass('active-icon');\n                $('.assignments-search').toggleClass('active-input');\n                if ($(window).width() < 799) {\n                    $('.npe-page-header-title').toggle();\n                }\n                $('#search').focus();\n            });\n\n            if (!$(SELECTORS.SEARCH).val()) {\n                $(SELECTORS.ACTIVITYLIST).each(function () {\n                    $(this).find('.count').html('(' + $(this).find('.activity-item').length + ')');\n                });\n            }\n\n            $('#search, .clean').on('keyup click', function () {\n                if (!$(SELECTORS.SEARCH).val()) {\n                    $(SELECTORS.ACTIVITYLIST).each(function () {\n                        $(this).find('.count').html('(' + $(this).find('.activity-item:not(.hidden)').length + ')');\n                    });\n                }\n\n                var searching = $(this).val();\n\n                $(SELECTORS.CLEAN).on('click', function () {\n                    $(SELECTORS.SEARCH).val('');\n                    $('.icon-lupa').removeClass('active-icon');\n                    $('.assignments-search').removeClass('active-input');\n                });\n\n                $(SELECTORS.ACTIVITYLIST).each(function () {\n                    let name = $(this).data(\"name\");\n                    let sec = $(this).data(\"section\");\n                    let list = $(`[data-section='${sec}'][data-name=\"${name}\"]`);\n                    let numberofelements = $(list).find('.activity-item').length;\n\n                    var elementshidden = $(list).find('.activity-item').filter(function () {\n                        return $(this).css('display') == 'none';\n                    }).length;\n\n                    if (elementshidden === numberofelements) {\n                        $(list).hide();\n                    } else {\n                        $(list).show();\n                    }\n                    // Count del numero de subcategorias al buscar por el search\n                    if (!$('.showmore[data-section=\"' + sec + '\"]').is(':visible')) {\n                        $('.activities-list[data-section=\"' + sec + '\"]').filter(':visible').each(function () {\n                            var section = this;\n                            var cat = $(this).data('category');\n                            $('#subcatsdropdown-' + sec + '-' + cat + ' .subcat').filter(':visible').each(function () {\n                                var subcat = $(this).data('subcategory');\n                                var counter = $(section).find('.activity-item')\n                                    .filter('[data-subcategoryid=\"' + subcat + '\"]')\n                                    .filter(':visible').length;\n                                var count = $(this).find('counter');\n                                if (counter === 0) {\n                                    $(this).hide();\n                                } else {\n                                    count.html('(' + counter + ')');\n                                    $(this).show();\n                                }\n                            });\n                        });\n                    }\n                });\n\n                var isVisible = 0;\n                $('.activity-item').each(function () {\n                    if ($(this).is(\":visible\")) {\n                        isVisible++;\n                    }\n                });\n\n                if (isVisible === 0 && searching.length > 0 && !($('.assignments-filter-selected').length > 0)) {\n                    $('.noresultsassignments').hide();\n                    $('.noresultsfilter').hide();\n                    $('.noresultssearch').show();\n                } else if (isVisible === 0 && !(searching.length > 0) && !($('.assignments-filter-selected').length > 0)) {\n                    $('.noresultssearch').hide();\n                    $('.noresultsfilter').hide();\n                    $('.noresultsassignments').show();\n                } else if (isVisible === 0 && !(searching.length > 0) && ($('.assignments-filter-selected').length > 0)) {\n                    $('.noresultssearch').hide();\n                    $('.noresultsfilter').show();\n                    $('.noresultsassignments').hide();\n                } else if (isVisible === 0 && (searching.length > 0) && ($('.assignments-filter-selected').length > 0)) {\n                    $('.noresultssearch').show();\n                    $('.noresultsfilter').hide();\n                    $('.noresultsassignments').hide();\n                } else if (isVisible !== 0) {\n                    $('.noresultsassignments').hide();\n                    $('.noresultsfilter').hide();\n                    $('.noresultssearch').hide();\n                }\n            });\n\n            $(SELECTORS.CLEAN).on('click', function () {\n                $(SELECTORS.ACTIVITYLIST).each(function () {\n                    $(this).find('.count').html('(' + $(this).find('.activity-item:not(.hidden)').length + ')');\n                    var section = this;\n                    let sec = $(this).data(\"section\");\n                    var cat = $(this).data('category');\n                    $('#subcatsdropdown-' + sec + '-' + cat + ' .subcat').each(function () {\n                        var subcat = $(this).data('subcategory');\n                        var counter = $(section).find('.activity-item')\n                            .filter('[data-subcategoryid=\"' + subcat + '\"]').length;\n                        var count = $(this).find('counter');\n                        count.html('(' + counter + ')');\n                        $(this).show();\n                    });\n                });\n            });\n\n            $('#search').click();\n        };\n\n        ManageAssignment.prototype.changeView = function () {\n            $('.sidebar-menu a').on('click', function () {\n                let section = $(this).data('section');\n                setTemplate(this.txtSequence, section);\n            });\n        };\n\n        /**\n         *\n         * @param {*} section\n         */\n        function session_storage_history(section) {\n            let urlParams = new URLSearchParams(window.location.search);\n            urlParams.set('section', section);\n            const urltogo = window.location.origin + window.location.pathname + '?' + urlParams;\n            window.history.pushState({}, \"\", urltogo);\n\n            // Notificación a navigation_history.js de cambio de url.\n            $(window).trigger('OnHistoryUrlStateUpdated', [urltogo]);\n        }\n\n        /**\n         *\n         * @param {*} txtSequence\n         * @param {*} section\n         * @param {*} noChangeText\n         */\n        function setTemplate(txtSequence, section = null, noChangeText = false) {\n            var textoBreadcrumbs = '';\n            let urlParams = new URLSearchParams(window.location.search);\n            if (section === null) {\n                section = urlParams.get('section') ?? 'actAndRec';\n            }\n            $('.activities-container')\n                .hide()\n                .addClass('hidden');\n            $('.activities-container.' + section)\n                .removeClass('hidden')\n                .show();\n            if (!noChangeText) {\n                const sections = $(\"a.section\");\n                let fromviewer = false;\n                let urlfromviewer = false;\n                let last_url = sessionStorage.getItem('urlhistory');\n                let last_url_array = last_url.split(',');\n                if (last_url_array.length >= 2) {\n                    let viewer_url_session = last_url_array[last_url_array.length - 2];\n                    fromviewer = viewer_url_session.includes(\"viewer/index.php\");\n                    urlfromviewer = urlParams.get('fromviewer') ?? false;\n                    if (fromviewer && urlfromviewer == true) {\n                        $('ul.npe-linkcontainer li').css('display', 'none');\n                        $(\"ul.npe-linkcontainer li:last-child\").css('content', '');\n                        let a = { 'txtSequence': txtSequence };\n                        Str.get_string(\n                            'backtosequence', 'local_npe', a\n                        ).done(function (string) {\n                            $(\"ul.npe-linkcontainer li:last-child\").text(string);\n                        });\n                        $(\"ul.npe-linkcontainer li:last-child\").addClass('noslash');\n                        $(\"ul.npe-linkcontainer li:last-child\").css('display', 'inline');\n                        // UPDATE SESSION STORAGE HISTORY\n                        session_storage_history(section);\n                    }\n                }\n                if (sections.length && (!fromviewer || !urlfromviewer)) {\n                    textoBreadcrumbs = sections.filter(\".\" + section).text();\n                    $(\"ul.npe-linkcontainer li:last-child\").text(textoBreadcrumbs);\n                    $(\"ul.npe-linkcontainer li:last-child\").removeClass('noslash');\n                    $('ul.npe-linkcontainer li').css('display', 'inline');\n                    //UPDATE SESSION STORAGE HISTORY\n                    session_storage_history(section);\n                }\n            }\n        }\n        return ManageAssignment;\n    });\n"], "names": ["define", "$", "jqueryui", "DatePicker", "EventListener", "DateValidator", "Str", "SELECTORS", "EVENTS", "ManageAssignment", "resourcesaved", "arguments", "length", "undefined", "activitysaved", "exams<PERSON>d", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isassignmentfilter", "txtSequence", "prototype", "constructor", "root", "inputdescription", "form", "setTemplate", "this", "init", "isAssignmentSaved", "changeView", "session_storage_history", "section", "urlParams", "URLSearchParams", "window", "location", "search", "set", "urltogo", "origin", "pathname", "history", "pushState", "trigger", "noChangeText", "textoBreadcrumbs", "get", "hide", "addClass", "removeClass", "show", "sections", "fromviewer", "urlfromviewer", "last_url_array", "sessionStorage", "getItem", "split", "includes", "css", "a", "get_string", "done", "string", "text", "filter", "hearEvent", "e", "detail", "data", "response", "haselements", "prop", "bind", "require", "ModalTeamConflict", "ModalFactory", "create", "type", "TYPE", "modal", "setData", "ready", "setFields", "<PERSON><PERSON><PERSON><PERSON>", "searchInList", "on", "hasClass", "performance", "navigation", "TYPE_BACK_FORWARD", "that", "ModalSuccessAssign", "toggleClass", "width", "toggle", "focus", "val", "each", "find", "html", "searching", "name", "sec", "list", "numberofelements", "is", "cat", "subcat", "counter", "count", "isVisible", "click"], "mappings": "AAAAA,OAAM,+BAAC,CAAC,SAAU,WAAY,uBAAwB,2BAA4B,2BAA4B,aAC1G,SAAUC,EAAGC,SAAUC,WAAYC,cAAeC,cAAeC,KAE7D,IAAIC,eACM,iBADNA,qBAEY,OAFZA,uBAKc,mBALdA,iBAOQ,UAPRA,gBAQO,SARPA,eAUM,iBAVNA,kBAWS,YAKTC,kBACY,eADZA,wBAEkB,qBAFlBA,0BAGoB,uBAaxB,SAASC,mBAMa,IANIC,cAAaC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACtCG,cAAaH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GAChBI,UAASJ,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACZK,eAAcL,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACjBM,YAAWN,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACdO,mBAAkBP,UAAAC,OAAA,QAAAC,IAAAF,UAAA,IAAAA,UAAA,GAClBQ,YAAWR,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACdF,iBAAiBW,UAAUC,YAAcZ,iBACzCA,iBAAiBW,UAAUE,KAAO,KAClCb,iBAAiBW,UAAUG,iBAAmB,KAC9Cd,iBAAiBW,UAAUI,KAAO,KAClCf,iBAAiBW,UAAUV,cAAgBA,cAC3CD,iBAAiBW,UAAUN,cAAgBA,cAC3CL,iBAAiBW,UAAUL,UAAYA,UACvCN,iBAAiBW,UAAUJ,eAAiBA,eAC5CP,iBAAiBW,UAAUH,YAAcA,YACzCR,iBAAiBW,UAAUF,mBAAqBA,mBAChDT,iBAAiBW,UAAUD,YAAcA,YAGzCM,YAAYN,aAEZO,KAAKC,OACLD,KAAKE,oBACLF,KAAKG,YACT,CAuPA,SAASC,wBAAwBC,SAC7B,IAAIC,UAAY,IAAIC,gBAAgBC,OAAOC,SAASC,QACpDJ,UAAUK,IAAI,UAAWN,SACzB,MAAMO,QAAUJ,OAAOC,SAASI,OAASL,OAAOC,SAASK,SAAW,IAAMR,UAC1EE,OAAOO,QAAQC,UAAU,CAAE,EAAE,GAAIJ,SAGjCrC,EAAEiC,QAAQS,QAAQ,2BAA4B,CAACL,SACnD,CAQA,SAASb,YAAYN,aAAmD,IAAtCY,QAAOpB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,KAAMiC,aAAYjC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,IAAAA,UAAA,GAC1D,IAAIkC,iBAAmB,GACvB,IAAIb,UAAY,IAAIC,gBAAgBC,OAAOC,SAASC,QAUpD,GATgB,OAAZL,UACAA,QAAUC,UAAUc,IAAI,YAAc,aAE1C7C,EAAE,yBACG8C,OACAC,SAAS,UACd/C,EAAE,yBAA2B8B,SACxBkB,YAAY,UACZC,QACAN,aAAc,CACf,MAAMO,SAAWlD,EAAE,aACnB,IAAImD,YAAa,EACbC,eAAgB,EAEhBC,eADWC,eAAeC,QAAQ,cACRC,MAAM,KACpC,GAAIH,eAAe1C,QAAU,EAAG,CAI5B,GAFAwC,WADyBE,eAAeA,eAAe1C,OAAS,GAChC8C,SAAS,oBACzCL,cAAgBrB,UAAUc,IAAI,gBAAiB,EAC3CM,YAA+B,GAAjBC,cAAuB,CACrCpD,EAAE,2BAA2B0D,IAAI,UAAW,QAC5C1D,EAAE,sCAAsC0D,IAAI,UAAW,IACvD,IAAIC,EAAI,CAAEzC,YAAeA,aACzBb,IAAIuD,WACA,iBAAkB,YAAaD,GACjCE,MAAK,SAAUC,QACb9D,EAAE,sCAAsC+D,KAAKD,OACjD,IACA9D,EAAE,sCAAsC+C,SAAS,WACjD/C,EAAE,sCAAsC0D,IAAI,UAAW,UAEvD7B,wBAAwBC,QAC5B,CACJ,EACIoB,SAASvC,QAAYwC,YAAeC,gBACpCR,iBAAmBM,SAASc,OAAO,IAAMlC,SAASiC,OAClD/D,EAAE,sCAAsC+D,KAAKnB,kBAC7C5C,EAAE,sCAAsCgD,YAAY,WACpDhD,EAAE,2BAA2B0D,IAAI,UAAW,UAE5C7B,wBAAwBC,SAEhC,CACJ,CACA,OApTAtB,iBAAiBW,UAAUO,KAAO,WAC9BD,KAAKJ,KAAOrB,EAAEM,gBAEdH,cAAc8D,UAAU1D,kBAAmB,SAAU2D,GAC7CA,EAAEC,OAAOC,KAAKC,SAASC,YACvBtE,EAAEM,sBAAsBiE,KAAK,YAAY,GAEzCvE,EAAEM,sBAAsBiE,KAAK,YAAY,EAEjD,EAAEC,KAAK/C,OAEPtB,cAAc8D,UAAU1D,wBAAyB,WAC7CkE,QAAQ,CAAC,gCAAiC,uBACtC,SAAUC,kBAAmBC,cACzBA,aAAaC,OAAO,CAAEC,KAAMH,kBAAkBI,MAAQ9E,EAAE,aAAa6D,MAAK,SAAUkB,OAChFA,MAAMC,QAAQ,UACdD,MAAM9B,MACV,GACJ,GACR,EAAEuB,KAAK/C,OAEPtB,cAAc8D,UAAU1D,0BAA2B,WAC/CkE,QAAQ,CAAC,gCAAiC,uBACtC,SAAUC,kBAAmBC,cACzBA,aAAaC,OAAO,CAAEC,KAAMH,kBAAkBI,OAAQjB,MAAK,SAAUkB,OACjEA,MAAMC,QAAQ,YACdD,MAAM9B,MACV,GACJ,GACR,EAAEuB,KAAK/C,OAEHzB,EAAEM,gBAAgBK,QAClBX,EAAEM,gBAAgB2E,OAAM,WACpB7E,cAAc8E,UACV,8BACA5E,eACAA,kBACAA,qBAER,IAGJmB,KAAK0D,cACL1D,KAAK2D,gBAGT5E,iBAAiBW,UAAUgE,YAAc,WACrCnF,EAAE,kBAAkBqF,GAAG,SAAS,WACxBrF,EAAEyB,MAAM6D,SAAS,mBACjBtF,EAAE,oBAAoBiD,OACtBjD,EAAE,kBAAkBgD,YAAY,kBAAkBD,SAAS,iBAC3D/C,EAAE,wBAAwBgD,YAAY,kBACtChD,EAAE,cAAcgD,YAAY,sBAAsBD,SAAS,eAE3D/C,EAAE,oBAAoB8C,OACtB9C,EAAE,kBAAkBgD,YAAY,iBAAiBD,SAAS,kBAC1D/C,EAAE,wBAAwB+C,SAAS,kBACnC/C,EAAE,cAAcgD,YAAY,aAAaD,SAAS,sBAE1D,KAGJvC,iBAAiBW,UAAUQ,kBAAoB,WACvCM,OAAOsD,aAAetD,OAAOsD,YAAYC,WAAWX,OAAS5C,OAAOsD,YAAYC,WAAWC,oBAC3FhE,KAAKhB,cAAgB,GACrBgB,KAAKZ,cAAgB,GACrBY,KAAKX,UAAY,IAErB,IAAI4E,KAAOjE,KAEgB,KAAvBA,KAAKhB,eAA+C,MAAvBgB,KAAKhB,eAClCgE,QAAQ,CAAC,iCAAkC,uBACvC,SAAUkB,mBAAoBhB,cAC1BA,aAAaC,OAAO,CAAEC,KAAMc,mBAAmBb,MAAQ9E,EAAE,aAAa6D,MAAK,SAAUkB,OAC7EW,KAAKzE,oBACL8D,MAAMC,QAAQ,wBAAyBU,KAAK1E,YAAa0E,KAAKzE,mBAAoByE,KAAKxE,aACvF6D,MAAM9B,SAEN8B,MAAMC,QAAQ,wBAAyBU,KAAK3E,gBAC5CgE,MAAM9B,OAEd,GACJ,IAGmB,KAAvBxB,KAAKZ,eAA+C,MAAvBY,KAAKhB,eAClCgE,QAAQ,CAAC,iCAAkC,uBACvC,SAAUkB,mBAAoBhB,cAC1BA,aAAaC,OAAO,CAAEC,KAAMc,mBAAmBb,MAAQ9E,EAAE,aAAa6D,MAAK,SAAUkB,OAC7EW,KAAKzE,oBACL8D,MAAMC,QAAQ,wBAAyBU,KAAK1E,YAAa0E,KAAKzE,mBAAoByE,KAAKxE,aACvF6D,MAAM9B,SAEN8B,MAAMC,QAAQ,wBAAyBU,KAAK3E,gBAC5CgE,MAAM9B,OAEd,GACJ,IAGe,KAAnBxB,KAAKX,WAA2C,MAAvBW,KAAKhB,eAC9BgE,QAAQ,CAAC,iCAAkC,uBACvC,SAAUkB,mBAAoBhB,cAC1BA,aAAaC,OAAO,CAAEC,KAAMc,mBAAmBb,MAAQ9E,EAAE,aAAa6D,MAAK,SAAUkB,OAC7EW,KAAKzE,oBACL8D,MAAMC,QAAQ,wBAAyBU,KAAK1E,YAAa0E,KAAKzE,mBAAoByE,KAAKxE,aACvF6D,MAAM9B,SAEN8B,MAAMC,QAAQ,wBAAyBU,KAAK3E,gBAC5CgE,MAAM9B,OAEd,GACJ,KAIZzC,iBAAiBW,UAAUiE,aAAe,WACtCpF,EAAE,cAAcqF,GAAG,SAAS,WACxBrF,EAAE,cAAc4F,YAAY,eAC5B5F,EAAE,uBAAuB4F,YAAY,gBACjC5F,EAAEiC,QAAQ4D,QAAU,KACpB7F,EAAE,0BAA0B8F,SAEhC9F,EAAE,WAAW+F,OACjB,IAEK/F,EAAEM,kBAAkB0F,OACrBhG,EAAEM,wBAAwB2F,MAAK,WAC3BjG,EAAEyB,MAAMyE,KAAK,UAAUC,KAAK,IAAMnG,EAAEyB,MAAMyE,KAAK,kBAAkBvF,OAAS,IAC9E,IAGJX,EAAE,mBAAmBqF,GAAG,eAAe,WAC9BrF,EAAEM,kBAAkB0F,OACrBhG,EAAEM,wBAAwB2F,MAAK,WAC3BjG,EAAEyB,MAAMyE,KAAK,UAAUC,KAAK,IAAMnG,EAAEyB,MAAMyE,KAAK,+BAA+BvF,OAAS,IAC3F,IAGJ,IAAIyF,UAAYpG,EAAEyB,MAAMuE,MAExBhG,EAAEM,iBAAiB+E,GAAG,SAAS,WAC3BrF,EAAEM,kBAAkB0F,IAAI,IACxBhG,EAAE,cAAcgD,YAAY,eAC5BhD,EAAE,uBAAuBgD,YAAY,eACzC,IAEAhD,EAAEM,wBAAwB2F,MAAK,WAC3B,IAAII,KAAOrG,EAAEyB,MAAM2C,KAAK,QACpBkC,IAAMtG,EAAEyB,MAAM2C,KAAK,WACnBmC,KAAOvG,EAAE,kBAAkBsG,oBAAoBD,UAC/CG,iBAAmBxG,EAAEuG,MAAML,KAAK,kBAAkBvF,OAEjCX,EAAEuG,MAAML,KAAK,kBAAkBlC,QAAO,WACvD,MAAiC,QAA1BhE,EAAEyB,MAAMiC,IAAI,UACtB,IAAE/C,SAEoB6F,iBACnBxG,EAAEuG,MAAMzD,OAER9C,EAAEuG,MAAMtD,OAGPjD,EAAE,2BAA6BsG,IAAM,MAAMG,GAAG,aAC/CzG,EAAE,kCAAoCsG,IAAM,MAAMtC,OAAO,YAAYiC,MAAK,WACtE,IAAInE,QAAUL,KACViF,IAAM1G,EAAEyB,MAAM2C,KAAK,YACvBpE,EAAE,oBAAsBsG,IAAM,IAAMI,IAAM,YAAY1C,OAAO,YAAYiC,MAAK,WAC1E,IAAIU,OAAS3G,EAAEyB,MAAM2C,KAAK,eACtBwC,QAAU5G,EAAE8B,SAASoE,KAAK,kBACzBlC,OAAO,wBAA0B2C,OAAS,MAC1C3C,OAAO,YAAYrD,OACpBkG,MAAQ7G,EAAEyB,MAAMyE,KAAK,WACT,IAAZU,QACA5G,EAAEyB,MAAMqB,QAER+D,MAAMV,KAAK,IAAMS,QAAU,KAC3B5G,EAAEyB,MAAMwB,OAEhB,GACJ,GAER,IAEA,IAAI6D,UAAY,EAChB9G,EAAE,kBAAkBiG,MAAK,WACjBjG,EAAEyB,MAAMgF,GAAG,aACXK,WAER,IAEkB,IAAdA,WAAmBV,UAAUzF,OAAS,KAAOX,EAAE,gCAAgCW,OAAS,IACxFX,EAAE,yBAAyB8C,OAC3B9C,EAAE,oBAAoB8C,OACtB9C,EAAE,oBAAoBiD,QACD,IAAd6D,WAAqBV,UAAUzF,OAAS,GAAQX,EAAE,gCAAgCW,OAAS,EAI7E,IAAdmG,aAAqBV,UAAUzF,OAAS,IAAOX,EAAE,gCAAgCW,OAAS,GACjGX,EAAE,oBAAoB8C,OACtB9C,EAAE,oBAAoBiD,OACtBjD,EAAE,yBAAyB8C,QACN,IAAdgE,WAAoBV,UAAUzF,OAAS,GAAOX,EAAE,gCAAgCW,OAAS,GAChGX,EAAE,oBAAoBiD,OACtBjD,EAAE,oBAAoB8C,OACtB9C,EAAE,yBAAyB8C,QACN,IAAdgE,YACP9G,EAAE,yBAAyB8C,OAC3B9C,EAAE,oBAAoB8C,OACtB9C,EAAE,oBAAoB8C,SAdtB9C,EAAE,oBAAoB8C,OACtB9C,EAAE,oBAAoB8C,OACtB9C,EAAE,yBAAyBiD,OAcnC,IAEAjD,EAAEM,iBAAiB+E,GAAG,SAAS,WAC3BrF,EAAEM,wBAAwB2F,MAAK,WAC3BjG,EAAEyB,MAAMyE,KAAK,UAAUC,KAAK,IAAMnG,EAAEyB,MAAMyE,KAAK,+BAA+BvF,OAAS,KACvF,IAAImB,QAAUL,KACd,IAAI6E,IAAMtG,EAAEyB,MAAM2C,KAAK,WACvB,IAAIsC,IAAM1G,EAAEyB,MAAM2C,KAAK,YACvBpE,EAAE,oBAAsBsG,IAAM,IAAMI,IAAM,YAAYT,MAAK,WACvD,IAAIU,OAAS3G,EAAEyB,MAAM2C,KAAK,eACtBwC,QAAU5G,EAAE8B,SAASoE,KAAK,kBACzBlC,OAAO,wBAA0B2C,OAAS,MAAMhG,OACzCX,EAAEyB,MAAMyE,KAAK,WACnBC,KAAK,IAAMS,QAAU,KAC3B5G,EAAEyB,MAAMwB,MACZ,GACJ,GACJ,IAEAjD,EAAE,WAAW+G,SAGjBvG,iBAAiBW,UAAUS,WAAa,WACpC5B,EAAE,mBAAmBqF,GAAG,SAAS,WAC7B,IAAIvD,QAAU9B,EAAEyB,MAAM2C,KAAK,WAC3B5C,YAAYC,KAAKP,YAAaY,QAClC,KAsEGtB,gBACX"}
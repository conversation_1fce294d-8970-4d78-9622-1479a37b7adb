{"version": 3, "file": "manage_assignments.min.js", "sources": ["../src/manage_assignments.js"], "sourcesContent": ["define(['jquery', 'jqueryui', 'local_npe/datepicker', 'local_npe/event_listener', 'local_npe/date_validator', \"core/str\"],\n    function ($, jqueryui, DatePicker, EventListener, DateValidator, Str) {\n\n        let SELECTORS = {\n            ROOT: '.myassignments',\n            ADD_BUTTON: '.add',\n            CANCEL_BUTTON: '.cancel',\n            NAME_ERROR: '.invalid-feedback',\n            ACTIVITYLIST: '.activities-list',\n            INFOBUTTON: '.info_drop_button',\n            SEARCH: '#search',\n            CLEAN: '.clean',\n            ICON_SECTION: '.icon_menu.section',\n            HOUR: '.hour-begining',\n            HOUREND: '.hour-end',\n            ERROR_HOUR: '#errorhourend',\n            BACKTOSEQUENCE: 'ul.npe-linkcontainer li'\n        };\n\n        let EVENTS = {\n            LISTCHANGE: 'list-changed',\n            TEAMWITHCONFLICT: 'team-with-conflict',\n            TEAMSWITHCONFLICTS: 'teams-with-conflicts',\n        };\n\n        /**\n         *\n         * @param {*} resourcesaved\n         * @param {*} activitysaved\n         * @param {*} examsaved\n         * @param {*} assignmentsurl\n         * @param {*} urltoviewer\n         * @param {*} isassignmentfilter\n         * @param {*} txtSequence\n         */\n        function ManageAssignment(resourcesaved = '',\n            activitysaved = '',\n            examsaved = '',\n            assignmentsurl = '',\n            urltoviewer = '',\n            isassignmentfilter = false,\n            txtSequence = '') {\n            ManageAssignment.prototype.constructor = ManageAssignment;\n            ManageAssignment.prototype.root = null;\n            ManageAssignment.prototype.inputdescription = null;\n            ManageAssignment.prototype.form = null;\n            ManageAssignment.prototype.resourcesaved = resourcesaved;\n            ManageAssignment.prototype.activitysaved = activitysaved;\n            ManageAssignment.prototype.examsaved = examsaved;\n            ManageAssignment.prototype.assignmentsurl = assignmentsurl;\n            ManageAssignment.prototype.urltoviewer = urltoviewer;\n            ManageAssignment.prototype.isassignmentfilter = isassignmentfilter;\n            ManageAssignment.prototype.txtSequence = txtSequence;\n\n            // Prepare view first.\n            setTemplate(txtSequence);\n\n            this.init();\n            this.isAssignmentSaved();\n            this.changeView();\n        }\n\n        ManageAssignment.prototype.init = function () {\n            this.root = $(SELECTORS.ROOT);\n\n            EventListener.hearEvent(EVENTS.LISTCHANGE, function (e) {\n                if (e.detail.data.response.haselements) {\n                    $(SELECTORS.ADD_BUTTON).prop('disabled', false);\n                } else {\n                    $(SELECTORS.ADD_BUTTON).prop('disabled', true);\n                }\n            }.bind(this));\n\n            EventListener.hearEvent(EVENTS.TEAMWITHCONFLICT, function () {\n                require(['local_npe/modal_team_conflict', 'core/modal_factory'],\n                    function (ModalTeamConflict, ModalFactory) {\n                        ModalFactory.create({ type: ModalTeamConflict.TYPE }, $(\"#success\")).done(function (modal) {\n                            modal.setData('single');\n                            modal.show();\n                        });\n                    });\n            }.bind(this));\n\n            EventListener.hearEvent(EVENTS.TEAMSWITHCONFLICTS, function () {\n                require(['local_npe/modal_team_conflict', 'core/modal_factory'],\n                    function (ModalTeamConflict, ModalFactory) {\n                        ModalFactory.create({ type: ModalTeamConflict.TYPE }).done(function (modal) {\n                            modal.setData('multiple');\n                            modal.show();\n                        });\n                    });\n            }.bind(this));\n\n            if ($(SELECTORS.HOUR).length) {\n                $(SELECTORS.ROOT).ready(function () {\n                    DateValidator.setFields(\n                        '#userdate[name=\"user_date\"]',\n                        SELECTORS.HOUR,\n                        SELECTORS.HOUREND,\n                        SELECTORS.ADD_BUTTON\n                    );\n                });\n            }\n\n            this.prepareView();\n            this.searchInList();\n        };\n\n        ManageAssignment.prototype.prepareView = function () {\n            $('#collapse-icon').on('click', function () {\n                if ($(this).hasClass('collapse-right')) {\n                    $('.list-group-item').show();\n                    $('#collapse-icon').removeClass('collapse-right').addClass('collapse-left');\n                    $('.assignments-sidebar').removeClass('collapse-width');\n                    $('#separator').removeClass('separator-collapse').addClass('separator');\n                } else {\n                    $('.list-group-item').hide();\n                    $('#collapse-icon').removeClass('collapse-left').addClass('collapse-right');\n                    $('.assignments-sidebar').addClass('collapse-width');\n                    $('#separator').removeClass('separator').addClass('separator-collapse');\n                }\n            });\n        };\n\n        ManageAssignment.prototype.isAssignmentSaved = function () {\n            if (window.performance && window.performance.navigation.type === window.performance.navigation.TYPE_BACK_FORWARD) {\n                this.resourcesaved = '';\n                this.activitysaved = '';\n                this.examsaved = '';\n            }\n            var that = this;\n\n            if (this.resourcesaved !== '' && this.resourcesaved !== '0') {\n                require(['local_npe/modal_success_assign', 'core/modal_factory'],\n                    function (ModalSuccessAssign, ModalFactory) {\n                        ModalFactory.create({ type: ModalSuccessAssign.TYPE }, $(\"#success\")).done(function (modal) {\n                            if (that.isassignmentfilter) {\n                                modal.setData('newresourceassignment', that.urltoviewer, that.isassignmentfilter, that.txtSequence);\n                                modal.show();\n                            } else {\n                                modal.setData('newresourceassignment', that.assignmentsurl);\n                                modal.show();\n                            }\n                        });\n                    });\n            }\n\n            if (this.activitysaved !== '' && this.resourcesaved !== '0') {\n                require(['local_npe/modal_success_assign', 'core/modal_factory'],\n                    function (ModalSuccessAssign, ModalFactory) {\n                        ModalFactory.create({ type: ModalSuccessAssign.TYPE }, $(\"#success\")).done(function (modal) {\n                            if (that.isassignmentfilter) {\n                                modal.setData('newactivityassignment', that.urltoviewer, that.isassignmentfilter, that.txtSequence);\n                                modal.show();\n                            } else {\n                                modal.setData('newactivityassignment', that.assignmentsurl);\n                                modal.show();\n                            }\n                        });\n                    });\n            }\n\n            if (this.examsaved !== '' && this.resourcesaved !== '0') {\n                require(['local_npe/modal_success_assign', 'core/modal_factory'],\n                    function (ModalSuccessAssign, ModalFactory) {\n                        ModalFactory.create({ type: ModalSuccessAssign.TYPE }, $(\"#success\")).done(function (modal) {\n                            if (that.isassignmentfilter) {\n                                modal.setData('newactivityassignment', that.urltoviewer, that.isassignmentfilter, that.txtSequence);\n                                modal.show();\n                            } else {\n                                modal.setData('newactivityassignment', that.assignmentsurl);\n                                modal.show();\n                            }\n                        });\n                    });\n            }\n        };\n\n        ManageAssignment.prototype.searchInList = function () {\n            $('.icon-lupa').on('click', function() {\n                $('.icon-lupa').toggleClass('active-icon');\n                $('.assignments-search').toggleClass('active-input');\n                if ($(window).width() < 799) {\n                    $('.npe-page-header-title').toggle();\n                }\n                $('#search').focus();\n            });\n\n            if (!$(SELECTORS.SEARCH).val()) {\n                $(SELECTORS.ACTIVITYLIST).each(function () {\n                    $(this).find('.count').html('(' + $(this).find('.activity-item').length + ')');\n                });\n            }\n\n            $('#search, .clean').on('keyup click', function () {\n                if (!$(SELECTORS.SEARCH).val()) {\n                    $(SELECTORS.ACTIVITYLIST).each(function () {\n                        $(this).find('.count').html('(' + $(this).find('.activity-item:not(.hidden)').length + ')');\n                    });\n                }\n\n                var searching = $(this).val();\n\n                $(SELECTORS.CLEAN).on('click', function () {\n                    $(SELECTORS.SEARCH).val('');\n                    $('.icon-lupa').removeClass('active-icon');\n                    $('.assignments-search').removeClass('active-input');\n                });\n\n                $(SELECTORS.ACTIVITYLIST).each(function () {\n                    var filtercounter = 0;\n                    var activities = $(this);\n                    $(activities).find('.activity-item:not(.hidden)').each(function () {\n                        let item = $(this).find('.activity-name');\n                        if (searching.length > 0 && $(item[0]).data('name').toLowerCase().indexOf(searching.toLowerCase()) > -1) {\n                            filtercounter++;\n                            $(this).show();\n                        } else if (searching.length > 0) {\n                            $(this).hide();\n                        } else if (searching.length === 0) {\n                            $(this).show();\n                            filtercounter++;\n                        }\n                    });\n\n                    // Cambiar el total de actividades en función del filtro realizado.\n                    $(this).find('.count').html('(' + filtercounter + ')');\n                });\n\n                $(SELECTORS.ACTIVITYLIST).each(function () {\n                    let name = $(this).data(\"name\");\n                    let sec = $(this).data(\"section\");\n                    let list = $(`[data-section='${sec}'][data-name=\"${name}\"]`);\n                    let numberofelements = $(list).find('.activity-item').length;\n\n                    var elementshidden = $(list).find('.activity-item').filter(function () {\n                        return $(this).css('display') == 'none';\n                    }).length;\n\n                    if (elementshidden === numberofelements) {\n                        $(list).hide();\n                    } else {\n                        $(list).show();\n                    }\n                    // Count del numero de subcategorias al buscar por el search\n                    if (!$('.showmore[data-section=\"' + sec + '\"]').is(':visible')) {\n                        $('.activities-list[data-section=\"' + sec + '\"]').filter(':visible').each(function () {\n                            var section = this;\n                            var cat = $(this).data('category');\n                            $('#subcatsdropdown-' + sec + '-' + cat + ' .subcat').filter(':visible').each(function () {\n                                var subcat = $(this).data('subcategory');\n                                var counter = $(section).find('.activity-item')\n                                    .filter('[data-subcategoryid=\"' + subcat + '\"]')\n                                    .filter(':visible').length;\n                                var count = $(this).find('counter');\n                                if (counter === 0) {\n                                    $(this).hide();\n                                } else {\n                                    count.html('(' + counter + ')');\n                                    $(this).show();\n                                }\n                            });\n                        });\n                    }\n                });\n\n                var isVisible = 0;\n                $('.activity-item').each(function () {\n                    if ($(this).is(\":visible\")) {\n                        isVisible++;\n                    }\n                });\n\n                if (isVisible === 0 && searching.length > 0 && !($('.assignments-filter-selected').length > 0)) {\n                    $('.noresultsassignments').hide();\n                    $('.noresultsfilter').hide();\n                    $('.noresultssearch').show();\n                } else if (isVisible === 0 && !(searching.length > 0) && !($('.assignments-filter-selected').length > 0)) {\n                    $('.noresultssearch').hide();\n                    $('.noresultsfilter').hide();\n                    $('.noresultsassignments').show();\n                } else if (isVisible === 0 && !(searching.length > 0) && ($('.assignments-filter-selected').length > 0)) {\n                    $('.noresultssearch').hide();\n                    $('.noresultsfilter').show();\n                    $('.noresultsassignments').hide();\n                } else if (isVisible === 0 && (searching.length > 0) && ($('.assignments-filter-selected').length > 0)) {\n                    $('.noresultssearch').show();\n                    $('.noresultsfilter').hide();\n                    $('.noresultsassignments').hide();\n                } else if (isVisible !== 0) {\n                    $('.noresultsassignments').hide();\n                    $('.noresultsfilter').hide();\n                    $('.noresultssearch').hide();\n                }\n            });\n\n            $(SELECTORS.CLEAN).on('click', function () {\n                $(SELECTORS.ACTIVITYLIST).each(function () {\n                    $(this).find('.count').html('(' + $(this).find('.activity-item:not(.hidden)').length + ')');\n                    var section = this;\n                    let sec = $(this).data(\"section\");\n                    var cat = $(this).data('category');\n                    $('#subcatsdropdown-' + sec + '-' + cat + ' .subcat').each(function () {\n                        var subcat = $(this).data('subcategory');\n                        var counter = $(section).find('.activity-item')\n                            .filter('[data-subcategoryid=\"' + subcat + '\"]').length;\n                        var count = $(this).find('counter');\n                        count.html('(' + counter + ')');\n                        $(this).show();\n                    });\n                });\n            });\n\n            $('#search').click();\n        };\n\n        ManageAssignment.prototype.changeView = function () {\n            $('.sidebar-menu a').on('click', function () {\n                let section = $(this).data('section');\n                setTemplate(this.txtSequence, section);\n            });\n        };\n\n        /**\n         *\n         * @param {*} section\n         */\n        function session_storage_history(section) {\n            let urlParams = new URLSearchParams(window.location.search);\n            urlParams.set('section', section);\n            const urltogo = window.location.origin + window.location.pathname + '?' + urlParams;\n            window.history.pushState({}, \"\", urltogo);\n\n            // Notificación a navigation_history.js de cambio de url.\n            $(window).trigger('OnHistoryUrlStateUpdated', [urltogo]);\n        }\n\n        /**\n         *\n         * @param {*} txtSequence\n         * @param {*} section\n         * @param {*} noChangeText\n         */\n        function setTemplate(txtSequence, section = null, noChangeText = false) {\n            var textoBreadcrumbs = '';\n            let urlParams = new URLSearchParams(window.location.search);\n            if (section === null) {\n                section = urlParams.get('section') ?? 'actAndRec';\n            }\n            $('.activities-container')\n                .hide()\n                .addClass('hidden');\n            $('.activities-container.' + section)\n                .removeClass('hidden')\n                .show();\n            if (!noChangeText) {\n                const sections = $(\"a.section\");\n                let fromviewer = false;\n                let urlfromviewer = false;\n                let last_url = sessionStorage.getItem('urlhistory');\n                let last_url_array = last_url.split(',');\n                if (last_url_array.length >= 2) {\n                    let viewer_url_session = last_url_array[last_url_array.length - 2];\n                    fromviewer = viewer_url_session.includes(\"viewer/index.php\");\n                    urlfromviewer = urlParams.get('fromviewer') ?? false;\n                    if (fromviewer && urlfromviewer == true) {\n                        // Verificar si ya se ejecutó para evitar ejecuciones múltiples\n                        if ($(\"ul.npe-linkcontainer li:last-child\").hasClass('noslash')) {\n                            return; // Ya se ejecutó, salir\n                        }\n\n                        $('ul.npe-linkcontainer li').css('display', 'none');\n                        $(\"ul.npe-linkcontainer li:last-child\").css('content', '');\n                        let a = { 'txtSequence': txtSequence };\n                        Str.get_string(\n                            'backtosequence', 'local_npe', a\n                        ).done(function (string) {\n                            $(\"ul.npe-linkcontainer li:last-child\").text(string);\n                        });\n                        $(\"ul.npe-linkcontainer li:last-child\").addClass('noslash');\n                        $(\"ul.npe-linkcontainer li:last-child\").css('display', 'inline');\n                        // UPDATE SESSION STORAGE HISTORY\n                        session_storage_history(section);\n                    }\n                }\n                if (sections.length && (!fromviewer || !urlfromviewer)) {\n                    textoBreadcrumbs = sections.filter(\".\" + section).text();\n                    $(\"ul.npe-linkcontainer li:last-child\").text(textoBreadcrumbs);\n                    $(\"ul.npe-linkcontainer li:last-child\").removeClass('noslash');\n                    $('ul.npe-linkcontainer li').css('display', 'inline');\n                    //UPDATE SESSION STORAGE HISTORY\n                    session_storage_history(section);\n                }\n            }\n        }\n        return ManageAssignment;\n    });\n"], "names": ["define", "$", "jqueryui", "DatePicker", "EventListener", "DateValidator", "Str", "SELECTORS", "EVENTS", "ManageAssignment", "resourcesaved", "activitysaved", "exams<PERSON>d", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isassignmentfilter", "txtSequence", "prototype", "constructor", "root", "inputdescription", "form", "setTemplate", "init", "isAssignmentSaved", "changeView", "session_storage_history", "section", "urlParams", "URLSearchParams", "window", "location", "search", "set", "urltogo", "origin", "pathname", "history", "pushState", "trigger", "noChangeText", "textoBreadcrumbs", "get", "hide", "addClass", "removeClass", "show", "sections", "fromviewer", "urlfromviewer", "last_url_array", "sessionStorage", "getItem", "split", "length", "includes", "hasClass", "css", "a", "get_string", "done", "string", "text", "filter", "hearEvent", "e", "detail", "data", "response", "haselements", "prop", "bind", "this", "require", "ModalTeamConflict", "ModalFactory", "create", "type", "TYPE", "modal", "setData", "ready", "setFields", "<PERSON><PERSON><PERSON><PERSON>", "searchInList", "on", "performance", "navigation", "TYPE_BACK_FORWARD", "that", "ModalSuccessAssign", "toggleClass", "width", "toggle", "focus", "val", "each", "find", "html", "searching", "filtercounter", "activities", "item", "toLowerCase", "indexOf", "name", "sec", "list", "numberofelements", "is", "cat", "subcat", "counter", "count", "isVisible", "click"], "mappings": "AAAAA,sCAAO,CAAC,SAAU,WAAY,uBAAwB,2BAA4B,2BAA4B,aAC1G,SAAUC,EAAGC,SAAUC,WAAYC,cAAeC,cAAeC,SAEzDC,eACM,iBADNA,qBAEY,OAFZA,uBAKc,mBALdA,iBAOQ,UAPRA,gBAQO,SARPA,eAUM,iBAVNA,kBAWS,YAKTC,kBACY,eADZA,wBAEkB,qBAFlBA,0BAGoB,gCAafC,uBAAiBC,qEAAgB,GACtCC,qEAAgB,GAChBC,iEAAY,GACZC,sEAAiB,GACjBC,mEAAc,GACdC,2EACAC,mEAAc,GACdP,iBAAiBQ,UAAUC,YAAcT,iBACzCA,iBAAiBQ,UAAUE,KAAO,KAClCV,iBAAiBQ,UAAUG,iBAAmB,KAC9CX,iBAAiBQ,UAAUI,KAAO,KAClCZ,iBAAiBQ,UAAUP,cAAgBA,cAC3CD,iBAAiBQ,UAAUN,cAAgBA,cAC3CF,iBAAiBQ,UAAUL,UAAYA,UACvCH,iBAAiBQ,UAAUJ,eAAiBA,eAC5CJ,iBAAiBQ,UAAUH,YAAcA,YACzCL,iBAAiBQ,UAAUF,mBAAqBA,mBAChDN,iBAAiBQ,UAAUD,YAAcA,YAGzCM,YAAYN,kBAEPO,YACAC,yBACAC,sBA4QAC,wBAAwBC,aACzBC,UAAY,IAAIC,gBAAgBC,OAAOC,SAASC,QACpDJ,UAAUK,IAAI,UAAWN,eACnBO,QAAUJ,OAAOC,SAASI,OAASL,OAAOC,SAASK,SAAW,IAAMR,UAC1EE,OAAOO,QAAQC,UAAU,GAAI,GAAIJ,SAGjCjC,EAAE6B,QAAQS,QAAQ,2BAA4B,CAACL,mBAS1CZ,YAAYN,iBAAaW,+DAAU,KAAMa,yEAC1CC,iBAAmB,OACnBb,UAAY,IAAIC,gBAAgBC,OAAOC,SAASC,WACpC,OAAZL,UACAA,QAAUC,UAAUc,IAAI,YAAc,aAE1CzC,EAAE,yBACG0C,OACAC,SAAS,UACd3C,EAAE,yBAA2B0B,SACxBkB,YAAY,UACZC,QACAN,aAAc,OACTO,SAAW9C,EAAE,iBACf+C,YAAa,EACbC,eAAgB,EAEhBC,eADWC,eAAeC,QAAQ,cACRC,MAAM,QAChCH,eAAeI,QAAU,EAAG,IAE5BN,WADyBE,eAAeA,eAAeI,OAAS,GAChCC,SAAS,oBACzCN,cAAgBrB,UAAUc,IAAI,gBAAiB,EAC3CM,YAA+B,GAAjBC,cAAuB,IAEjChD,EAAE,sCAAsCuD,SAAS,kBAIrDvD,EAAE,2BAA2BwD,IAAI,UAAW,QAC5CxD,EAAE,sCAAsCwD,IAAI,UAAW,QACnDC,EAAI,aAAiB1C,aACzBV,IAAIqD,WACA,iBAAkB,YAAaD,GACjCE,MAAK,SAAUC,QACb5D,EAAE,sCAAsC6D,KAAKD,WAEjD5D,EAAE,sCAAsC2C,SAAS,WACjD3C,EAAE,sCAAsCwD,IAAI,UAAW,UAEvD/B,wBAAwBC,WAG5BoB,SAASO,QAAYN,YAAeC,gBACpCR,iBAAmBM,SAASgB,OAAO,IAAMpC,SAASmC,OAClD7D,EAAE,sCAAsC6D,KAAKrB,kBAC7CxC,EAAE,sCAAsC4C,YAAY,WACpD5C,EAAE,2BAA2BwD,IAAI,UAAW,UAE5C/B,wBAAwBC,kBAzUpClB,iBAAiBQ,UAAUM,KAAO,gBACzBJ,KAAOlB,EAAEM,gBAEdH,cAAc4D,UAAUxD,kBAAmB,SAAUyD,GAC7CA,EAAEC,OAAOC,KAAKC,SAASC,YACvBpE,EAAEM,sBAAsB+D,KAAK,YAAY,GAEzCrE,EAAEM,sBAAsB+D,KAAK,YAAY,IAE/CC,KAAKC,OAEPpE,cAAc4D,UAAUxD,wBAAyB,WAC7CiE,QAAQ,CAAC,gCAAiC,uBACtC,SAAUC,kBAAmBC,cACzBA,aAAaC,OAAO,CAAEC,KAAMH,kBAAkBI,MAAQ7E,EAAE,aAAa2D,MAAK,SAAUmB,OAChFA,MAAMC,QAAQ,UACdD,MAAMjC,cAGpByB,KAAKC,OAEPpE,cAAc4D,UAAUxD,0BAA2B,WAC/CiE,QAAQ,CAAC,gCAAiC,uBACtC,SAAUC,kBAAmBC,cACzBA,aAAaC,OAAO,CAAEC,KAAMH,kBAAkBI,OAAQlB,MAAK,SAAUmB,OACjEA,MAAMC,QAAQ,YACdD,MAAMjC,cAGpByB,KAAKC,OAEHvE,EAAEM,gBAAgB+C,QAClBrD,EAAEM,gBAAgB0E,OAAM,WACpB5E,cAAc6E,UACV,8BACA3E,eACAA,kBACAA,8BAKP4E,mBACAC,gBAGT3E,iBAAiBQ,UAAUkE,YAAc,WACrClF,EAAE,kBAAkBoF,GAAG,SAAS,WACxBpF,EAAEuE,MAAMhB,SAAS,mBACjBvD,EAAE,oBAAoB6C,OACtB7C,EAAE,kBAAkB4C,YAAY,kBAAkBD,SAAS,iBAC3D3C,EAAE,wBAAwB4C,YAAY,kBACtC5C,EAAE,cAAc4C,YAAY,sBAAsBD,SAAS,eAE3D3C,EAAE,oBAAoB0C,OACtB1C,EAAE,kBAAkB4C,YAAY,iBAAiBD,SAAS,kBAC1D3C,EAAE,wBAAwB2C,SAAS,kBACnC3C,EAAE,cAAc4C,YAAY,aAAaD,SAAS,2BAK9DnC,iBAAiBQ,UAAUO,kBAAoB,WACvCM,OAAOwD,aAAexD,OAAOwD,YAAYC,WAAWV,OAAS/C,OAAOwD,YAAYC,WAAWC,yBACtF9E,cAAgB,QAChBC,cAAgB,QAChBC,UAAY,QAEjB6E,KAAOjB,KAEgB,KAAvBA,KAAK9D,eAA+C,MAAvB8D,KAAK9D,eAClC+D,QAAQ,CAAC,iCAAkC,uBACvC,SAAUiB,mBAAoBf,cAC1BA,aAAaC,OAAO,CAAEC,KAAMa,mBAAmBZ,MAAQ7E,EAAE,aAAa2D,MAAK,SAAUmB,OAC7EU,KAAK1E,oBACLgE,MAAMC,QAAQ,wBAAyBS,KAAK3E,YAAa2E,KAAK1E,mBAAoB0E,KAAKzE,aACvF+D,MAAMjC,SAENiC,MAAMC,QAAQ,wBAAyBS,KAAK5E,gBAC5CkE,MAAMjC,cAMC,KAAvB0B,KAAK7D,eAA+C,MAAvB6D,KAAK9D,eAClC+D,QAAQ,CAAC,iCAAkC,uBACvC,SAAUiB,mBAAoBf,cAC1BA,aAAaC,OAAO,CAAEC,KAAMa,mBAAmBZ,MAAQ7E,EAAE,aAAa2D,MAAK,SAAUmB,OAC7EU,KAAK1E,oBACLgE,MAAMC,QAAQ,wBAAyBS,KAAK3E,YAAa2E,KAAK1E,mBAAoB0E,KAAKzE,aACvF+D,MAAMjC,SAENiC,MAAMC,QAAQ,wBAAyBS,KAAK5E,gBAC5CkE,MAAMjC,cAMH,KAAnB0B,KAAK5D,WAA2C,MAAvB4D,KAAK9D,eAC9B+D,QAAQ,CAAC,iCAAkC,uBACvC,SAAUiB,mBAAoBf,cAC1BA,aAAaC,OAAO,CAAEC,KAAMa,mBAAmBZ,MAAQ7E,EAAE,aAAa2D,MAAK,SAAUmB,OAC7EU,KAAK1E,oBACLgE,MAAMC,QAAQ,wBAAyBS,KAAK3E,YAAa2E,KAAK1E,mBAAoB0E,KAAKzE,aACvF+D,MAAMjC,SAENiC,MAAMC,QAAQ,wBAAyBS,KAAK5E,gBAC5CkE,MAAMjC,eAO9BrC,iBAAiBQ,UAAUmE,aAAe,WACtCnF,EAAE,cAAcoF,GAAG,SAAS,WACxBpF,EAAE,cAAc0F,YAAY,eAC5B1F,EAAE,uBAAuB0F,YAAY,gBACjC1F,EAAE6B,QAAQ8D,QAAU,KACpB3F,EAAE,0BAA0B4F,SAEhC5F,EAAE,WAAW6F,WAGZ7F,EAAEM,kBAAkBwF,OACrB9F,EAAEM,wBAAwByF,MAAK,WAC3B/F,EAAEuE,MAAMyB,KAAK,UAAUC,KAAK,IAAMjG,EAAEuE,MAAMyB,KAAK,kBAAkB3C,OAAS,QAIlFrD,EAAE,mBAAmBoF,GAAG,eAAe,WAC9BpF,EAAEM,kBAAkBwF,OACrB9F,EAAEM,wBAAwByF,MAAK,WAC3B/F,EAAEuE,MAAMyB,KAAK,UAAUC,KAAK,IAAMjG,EAAEuE,MAAMyB,KAAK,+BAA+B3C,OAAS,YAI3F6C,UAAYlG,EAAEuE,MAAMuB,MAExB9F,EAAEM,iBAAiB8E,GAAG,SAAS,WAC3BpF,EAAEM,kBAAkBwF,IAAI,IACxB9F,EAAE,cAAc4C,YAAY,eAC5B5C,EAAE,uBAAuB4C,YAAY,mBAGzC5C,EAAEM,wBAAwByF,MAAK,eACvBI,cAAgB,EAChBC,WAAapG,EAAEuE,MACnBvE,EAAEoG,YAAYJ,KAAK,+BAA+BD,MAAK,eAC/CM,KAAOrG,EAAEuE,MAAMyB,KAAK,kBACpBE,UAAU7C,OAAS,GAAKrD,EAAEqG,KAAK,IAAInC,KAAK,QAAQoC,cAAcC,QAAQL,UAAUI,gBAAkB,GAClGH,gBACAnG,EAAEuE,MAAM1B,QACDqD,UAAU7C,OAAS,EAC1BrD,EAAEuE,MAAM7B,OACoB,IAArBwD,UAAU7C,SACjBrD,EAAEuE,MAAM1B,OACRsD,oBAKRnG,EAAEuE,MAAMyB,KAAK,UAAUC,KAAK,IAAME,cAAgB,QAGtDnG,EAAEM,wBAAwByF,MAAK,eACvBS,KAAOxG,EAAEuE,MAAML,KAAK,QACpBuC,IAAMzG,EAAEuE,MAAML,KAAK,WACnBwC,KAAO1G,EAAG,kBAAiByG,oBAAoBD,UAC/CG,iBAAmB3G,EAAE0G,MAAMV,KAAK,kBAAkB3C,OAEjCrD,EAAE0G,MAAMV,KAAK,kBAAkBlC,QAAO,iBACtB,QAA1B9D,EAAEuE,MAAMf,IAAI,cACpBH,SAEoBsD,iBACnB3G,EAAE0G,MAAMhE,OAER1C,EAAE0G,MAAM7D,OAGP7C,EAAE,2BAA6ByG,IAAM,MAAMG,GAAG,aAC/C5G,EAAE,kCAAoCyG,IAAM,MAAM3C,OAAO,YAAYiC,MAAK,eAClErE,QAAU6C,KACVsC,IAAM7G,EAAEuE,MAAML,KAAK,YACvBlE,EAAE,oBAAsByG,IAAM,IAAMI,IAAM,YAAY/C,OAAO,YAAYiC,MAAK,eACtEe,OAAS9G,EAAEuE,MAAML,KAAK,eACtB6C,QAAU/G,EAAE0B,SAASsE,KAAK,kBACzBlC,OAAO,wBAA0BgD,OAAS,MAC1ChD,OAAO,YAAYT,OACpB2D,MAAQhH,EAAEuE,MAAMyB,KAAK,WACT,IAAZe,QACA/G,EAAEuE,MAAM7B,QAERsE,MAAMf,KAAK,IAAMc,QAAU,KAC3B/G,EAAEuE,MAAM1B,qBAOxBoE,UAAY,EAChBjH,EAAE,kBAAkB+F,MAAK,WACjB/F,EAAEuE,MAAMqC,GAAG,aACXK,eAIU,IAAdA,WAAmBf,UAAU7C,OAAS,KAAOrD,EAAE,gCAAgCqD,OAAS,IACxFrD,EAAE,yBAAyB0C,OAC3B1C,EAAE,oBAAoB0C,OACtB1C,EAAE,oBAAoB6C,QACD,IAAdoE,WAAqBf,UAAU7C,OAAS,GAAQrD,EAAE,gCAAgCqD,OAAS,EAI7E,IAAd4D,aAAqBf,UAAU7C,OAAS,IAAOrD,EAAE,gCAAgCqD,OAAS,GACjGrD,EAAE,oBAAoB0C,OACtB1C,EAAE,oBAAoB6C,OACtB7C,EAAE,yBAAyB0C,QACN,IAAduE,WAAoBf,UAAU7C,OAAS,GAAOrD,EAAE,gCAAgCqD,OAAS,GAChGrD,EAAE,oBAAoB6C,OACtB7C,EAAE,oBAAoB0C,OACtB1C,EAAE,yBAAyB0C,QACN,IAAduE,YACPjH,EAAE,yBAAyB0C,OAC3B1C,EAAE,oBAAoB0C,OACtB1C,EAAE,oBAAoB0C,SAdtB1C,EAAE,oBAAoB0C,OACtB1C,EAAE,oBAAoB0C,OACtB1C,EAAE,yBAAyB6C,WAgBnC7C,EAAEM,iBAAiB8E,GAAG,SAAS,WAC3BpF,EAAEM,wBAAwByF,MAAK,WAC3B/F,EAAEuE,MAAMyB,KAAK,UAAUC,KAAK,IAAMjG,EAAEuE,MAAMyB,KAAK,+BAA+B3C,OAAS,SACnF3B,QAAU6C,SACVkC,IAAMzG,EAAEuE,MAAML,KAAK,eACnB2C,IAAM7G,EAAEuE,MAAML,KAAK,YACvBlE,EAAE,oBAAsByG,IAAM,IAAMI,IAAM,YAAYd,MAAK,eACnDe,OAAS9G,EAAEuE,MAAML,KAAK,eACtB6C,QAAU/G,EAAE0B,SAASsE,KAAK,kBACzBlC,OAAO,wBAA0BgD,OAAS,MAAMzD,OACzCrD,EAAEuE,MAAMyB,KAAK,WACnBC,KAAK,IAAMc,QAAU,KAC3B/G,EAAEuE,MAAM1B,gBAKpB7C,EAAE,WAAWkH,SAGjB1G,iBAAiBQ,UAAUQ,WAAa,WACpCxB,EAAE,mBAAmBoF,GAAG,SAAS,eACzB1D,QAAU1B,EAAEuE,MAAML,KAAK,WAC3B7C,YAAYkD,KAAKxD,YAAaW,aA4E/BlB,gBACV"}
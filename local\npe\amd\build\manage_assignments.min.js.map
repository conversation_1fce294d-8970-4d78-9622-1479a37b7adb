{"version": 3, "file": "manage_assignments.min.js", "sources": ["../src/manage_assignments.js"], "sourcesContent": ["define(['jquery', 'jqueryui', 'local_npe/datepicker', 'local_npe/event_listener', 'local_npe/date_validator', \"core/str\"],\n    function ($, jqueryui, DatePicker, EventListener, DateValidator, Str) {\n\n        let SELECTORS = {\n            ROOT: '.myassignments',\n            ADD_BUTTON: '.add',\n            CANCEL_BUTTON: '.cancel',\n            NAME_ERROR: '.invalid-feedback',\n            ACTIVITYLIST: '.activities-list',\n            INFOBUTTON: '.info_drop_button',\n            SEARCH: '#search',\n            CLEAN: '.clean',\n            ICON_SECTION: '.icon_menu.section',\n            HOUR: '.hour-begining',\n            HOUREND: '.hour-end',\n            ERROR_HOUR: '#errorhourend',\n            BACKTOSEQUENCE: 'ul.npe-linkcontainer li'\n        };\n\n        let EVENTS = {\n            LISTCHANGE: 'list-changed',\n            TEAMWITHCONFLICT: 'team-with-conflict',\n            TEAMSWITHCONFLICTS: 'teams-with-conflicts',\n        };\n\n        /**\n         *\n         * @param {*} resourcesaved\n         * @param {*} activitysaved\n         * @param {*} examsaved\n         * @param {*} assignmentsurl\n         * @param {*} urltoviewer\n         * @param {*} isassignmentfilter\n         * @param {*} txtSequence\n         */\n        function ManageAssignment(resourcesaved = '',\n            activitysaved = '',\n            examsaved = '',\n            assignmentsurl = '',\n            urltoviewer = '',\n            isassignmentfilter = false,\n            txtSequence = '') {\n            ManageAssignment.prototype.constructor = ManageAssignment;\n            ManageAssignment.prototype.root = null;\n            ManageAssignment.prototype.inputdescription = null;\n            ManageAssignment.prototype.form = null;\n            ManageAssignment.prototype.resourcesaved = resourcesaved;\n            ManageAssignment.prototype.activitysaved = activitysaved;\n            ManageAssignment.prototype.examsaved = examsaved;\n            ManageAssignment.prototype.assignmentsurl = assignmentsurl;\n            ManageAssignment.prototype.urltoviewer = urltoviewer;\n            ManageAssignment.prototype.isassignmentfilter = isassignmentfilter;\n            ManageAssignment.prototype.txtSequence = txtSequence;\n\n            // Prepare view first.\n            setTemplate(txtSequence);\n\n            this.init();\n            this.isAssignmentSaved();\n            this.changeView();\n        }\n\n        ManageAssignment.prototype.init = function () {\n            this.root = $(SELECTORS.ROOT);\n\n            EventListener.hearEvent(EVENTS.LISTCHANGE, function (e) {\n                if (e.detail.data.response.haselements) {\n                    $(SELECTORS.ADD_BUTTON).prop('disabled', false);\n                } else {\n                    $(SELECTORS.ADD_BUTTON).prop('disabled', true);\n                }\n            }.bind(this));\n\n            EventListener.hearEvent(EVENTS.TEAMWITHCONFLICT, function () {\n                require(['local_npe/modal_team_conflict', 'core/modal_factory'],\n                    function (ModalTeamConflict, ModalFactory) {\n                        ModalFactory.create({ type: ModalTeamConflict.TYPE }, $(\"#success\")).done(function (modal) {\n                            modal.setData('single');\n                            modal.show();\n                        });\n                    });\n            }.bind(this));\n\n            EventListener.hearEvent(EVENTS.TEAMSWITHCONFLICTS, function () {\n                require(['local_npe/modal_team_conflict', 'core/modal_factory'],\n                    function (ModalTeamConflict, ModalFactory) {\n                        ModalFactory.create({ type: ModalTeamConflict.TYPE }).done(function (modal) {\n                            modal.setData('multiple');\n                            modal.show();\n                        });\n                    });\n            }.bind(this));\n\n            if ($(SELECTORS.HOUR).length) {\n                $(SELECTORS.ROOT).ready(function () {\n                    DateValidator.setFields(\n                        '#userdate[name=\"user_date\"]',\n                        SELECTORS.HOUR,\n                        SELECTORS.HOUREND,\n                        SELECTORS.ADD_BUTTON\n                    );\n                });\n            }\n\n            this.prepareView();\n            this.searchInList();\n        };\n\n        ManageAssignment.prototype.prepareView = function () {\n            $('#collapse-icon').on('click', function () {\n                if ($(this).hasClass('collapse-right')) {\n                    $('.list-group-item').show();\n                    $('#collapse-icon').removeClass('collapse-right').addClass('collapse-left');\n                    $('.assignments-sidebar').removeClass('collapse-width');\n                    $('#separator').removeClass('separator-collapse').addClass('separator');\n                } else {\n                    $('.list-group-item').hide();\n                    $('#collapse-icon').removeClass('collapse-left').addClass('collapse-right');\n                    $('.assignments-sidebar').addClass('collapse-width');\n                    $('#separator').removeClass('separator').addClass('separator-collapse');\n                }\n            });\n        };\n\n        ManageAssignment.prototype.isAssignmentSaved = function () {\n            if (window.performance && window.performance.navigation.type === window.performance.navigation.TYPE_BACK_FORWARD) {\n                this.resourcesaved = '';\n                this.activitysaved = '';\n                this.examsaved = '';\n            }\n            var that = this;\n\n            if (this.resourcesaved !== '' && this.resourcesaved !== '0') {\n                require(['local_npe/modal_success_assign', 'core/modal_factory'],\n                    function (ModalSuccessAssign, ModalFactory) {\n                        ModalFactory.create({ type: ModalSuccessAssign.TYPE }, $(\"#success\")).done(function (modal) {\n                            if (that.isassignmentfilter) {\n                                modal.setData('newresourceassignment', that.urltoviewer, that.isassignmentfilter, that.txtSequence);\n                                modal.show();\n                            } else {\n                                modal.setData('newresourceassignment', that.assignmentsurl);\n                                modal.show();\n                            }\n                        });\n                    });\n            }\n\n            if (this.activitysaved !== '' && this.resourcesaved !== '0') {\n                require(['local_npe/modal_success_assign', 'core/modal_factory'],\n                    function (ModalSuccessAssign, ModalFactory) {\n                        ModalFactory.create({ type: ModalSuccessAssign.TYPE }, $(\"#success\")).done(function (modal) {\n                            if (that.isassignmentfilter) {\n                                modal.setData('newactivityassignment', that.urltoviewer, that.isassignmentfilter, that.txtSequence);\n                                modal.show();\n                            } else {\n                                modal.setData('newactivityassignment', that.assignmentsurl);\n                                modal.show();\n                            }\n                        });\n                    });\n            }\n\n            if (this.examsaved !== '' && this.resourcesaved !== '0') {\n                require(['local_npe/modal_success_assign', 'core/modal_factory'],\n                    function (ModalSuccessAssign, ModalFactory) {\n                        ModalFactory.create({ type: ModalSuccessAssign.TYPE }, $(\"#success\")).done(function (modal) {\n                            if (that.isassignmentfilter) {\n                                modal.setData('newactivityassignment', that.urltoviewer, that.isassignmentfilter, that.txtSequence);\n                                modal.show();\n                            } else {\n                                modal.setData('newactivityassignment', that.assignmentsurl);\n                                modal.show();\n                            }\n                        });\n                    });\n            }\n        };\n\n        ManageAssignment.prototype.searchInList = function () {\n            $('.icon-lupa').on('click', function() {\n                $('.icon-lupa').toggleClass('active-icon');\n                $('.assignments-search').toggleClass('active-input');\n                if ($(window).width() < 799) {\n                    $('.npe-page-header-title').toggle();\n                }\n                $('#search').focus();\n            });\n\n            if (!$(SELECTORS.SEARCH).val()) {\n                $(SELECTORS.ACTIVITYLIST).each(function () {\n                    $(this).find('.count').html('(' + $(this).find('.activity-item').length + ')');\n                });\n            }\n\n            $('#search, .clean').on('keyup click', function () {\n                if (!$(SELECTORS.SEARCH).val()) {\n                    $(SELECTORS.ACTIVITYLIST).each(function () {\n                        $(this).find('.count').html('(' + $(this).find('.activity-item:not(.hidden)').length + ')');\n                    });\n                }\n\n                var searching = $(this).val();\n\n                $(SELECTORS.CLEAN).on('click', function () {\n                    $(SELECTORS.SEARCH).val('');\n                    $('.icon-lupa').removeClass('active-icon');\n                    $('.assignments-search').removeClass('active-input');\n                });\n\n                $(SELECTORS.ACTIVITYLIST).each(function () {\n                    var filtercounter = 0;\n                    var activities = $(this);\n                    $(activities).find('.activity-item:not(.hidden)').each(function () {\n                        let item = $(this).find('.activity-name');\n                        if (searching.length > 0 && $(item[0]).data('name').toLowerCase().indexOf(searching.toLowerCase()) > -1) {\n                            filtercounter++;\n                            $(this).show();\n                        } else if (searching.length > 0) {\n                            $(this).hide();\n                        } else if (searching.length === 0) {\n                            $(this).show();\n                            filtercounter++;\n                        }\n                    });\n\n                    // Cambiar el total de actividades en función del filtro realizado.\n                    $(this).find('.count').html('(' + filtercounter + ')');\n                });\n\n                $(SELECTORS.ACTIVITYLIST).each(function () {\n                    let name = $(this).data(\"name\");\n                    let sec = $(this).data(\"section\");\n                    let list = $(`[data-section='${sec}'][data-name=\"${name}\"]`);\n                    let numberofelements = $(list).find('.activity-item').length;\n\n                    var elementshidden = $(list).find('.activity-item').filter(function () {\n                        return $(this).css('display') == 'none';\n                    }).length;\n\n                    if (elementshidden === numberofelements) {\n                        $(list).hide();\n                    } else {\n                        $(list).show();\n                    }\n                    // Count del numero de subcategorias al buscar por el search\n                    if (!$('.showmore[data-section=\"' + sec + '\"]').is(':visible')) {\n                        $('.activities-list[data-section=\"' + sec + '\"]').filter(':visible').each(function () {\n                            var section = this;\n                            var cat = $(this).data('category');\n                            $('#subcatsdropdown-' + sec + '-' + cat + ' .subcat').filter(':visible').each(function () {\n                                var subcat = $(this).data('subcategory');\n                                var counter = $(section).find('.activity-item')\n                                    .filter('[data-subcategoryid=\"' + subcat + '\"]')\n                                    .filter(':visible').length;\n                                var count = $(this).find('counter');\n                                if (counter === 0) {\n                                    $(this).hide();\n                                } else {\n                                    count.html('(' + counter + ')');\n                                    $(this).show();\n                                }\n                            });\n                        });\n                    }\n                });\n\n                var isVisible = 0;\n                $('.activity-item').each(function () {\n                    if ($(this).is(\":visible\")) {\n                        isVisible++;\n                    }\n                });\n\n                if (isVisible === 0 && searching.length > 0 && !($('.assignments-filter-selected').length > 0)) {\n                    $('.noresultsassignments').hide();\n                    $('.noresultsfilter').hide();\n                    $('.noresultssearch').show();\n                } else if (isVisible === 0 && !(searching.length > 0) && !($('.assignments-filter-selected').length > 0)) {\n                    $('.noresultssearch').hide();\n                    $('.noresultsfilter').hide();\n                    $('.noresultsassignments').show();\n                } else if (isVisible === 0 && !(searching.length > 0) && ($('.assignments-filter-selected').length > 0)) {\n                    $('.noresultssearch').hide();\n                    $('.noresultsfilter').show();\n                    $('.noresultsassignments').hide();\n                } else if (isVisible === 0 && (searching.length > 0) && ($('.assignments-filter-selected').length > 0)) {\n                    $('.noresultssearch').show();\n                    $('.noresultsfilter').hide();\n                    $('.noresultsassignments').hide();\n                } else if (isVisible !== 0) {\n                    $('.noresultsassignments').hide();\n                    $('.noresultsfilter').hide();\n                    $('.noresultssearch').hide();\n                }\n            });\n\n            $(SELECTORS.CLEAN).on('click', function () {\n                $(SELECTORS.ACTIVITYLIST).each(function () {\n                    $(this).find('.count').html('(' + $(this).find('.activity-item:not(.hidden)').length + ')');\n                    var section = this;\n                    let sec = $(this).data(\"section\");\n                    var cat = $(this).data('category');\n                    $('#subcatsdropdown-' + sec + '-' + cat + ' .subcat').each(function () {\n                        var subcat = $(this).data('subcategory');\n                        var counter = $(section).find('.activity-item')\n                            .filter('[data-subcategoryid=\"' + subcat + '\"]').length;\n                        var count = $(this).find('counter');\n                        count.html('(' + counter + ')');\n                        $(this).show();\n                    });\n                });\n            });\n\n            $('#search').click();\n        };\n\n        ManageAssignment.prototype.changeView = function () {\n            $('.sidebar-menu a').on('click', function () {\n                let section = $(this).data('section');\n                setTemplate(this.txtSequence, section);\n            });\n        };\n\n        /**\n         *\n         * @param {*} section\n         */\n        function session_storage_history(section) {\n            let urlParams = new URLSearchParams(window.location.search);\n            urlParams.set('section', section);\n            const urltogo = window.location.origin + window.location.pathname + '?' + urlParams;\n            window.history.pushState({}, \"\", urltogo);\n\n            // Notificación a navigation_history.js de cambio de url.\n            $(window).trigger('OnHistoryUrlStateUpdated', [urltogo]);\n        }\n\n        /**\n         *\n         * @param {*} txtSequence\n         * @param {*} section\n         * @param {*} noChangeText\n         */\n        function setTemplate(txtSequence, section = null, noChangeText = false) {\n            var textoBreadcrumbs = '';\n            let urlParams = new URLSearchParams(window.location.search);\n            if (section === null) {\n                section = urlParams.get('section') ?? 'actAndRec';\n            }\n            $('.activities-container')\n                .hide()\n                .addClass('hidden');\n            $('.activities-container.' + section)\n                .removeClass('hidden')\n                .show();\n            if (!noChangeText) {\n                const sections = $(\"a.section\");\n                let fromviewer = false;\n                let urlfromviewer = false;\n                let last_url = sessionStorage.getItem('urlhistory');\n                let last_url_array = last_url.split(',');\n                if (last_url_array.length >= 2) {\n                    let viewer_url_session = last_url_array[last_url_array.length - 2];\n                    fromviewer = viewer_url_session.includes(\"viewer/index.php\");\n                    urlfromviewer = urlParams.get('fromviewer') ?? false;\n                    if (fromviewer && urlfromviewer == true) { window.console.log('dentro');\n                        $('ul.npe-linkcontainer li').css('display', 'none');\n                        $(\"ul.npe-linkcontainer li:last-child\").css('content', '');\n                        let a = { 'txtSequence': txtSequence };\n                        Str.get_string(\n                            'backtosequence', 'local_npe', a\n                        ).done(function (string) {\n                            $(\"ul.npe-linkcontainer li:last-child\").text(string);\n                        });\n                        $(\"ul.npe-linkcontainer li:last-child\").addClass('noslash');\n                        $(\"ul.npe-linkcontainer li:last-child\").css('display', 'inline');\n                        // UPDATE SESSION STORAGE HISTORY\n                        session_storage_history(section);\n                    } window.console.log('fuera');\n                }\n                if (sections.length && (!fromviewer || !urlfromviewer)) {\n                    textoBreadcrumbs = sections.filter(\".\" + section).text();\n                    $(\"ul.npe-linkcontainer li:last-child\").text(textoBreadcrumbs);\n                    $(\"ul.npe-linkcontainer li:last-child\").removeClass('noslash');\n                    $('ul.npe-linkcontainer li').css('display', 'inline');\n                    //UPDATE SESSION STORAGE HISTORY\n                    session_storage_history(section);\n                }\n            }\n        }\n        return ManageAssignment;\n    });\n"], "names": ["define", "$", "jqueryui", "DatePicker", "EventListener", "DateValidator", "Str", "SELECTORS", "EVENTS", "ManageAssignment", "resourcesaved", "activitysaved", "exams<PERSON>d", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isassignmentfilter", "txtSequence", "prototype", "constructor", "root", "inputdescription", "form", "setTemplate", "init", "isAssignmentSaved", "changeView", "session_storage_history", "section", "urlParams", "URLSearchParams", "window", "location", "search", "set", "urltogo", "origin", "pathname", "history", "pushState", "trigger", "noChangeText", "textoBreadcrumbs", "get", "hide", "addClass", "removeClass", "show", "sections", "fromviewer", "urlfromviewer", "last_url_array", "sessionStorage", "getItem", "split", "length", "includes", "console", "log", "css", "a", "get_string", "done", "string", "text", "filter", "hearEvent", "e", "detail", "data", "response", "haselements", "prop", "bind", "this", "require", "ModalTeamConflict", "ModalFactory", "create", "type", "TYPE", "modal", "setData", "ready", "setFields", "<PERSON><PERSON><PERSON><PERSON>", "searchInList", "on", "hasClass", "performance", "navigation", "TYPE_BACK_FORWARD", "that", "ModalSuccessAssign", "toggleClass", "width", "toggle", "focus", "val", "each", "find", "html", "searching", "filtercounter", "activities", "item", "toLowerCase", "indexOf", "name", "sec", "list", "numberofelements", "is", "cat", "subcat", "counter", "count", "isVisible", "click"], "mappings": "AAAAA,sCAAO,CAAC,SAAU,WAAY,uBAAwB,2BAA4B,2BAA4B,aAC1G,SAAUC,EAAGC,SAAUC,WAAYC,cAAeC,cAAeC,SAEzDC,eACM,iBADNA,qBAEY,OAFZA,uBAKc,mBALdA,iBAOQ,UAPRA,gBAQO,SARPA,eAUM,iBAVNA,kBAWS,YAKTC,kBACY,eADZA,wBAEkB,qBAFlBA,0BAGoB,gCAafC,uBAAiBC,qEAAgB,GACtCC,qEAAgB,GAChBC,iEAAY,GACZC,sEAAiB,GACjBC,mEAAc,GACdC,2EACAC,mEAAc,GACdP,iBAAiBQ,UAAUC,YAAcT,iBACzCA,iBAAiBQ,UAAUE,KAAO,KAClCV,iBAAiBQ,UAAUG,iBAAmB,KAC9CX,iBAAiBQ,UAAUI,KAAO,KAClCZ,iBAAiBQ,UAAUP,cAAgBA,cAC3CD,iBAAiBQ,UAAUN,cAAgBA,cAC3CF,iBAAiBQ,UAAUL,UAAYA,UACvCH,iBAAiBQ,UAAUJ,eAAiBA,eAC5CJ,iBAAiBQ,UAAUH,YAAcA,YACzCL,iBAAiBQ,UAAUF,mBAAqBA,mBAChDN,iBAAiBQ,UAAUD,YAAcA,YAGzCM,YAAYN,kBAEPO,YACAC,yBACAC,sBA4QAC,wBAAwBC,aACzBC,UAAY,IAAIC,gBAAgBC,OAAOC,SAASC,QACpDJ,UAAUK,IAAI,UAAWN,eACnBO,QAAUJ,OAAOC,SAASI,OAASL,OAAOC,SAASK,SAAW,IAAMR,UAC1EE,OAAOO,QAAQC,UAAU,GAAI,GAAIJ,SAGjCjC,EAAE6B,QAAQS,QAAQ,2BAA4B,CAACL,mBAS1CZ,YAAYN,iBAAaW,+DAAU,KAAMa,yEAC1CC,iBAAmB,OACnBb,UAAY,IAAIC,gBAAgBC,OAAOC,SAASC,WACpC,OAAZL,UACAA,QAAUC,UAAUc,IAAI,YAAc,aAE1CzC,EAAE,yBACG0C,OACAC,SAAS,UACd3C,EAAE,yBAA2B0B,SACxBkB,YAAY,UACZC,QACAN,aAAc,OACTO,SAAW9C,EAAE,iBACf+C,YAAa,EACbC,eAAgB,EAEhBC,eADWC,eAAeC,QAAQ,cACRC,MAAM,QAChCH,eAAeI,QAAU,EAAG,IAE5BN,WADyBE,eAAeA,eAAeI,OAAS,GAChCC,SAAS,oBACzCN,cAAgBrB,UAAUc,IAAI,gBAAiB,EAC3CM,YAA+B,GAAjBC,cAAuB,CAAEnB,OAAO0B,QAAQC,IAAI,UAC1DxD,EAAE,2BAA2ByD,IAAI,UAAW,QAC5CzD,EAAE,sCAAsCyD,IAAI,UAAW,QACnDC,EAAI,aAAiB3C,aACzBV,IAAIsD,WACA,iBAAkB,YAAaD,GACjCE,MAAK,SAAUC,QACb7D,EAAE,sCAAsC8D,KAAKD,WAEjD7D,EAAE,sCAAsC2C,SAAS,WACjD3C,EAAE,sCAAsCyD,IAAI,UAAW,UAEvDhC,wBAAwBC,SAC1BG,OAAO0B,QAAQC,IAAI,UAErBV,SAASO,QAAYN,YAAeC,gBACpCR,iBAAmBM,SAASiB,OAAO,IAAMrC,SAASoC,OAClD9D,EAAE,sCAAsC8D,KAAKtB,kBAC7CxC,EAAE,sCAAsC4C,YAAY,WACpD5C,EAAE,2BAA2ByD,IAAI,UAAW,UAE5ChC,wBAAwBC,kBApUpClB,iBAAiBQ,UAAUM,KAAO,gBACzBJ,KAAOlB,EAAEM,gBAEdH,cAAc6D,UAAUzD,kBAAmB,SAAU0D,GAC7CA,EAAEC,OAAOC,KAAKC,SAASC,YACvBrE,EAAEM,sBAAsBgE,KAAK,YAAY,GAEzCtE,EAAEM,sBAAsBgE,KAAK,YAAY,IAE/CC,KAAKC,OAEPrE,cAAc6D,UAAUzD,wBAAyB,WAC7CkE,QAAQ,CAAC,gCAAiC,uBACtC,SAAUC,kBAAmBC,cACzBA,aAAaC,OAAO,CAAEC,KAAMH,kBAAkBI,MAAQ9E,EAAE,aAAa4D,MAAK,SAAUmB,OAChFA,MAAMC,QAAQ,UACdD,MAAMlC,cAGpB0B,KAAKC,OAEPrE,cAAc6D,UAAUzD,0BAA2B,WAC/CkE,QAAQ,CAAC,gCAAiC,uBACtC,SAAUC,kBAAmBC,cACzBA,aAAaC,OAAO,CAAEC,KAAMH,kBAAkBI,OAAQlB,MAAK,SAAUmB,OACjEA,MAAMC,QAAQ,YACdD,MAAMlC,cAGpB0B,KAAKC,OAEHxE,EAAEM,gBAAgB+C,QAClBrD,EAAEM,gBAAgB2E,OAAM,WACpB7E,cAAc8E,UACV,8BACA5E,eACAA,kBACAA,8BAKP6E,mBACAC,gBAGT5E,iBAAiBQ,UAAUmE,YAAc,WACrCnF,EAAE,kBAAkBqF,GAAG,SAAS,WACxBrF,EAAEwE,MAAMc,SAAS,mBACjBtF,EAAE,oBAAoB6C,OACtB7C,EAAE,kBAAkB4C,YAAY,kBAAkBD,SAAS,iBAC3D3C,EAAE,wBAAwB4C,YAAY,kBACtC5C,EAAE,cAAc4C,YAAY,sBAAsBD,SAAS,eAE3D3C,EAAE,oBAAoB0C,OACtB1C,EAAE,kBAAkB4C,YAAY,iBAAiBD,SAAS,kBAC1D3C,EAAE,wBAAwB2C,SAAS,kBACnC3C,EAAE,cAAc4C,YAAY,aAAaD,SAAS,2BAK9DnC,iBAAiBQ,UAAUO,kBAAoB,WACvCM,OAAO0D,aAAe1D,OAAO0D,YAAYC,WAAWX,OAAShD,OAAO0D,YAAYC,WAAWC,yBACtFhF,cAAgB,QAChBC,cAAgB,QAChBC,UAAY,QAEjB+E,KAAOlB,KAEgB,KAAvBA,KAAK/D,eAA+C,MAAvB+D,KAAK/D,eAClCgE,QAAQ,CAAC,iCAAkC,uBACvC,SAAUkB,mBAAoBhB,cAC1BA,aAAaC,OAAO,CAAEC,KAAMc,mBAAmBb,MAAQ9E,EAAE,aAAa4D,MAAK,SAAUmB,OAC7EW,KAAK5E,oBACLiE,MAAMC,QAAQ,wBAAyBU,KAAK7E,YAAa6E,KAAK5E,mBAAoB4E,KAAK3E,aACvFgE,MAAMlC,SAENkC,MAAMC,QAAQ,wBAAyBU,KAAK9E,gBAC5CmE,MAAMlC,cAMC,KAAvB2B,KAAK9D,eAA+C,MAAvB8D,KAAK/D,eAClCgE,QAAQ,CAAC,iCAAkC,uBACvC,SAAUkB,mBAAoBhB,cAC1BA,aAAaC,OAAO,CAAEC,KAAMc,mBAAmBb,MAAQ9E,EAAE,aAAa4D,MAAK,SAAUmB,OAC7EW,KAAK5E,oBACLiE,MAAMC,QAAQ,wBAAyBU,KAAK7E,YAAa6E,KAAK5E,mBAAoB4E,KAAK3E,aACvFgE,MAAMlC,SAENkC,MAAMC,QAAQ,wBAAyBU,KAAK9E,gBAC5CmE,MAAMlC,cAMH,KAAnB2B,KAAK7D,WAA2C,MAAvB6D,KAAK/D,eAC9BgE,QAAQ,CAAC,iCAAkC,uBACvC,SAAUkB,mBAAoBhB,cAC1BA,aAAaC,OAAO,CAAEC,KAAMc,mBAAmBb,MAAQ9E,EAAE,aAAa4D,MAAK,SAAUmB,OAC7EW,KAAK5E,oBACLiE,MAAMC,QAAQ,wBAAyBU,KAAK7E,YAAa6E,KAAK5E,mBAAoB4E,KAAK3E,aACvFgE,MAAMlC,SAENkC,MAAMC,QAAQ,wBAAyBU,KAAK9E,gBAC5CmE,MAAMlC,eAO9BrC,iBAAiBQ,UAAUoE,aAAe,WACtCpF,EAAE,cAAcqF,GAAG,SAAS,WACxBrF,EAAE,cAAc4F,YAAY,eAC5B5F,EAAE,uBAAuB4F,YAAY,gBACjC5F,EAAE6B,QAAQgE,QAAU,KACpB7F,EAAE,0BAA0B8F,SAEhC9F,EAAE,WAAW+F,WAGZ/F,EAAEM,kBAAkB0F,OACrBhG,EAAEM,wBAAwB2F,MAAK,WAC3BjG,EAAEwE,MAAM0B,KAAK,UAAUC,KAAK,IAAMnG,EAAEwE,MAAM0B,KAAK,kBAAkB7C,OAAS,QAIlFrD,EAAE,mBAAmBqF,GAAG,eAAe,WAC9BrF,EAAEM,kBAAkB0F,OACrBhG,EAAEM,wBAAwB2F,MAAK,WAC3BjG,EAAEwE,MAAM0B,KAAK,UAAUC,KAAK,IAAMnG,EAAEwE,MAAM0B,KAAK,+BAA+B7C,OAAS,YAI3F+C,UAAYpG,EAAEwE,MAAMwB,MAExBhG,EAAEM,iBAAiB+E,GAAG,SAAS,WAC3BrF,EAAEM,kBAAkB0F,IAAI,IACxBhG,EAAE,cAAc4C,YAAY,eAC5B5C,EAAE,uBAAuB4C,YAAY,mBAGzC5C,EAAEM,wBAAwB2F,MAAK,eACvBI,cAAgB,EAChBC,WAAatG,EAAEwE,MACnBxE,EAAEsG,YAAYJ,KAAK,+BAA+BD,MAAK,eAC/CM,KAAOvG,EAAEwE,MAAM0B,KAAK,kBACpBE,UAAU/C,OAAS,GAAKrD,EAAEuG,KAAK,IAAIpC,KAAK,QAAQqC,cAAcC,QAAQL,UAAUI,gBAAkB,GAClGH,gBACArG,EAAEwE,MAAM3B,QACDuD,UAAU/C,OAAS,EAC1BrD,EAAEwE,MAAM9B,OACoB,IAArB0D,UAAU/C,SACjBrD,EAAEwE,MAAM3B,OACRwD,oBAKRrG,EAAEwE,MAAM0B,KAAK,UAAUC,KAAK,IAAME,cAAgB,QAGtDrG,EAAEM,wBAAwB2F,MAAK,eACvBS,KAAO1G,EAAEwE,MAAML,KAAK,QACpBwC,IAAM3G,EAAEwE,MAAML,KAAK,WACnByC,KAAO5G,EAAG,kBAAiB2G,oBAAoBD,UAC/CG,iBAAmB7G,EAAE4G,MAAMV,KAAK,kBAAkB7C,OAEjCrD,EAAE4G,MAAMV,KAAK,kBAAkBnC,QAAO,iBACtB,QAA1B/D,EAAEwE,MAAMf,IAAI,cACpBJ,SAEoBwD,iBACnB7G,EAAE4G,MAAMlE,OAER1C,EAAE4G,MAAM/D,OAGP7C,EAAE,2BAA6B2G,IAAM,MAAMG,GAAG,aAC/C9G,EAAE,kCAAoC2G,IAAM,MAAM5C,OAAO,YAAYkC,MAAK,eAClEvE,QAAU8C,KACVuC,IAAM/G,EAAEwE,MAAML,KAAK,YACvBnE,EAAE,oBAAsB2G,IAAM,IAAMI,IAAM,YAAYhD,OAAO,YAAYkC,MAAK,eACtEe,OAAShH,EAAEwE,MAAML,KAAK,eACtB8C,QAAUjH,EAAE0B,SAASwE,KAAK,kBACzBnC,OAAO,wBAA0BiD,OAAS,MAC1CjD,OAAO,YAAYV,OACpB6D,MAAQlH,EAAEwE,MAAM0B,KAAK,WACT,IAAZe,QACAjH,EAAEwE,MAAM9B,QAERwE,MAAMf,KAAK,IAAMc,QAAU,KAC3BjH,EAAEwE,MAAM3B,qBAOxBsE,UAAY,EAChBnH,EAAE,kBAAkBiG,MAAK,WACjBjG,EAAEwE,MAAMsC,GAAG,aACXK,eAIU,IAAdA,WAAmBf,UAAU/C,OAAS,KAAOrD,EAAE,gCAAgCqD,OAAS,IACxFrD,EAAE,yBAAyB0C,OAC3B1C,EAAE,oBAAoB0C,OACtB1C,EAAE,oBAAoB6C,QACD,IAAdsE,WAAqBf,UAAU/C,OAAS,GAAQrD,EAAE,gCAAgCqD,OAAS,EAI7E,IAAd8D,aAAqBf,UAAU/C,OAAS,IAAOrD,EAAE,gCAAgCqD,OAAS,GACjGrD,EAAE,oBAAoB0C,OACtB1C,EAAE,oBAAoB6C,OACtB7C,EAAE,yBAAyB0C,QACN,IAAdyE,WAAoBf,UAAU/C,OAAS,GAAOrD,EAAE,gCAAgCqD,OAAS,GAChGrD,EAAE,oBAAoB6C,OACtB7C,EAAE,oBAAoB0C,OACtB1C,EAAE,yBAAyB0C,QACN,IAAdyE,YACPnH,EAAE,yBAAyB0C,OAC3B1C,EAAE,oBAAoB0C,OACtB1C,EAAE,oBAAoB0C,SAdtB1C,EAAE,oBAAoB0C,OACtB1C,EAAE,oBAAoB0C,OACtB1C,EAAE,yBAAyB6C,WAgBnC7C,EAAEM,iBAAiB+E,GAAG,SAAS,WAC3BrF,EAAEM,wBAAwB2F,MAAK,WAC3BjG,EAAEwE,MAAM0B,KAAK,UAAUC,KAAK,IAAMnG,EAAEwE,MAAM0B,KAAK,+BAA+B7C,OAAS,SACnF3B,QAAU8C,SACVmC,IAAM3G,EAAEwE,MAAML,KAAK,eACnB4C,IAAM/G,EAAEwE,MAAML,KAAK,YACvBnE,EAAE,oBAAsB2G,IAAM,IAAMI,IAAM,YAAYd,MAAK,eACnDe,OAAShH,EAAEwE,MAAML,KAAK,eACtB8C,QAAUjH,EAAE0B,SAASwE,KAAK,kBACzBnC,OAAO,wBAA0BiD,OAAS,MAAM3D,OACzCrD,EAAEwE,MAAM0B,KAAK,WACnBC,KAAK,IAAMc,QAAU,KAC3BjH,EAAEwE,MAAM3B,gBAKpB7C,EAAE,WAAWoH,SAGjB5G,iBAAiBQ,UAAUQ,WAAa,WACpCxB,EAAE,mBAAmBqF,GAAG,SAAS,eACzB3D,QAAU1B,EAAEwE,MAAML,KAAK,WAC3B9C,YAAYmD,KAAKzD,YAAaW,aAuE/BlB,gBACV"}
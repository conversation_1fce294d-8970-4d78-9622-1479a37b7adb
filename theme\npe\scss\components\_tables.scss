.npe-table {
    width: 100%;
    margin: 0 auto 10px auto;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .npe-table-label {
        font-family: $ossemibold;
        font-size: 12px;
        color: $npe_black;
        display: flex;
    }
    &.npe-table-x4 {
        margin-top: 10px;
        &:not(.npe-table-header) {
            border: 2px solid $basic-color-b;
            border-radius: 20px;
            background: $basic-color-w;
            .npe-table-label {
                font-size: 14px;
                font-family: $osregular;
                font-weight: lighter;
                color: $basic-color-c;
                &.td-4 {
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    padding-right: 2%;
                    gap: 4%;
                }
            }
        }
        .td-1,
        .td-4 {
            width: 30%;
        }
        .td-2,
        .td-3 {
            width: 20%;
        }
        .td-1 {
            padding-left: 20px;
        }
    }
    &.npe-table-x5 {
        .td-1 {
            width: 5%;
            justify-content: center;
        }
        .td-2 {
            width: 50%;
            flex-direction: column;
            .onlymobile-npe {
                display: none;
                font-style: italic;
                font-weight: lighter;
                font-size: 12px;
                color: $basic-color-c;
            }
        }
        .td-3 {
            width: 15%;
            pointer-events: none;
        }
        .td-4 {
            width: 20%;
        }
        .td-5 {
            width: 20%;
            &.buttons {
                justify-content: flex-end;
                .info_drop,
                .dropdown {
                    position: unset;
                    justify-content: center;
                    display: flex;
                    padding: 20px;
                }
            }
        }
        .td-3,
        .td-4 {
            justify-content: center;
        }
    }
    &.npe-table-x5-activities {
        margin: unset;
        &.activities-header {
            margin-bottom: 10px;
            margin-top: 20px;
            align-items: end;
            .td-1 {
                padding-left: 10px;
            }
            .td-4 {
                padding-left: 65px;
            }
            .td-3 {
                padding-left: 25px;
            }
            .td-3.npe-table-label.header-visibility {
                display: none;
            }
            .title {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
        &.activity-item {
            min-height: 60px;
            border: none;
            .npe-table-label {
                font-size: 14px;
                &.td-3,
                &.td-4 {
                    font-family: "Work Sans Regular";
                    font-weight: lighter;
                    color: $basic-color-c;
                }
                &.td-4 {
                    padding-left: 50px;
                }
                &.td-2 {
                    .activity-keyevidence {
                        background-image: url([[pix:theme|star_blue]]);
                        width: 25px;
                        height: 20px;
                        background-repeat: no-repeat;
                        background-position: center center;
                        background-size: contain;
                        margin-right: 5px;
                        margin-left: 5px;
                        @media only screen and (max-width: 799px) {
                            width: 0px;
                            height: 0px;
                        }
                    }
                }
                &.activity-type {
                    width: 32px;
                    height: 32px;
                    background-repeat: no-repeat;
                    background-position: center center;
                    background-size: contain;
                    margin-right: 10px;
                    margin-left: 10px;

                    &.openActivity, &.closedActivity, &.examActivity, &.forum,
                    &.rubrica, &.idea, &.mural, &.board, &.gallery {
                        background-image: url([[pix:theme|activitypicto]]);
                    }
                    &.audio {
                        background-image: url([[pix:theme|audiopicto]]);
                    }
                    &.document, &.text {
                        background-image: url([[pix:theme|documentpicto]]);
                    }
                    &.link {
                        background-image: url([[pix:theme|linkpicto]]);
                    }
                    &.zipFile {
                        background-image: url([[pix:theme|zippicto]]);
                    }
                    &.image {
                        background-image: url([[pix:theme|imagepicto]]);
                    }
                    &.interactiveResource, &.kahoot {
                        background-image: url([[pix:theme|interactivepicto]]);
                    }
                    &.video {
                        background-image: url([[pix:theme|videopicto]]);
                    }
                    &.visor {
                        background-image: url([[pix:theme|visorpicto]]);
                    }
                }
                &.td-1 {
                    display: flex;
                    flex-flow: nowrap;
                    align-self: auto;
                    padding-left: 0;
                    .activity-name {
                        width: 100%;
                        word-break: break-word;
                    }
                }
                &.td-2 {
                    display: flex;
                    flex-direction: column;
                    .activity-license,
                    .activity-resource {
                        display: block;
                    }
                    .activity-license {
                        color: $npe-blue-lic;
                    }
                }
                &.td-3 {
                    &.activity-delivery-type {
                        height: 60px;
                        grid-column: 3;
                        grid-row: 1;
                        background-repeat: no-repeat;
                        background-size: 30px auto;
                        background-position: center center;
                        &.single {
                            background-image: url([[pix:theme|user]]);
                        }
                        &.group {
                            background-image: url([[pix:theme|users]]);
                        }
                        &.grandgroup {
                            background-image: url([[pix:theme|target]]);
                        }
                        &.convertTeam {
                            background-image: url([[pix:theme|convert2group]]);
                        }
                    }
                }
                &.td-5.activity-dropdown {
                    display: flex;
                    justify-content: flex-end;
                    align-self: baseline;
                    .npe-dropdown-container {
                        padding: 20px;
                    }
                }
            }
        }
        .td-1 {
            width: 40%;
        }
        .td-4 {
            width: 30%;
        }
        .td-2,
        .td-5 {
            width: 5%;
        }
        .td-3 {
            width: 25%;
        }

        &.notsubcat{
            .td-1 {
                width: 45%;
            }
            .td-4 {
                width: 0%;
            }
            .td-2,
            .td-5 {
                width: 5%;
            }
            .td-3 {
                width: 45%;
            }
        }

        .td-invisible {
            display: none;
        }

    }
    &.npe-table-x5-repository {
        margin-top: 10px;

        .td-1,
        .td-4 {
            width: 30%;
        }
        .td-2,
        .td-3 {
            width: 20%;
        }
        .td-1 {
            padding-left: 20px;
        }
        .td-5 {
            width: 30%;
            &.buttons {
                justify-content: flex-end;
                .info_drop,
                .dropdown {
                    position: unset;
                    justify-content: center;
                    display: flex;
                    padding: 20px;
                }
            }
        }
        .td-3,
        .td-4 {
            justify-content: center;
        }
        .dotsmenu {
            align-items: center;
            width: 30%;
        }
    }
}
.cards-view {
    display: flex;
    align-items: flex-start;
    flex-direction: row;
    flex-wrap: wrap;

    .npe-table.activity-item.npe-table-x5-activities {
        min-width: 220px;
        max-width: 220px;
        min-height: 130px;
        display: inline-flex;
        flex-flow: column;
        margin-right: 10px;
        overflow: hidden;
        padding: 0;
        .npe-table-label {
            &.td-1,
            &.td-2,
            &.td-3,
            &.td-invisible.activity-visibility {
                width: 100%;
                display: unset;
                height: unset;
                padding-left: unset;
            }
            &.td-1,
            &.td-2,
            &.td-3 {
                padding: 0 10px;
                padding-bottom: 10px;
            }
            &.td-1 {
                padding-top: 0;
                .activity-name {
                    min-height: 30px;
                    padding-bottom: 0;
                    word-wrap: break-word;
                }
                &.activity-actions {
                    justify-content: left;
                }
            }
            &.td-invisible.activity-visibility {
                font-weight: lighter;
                font-size: 12px;
                padding: 10px;
                font-family: 'Work Sans Light';
                span {
                    font-weight: bold;
                }
            }
            &.td-2 {
                order: -1;
                padding-bottom: 0;
                .activity-resource,
                .activity-license {
                    font-size: 12px;
                    display: inline-block;
                }
            }
            &.td-3.activity-delivery-type.single,
            &.td-3.activity-delivery-type.group,
            &.td-3.activity-delivery-type.convertTeam,
            &.td-3.activity-delivery-type.grandgroup {
                background-image: unset;
            }
            &.td-4 {
                display: none;
            }
            &.td-5.activity-dropdown {
                align-self: flex-end;
                justify-content: center;
                height: 20px;
                order: -2;
                .npe-dropdown-container {
                    padding: unset;
                    position: absolute;
                }
            }


        }
    }
}

.tablet-view {
    .activity-item{
        min-height: 60px;
        min-width: 360px;
    }
    .npe-table-x5-activities {
        .td-3.activity-delivery-type {
            display: none;
        }
        .td-3.activity-visibility.td-invisible {
            text-align: left;
            display: block;
        }
    }
    .npe-table.npe-table-x5-activities.activity-item .npe-table-label.td-2,
    .npe-table.npe-table-x5-activities.activity-item .npe-table-label.td-3,
    .npe-table.npe-table-x5-activities.activity-item .npe-table-label.td-4{
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
    }
    .npe-table.npe-table-x5-activities.activity-item .npe-table-label.td-1{
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
    }
    .npe-table.npe-table-x5-activities.activity-item
    .npe-table-label.td-5.activity-dropdown{
        align-self: center;
        min-width: 40px;

        .npe-dropdown-container{
            padding-left: 5px;
            padding-right: 11px;
        }
        .dotsmenu {
            margin-bottom: 0px;
        }
    }

}
@media (min-width: 768px) {
    .activity-item .activity-info,
    .activity-item .activity-actions {
        justify-content: left;
    }
    .activity-item:not(.activityinline) {
        padding: 0;
    }
}
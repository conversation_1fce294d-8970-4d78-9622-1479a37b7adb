.mycourse {
    .top-menu {
        min-width: 776px;
        height: 40px;
        box-shadow: 0 2px 5px #0000001a;
        border-radius: 25px;
        margin: 0 auto;
        margin-bottom: 20px;
    }

    .info_drop {
        margin-right: 20px;
    }

    #npeAccordion,
    #npeAccordionAlt,
    #npeAccordionOriginal {
        padding-bottom: 80px;

        &.mmt {
            padding-top: 10px;
        }
    }

    #join-group-button {
        font-family: $ossemibold;
        font-size: 16px;
        float: right;
        position: absolute;
        right: 20px;
        background-color: $basic-color-c;
        color: $npe-bg-color;
        height: 40px;
        min-width: 200px;
        margin-right: 20px;
        margin-left: 20px;
        border-radius: 50px;
        -webkit-box-shadow: none;
        box-shadow: none;
        margin-top: 6px;

        &.isinfant {
            @media only screen and (min-width: 1200px) {
                font-family: $osregular;
                font-size: 20px;
                padding: 8px 18px 8px 19px;
                border-radius: 20px 10px;
                box-shadow: 0px 3px 6px #00000029;
                right: 0;
                margin: 0;
                margin-top: 30px;
                width: fit-content;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 350px;
                min-width: 270px;
                white-space: nowrap;

                &.m2l {
                    right: -60px
                }
            }
        }

        @media only screen and (max-width: 1199px) {
            background-color: transparent !important;
            font-size: 14px;
            font-family: "Work Sans Regular";
            text-decoration: underline;
            top: 10px;
            right: 15px;
        }
    }

    button:focus,
    svg:focus {
        outline: none;
    }

    .flippingImage {
        transform: rotate(-180deg);
    }

    .configicon {
        @media (max-width: 799px) {
            display: none !important;
        }

        &:hover {
            .tooltiptextcustom {
                visibility: visible;
            }
        }

        .tooltiptextcustom {
            visibility: hidden;
            position: absolute;
            z-index: 2000;
            right: 10px;
            top: 35px;
            width: max-content;
            padding: 10px 12px;
            font-family: $ossemibold;
            font-size: 14px;
            text-align: center;
            color: $npe-black;
            font-weight: bolder;
            border-radius: 12px;
            background-color: $npe-white;
            line-height: 1.2;
            -webkit-box-shadow: 0 5px 10px rgb(0 0 0 / 20%);
            box-shadow: 0 5px 10px rgb(0 0 0 / 20%);
        }

        [aria-expanded="false"] {
            padding: 15px;
            display: inline-block;
            width: 30px;
            height: 30px;
            border: none;
            font-size: 10px;
            border-radius: 50%;
            position: relative;
            background-size: 20px;
            background-repeat: no-repeat;
            background-position: center;
            transition-duration: 0.5s;
            transform: translateZ(0);
            background-color: white;
        }

        [aria-expanded="true"] {
            display: inline-block;
            width: 30px;
            height: 30px;
            border: none;
            font-size: 10px;
            border-radius: 50%;
            position: relative;
            transition-duration: 0.5s;
            background-image: url([[pix:theme|plus_white]]);
            background-size: 15px;
            background-repeat: no-repeat;
            background-position: center;
            -webkit-transform: rotate(-180deg);
            -moz-transform: rotate(-180deg);
            -ms-transform: rotate(-180deg);
            -o-transform: rotate(-180deg);
            transform: rotate(-180deg);

            svg {
                opacity: 0;
                background-color: darkgreen;
            }
        }

        .wsep {
            width: 88%;
        }
    }

    .configswitch {
        display: none;
        width: 300px;
        position: absolute;
        right: 100px;
    }

    .content-bottom-gear-close {
        margin-bottom: 30px;
    }

    .switch-button {
        width: 250px;
        height: 30px;
        position: relative;
        transform: translate3d(-50%, -50%, 0);
        will-change: transform;
        cursor: pointer;
        transition: .3s ease all;
        border: 2px solid white;
        border-radius: 20px;
        font-size: 12px;
        font-family: $osregular;
        box-shadow: 0 5px 10px #0003;
        overflow: hidden;
        margin-left: 210px;
        background-color: $basic-color-w;

        &-case {
            display: inline-block;
            background: none;
            height: 100%;
            border: none;
            transition: .3s ease all;

            &:hover {
                cursor: pointer;
            }
        }

        .active {
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            z-index: -1;
            transition: .3s ease-out all;
            border-radius: 20px;

            &-case {
                color: $basic-color-w;
            }
        }

        .left {
            margin-left: 15px;
        }

        .right {
            margin-left: 25px;
        }
    }

    .switchinfo {
        background-image: url([[pix:theme|i]]);
        background-repeat: no-repeat;
        background-size: 15px auto;
        height: 15px;
        width: 15px;
        cursor: pointer;
        border-radius: 10px;
        background-color: $basic-color-w;
        background-position-x: center;
        background-position-y: center;
        border: 2px solid white;
        margin-left: 65px;
        z-index: 2;

        &:hover {
            .tooltiptexthelp {
                visibility: visible;
            }
        }

        .tooltiptexthelp {
            visibility: hidden;
            position: absolute;
            z-index: 1;
            margin-top: 35px;
            padding: 10px 12px;
            font-family: $ossemibold;
            font-size: 14px;
            color: $basic-color-c;
            border-radius: 12px;
            background-color: $npe-white;
            width: 300px;
        }
    }

    .cards {
        margin: 0 auto;
        margin-bottom: 0px;
        width: 100%;

        .wrapper_topic {
            padding: 8px;
            border-radius: 20px;

            a.group-card-link {
                height: 100%;
                width: 100%;
                display: block;
            }
        }

        .npe_topic_white {
            &.topic_card {
                flex-flow: column;
                width: 364px;
                height: 142px;
                min-height: 120px;
                display: inline-block;
                border-radius: 20px;
                position: relative;
                cursor: pointer;
                padding: 0 10px;
                margin-right: 32px;
                margin-bottom: 32px;

                @media (max-width: 799px) {
                    width: 305px;
                }

                &.standard {
                    padding: 0;
                }

                .wrapper_topic {
                    background-color: white;
                    height: 100%;
                }
            }

            .item-index-into-topic {
                width: 24px;
                height: 24px;
                cursor: pointer;
                background-color: transparent;
                border: none;

                .icon-eye-open-topic {
                    background-image: url([[pix:theme|eye-open]]);
                    width: 24px;
                    height: 24px;
                    background-repeat: no-repeat;
                    position: absolute;
                    margin-left: -15px;
                    margin-top: 5px;
                    background-size: 20px 20px;
                }

                .icon-fullscream-topic {
                    background-image: url([[pix:theme|move]]);
                }
            }

            .npe-text-view {
                display: initial;
                position: absolute;
                width: 110%;
                left: -10px;
                top: 5px;

                &.only-teacher{
                    left: 0;
                }
            }

            .npe-text-hide {
                display: none;
            }
        }

        .npe_topic_black {
            &.topic_card {
                flex-flow: column;
                width: 364px;
                height: 142px;
                min-height: 120px;
                display: inline-block;
                border-radius: 20px;
                position: relative;
                cursor: pointer;
                padding: 0;
                margin-right: 32px;
                margin-bottom: 32px;

                @media (max-width: 799px) {
                    width: 305px;
                }

                .wrapper_topic {
                    background-color: $basic-color-0;
                    color: white;

                    .unit-number {
                        margin-top: 10px;
                        color: #fff;
                    }

                    .topic_name .info-text-title {
                        color: white;
                        text-align: left;
                    }
                }

                &.ui-sortable-handle {
                    .topic_name .info-text-title {
                        text-align: left;
                    }
                }

                &.npe_topic_black_only_teacher   {
                    .topic_name .info-text-title {
                        color: $npe-black;
                    }
                }
            }

            .topic-col-info-text .topic_name a {
                color: white !important;
            }

            .view-move-topic .item-text-into-topic {
                color: white !important;
            }

            .position-kebab-topic {
                display: none;
            }

            .item-index-into-topic {
                width: 30px;
                height: 30px;
                background-color: transparent;
                border: none;
                pointer-events: none;

                .icon-eye-open-topic {
                    background-image: url([[pix:theme|eye-off]]);
                    width: 35px;
                    height: 35px;
                    background-repeat: no-repeat;
                    position: absolute;
                    margin-left: -15px;
                    margin-top: 5px;
                    background-size: 20px 20px;
                }

                .icon-fullscream-topic {
                    background-image: url([[pix:theme|move_black]]);

                    &.black {
                        background-image: url([[pix:theme|move]]);
                    }
                }
            }

            &.ui-sortable-handle {
                .item-index-into-topic {
                    pointer-events: auto;
                }

                .npe-text-view {
                    display: none;
    
                    .
                    &.only-teacher-text {
                        display: none;
                    }
                }
            }

            .npe-text-view {
                display: none;

                &.only-teacher {
                    display: initial;
                    position: relative;
                    top: 2px;
                    color: $npe-black;
                }
            }

            .npe-text-hide {
                display: initial;
                position: absolute;
                width: 110%;
                left: -10px;
                top: 7px;
            }
        }

        .topic_card {
            flex-flow: column;
            width: 364px;
            height: 142px;
            min-height: 120px;
            display: inline-block;
            border-radius: 20px;
            position: relative;
            cursor: pointer;
            padding: 0 10px;

            a {
                &:hover {
                    text-decoration: none;
                }
            }

            .npe-card-row {
                display: flex;
                flex-direction: row;
                margin: 0;

                .npe-card-row-md5 {
                    width: 55%;

                    .row {
                        padding-left: 20px;
                    }

                    &.topic-col-img {
                        width: 120px;
                        position: relative;

                        .only-teacher {
                            width: 58%;
                            margin: 0;
                            cursor: auto;
                            position: absolute;
                            top: 110px;
                            right: 10px;

                            .icon {
                                position: absolute;
                                bottom: -10px;
                                width: 24px;
                                height: 24px;
                                left: -35px;
                            }

                            

                            .only-teacher {
                                display: none;
                                position: absolute;
                                z-index: 2000;
                                top: 15px;
                                left: -160px;
                                width: max-content;
                                padding: 10px 12px;
                                font-family: $osregular;
                                font-size: 14px;
                                text-align: center;
                                color: $npe-black;
                                border-radius: 10px;
                                background-color: $npe-white;
                                -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
                                box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
                            }
                        }

                        @media (min-width: 716px) and (max-width: 799px) {
                            margin-right: 10px;
                        }

                        @media (min-width: 1033px) {
                            margin-right: 10px;
                        }

                        @media (max-width: 715px) {
                            margin-right: 0px;
                        }
                    }
                    &.topic-col-info {
                        width: calc(100% - 140px);
                    }
                }
            }



            .position-kebab-topic {
                text-align: right;

                .item-kebab-topic {
                    width: 20px;
                    height: 20px;
                    border-radius: 10px;
                    cursor: pointer;
                    background-color: $basic-color-w;
                    border: solid 2px $npe-green-principal;
                }
            }

            .img-topics-grid,
            .pix-topics-grid img {
                width: 126px;
                height: 126px;
                border-radius: 12px;
            }

            .unit-number {
                margin-top: 15px;
                padding-right: 0;
                height: 20px;
                min-width: 1px;
                color: $basic-color-c;

                .row {
                    padding-left: 0px !important;
                }
            }

            .topic-unit {
                color: $fucsia-color-c;
                font-size: 12px;
                padding-top: 10px;
                padding-right: 0;
                height: 40px;
                white-space: normal;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                position: relative;

                &.only-teacher {
                    -webkit-line-clamp:1;
                    height: 25px;
                }
            }

            .topic-col-info-text {
                padding-right: 0;
                max-height: 60px;
                min-height: 50px;

                &.ellipsys {
                    white-space: normal;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                }

                &.only-teacher {
                    min-height: 35px;

                    &.ellipsys {
                        -webkit-line-clamp: 2;
                    }
                }

                .topic_name {
                    font-size: 11px;
                    font-family: $ossemibold;
                    color: $basic-color-0;

                    .info-text-title {
                        color: $basic-color-0;
                        text-decoration: none;
                        font-size: 14px;
                        margin-bottom: 6px;

                        &.pt-30 {
                            position: relative;
                            top: 30px;

                            @media only screen and (max-width: 896px) {
                                top: 0;
                            }
                        }
                    }
                }

                &.h90 {
                    max-height: 90px;
                    white-space: normal;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 5;
                    -webkit-box-orient: vertical;

                    &.only-teacher {
                        -webkit-line-clamp: 3;
                    }
                }
            }

            .topic-border-top {
                border-top: 1px solid #C0C0C0;
            }

            .topic-border-move-top {
                border-top: 1px solid $basic-color-b;
            }

            // Si estamos en el modo edicion...
            &.editmode,
            &.npe_topic_black {
                .topic-unit {
                    color: $fucsia-color-c;
                    font-size: 12px;
                    padding-top: 0;
                    padding-right: 0;
                    height: 15px;
                    white-space: normal;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                    position: relative;
                    top: 5px;

                    .areaname {
                        position: initial;
                    }

                }

                .unit-number {
                    margin-top: 10px;
                    color: #fff;
                }

                .topic-col-info-text {
                    &.simple {
                        padding-right: 0;
                        height: 35px;
                        min-height: initial;
                        max-height: initial;
                        margin-bottom: 15px;
                    }

                    &.ellipsys {
                        white-space: normal;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                    }

                    .topic_name {
                        font-size: 11px;
                        font-family: $ossemibold;
                        color: $basic-color-0;
                        margin-bottom: 10px;

                        .info-text-title {
                            text-decoration: none;
                            font-size: 14px;
                            margin-bottom: 6px;
                            display: block;

                            &.pt-30 {
                                position: relative;
                                top: 30px;

                                @media only screen and (max-width: 896px) {
                                    top: 0;
                                }
                            }
                        }
                    }

                    &.h90 {
                        max-height: 90px;
                        white-space: normal;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 5;
                        -webkit-box-orient: vertical;
                    }
                }
            }

            .view-move-topic {
                display: none;
                width: 58%;
                margin: 0;
                cursor: auto;
                position: absolute;
                top: 110px;
                right: 10px;

                &.only-teacher {
                    display: initial;

                    .only-teacher {
                        font-size: 12px;
                        color: $npe-black;
                    }

                    .icon-tooltip-onlyteacher {
                        .icon,
                        svg {
                            position: absolute;
                            bottom: -18px;
                            width: 36px;
                            height: 36px;
                            left: 38px;
                        }

                        .infant-ot-msg {
                            display: none;
                            position: absolute;
                            z-index: 2000;
                            top: 105px;
                            width: max-content;
                            padding: 7px 15px;
                            font-family: $osregular;
                            font-size: 20px;
                            text-align: center;
                            color: $npe-white;
                            border-radius: 20px;
                            background-color: $npe-black;
                            line-height: 1.2;
                            -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
                            box-shadow: 0 5px 10px rgba(0, 0, 0, .2);

                            &.light-bg {
                                color: $npe-grey-mid;
                                background-color: $npe-white;
                                border-radius: 20px;
                                box-shadow: none;
                                border: 1px solid $npe-grey-mid;
                                padding: 7px 15px;
                                top: 103px;
                                left: 40px;
                            }
                        }

                        @media (max-width: 799px) {
                            display: none;
                        }
                    }
                }

                @media (max-width: 799px) {
                    width: 51%;
                }

                .item-index-into-topic {
                    width: 24px;
                    height: 24px;
                    background-color: transparent;
                    border: none;

                    .icon-eye-close-topic {
                        width: 24px;
                        height: 24px;
                        background-image: url([[pix:theme|eye-off]]);
                        background-repeat: no-repeat;
                        position: absolute;
                        margin-left: -15px;
                        margin-top: 6px;
                        background-size: 24px;
                    }

                    .icon-fullscream-topic {
                        width: 20px;
                        height: 20px;
                        position: relative;
                        background-repeat: no-repeat;
                        display: block;
                        margin-top: 5px;
                        background-size: 18px 18px;
                    }
                }

                .item-text-into-topic {
                    display: flex;
                    align-items: center;
                    font-size: 12px;
                    padding-left: 0;
                    color: $basic-color-c;
                    padding-right: 20px;

                    .npe-text-hide {
                        @media (max-width: 799px) {
                            left: 0px;
                            top: 4px;
                        }
                    }
                }
            }

            &.ui-sortable-handle {
                .topic_name .info-text-title {
                    text-align: left;
                }

                .view-move-topic {
                    cursor: pointer;
                }
            }
        }
    }

    .card-index {
        padding-bottom: 30px;

        @media (max-width: 799px) {
            padding-top: 30px;
        }

        .card-header {
            position: relative;
            background-color: transparent;
            text-align: left;
            display: flex;
            padding: 0 0 0 0;
            cursor: pointer;
            align-items: center;

            .buttons-car-header-index {
                margin-bottom: 5px;
                padding-right: 0;
                padding-left: 0;
                position: relative;
                display: none;
            }

            .text-car-header-index {
                margin-bottom: 5px;
                padding-left: 0;
                color: $basic-color-w;
                font-size: 18px;
                min-height: 22px;
                display: block;
                &>h2 {
                    font-size: 18px;
                    pointer-events: none;
                }

                &.editing {
                    position: absolute;
                    left: 120px;
                }
            }

            .arrow-car-header-index {
                margin-bottom: 5px;
                padding-right: 0;
                display: none;

                button.btn {
                    min-width: 0;
                }
            }

            .btn-close-item-index {
                background-image: url([[pix:theme|chevron-down_white]]);
                background-repeat: no-repeat;
                height: 20px;
                padding: 10px;
                background-size: 20px;
            }

            .item-index {
                width: 40px;
                height: 40px;
                cursor: pointer;
                border-radius: 22px;
                background-color: $basic-color-w;
                border: none;
                display: inline-flex;

            }

            .npe-eye-topics {
                background-color: $basic-color-w;

                .icon-eye-open {
                    width: 30px;
                    height: 30px;
                    background-image: url([[pix:theme|eye-open]]);
                    background-repeat: no-repeat;
                    position: absolute;
                    top: 10px;
                }

            }

            .npe-eye-topics-close {
                background-color: $basic-color-0;

                .icon-eye-open {
                    width: 30px;
                    height: 30px;
                    background-image: url([[pix:theme|eye-off]]);
                    background-repeat: no-repeat;
                    position: absolute;
                    margin-top: 5px;
                }

                .icon-fullscream {
                    width: 20px;
                    height: 20px;
                    background-image: url([[pix:theme|move]]);
                    position: absolute;
                    background-repeat: no-repeat;
                    margin: 10px;
                }
            }

            .icon-fullscream {
                width: 20px;
                height: 20px;
                background-image: url([[pix:theme|move]]);
                position: absolute;
                background-repeat: no-repeat;
                margin: 10px;
            }

        }
    }

    .card-body {
        padding: 2rem 0 0 0;

        &.sortable.ui-sortable {
            .btn-topic-npe-link {
                display: none;
            }
        }
    }

    .btn-link:hover {
        text-decoration: none;
    }

    .infantTheme {
        .npe_topic_white {
            &.topic_card {
                flex-flow: column;
                width: 100%;
                height: 175px;
                max-width: 643px;
                min-height: 120px;
                display: inline-block;
                border-radius: 20px;
                position: relative;
                cursor: pointer;
                padding: 0 10px;

                &.standard {
                    padding: 0;
                }

                .wrapper_topic {
                    background-color: white;
                    height: 100%;
                }
            }

            .view-move-topic {
                display: none;
                width: 100%;
                position: initial;

                .item-index-into-topic {
                    width: 30px;
                    height: 30px;
                    cursor: pointer;
                    background-color: transparent;
                    border: none;

                    .icon-eye-open-topic {
                        background-image: url([[pix:theme|eye-open]]);
                        width: 35px;
                        height: 35px;
                        background-repeat: no-repeat;
                        position: absolute;
                        margin-left: -5px;
                        margin-top: 7px;
                    }

                    .icon-fullscream-topic {
                        background-image: url([[pix:theme|move]]);
                    }
                }
            }

            .npe-text-view {
                display: initial;
            }

            .npe-text-hide {
                display: none;
            }
        }

        .npe_topic_black {
            &.topic_card {
                flex-flow: column;
                width: 100%;
                height: 175px;
                max-width: 643px;
                min-height: 120px;
                display: inline-block;
                border-radius: 20px;
                position: relative;
                cursor: pointer;
                padding: 0 10px;

                .wrapper_topic {
                    background-color: $basic-color-0;
                    color: white;

                    .topic_name .info-text-title {
                        color: white;
                        text-align: left;
                    }
                }

                &.ui-sortable-handle {
                    .topic_name .info-text-title {
                        text-align: left;
                    }
                }
            }

            .topic-col-info-text .topic_name a {
                color: white !important;
            }

            .topic-unit {
                color: white !important;
            }

            .view-move-topic .item-text-into-topic {
                color: white !important;
            }

            .position-kebab-topic {
                display: none;
            }

            .view-move-topic {
                display: none;
                width: 100%;
                position: initial;

                .item-index-into-topic {
                    width: 30px;
                    height: 30px;
                    background-color: transparent;
                    border: none;
                    pointer-events: none;

                    .icon-eye-open-topic {
                        background-image: url([[pix:theme|eye-off]]);
                        width: 35px;
                        height: 35px;
                        background-repeat: no-repeat;
                        position: absolute;
                        margin-left: -5px;
                        margin-top: 4px;

                        &.only-teacher {
                            width: 30px;
                            height: 30px;
                            background-position: 4px 5px!important;
                            background-size: 22px 22px!important;
                            left: 40px!important;
                            top: -36px!important;

                            @media (max-width: 799px) {
                                background-size: 16px 16px!important;
                            }
                        }
                    }

                    .icon-fullscream-topic {
                        background-image: url([[pix:theme|move_black]]);
                    }
                }
            }

            &.ui-sortable-handle {
                .item-index-into-topic {
                    pointer-events: auto;
                }
            }

            .npe-text-view {
                display: none;
            }

            .npe-text-hide {
                display: initial;
            }
        }

        .topic_card {
            margin-bottom: 40px;

            .wrapper_topic {
                border-radius: 60px 30px;
                max-width: 643px;
                width: 100%;
                height: 175px;
                padding: 0;
                position: relative;
            }

            #dotsmenu {
                position: absolute;
                top: 10px;
                right: 10px;
                margin: 0;
                padding: 0;

                .dots {
                    height: 28px;
                    width: 28px;
                    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="white" viewBox="0 0 3.1 13.2"><path d="M3.1,11.6c0,0.8-0.7,1.5-1.5,1.5s-1.5-0.7-1.5-1.5s0.7-1.5,1.5-1.5S3.1,10.8,3.1,11.6z M3.1,6.6c0,0.8-0.7,1.5-1.5,1.5 S0.1,7.4,0.1,6.6s0.7-1.5,1.5-1.5S3.1,5.8,3.1,6.6z M3.1,1.6c0,0.8-0.7,1.5-1.5,1.5S0.1,2.4,0.1,1.6s0.7-1.5,1.5-1.5 S3.1,0.8,3.1,1.6z"/></svg>');

                    &[aria-expanded="true"] {
                        background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"%3E%3Cpath fill="white" stroke="white" d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/%3E%3C/svg%3E');
                    }
                }
            }

            &.npe_topic_black {
                #dotsmenu {
                    .dots {
                        background-color: $npe-black !important;
                        border-color: $npe-black;
                    }
                }

                .wrapper_topic {
                    border: 5px solid $npe-black !important;
                    background-color: $npe-white;

                    .npe-card-row {
                        .topic-col-info {

                            .unit-number,
                            .topic-unit,
                            .topic_name {
                                background-color: $npe-black !important;

                                .areaname {
                                    position: initial;
                                }
                            }
                        }
                    }

                    .view-move-topic {
                        .content-icon-eye-open-topic {
                            i {
                                background-color: $npe-black;
                                left: 15px;
                                top: -19px;
                                background-position: 5px 5px;
                                background-size: 25px 25px;
                                box-shadow: none;
                            }
                        }

                        .content-icon-move-topic {
                            i {
                                background-image: url([[pix:theme|move]]);
                            }
                        }
                    }
                }

                &.standard {
                    .view-move-topic {
                        .content-icon-eye-open-topic {
                            i {
                                left: 8px;
                                top: -20.5px;
                            }
                        }
                    }
                }

                &.simple {
                    .wrapper_topic {
                        .npe-card-row {
                            .topic-col-img {
                                width: 165px;
                                height: 165px;
                            }

                            .topic-col-info {
                                .topic_name {
                                    background-color: transparent!important;
                                }
                            }
                        }
                    }
                }
            }

            &.standard {
                .view-move-topic {
                    border: 0;
                    padding-left: 40px;

                    .content-icon-eye-open-topic {
                        i {
                            background-color: white;
                            border-radius: 20px;
                            background-position: 4px;
                            left: 10px;
                            top: -21px;
                            -webkit-box-shadow: 0 3px 12px -4px #666;
                            box-shadow: 0 3px 12px -4px #666;
                            width: 28px;
                            height: 28px;
                        }

                        @media (max-width: 799px) {

                        }
                    }

                    .item-text-into-topic {

                        .npe-text-hide,
                        .npe-text-view {
                            display: none;
                        }
                    }

                    .content-icon-move-topic {
                        .icon-fullscream-topic {
                            height: 48px;
                            width: 48px;
                            background-color: $npe-white;
                            background-position: 12px 12px;
                            background-size: 24px 24px;
                            top: -112px;
                            right: -12px;
                            border-radius: 40px;
                            -webkit-box-shadow: 0 3px 12px -4px #666;
                            box-shadow: 0 3px 12px -4px #666;
                        }
                    }
                }

                .group-card-link {
                    width: 100% !important;
                }

                .npe-card-row {
                    position: relative;
                    width: 100%;
                    height: 100%;
                    display: block;

                    .topic-col-img {
                        width: 100%;
                        height: 100%;
                        margin: 0;

                        img {
                            // Solo necesario en caso de que las imágenes vengan cada una de su padre y de su madre
                            object-fit: cover;
                            object-position: 100% 50%;
                            border-radius: 55px 24px;
                            width: 100%;
                            height: 100%;
                            max-height: 165px !important;
                            max-width: 633px;
                            margin: 0;
                        }
                    }

                    .topic-col-info {
                        width: 100%;
                        height: 100%;
                        margin: 0;
                        position: absolute;
                        top: 0;

                        .unit-number {
                            font-family: $ossmcuadrado;
                            width: 80px;
                            height: 76px;
                            color: white;
                            font-size: 40px;
                            border-radius: 51% 49% 43% 57% / 44% 49% 51% 56%;
                            margin-top: 45px;
                            margin-left: 10px;

                            .col-12 {
                                display: table-cell;
                                text-align: center;
                                line-height: 70px;
                            }
                        }

                        .topic-col-info-text {
                            position: absolute;
                            bottom: 0;
                            height: fit-content !important;
                            border-radius: 20px;
                            padding: 0;

                            .topic_name {
                                text-align: center !important;
                                max-width: 563px;
                                width: 87%;
                                margin: auto;
                                padding: 2px 60px 2px 60px;
                                border-radius: 20px;
                                position: absolute;
                                top: 100%;
                                right: 45px;
                                transform: translateY(-50%);

                                @media only screen and (max-width: 1199px) {
                                    width: 85%;
                                }

                                span {
                                    color: $npe-white;
                                    font-size: 24px;
                                    font-family: $osregular;
                                    display: -webkit-box;
                                    -webkit-line-clamp: 2;
                                    -webkit-box-orient: vertical;
                                    overflow: hidden;
                                    margin-bottom: 0;
                                    text-align: center;
                                }
                            }
                        }

                        .topic-unit {
                            height: fit-content;
                            padding: 5px 20px 5px 17px !important;
                            position: absolute;
                            right: 55px;
                            top: 10px;
                            border-radius: 20px;
                            font-size: 14px;
                            color: $npe-white;
                            width: fit-content;
                            max-width: 60%;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            display: initial;

                            .areaname {
                                position: initial;
                            }

                            &.nomenu {
                                right: 10px;
                            }
                        }
                    }
                }
            }

            &.simple {
                .wrapper_topic {
                    border-color: transparent;
                }

                .view-move-topic {
                    border: 0;
                    padding-left: 40px;

                    .content-icon-eye-open-topic {
                        i {
                            background-color: white;
                            border-radius: 20px;
                            background-position: 5px;
                            top: -20px;
                            -webkit-box-shadow: 0 3px 12px -4px #666;
                            box-shadow: 0 3px 12px -4px #666;
                        }
                    }

                    .item-text-into-topic {

                        .npe-text-hide,
                        .npe-text-view {
                            display: none;
                        }
                    }

                    .content-icon-move-topic {
                        .icon-fullscream-topic {
                            height: 48px;
                            width: 48px;
                            background-color: $npe-white;
                            background-position: 12px 12px;
                            background-size: 24px 24px;
                            top: -112px;
                            right: -12px;
                            border-radius: 40px;
                            -webkit-box-shadow: 0 3px 12px -4px #666;
                            box-shadow: 0 3px 12px -4px #666;
                        }
                    }
                }

                .group-card-link {
                    width: 100%;
                }

                .npe-card-row {
                    position: relative;
                    width: 100%;
                    height: 100%;
                    display: block;

                    .topic-col-img {
                        // width: 100%;
                        // height: 100%;
                        // margin: 0;
                        border-radius: 55px 24px;
                        width: 165px;
                        height: 165px;

                        img {
                            // Solo necesario en caso de que las imágenes vengan cada una de su padre y de su madre
                            // object-fit: cover;
                            // object-position: 100% 50%;
                            border-radius: 55px 24px;
                            width: 100%;
                            height: 100%;
                            margin: 0;
                        }
                    }

                    .topic-col-info {
                        width: 100%;
                        height: 100%;
                        margin: 0;
                        position: absolute;
                        top: 0;

                        .unit-number {
                            display: none;
                        }

                        .topic-col-info-text {
                            position: absolute;
                            top: 50%;
                            height: fit-content !important;
                            border-radius: 20px;
                            padding: 0;

                            .topic_name {
                                text-align: center !important;
                                width: 60%;
                                max-width: 420px;
                                margin: auto;
                                padding: 5px 40px 5px 40px;
                                border-radius: 20px;
                                position: absolute;
                                top: 100%;
                                right: 50px;
                                transform: translateY(-50%);

                                span {
                                    color: $npe-white;
                                    font-size: 24px;
                                    font-family: $osregular;
                                    display: -webkit-box;
                                    -webkit-line-clamp: 2;
                                    -webkit-box-orient: vertical;
                                    overflow: hidden;
                                    margin-bottom: 0;
                                    text-align: center;
                                }
                            }
                        }

                        .topic-unit {
                            height: fit-content;
                            padding: 5px 20px 5px 17px !important;
                            position: absolute;
                            right: 55px;
                            top: 10px;
                            border-radius: 20px;
                            font-size: 14px;
                            color: $npe-white;
                            max-width: 60%;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            width: fit-content;
                            display: initial;

                            .areaname {
                                position: initial;
                            }

                            &.nomenu {
                                right: 10px;
                            }
                        }
                    }
                }
            }
        }
    }   

    // Cambios para el tema de Matices
    .cardindex {
        &.matices {
            .npe-content-bottom-gear-close {
                .exitindex  {
                    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
                }
            }
            .card-index {
                background-color: white;
                padding: 40px;
                border-radius: 40px;
                max-width: 1600px;
                padding-bottom: 20px;
                margin-bottom: 30px;
    
                .card-header {
                    border-radius: 24px;
                    padding: 15px 20px;
                    justify-content: space-between;
                    max-height: 60px;
                    min-height: 43px;
    
                    &.hidden {
                        display: none;
                    }
    
                    &.black {
                        background-color: $npe-black;
                    }
    
                    .text-car-header-index {
                        margin-bottom: 0;
    
                        &.editing {
                            position: initial;
                        }
    
                        h2 {
                            font-size: 24px;
                            position: initial;
                            margin-bottom: 0;
                        }
                    }
    
                    .buttons-car-header-index {
                        margin-bottom: 0;
                        position: relative;
                        left: 5px;
    
                        &.show {
                            display: flex!important;
                        }
    
                        .item-index {
                            background-color: transparent;
                            width: 30px;
                            height: 30px;
                            padding: 0;
                            border-radius: 20px;
                            align-items: center;
                            justify-content: center;
    
                            &.content-icon-eye-open {
                                margin-right: 10px;
                            }
    
                            i {
                                background-size: 15px;
                                -webkit-filter: invert(100%); /* Safari/Chrome */
                                filter: invert(100%);
    ;
                                &:after {
                                    all: unset;
                                }
    
                                &.icon-eye-open {
                                    width: 20px;
                                    height: 20px;
                                    top: 10px;
                                    left: 7px;
                                }
    
                                &.icon-fullscream {
                                    margin: 0px;
                                    top: 7px;
                                    left: 47px
                                }
                            }
    
                            &.npe-eye-topics-close {
                                .icon-eye-open {
                                    width: 20px;
                                    height: 20px;
                                    top: 8px;
                                    left: 7px;
                                    margin-top: 0;
                                    -webkit-filter: invert(0%); /* Safari/Chrome */
                                    filter: invert(0%);
                                }
                            }
                        }
                    }
                }
    
                .topic_card {
                    width: 346px;
                    height: 242px;
                    margin-right: 40px;
                    padding: 0;

                    .wrapper_topic {
                        padding: 4px;;
                    }

                    .npe-card-row {
                        flex-direction: column;
                    }
    
                    .topic-col-img {
                        width: 338px;
                        height: 169px;
                        overflow: hidden;
                        border-radius: 16px;
                        border: 4px solid transparent;
                        box-sizing: border-box;

                        &.npe_topic_black {
                            border: 4px solid $npe-black;

                            .only-teacher {
                                top: 25px;
                                left: 45px;
                                right: 0;
                            }
                        }

                        img {
                            transition: transform 0.3s ease;
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            border-radius: 0;
                        }

                        &:hover img {
                            transform: scale(1.1); 
                        }

                        .unittitle_special {
                            position: absolute;
                            height: 31px;
                            top: -1px;
                            right: 0;
                            background: $npe-white;
                            color: $npe-black;
                            padding: 4px 10px;
                            border-radius: 20px;
                            margin: 13px;
                            max-width: 25ch;
                            word-break: break-all;
                            white-space: normal;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            display: -webkit-box;
                            -webkit-line-clamp: 1;
                            -webkit-box-orient: vertical;
                            line-height: 1.8;
                            z-index: 1;
                        }
                        .sdanumber_special {
                            position: absolute;
                            bottom: 0;
                            left: 0;
                            font-size: 20px;
                            width: 48px;
                            height: 48px;
                            border-radius: 30px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            margin-left: 13px;
                            margin-bottom: 8px;
                        }
                    }
    
                    .topic-col-info {
                        margin: 15px 10px;
                        width: 95%;
    
                        .topic-col-info-text {
                            display: flex;
                            flex-direction: row;
                            overflow: visible;

                            .topic_name_sub {
                                .info-text-subtitle {
                                    font-size: 14px;
                                    width: 90%;
                                    white-space: normal;
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    display: -webkit-box;
                                    -webkit-line-clamp: 2;
                                    -webkit-box-orient: vertical;
                                }
                            }
                            .topic_name {
                                display: flex;
                                justify-content: flex-start;
                                flex: 90;
                                white-space: normal;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                display: -webkit-box;
                                -webkit-line-clamp: 2;
                                -webkit-box-orient: vertical;

                                .info-text-title {
                                    font-size: 18px;
                                    width: 90%;
                                }
                            }

                            #dotsmenu {
                                padding-top: 0;
                                flex: 10;

                                .dropdown-menu-right {
                                    right: 40px;
                                    left: initial!important;
                                }
                            }
                        }
                    }

                    .topic-col-filter {
                        position: absolute;
                        display: flex;
                        height: 36px;
                        top: 115px;
                        right: 25px;
                        background-color: $npe-white;
                        border-radius: 20px;
                        align-items: center;
                        padding: 8px;

                        .topic-filter-text {
                            max-width: 140px;
                            word-break: break-all;
                            white-space: normal;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            display: -webkit-box;
                            -webkit-line-clamp: 1;
                            -webkit-box-orient: vertical;
                            height: 18px;
                            align-items: center;
                            position: relative;
                            top: 2px;
                            font-weight: bold;
                            padding-right: 6px
                        }
                        .topic-filter-icon {
                            text-align: center;
                            width: 20px;
                            height: 20px;

                            svg {
                                width: 20px;
                                height: 20px;
                            }

                        }

                        &.bgf-c {
                            .topic-filter-icon {
                                -webkit-filter: invert(100%);
                                filter: invert(100%);
                            }
                        }
                    }

                    &.editmode {
                        .topic-col-filter {
                            display: none;
                        }

                        .all {
                            display: initial!important;
                        }
                    }

                    .view-move-topic {
                        top: 20px;
                        left: 20px;
                        width: initial;

                        &.visibility_teacher {
                            .item-text-into-topic {
                                line-height: 1.3;

                                .npe-text-hide {
                                    padding-left: 30px;

                                    &.nomargin {
                                        padding-left: 10px;
                                    }
                                }
                            }
                        }

                        &.visibility_only_teacher {
                            top: 15px;
                            left: 30px;
                            display: initial!important;

                            .only-teacher-white {
                                background-color: $npe-white;
                                padding: 6px 10px;
                                border-radius: 20px;
                                color: $npe-black;
                                width: fit-content;            
                            }
                        }

                        &.visibility_all {
                            top: 20px;
                            left: 20px;
                            width: fit-content;
                            
                            .item-text-into-topic {
                                padding-right: 0;
                                line-height: 1.5;

                                .npe-text-hide,
                                .npe-text-show {
                                    padding: 5px 10px;
                                    font-size: 14px;
                                    color: $npe-white;
                                    background-color: $npe-black;
                                    border-radius: 20px;
                                }

                                .npe-text-show {
                                    color: $npe-black;
                                    background-color: $npe-white;

                                }

                            }

                            .npe-text-show {
                                display: none!important;
                            }
                        }
                    }

                    &.npe_topic_white, 
                    &.npe_topic_black {
                        .item-text-into-topic {
                            padding-right: 0;
                            line-height: 1.5;

                            .npe-text-hide {
                                width: initial;
                                position: initial;
                                padding: 5px 10px;
                                font-size: 14px;
                                background-color: $npe-black;
                                border-radius: 20px;
                            }

                            .npe-text-show {
                                width: initial;
                                position: initial;
                                padding: 5px 10px;
                                font-size: 14px;
                                color: $npe-black;
                                background-color: $npe-white;
                                border-radius: 20px;

                                &.all {
                                    display: none;
                                }
                            }
                        }
                    }

                    &.npe_topic_white {
                        .content-icon-move-topic {
                            position: absolute;
                            top: 165px;
                            right: 10px;
                        }

                        .content-icon-eye-open-topic {
                            position: absolute;
                            top: 105px;
                            right: 10px;
                            width: 36px;
                            height: 36px;
                            background-color: $npe-white;
                            border-radius: 20px;

                            .icon-eye-open-topic {
                                background-position: center;
                                width: 36px;
                                height: 36px;
                                margin-left: 0;
                                margin-top: 0;
                            }

                            &.bgf-c {
                                i {
                                    -webkit-filter: invert(100%);
                                    filter: invert(100%);

                                    &:after {
                                        content: none!important;
                                    }
                                }
                            }
                        }
                    }

                    &.npe_topic_black {
                        .wrapper_topic {
                            background-color: initial;

                            .only-teacher {
                                position: absolute;
                                top: 22px;
                                left: 22px;

                                img {
                                    width: 24px;
                                    height: 24px;
                                    filter: contrast(100);
                                }

                                .infant-ot-msg {
                                    display: none;
                                    position: absolute;
                                    z-index: 2000;
                                    width: max-content;
                                    font-family: $osregular;
                                    font-size: 14px;
                                    text-align: center;
                                    line-height: 1.2;
                                    color: #707070;
                                    background-color: #fff;
                                    border-radius: 10px;
                                    box-shadow: none;
                                    padding: 10px 15px;
                                    top: 30px;
                                    left: 0px;
                                }
                            }

                            .info-text-title {
                                color: $npe-black;
                            }
                        }

                        .content-icon-move-topic {
                            position: absolute;
                            top: 165px;
                            right: 10px;

                            .icon-fullscream-topic {
                                background-image: url([[pix:theme|move]]);
                            }
                        }

                        .content-icon-eye-open-topic {
                            position: absolute;
                            top: 105px;
                            right: 10px;
                            width: 36px;
                            height: 36px;
                            background-color: $npe-black;
                            border-radius: 20px;

                            .icon-eye-open-topic {
                                background-position: center;
                                width: 36px;
                                height: 36px;
                                margin-left: 0;
                                margin-top: 0;
                            }
                        }

                        .npe-text-show {
                            display: none!important;
                        }	
                    }
                }

                @media only screen and (max-width: 1024px) {
                    padding: 30px;
                    margin: 20px;

                    .topic_card {
                        margin-right: 10px;
                    }
                }

                @media only screen and (max-width: 450px) {
                    
                }
            }
        }
    }
}

.layer-save-index {
    color: $basic-color-w;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 120px;
    z-index: 2;
    background-color: rgba(0, 0, 0, 0.9);
    display: none;

    .cancel-index {
        width: 200px;
        background-color: $basic-color-w;
        color: $basic-color-0;
        border-radius: 20px;

    }

    .public-change-index {
        width: 200px;
        background-color: $npe-green-principal;
        color: $basic-color-w;
        border-radius: 20px;

    }

    .restart-index {
        text-align: center;
        text-decoration-line: underline;
        cursor: pointer;
        color: #fff;
    }
}

#modal_exit.success-modal,
#modal_restore.success-modal {
    .modal-dialog {
        .modal-content {
            .success-text {
                border-bottom: none;
            }

            .alerticon {
                width: 50px;
                height: 50px;
                background-image: url([[pix:theme|warningicon]]);
                background-repeat: no-repeat;
                background-size: 40px 40px;
                background-position-x: 5px;
                background-position-y: 4px;
                margin: -23px auto 20px;
                margin-top: 20px;
            }
        }
    }
}


.btn-topic-wo-content {
    cursor: pointer;
}

.dropdown-course-index .dropdown-course-index-item:not(:last-child) {
    border-bottom: 1px solid #ededed;
    padding: 0;
    width: 80%;
    margin: 0 auto;
    margin-top: 10px;
    padding-bottom: 10px;
}

.topic-options {
    border-radius: 10px;
}

.headereducamos {
    .right-things {
        top: 88px;
    }
}

.right-things {
    position: absolute;
    top: 110px;
    width: 100%;
    left: 0;

    &.isinfant {
        @media only screen and (min-width: 1200px) {
            width: 350px;
            right: 95px;
            left: initial;
        }
    }

    .join-group {
        margin-top: 30px;
    }

    .familyresources {
        display: flex;
        width: 100%;
        justify-content: flex-end;
        padding-right: 70px;
        padding-top: 2px;
        position: absolute;
        line-height: 20px;

        .arrow-right {
            line-height: 20px;
            padding-right: 30px;
            background-image: url([[pix:theme|arrow-right-white]]);
            background-position: right top;
            background-repeat: no-repeat;
        }
    }
}

#page-local-npe-courses-index {
    #page-content {
        width: 100%;
    }

    #last-groups-list-item {
        width: 100%;
        padding: 0;
    }

    .npe-margin-auto-center {
        margin: 0 auto;
        max-width: 1366px;

        &.extended {
            @media (min-width: 1920px) {
                min-width: 1608px;

                .card-deck {
                    min-width: 1608px;
                }
            }

            @media (min-width: 1321px) and (max-width: 1919px) {
                min-width: 1321px;

                .card-deck {
                    min-width: 1321px;

                    .topic_card {
                        margin-right: 24px;
                        margin-bottom: 24px;
                    }
                }
            }

            @media (min-width: 801px) and (max-width: 1320px) {
                min-width: 801px;

                .card-deck {
                    min-width: 801px;
                }
            }

            @media (max-width: 800px) {
                min-width: 320px;
            }
        }

        &.maticesTheme {
            @media (min-width: 1600px) {
                min-width: 1528px;

                .card-deck {
                    .topic_card {
                        margin-right: 16px;

                        .wrapper_topic {
                            padding: 0;
                        }
                    }
                }
            }

            @media (min-width: 1200px) and (max-width: 1599px) {
                width: 1528px;

                .card-deck {
                    min-width: 1432px;

                    .topic_card {
                        margin-right: 16px;
                        width: 466px;
                        height: 302px;

                        .wrapper_topic {
                            padding: 0;

                            .topic-col-img {
                                width: 458px;
                                height: 229px;
                            }

                            .topic-col-filter {
                                top: 175px;
                            }
                        }
                    }
                }
            }

            @media (min-width: 800px) and (max-width: 1199px) {
                width: 729px;

                .card-deck {
                    max-width: 681px;

                    .topic_card {
                        margin-right: 16px;
                        width: 332px;
                        height: 235px;

                        .wrapper_topic {
                            padding: 0;

                            .topic-col-img {
                                width: 324px;
                                height: 162px;
                            }

                            .topic-col-filter {
                                //top: 175px;
                            }
                        }
                    }
                }
            }

            @media (min-width: 375px) {

            }
        }
    }

    .collapse {
        padding: 0 10px;

        &.pd0 {
            padding: 0;
        }
    }

    .configicon {
        display: flex;
        flex: 0;
    }

    .npe-content-bottom-gear-close {
        display: flex;
        justify-content: flex-end;
        position: relative;
        top: 50px;
        z-index: 100;

        &.matices {
            justify-content: flex-start;
            top:0;
            margin-top: 30px;

            .options {
                margin-left: 0;
                width: fit-content;
                box-shadow: none;
                background-color: transparent;

                .matices-edit {
                    min-width: 190px;
                    width: max-content;

                    a {
                        display: inline-flex;
                        align-items: center;
                        background-color: #fff;
                        color: #000;
                        border-radius: 20px;
                        text-decoration: none;
                        font-family: $osregular;
                        font-size: 14px;
                        font-weight: bold;
                        padding: 10px 15px;

                        &:before {
                            content: '';
                            display: inline-block;
                            width: 20px;
                            height: 20px;
                            margin-right: 10px;
                            background-image: url('[[pix:theme_npe|matices/perso_icon]]');
                            background-size: contain;
                            background-repeat: no-repeat;
                        }

                        &.bgf-c {
                            &:before {
                                -webkit-filter: invert(100%);
                                filter: invert(100%)
                            }
                        }
                    }

                    &.close {
                        display: none;
                        text-shadow: none;
                        float: none;
                        opacity: 1;
        
                        a {
                            display: inline-flex;
                            align-items: center;
                            border-radius: 20px;
                            text-decoration: none;
                            font-family: $osregular;
                            font-size: 14px;
                            font-weight: bold;
                            padding: 10px 15px;
                            height: 40px;

                            &:before {
                                content: '';
                                display: inline-block;
                                width: 12px;
                                height: 12px;
                                margin-right: 10px;
                                margin-left: 5px;
                                background-image: url('[[pix:theme_npe|close]]');
                                background-size: contain;
                                background-repeat: no-repeat;
                            }

                            &.bgf-c {
                                &:before {
                                    -webkit-filter: invert(0%);
                                    filter: invert(0%)
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.onboarding-groups {
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    z-index: 2000;
    display: none;

    #backdrop-container {
        animation: 1.5s onboarding-opacity ease-in-out;
    }

    .backdrop-menu,
    .backdrop-listgroups,
    .backdrop-estructure,
    .backdrop-classnotebook {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
        animation: 1.5s onboarding-opacity ease-in-out;
    }

    .backdrop-menu::after,
    .backdrop-listgroups::after,
    .backdrop-estructure::after,
    .backdrop-classnotebook::after {
        content: '';
        position: absolute;
        box-shadow: 0 0 0 10000px $npe-backdrop;
    }

    .backdrop-menu::after {
        top: 80px;
        width: 100%;
        height: 50px;
    }

    .backdrop-listgroups::after {
        top: 316px;
        width: 22%;
        min-height: auto;
        border-radius: 20px;
        left: 18%;
    }

    .backdrop-estructure::after {
        top: 362px;
        width: 62%;
        height: 353px;
        border-radius: 20px;
        left: 18%;
    }

    #wizard-container {
        animation: 1.5s onboarding-opacity ease-in-out;
        height: 100%;
        padding: 0;
        margin: 0;
    }

    .close {
        position: fixed;
        color: $npe-black;
        top: 40px;
        font-size: 40px;
        opacity: 1;
        right: 30px;
        width: 30px;
        height: 30px;
        background-color: $npe-white;
        border-radius: 50px;
        background-image: url([[pix:theme|plus]]);
        background-repeat: no-repeat;
        background-position-x: 8.4px;
        background-position-y: 8.4px;

        span {
            position: relative;
            top: -14px;
            left: -5px;
        }
    }

    .panel-welcome-onboarding-groups {
        width: 100%;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: 1.5s onboarding-opacity ease-in-out;

        .panel {
            position: relative;
            display: flex;
            flex-flow: column;
            max-width: 622px;
            background: $npe-white 0 0 no-repeat padding-box;
            border-radius: 20px;
            text-align: center;

            h2 {
                font-weight: bold;
            }

            h4 {
                color: $npe-dark-grey;
                font-family: $osregular;
            }

            .buttons,
            h2,
            h4 {
                padding: 20px;
            }

            .buttons {
                display: flex;
                justify-content: center;
                gap: 20px;
            }
        }
    }

    .wizard {
        background: none;

        .cards {
            margin: 0 auto;
            margin-bottom: 20px;

            .arrow-right {
                width: 0;
                height: 0;
                border-top: 15px solid transparent;
                border-bottom: 15px solid transparent;
                border-left: 15px solid $npe-white;
                position: absolute;
                right: -14px;
                top: 60px;
            }

            .group_card {
                flex-flow: column;
                width: 298px;
                min-height: 140px;
                box-shadow: 0 2px 5px #0000001a;
                display: inline-block;
                border-radius: 20px;
                margin: 0 10px 20px 10px;
                backdrop-filter: blur(1px);
                background-color: white;

                .group_name {
                    width: 100%;
                    height: 60px;
                    margin: auto;
                    font-size: 18px;
                    font-family: $ossemibold;
                    color: $npe-black;

                    a {
                        color: $npe-black;
                        text-decoration: none;
                    }
                }

                .card_footer {
                    background-color: $npe-grey-alt;
                    height: 40px;
                    border-bottom-right-radius: 20px;
                    border-bottom-left-radius: 20px;
                    font-size: 14px;
                    font-family: $oslight;
                    color: #000;

                    .count_users {
                        padding-top: 5px;

                        b {
                            font-weight: initial;
                            font-family: $ossemibold;
                        }

                        .first {
                            float: left;
                            margin-left: 20px;
                        }
                    }

                    .separator {
                        border-top: 2px solid white;
                        width: 258px;
                        margin: 0 auto;
                        margin-top: 7px;
                    }
                }
            }
        }

        .arrow-bottom {
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-top: 15px solid $npe-white;
            position: absolute;
            bottom: -14px;
            right: 120px;
        }

        .wizard_card {
            position: absolute;
            width: 300px;
            background: $npe-white;
            border-radius: 20px;
            text-align: left;
            font-family: $osregular;
            z-index: 10;
            animation: 1.5s onboarding-opacity ease-in-out;

            &.second {
                top: 177px;
                left: 39%;
            }

            &.third {
                top: 300px;
                left: 45%;
            }

            &.fourth {
                top: 100px;
                left: 39.5%;
            }

            &.fifth {
                bottom: 75px;
                left: 39.7%;
            }

            .arrow-top {
                width: 0;
                height: 0;
                border-left: 15px solid transparent;
                border-right: 15px solid transparent;
                border-bottom: 15px solid $npe-white;
                position: absolute;
                top: -14px;
                left: 140px;
            }

            .arrow-right {
                width: 0;
                height: 0;
                border-top: 15px solid transparent;
                border-bottom: 15px solid transparent;
                border-left: 15px solid $npe-white;
                position: absolute;
                right: -14px;
                top: 60px;
            }

            .arrow-left {
                width: 0;
                height: 0;
                border-top: 15px solid transparent;
                border-bottom: 15px solid transparent;
                border-right: 15px solid $npe-white;
                position: absolute;
                left: -14px;
                top: 60px;
            }

            .arrow-bottom {
                width: 0;
                height: 0;
                border-left: 15px solid transparent;
                border-right: 15px solid transparent;
                border-top: 15px solid $npe-white;
                position: absolute;
                bottom: -14px;
                right: 120px;
            }

            .card-group-title {
                font-family: $ossemibold;
                font-size: 18px;
            }

            .card-group-text {
                font-size: 14px;
            }

            .actions {
                display: flex;
                justify-content: space-between;
                align-items: flex-end;

                a.skip-intro {
                    color: $npe-dark-grey;
                    font-size: 12px;
                    text-decoration: underline;

                    &:hover {
                        color: $npe-dark-grey;
                    }
                }

                .arrows {
                    a.arrow {
                        position: unset;
                        left: unset;
                        width: 40px;
                        margin: 0 10px;
                        background-repeat: no-repeat;
                        background-size: 30px 30px;
                        background-position-x: 5px;
                        background-position-y: 4px;

                        &.arrow-previous {
                            background-image: url([[pix:theme|arrow-left-white]]);
                        }

                        &.arrow-next {
                            background-image: url([[pix:theme|arrow-right-white]]);
                        }

                        &.next,
                        &.previous {
                            background-color: $npe-black;
                        }
                    }
                }
            }
        }
    }

    @keyframes onboarding-opacity {
        0% {
            opacity: 0;
        }

        100% {
            opacity: 1;
        }
    }
}

.notice-new-course {
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;

    #backdrop-container {
        animation: 1.5s onboarding-opacity ease-in-out;
    }

    .backdrop-menu,
    .backdrop-listgroups,
    .backdrop-estructure,
    .backdrop-classnotebook {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
        animation: 1.5s onboarding-opacity ease-in-out;
    }

    .backdrop-menu::after,
    .backdrop-listgroups::after,
    .backdrop-estructure::after,
    .backdrop-classnotebook::after {
        content: '';
        position: absolute;
        box-shadow: 0 0 0 10000px $npe-backdrop;
    }

    .backdrop-menu::after {
        top: 80px;
        width: 100%;
        height: 50px;
    }

    .backdrop-listgroups::after {
        top: 316px;
        width: 22%;
        min-height: auto;
        border-radius: 20px;
        left: 18%;
    }

    .backdrop-estructure::after {
        top: 362px;
        width: 62%;
        height: 353px;
        border-radius: 20px;
        left: 18%;
    }

    #wizard-container {
        animation: 1.5s onboarding-opacity ease-in-out;
        height: 100%;
    }

    .close {
        position: fixed;
        color: $npe-black;
        top: 40px;
        font-size: 40px;
        opacity: 1;
        right: 30px;
        width: 30px;
        height: 30px;
        background-color: $npe-white;
        border-radius: 50px;
        background-image: url([[pix:theme|plus]]);
        background-repeat: no-repeat;
        background-position-x: 8.4px;
        background-position-y: 8.4px;

        span {
            position: relative;
            top: -14px;
            left: -5px;
        }
    }

    .tooltip-alert {
        background: none;

        .wizard_card {
            position: absolute;
            width: 300px;
            background: $npe-white;
            border-radius: 20px;
            text-align: left;
            font-family: $osregular;
            z-index: 10;
            animation: 1.5s onboarding-opacity ease-in-out;

            &.first {
                top: 115px !important;
                left: 39%;
            }

            .arrow-top {
                width: 0;
                height: 0;
                border-left: 15px solid transparent;
                border-right: 15px solid transparent;
                border-bottom: 15px solid $npe-white;
                position: absolute;
                top: -14px;
                left: 140px;
            }

            .card-group-title {
                font-family: $ossemibold;
                font-size: 18px;
            }

            .card-group-text {
                font-size: 14px;
            }

            .actions {
                display: flex;
                justify-content: space-between;
                align-items: flex-end;

                a.skip-intro {
                    color: $npe-dark-grey;
                    font-size: 12px;
                    text-decoration: underline;

                    &:hover {
                        color: $npe-dark-grey;
                    }
                }

                .arrows {
                    a.arrow {
                        position: unset;
                        left: unset;
                        width: 40px;
                        margin: 0 10px;
                        background-repeat: no-repeat;
                        background-size: 30px 30px;
                        background-position-x: 5px;
                        background-position-y: 4px;

                        &.arrow-previous {
                            background-image: url([[pix:theme|arrow-left-white]]);
                        }

                        &.arrow-next {
                            background-image: url([[pix:theme|arrow-right-white]]);
                        }

                        &.next,
                        &.previous {
                            background-color: $npe-black;
                        }
                    }
                }
            }
        }
    }

    @keyframes noticenewcourse-opacity {
        0% {
            opacity: 0;
        }

        100% {
            opacity: 1;
        }
    }
}

.infobar-noticenewcourse {
    display: flex;
    position: fixed;
    margin: 0 auto;
    width: 100%;
    top: 50px;
    height: 40px;
    background-color: #dce6e7;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 4000;

    .open-modal {
        flex: 1;
        text-align: center;

        span.more-information-link {
            color: #008044;
            text-decoration: underline;
        }
    }
}

@media (min-width: 1321px) and (max-width: 1919px) {
    .mycourse {
        &.maticesTheme {
            .card-index {
                .card-body {
                    display: flex;
                    flex-direction: row;
                    flex-wrap: wrap;
                    width: 100%;
        
                    .topic_card {
                        position: relative;
                        left: 15px;
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 1200px) {}

@media only screen and (max-width: 1024px) {
    .mycourse {
        &.maticesTheme {
            padding: 0 30px!important;

            .cardindex.matices {
                .card-index {
                    margin: 0;
                    padding: 20px;
                    margin: 10px;

                    .card-deck {
                        min-width: initial!important;

                        .card-body {
                            display: flex;
                            flex-direction: row;
                            flex-wrap: wrap;
                            width: 100%;
                            gap: 20px;
                        }
                    }

                    .topic_card {
                        max-width: 434px;
                        width: 100%;
                        margin-right: 0;
    
                        .wrapper_topic {
                            padding: 0;

                            .topic-col-img {
                                max-width: 430px;;
                                width: 100%;
                                height: 200px;
                            }

                            .topic-col-filter {
                                top: 145px;
                                right: 20px;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 932px) {
    .mycourse {
        &.maticesTheme {
            padding: 0 30px!important;

            .cardindex.matices {
                .card-index {
                    .topic_card {
                        max-width: 384px;
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 896px) {
    .mycourse {
        .infantTheme {
            padding-top: 10px;

            .topic_card {
                padding: 0 5px !important;
                position: relative;
                right: 5px;
                max-width: 100%;
                width: 335px !important;
                max-height: 91px;
                margin-bottom: 0;
                margin: auto;

                .wrapper_topic {
                    max-width: 100%;
                    width: 335px !important;
                    max-height: 91px;
                    border-radius: 40px 20px;
                }

                #dotsmenu {
                    position: absolute;
                    top: 5px;
                    right: 5px;
                    margin: 0;
                    padding: 0;

                    .dots {
                        height: 24px;
                        width: 24px;
                    }
                }

                &.npe_topic_black {
                    #dotsmenu {
                        .dots {
                            background-color: $npe-black !important;
                            border-color: $npe-black;
                        }
                    }

                    .wrapper_topic {
                        border: 3px solid $npe-black !important;
                        background-color: $npe-white;

                        .npe-card-row {
                            .topic-col-info {

                                .unit-number,
                                .topic-unit,
                                .topic_name {
                                    background-color: $npe-black !important;
                                }

                                .topic-unit {
                                    font-size: 10px;
                                    right: 35px;
                                    top: 6px;

                                    .areaname {
                                        position: initial;
                                    }

                                    &.nomenu {
                                        right: 5px !important;
                                        top: 4px !important;
                                    }
                                }
                            }

                            .topic-col-img {
                                height: 85px;
                                width: 100%;
                            }

                        }

                        .view-move-topic {
                            .content-icon-eye-open-topic {
                                i {
                                    background-color: $npe-black;
                                    left: -16px !important;
                                    top: -16.5px !important;
                                    background-position: 3px 4px;
                                    background-size: 18px 18px;
                                    box-shadow: none;
                                    width: 25px;
                                    height: 25px;
                                }
                            }

                            .content-icon-move-topic {
                                i {
                                    background-image: url([[pix:theme|move]]);
                                }
                            }
                        }
                    }

                    &.standard {
                        .view-move-topic {
                            .content-icon-eye-open-topic {
                                i {
                                    left: 8px;
                                    top: -20.5px;
                                    width: 34px;
                                    height: 34px;

                                    @media (max-width: 799px) {
                                        left: -14px !important;
                                        top: -33px !important;
                                        width: 27px;
                                        height: 27px;
                                    }
                                }
                            }
                        }
                    }

                    &.simple {
                        .wrapper_topic {
                            .npe-card-row {
                                .topic-col-img {
                                    width: 165px;
                                    height: 165px;
                                }

                                .topic-col-info {
                                    .topic_name {
                                        background-color: $npe-white !important;
                                    }
                                }
                            }
                        }
                    }
                }

                &.simple {
                    .npe-card-row {
                        .topic-col-img {
                            height: 85px !important;
                            width: 90px !important;

                            img {
                                border-radius: 37px 17px;
                            }
                        }

                        .topic-col-info {
                            .unit-number {
                                width: 50px;
                                height: 46px;
                                font-size: 24px;
                                margin-top: 5px;
                                margin-left: -5px;

                                .col-12 {
                                    line-height: 50px;
                                }
                            }

                            .topic-unit {
                                font-size: 10px;
                                right: 35px;
                                top: 6px;

                                .areaname {
                                    position: initial;
                                }

                                &.nomenu {
                                    right: 5px !important;
                                    top: 4px !important;
                                }
                            }

                            .topic-col-info-text {
                                .topic_name {
                                    padding: 10px 0px 5px 0px;

                                    .info-text-title {
                                        font-size: 14px;
                                    }
                                }
                            }
                        }
                    }
                }

                &.standard {
                    .npe-card-row {
                        .topic-col-img {
                            height: 85px;
                            width: 100%;

                            img {
                                border-radius: 37px 17px;
                            }
                        }

                        .topic-col-info {
                            .unit-number {
                                width: 42px;
                                height: 38px;
                                font-size: 24px;
                                margin-top: 20px;
                                margin-left: -5px;

                                .col-12 {
                                    line-height: 40px;
                                }
                            }

                            .topic-unit {
                                font-size: 10px;
                                right: 35px;
                                top: 6px;

                                .areaname {
                                    position: initial;
                                }

                                &.nomenu {
                                    right: 5px !important;
                                    top: 4px !important;
                                }
                            }

                            .topic-col-info-text {
                                .topic_name {
                                    width: 90%;
                                    right: 30px;

                                    .info-text-title {
                                        font-size: 14px;
                                        -webkit-line-clamp: 2;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        &.maticesTheme {
            padding: 0 30px!important;

            .cardindex.matices {
                .card-index {
                    .topic_card {
                        max-width: 350px;
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 768px) {
    .mycourse {
        &.maticesTheme {
            padding: 0 30px!important;

            .cardindex.matices {
                .card-index {
                    .topic_card {
                        max-width: none;
                        height: 400px;

                        .wrapper_topic {
                            padding: 0;

                            .topic-col-img {
                                max-width: none;
                                width: 100%;
                                height: 320px;
                            }

                            .topic-col-filter {
                                top: 265px;
                                right: 20px;
                            }

                            .topic-col-info {
                                .topic-col-info-text {
                                    #dotsmenu {
                                        .dropdown-menu {
                                            transform: translate3d(35px, 24px, 0px)!important;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 430px) {
    .mycourse {
        .infantTheme {
            padding-top: 10px;

            .topic_card {
                width: 310px !important;
            }
        }

        &.maticesTheme {
            padding: 20px 20px 20px!important;

            .header_course {
                .other_info {
                    .logo_info {
                        margin-bottom: 40px;

                        .logo {
                            svg {
                                height: 126px !important;
                            }
                        }
                    }
                } 
            }

            .cardindex.matices {
                .card-index {
                    margin: 0;
                    padding: 15px;
                    margin-bottom: 30px;

                    .topic_card {
                        max-width: 358px;
                        width: 100%;
                        height: initial;
                        margin-bottom: 10px;
                        

                        .wrapper_topic {
                            padding: 0;

                            .topic-col-img {
                                max-width: 358px;
                                width: 100%;
                                height: 179px;
                            }

                            .topic-col-filter {
                                top: 125px;
                                right: 15px;
                            }

                            .topic-col-info {
                                .topic-col-info-text {
                                    #dotsmenu {
                                        .dropdown-menu {
                                            transform: translate3d(35px, 24px, 0px)!important;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}   
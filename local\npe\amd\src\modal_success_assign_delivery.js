import $ from 'jquery';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';

const SELECTORS = {
    HIDE: '.close',
    FINISH: '[data-action="cancel"]',
};

let registered = false;

export default class ModalSuccessAssignDelivery extends Modal {

    static TYPE = 'local_npe/modal_success_assign_delivery';
    static TEMPLATE = 'local_npe/assignments/modal_success_assign_delivery';

    constructor(root) {
        super(root);
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.success-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.success-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
        });
    }
}

if (!registered) {
    ModalRegistry.register(ModalSuccessAssignDelivery.TYPE, ModalSuccessAssignDelivery, ModalSuccessAssignDelivery.TEMPLATE);
    registered = true;
}

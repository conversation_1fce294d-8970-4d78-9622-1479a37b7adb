define(['jquery'], ($) => {
    'use strict';

    const MARGIN = 20;
    const MARGIN_TOP = 155;
    const MARGIN_RIGHT = 5;

    const adjustTopicFilterPosition = () => {
        $('.maticesTheme .topic-col-filter').each((_, element) => {
            const $filter = $(element);
            const $topicColImg = $filter.closest('.topic_card').find('.topic-col-img');

            if ($topicColImg.length > 0) {
                const imgHeight = $topicColImg.outerHeight();
                const rightPosition = MARGIN;
                let topPosition = imgHeight - $filter.outerHeight() - MARGIN;

                if (topPosition < 0) {
                    topPosition = MARGIN;
                }

                $filter.css({
                    right: `${rightPosition}px`,
                    top: `${topPosition}px`,
                    position: 'absolute'
                });
            }
        });

        $('.maticesTheme .view-move-topic-low').each((_, element) => {
            const $moveElement = $(element);
            const $topicColImg = $moveElement.closest('.topic_card').find('.topic-col-img');

            if ($topicColImg.length > 0) {
                const imgHeight = $topicColImg.outerHeight();

                const rightPosition = MARGIN_RIGHT;
                let topPosition = imgHeight - $moveElement.outerHeight() - MARGIN_TOP;

                if (topPosition < 0) {
                    topPosition = MARGIN_TOP;
                }

                $moveElement.css({
                    right: `${rightPosition}px`,
                    top: `${topPosition}px`,
                    position: 'absolute'
                });
            }
        });
    };

    const init = () => {
        $(document).ready(() => {
            adjustTopicFilterPosition();
        });

        let resizeTimeout;
        $(window).on('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(adjustTopicFilterPosition, 50);
        });

        // Adjust when new content is loaded (for dynamic content)
        $(document).on('DOMNodeInserted', '.topic_card', () => {
            setTimeout(adjustTopicFilterPosition, 100);
        });

        // Also adjust when images load (affects card height)
        $(document).on('load', '.topic_card img', () => {
            setTimeout(adjustTopicFilterPosition, 50);
        });

        // Adjust on orientation change (mobile devices)
        $(window).on('orientationchange', () => {
            setTimeout(adjustTopicFilterPosition, 300);
        });
    };

    return {
        init: init,
        adjustPosition: adjustTopicFilterPosition
    };
});

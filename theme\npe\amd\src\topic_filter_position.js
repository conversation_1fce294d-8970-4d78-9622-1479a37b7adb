/**
 * Dynamic positioning for topic-col-filter elements in responsive design
 * Positions filter in bottom-right corner of topic_card with 20px margin
 * Works on all breakpoints, not just mobile
 */

define(['jquery'], function($) {
    'use strict';

    /**
     * Adjust topic filter position based on topic_card size
     * Always positions in bottom-right corner with 20px margin
     */
    function adjustTopicFilterPosition() {
        $('.maticesTheme .topic-col-filter').each(function() {
            var $filter = $(this);
            var $topicCard = $filter.closest('.topic_card');

            if ($topicCard.length > 0) {
                var cardHeight = $topicCard.outerHeight();
                var margin = 20;

                // Position in bottom-right corner with 20px margin
                var rightPosition = margin;
                var topPosition = cardHeight - $filter.outerHeight() - margin;

                // Ensure minimum position values
                if (topPosition < 0) {
                    topPosition = margin;
                }

                // Apply the calculated position
                $filter.css({
                    'right': rightPosition + 'px',
                    'top': topPosition + 'px',
                    'position': 'absolute'
                });
            }
        });
    }

    /**
     * Initialize the positioning system
     */
    function init() {
        // Initial adjustment after DOM is ready
        $(document).ready(function() {
            adjustTopicFilterPosition();
        });

        // Adjust on window resize with debouncing
        var resizeTimeout;
        $(window).on('resize', function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(adjustTopicFilterPosition, 150);
        });

        // Adjust when new content is loaded (for dynamic content)
        $(document).on('DOMNodeInserted', '.topic_card', function() {
            setTimeout(adjustTopicFilterPosition, 100);
        });

        // Also adjust when images load (affects card height)
        $(document).on('load', '.topic_card img', function() {
            setTimeout(adjustTopicFilterPosition, 50);
        });

        // Adjust on orientation change (mobile devices)
        $(window).on('orientationchange', function() {
            setTimeout(adjustTopicFilterPosition, 300);
        });
    }

    return {
        init: init,
        adjustPosition: adjustTopicFilterPosition
    };
});

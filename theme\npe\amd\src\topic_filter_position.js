/**
 * Dynamic positioning for topic-col-filter elements in responsive design
 * Adjusts position based on container size for screens ≤799px
 */

define(['jquery'], function($) {
    'use strict';

    /**
     * Adjust topic filter position based on container size
     */
    function adjustTopicFilterPosition() {
        // Only apply for screens ≤799px
        if (window.innerWidth <= 799) {
            $('.maticesTheme .topic-col-filter').each(function() {
                var $filter = $(this);
                var $container = $filter.closest('.wrapper_topic');

                if ($container.length > 0) {
                    var containerWidth = $container.outerWidth();
                    var containerHeight = $container.outerHeight();

                    // Calculate proportional position
                    // Base position: right: 20px, top: 280px for a reference container
                    var baseContainerWidth = 720; // Reference width
                    var baseContainerHeight = 420; // Reference height
                    var baseRight = 20;
                    var baseTop = 280;

                    // Calculate proportional values
                    var proportionalRight = (baseRight / baseContainerWidth) * containerWidth;
                    var proportionalTop = (baseTop / baseContainerHeight) * containerHeight;

                    // Apply the calculated position
                    $filter.css({
                        'right': proportionalRight + 'px',
                        'top': proportionalTop + 'px'
                    });
                }
            });
        } else {
            // Reset to CSS defaults for larger screens
            $('.maticesTheme .topic-col-filter').css({
                'right': '',
                'top': ''
            });
        }
    }

    /**
     * Initialize the positioning system
     */
    function init() {
        // Initial adjustment
        adjustTopicFilterPosition();

        // Adjust on window resize with debouncing
        var resizeTimeout;
        $(window).on('resize', function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(adjustTopicFilterPosition, 150);
        });

        // Adjust when new content is loaded (for dynamic content)
        $(document).on('DOMNodeInserted', '.topic_card', function() {
            setTimeout(adjustTopicFilterPosition, 100);
        });
    }

    return {
        init: init,
        adjustPosition: adjustTopicFilterPosition
    };
});

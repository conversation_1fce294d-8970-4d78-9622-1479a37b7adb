define(['jquery'], ($) => {
    'use strict';

    const MARGIN = 20;

    const adjustTopicFilterPosition = () => {
        $('.maticesTheme .topic-col-filter').each((_, element) => {
            const $filter = $(element);
            const $topicColImg = $filter.closest('.topic_card').find('.topic-col-img');

            if ($topicColImg.length > 0) {
                const imgHeight = $topicColImg.outerHeight();

                // Position in bottom-right corner with 20px margin relative to topic-col-img
                const rightPosition = MARGIN;
                let topPosition = imgHeight - $filter.outerHeight() - MARGIN;

                // Ensure minimum position values
                if (topPosition < 0) {
                    topPosition = MARGIN;
                }

                // Apply the calculated position
                $filter.css({
                    right: `${rightPosition}px`,
                    top: `${topPosition}px`,
                    position: 'absolute'
                });
            }
        });
    };

    /**
     * Initialize the positioning system
     */
    const init = () => {
        // Initial adjustment after DOM is ready
        $(document).ready(() => {
            adjustTopicFilterPosition();
        });

        // Adjust on window resize with debouncing
        let resizeTimeout;
        $(window).on('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(adjustTopicFilterPosition, 50);
        });

        // Adjust when new content is loaded (for dynamic content)
        $(document).on('DOMNodeInserted', '.topic_card', () => {
            setTimeout(adjustTopicFilterPosition, 100);
        });

        // Also adjust when images load (affects card height)
        $(document).on('load', '.topic_card img', () => {
            setTimeout(adjustTopicFilterPosition, 50);
        });

        // Adjust on orientation change (mobile devices)
        $(window).on('orientationchange', () => {
            setTimeout(adjustTopicFilterPosition, 300);
        });
    };

    return {
        init: init,
        adjustPosition: adjustTopicFilterPosition
    };
});

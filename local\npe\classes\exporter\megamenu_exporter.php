<?php

namespace local_npe\exporter;

use core\external\exporter;
use local_npe\course;
use local_npe\DTO\course_package_dto;

class megamenu_exporter extends exporter {

    protected static function define_related() {

        return array(
            "courseid" => PARAM_INT,
            "pagetype" => "string",
            );
    }

    protected static function define_other_properties() {

        return [

            'init' => [
                'type' => PARAM_RAW
            ],
            'myclass' => [
                'type' => PARAM_RAW
            ],
            'library' => [
                'type' => PARAM_RAW
            ],
            'evaluation' => [
                'type' => PARAM_RAW
            ],
            'comunication' => [
                'type' => PARAM_RAW
            ],
            'hasteachers' => [
                'type' => PARAM_BOOL
            ],
            'enrolmentkey' => [
                'type' => PARAM_RAW
            ],
            'courseid' => [
                'type' => PARAM_RAW
            ],
            'alertsharecode' => [
                'type' => PARAM_BOOL
            ],
            'pagetype' => [
                'type' => PARAM_RAW
            ],
            'iseducamoscourse' => [
                'type' => PARAM_BOOL
            ],
            'islti' => [
                'type' => PARAM_BOOL
            ],
        ];
    }

    /**
     * @param \renderer_base $output
     * @return array
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\user_exception
     * @throws \moodle_exception
     */
    protected function get_other_values(\renderer_base $output) {

        $megamenudata = new megamenu_data(
            $this->related['courseid'],
            $this->related['pagetype']
        );
        $megamenudata->set_params();
        return $megamenudata->to_array();
    }
}

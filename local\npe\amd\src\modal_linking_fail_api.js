import $ from 'jquery';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';

const SELECTORS = {
    FINISH: '[data-action="cancel"]',
    EXIT: '[data-action="confirm"]',
};

let registered = false;

export default class ModalLinkingCCAAFailApi extends Modal {

    static TYPE = 'local_npe/modal_linking_ccaa_fail_api';
    static TEMPLATE = 'local_npe/courses/modal_linking_ccaa_fail';

    constructor(root) {
        super(root);
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.success-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
            window.location.reload();
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.EXIT, () => {
            window.location.reload();
        });
    }
}

if (!registered) {
    ModalRegistry.register(ModalLinkingCCAAFailApi.TYPE, ModalLinkingCCAAFailApi, ModalLinkingCCAAFailApi.TEMPLATE);
    registered = true;
}

import $ from 'jquery';
import Notification from 'core/notification';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';
import Str from 'core/str';

const SELECTORS = {
    MODALBOX: '.modal_text',
    HIDE: '.close',
    CONFIRM: '[data-action="confirm"]',
};

let registered = false;

export default class ModalTopicWoContent extends Modal {

    static TYPE = 'local_npe/modal_topic_wo_content';
    static TEMPLATE = 'local_npe/courses/modal_topic_wo_content';

    constructor(root) {
        super(root);
    }

    setData(text) {
        if (text === 'single') {
            Str.get_string('conflict_message', 'local_npe').done((string) => {
                this.getRoot().find(SELECTORS.MODALBOX).html(string);
            }).fail(Notification.exception);
        } else if (text === 'multiple') {
            Str.get_string('conflict_several_teams_message', 'local_npe').done((string) => {
                this.getRoot().find(SELECTORS.MODALBOX).html(string);
            }).fail(Notification.exception);
        }
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.CONFIRM, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
        });
    }
}

if (!registered) {
    ModalRegistry.register(ModalTopicWoContent.TYPE, ModalTopicWoContent, ModalTopicWoContent.TEMPLATE);
    registered = true;
}

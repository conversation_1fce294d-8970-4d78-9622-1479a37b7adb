{"version": 3, "file": "topic_filter_position.min.js", "sources": ["../src/topic_filter_position.js"], "sourcesContent": ["define(['jquery'], function($) {\n    'use strict';\n\n    /**\n     * Adjust topic filter position based on topic-col-img size\n     * Always positions in bottom-right corner with 20px margin\n     */\n    function adjustTopicFilterPosition() {\n        $('.maticesTheme .topic-col-filter').each(function() {\n            var $filter = $(this);\n            var $topicColImg = $filter.closest('.topic_card').find('.topic-col-img');\n\n            if ($topicColImg.length > 0) {\n                var imgHeight = $topicColImg.outerHeight();\n                var margin = 20;\n\n                // Position in bottom-right corner with 20px margin relative to topic-col-img\n                var rightPosition = margin;\n                var topPosition = imgHeight - $filter.outerHeight() - margin;\n\n                // Ensure minimum position values\n                if (topPosition < 0) {\n                    topPosition = margin;\n                }\n\n                // Apply the calculated position\n                $filter.css({\n                    'right': rightPosition + 'px',\n                    'top': topPosition + 'px',\n                    'position': 'absolute'\n                });\n            }\n        });\n    }\n\n    /**\n     * Initialize the positioning system\n     */\n    function init() {\n        // Initial adjustment after DOM is ready\n        $(document).ready(function() {\n            adjustTopicFilterPosition();\n        });\n\n        // Adjust on window resize with debouncing\n        var resizeTimeout;\n        $(window).on('resize', function() {\n            clearTimeout(resizeTimeout);\n            resizeTimeout = setTimeout(adjustTopicFilterPosition, 150);\n        });\n\n        // Adjust when new content is loaded (for dynamic content)\n        $(document).on('DOMNodeInserted', '.topic_card', function() {\n            setTimeout(adjustTopicFilterPosition, 100);\n        });\n\n        // Also adjust when images load (affects card height)\n        $(document).on('load', '.topic_card img', function() {\n            setTimeout(adjustTopicFilterPosition, 50);\n        });\n\n        // Adjust on orientation change (mobile devices)\n        $(window).on('orientationchange', function() {\n            setTimeout(adjustTopicFilterPosition, 300);\n        });\n    }\n\n    return {\n        init: init,\n        adjustPosition: adjustTopicFilterPosition\n    };\n});\n"], "names": ["define", "$", "adjustTopicFilterPosition", "each", "$filter", "this", "$topicColImg", "closest", "find", "length", "topPosition", "outerHeight", "css", "rightPosition", "init", "resizeTimeout", "document", "ready", "window", "on", "clearTimeout", "setTimeout", "adjustPosition"], "mappings": "AAAAA,yCAAO,CAAC,WAAW,SAASC,YAOfC,4BACLD,EAAE,mCAAmCE,MAAK,eAClCC,QAAUH,EAAEI,MACZC,aAAeF,QAAQG,QAAQ,eAAeC,KAAK,qBAEnDF,aAAaG,OAAS,EAAG,KAMrBC,YALYJ,aAAaK,cAKCP,QAAQO,cAJzB,GAOTD,YAAc,IACdA,YARS,IAYbN,QAAQQ,IAAI,OACCC,WACFH,YAAc,cACT,uBAsCrB,CACHI,oBAvBIC,cALJd,EAAEe,UAAUC,OAAM,WACdf,+BAKJD,EAAEiB,QAAQC,GAAG,UAAU,WACnBC,aAAaL,eACbA,cAAgBM,WAAWnB,0BAA2B,QAI1DD,EAAEe,UAAUG,GAAG,kBAAmB,eAAe,WAC7CE,WAAWnB,0BAA2B,QAI1CD,EAAEe,UAAUG,GAAG,OAAQ,mBAAmB,WACtCE,WAAWnB,0BAA2B,OAI1CD,EAAEiB,QAAQC,GAAG,qBAAqB,WAC9BE,WAAWnB,0BAA2B,SAM1CoB,eAAgBpB,0BAEvB"}
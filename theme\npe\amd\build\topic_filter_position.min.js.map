{"version": 3, "file": "topic_filter_position.min.js", "sources": ["../src/topic_filter_position.js"], "sourcesContent": ["define(['jquery'], ($) => {\n    'use strict';\n\n    const MARGIN = 20;\n    const MARGIN_TOP = 155;\n    const MARGIN_RIGHT = 5;\n\n    const adjustTopicFilterPosition = () => {\n        $('.maticesTheme .topic-col-filter').each((_, element) => {\n            const $filter = $(element);\n            const $topicColImg = $filter.closest('.topic_card').find('.topic-col-img');\n\n            if ($topicColImg.length > 0) {\n                const imgHeight = $topicColImg.outerHeight();\n                const rightPosition = MARGIN;\n                let topPosition = imgHeight - $filter.outerHeight() - MARGIN;\n\n                if (topPosition < 0) {\n                    topPosition = MARGIN;\n                }\n\n                $filter.css({\n                    right: `${rightPosition}px`,\n                    top: `${topPosition}px`,\n                    position: 'absolute'\n                });\n            }\n        });\n\n        $('.maticesTheme .view-move-topic-low').each((_, element) => {\n            const $moveElement = $(element);\n            const $topicColImg = $moveElement.closest('.topic_card').find('.topic-col-img');\n\n            if ($topicColImg.length > 0) {\n                const imgHeight = $topicColImg.outerHeight();\n\n                const rightPosition = MARGIN_RIGHT;\n                let topPosition = imgHeight - $moveElement.outerHeight() - MARGIN_TOP;\n\n                if (topPosition < 0) {\n                    topPosition = MARGIN_TOP;\n                }\n\n                $moveElement.css({\n                    right: `${rightPosition}px`,\n                    top: `${topPosition}px`,\n                    position: 'absolute'\n                });\n            }\n        });\n    };\n\n    const init = () => {\n        $(document).ready(() => {\n            adjustTopicFilterPosition();\n        });\n\n        let resizeTimeout;\n        $(window).on('resize', () => {\n            clearTimeout(resizeTimeout);\n            resizeTimeout = setTimeout(adjustTopicFilterPosition, 50);\n        });\n\n        $(document).on('DOMNodeInserted', '.topic_card', () => {\n            setTimeout(adjustTopicFilterPosition, 100);\n        });\n\n        $(document).on('load', '.topic_card img', () => {\n            setTimeout(adjustTopicFilterPosition, 50);\n        });\n\n        $(window).on('orientationchange', () => {\n            setTimeout(adjustTopicFilterPosition, 300);\n        });\n    };\n\n    return {\n        init: init,\n        adjustPosition: adjustTopicFilterPosition\n    };\n});\n"], "names": ["define", "$", "adjustTopicFilterPosition", "each", "_", "element", "$filter", "$topicColImg", "closest", "find", "length", "rightPosition", "topPosition", "outerHeight", "css", "right", "top", "position", "$moveElement", "init", "resizeTimeout", "document", "ready", "window", "on", "clearTimeout", "setTimeout", "adjustPosition"], "mappings": "AAAAA,yCAAO,CAAC,WAAYC,UAOVC,0BAA4B,KAC9BD,EAAE,mCAAmCE,MAAK,CAACC,EAAGC,iBACpCC,QAAUL,EAAEI,SACZE,aAAeD,QAAQE,QAAQ,eAAeC,KAAK,qBAErDF,aAAaG,OAAS,EAAG,OAEnBC,cAXH,OAYCC,YAFcL,aAAaM,cAEDP,QAAQO,cAZnC,GAcCD,YAAc,IACdA,YAfD,IAkBHN,QAAQQ,IAAI,CACRC,MAAQ,GAAEJ,kBACVK,IAAM,GAAEJ,gBACRK,SAAU,iBAKtBhB,EAAE,sCAAsCE,MAAK,CAACC,EAAGC,iBACvCa,aAAejB,EAAEI,SACjBE,aAAeW,aAAaV,QAAQ,eAAeC,KAAK,qBAE1DF,aAAaG,OAAS,EAAG,OAGnBC,cA/BG,MAgCLC,YAHcL,aAAaM,cAGDK,aAAaL,cAjCpC,IAmCHD,YAAc,IACdA,YApCG,KAuCPM,aAAaJ,IAAI,CACbC,MAAQ,GAAEJ,kBACVK,IAAM,GAAEJ,gBACRK,SAAU,wBA8BnB,CACHE,KAzBS,SAKLC,cAJJnB,EAAEoB,UAAUC,OAAM,KACdpB,+BAIJD,EAAEsB,QAAQC,GAAG,UAAU,KACnBC,aAAaL,eACbA,cAAgBM,WAAWxB,0BAA2B,GAAtD,IAGJD,EAAEoB,UAAUG,GAAG,kBAAmB,eAAe,KAC7CE,WAAWxB,0BAA2B,IAAtC,IAGJD,EAAEoB,UAAUG,GAAG,OAAQ,mBAAmB,KACtCE,WAAWxB,0BAA2B,GAAtC,IAGJD,EAAEsB,QAAQC,GAAG,qBAAqB,KAC9BE,WAAWxB,0BAA2B,IAAtC,KAMJyB,eAAgBzB,0BAFpB"}
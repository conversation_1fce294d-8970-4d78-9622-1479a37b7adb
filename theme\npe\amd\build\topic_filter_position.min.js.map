{"version": 3, "file": "topic_filter_position.min.js", "sources": ["../src/topic_filter_position.js"], "sourcesContent": ["define(['jquery'], ($) => {\n    'use strict';\n\n    const MARGIN = 20;\n\n    const adjustTopicFilterPosition = () => {\n        $('.maticesTheme .topic-col-filter').each((_, element) => {\n            const $filter = $(element);\n            const $topicColImg = $filter.closest('.topic_card').find('.topic-col-img');\n\n            if ($topicColImg.length > 0) {\n                const imgHeight = $topicColImg.outerHeight();\n\n                // Position in bottom-right corner with 20px margin relative to topic-col-img\n                const rightPosition = MARGIN;\n                let topPosition = imgHeight - $filter.outerHeight() - MARGIN;\n\n                // Ensure minimum position values\n                if (topPosition < 0) {\n                    topPosition = MARGIN;\n                }\n\n                // Apply the calculated position\n                $filter.css({\n                    right: `${rightPosition}px`,\n                    top: `${topPosition}px`,\n                    position: 'absolute'\n                });\n            }\n        });\n    };\n\n    /**\n     * Initialize the positioning system\n     */\n    const init = () => {\n        // Initial adjustment after DOM is ready\n        $(document).ready(() => {\n            adjustTopicFilterPosition();\n        });\n\n        // Adjust on window resize with debouncing\n        let resizeTimeout;\n        $(window).on('resize', () => {\n            clearTimeout(resizeTimeout);\n            resizeTimeout = setTimeout(adjustTopicFilterPosition, 50);\n        });\n\n        // Adjust when new content is loaded (for dynamic content)\n        $(document).on('DOMNodeInserted', '.topic_card', () => {\n            setTimeout(adjustTopicFilterPosition, 100);\n        });\n\n        // Also adjust when images load (affects card height)\n        $(document).on('load', '.topic_card img', () => {\n            setTimeout(adjustTopicFilterPosition, 50);\n        });\n\n        // Adjust on orientation change (mobile devices)\n        $(window).on('orientationchange', () => {\n            setTimeout(adjustTopicFilterPosition, 300);\n        });\n    };\n\n    return {\n        init: init,\n        adjustPosition: adjustTopicFilterPosition\n    };\n});\n"], "names": ["define", "$", "adjustTopicFilterPosition", "each", "_", "element", "$filter", "$topicColImg", "closest", "find", "length", "rightPosition", "topPosition", "outerHeight", "css", "right", "top", "position", "init", "resizeTimeout", "document", "ready", "window", "on", "clearTimeout", "setTimeout", "adjustPosition"], "mappings": "AAAAA,yCAAO,CAAC,WAAYC,UAKVC,0BAA4B,KAC9BD,EAAE,mCAAmCE,MAAK,CAACC,EAAGC,iBACpCC,QAAUL,EAAEI,SACZE,aAAeD,QAAQE,QAAQ,eAAeC,KAAK,qBAErDF,aAAaG,OAAS,EAAG,OAInBC,cAXH,OAYCC,YAJcL,aAAaM,cAIDP,QAAQO,cAZnC,GAeCD,YAAc,IACdA,YAhBD,IAoBHN,QAAQQ,IAAI,CACRC,MAAQ,GAAEJ,kBACVK,IAAM,GAAEJ,gBACRK,SAAU,wBAsCnB,CACHC,KA9BS,SAOLC,cALJlB,EAAEmB,UAAUC,OAAM,KACdnB,+BAKJD,EAAEqB,QAAQC,GAAG,UAAU,KACnBC,aAAaL,eACbA,cAAgBM,WAAWvB,0BAA2B,GAAtD,IAIJD,EAAEmB,UAAUG,GAAG,kBAAmB,eAAe,KAC7CE,WAAWvB,0BAA2B,IAAtC,IAIJD,EAAEmB,UAAUG,GAAG,OAAQ,mBAAmB,KACtCE,WAAWvB,0BAA2B,GAAtC,IAIJD,EAAEqB,QAAQC,GAAG,qBAAqB,KAC9BE,WAAWvB,0BAA2B,IAAtC,KAMJwB,eAAgBxB,0BAFpB"}
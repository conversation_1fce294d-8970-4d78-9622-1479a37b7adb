{"version": 3, "file": "topic_filter_position.min.js", "sources": ["../src/topic_filter_position.js"], "sourcesContent": ["/**\n * Dynamic positioning for topic-col-filter elements in responsive design\n * Positions filter in bottom-right corner of topic_card with 20px margin\n * Works on all breakpoints, not just mobile\n */\n\ndefine(['jquery'], function($) {\n    'use strict';\n\n    /**\n     * Adjust topic filter position based on topic_card size\n     * Always positions in bottom-right corner with 20px margin\n     */\n    function adjustTopicFilterPosition() {\n        $('.maticesTheme .topic-col-filter').each(function() {\n            var $filter = $(this);\n            var $topicCard = $filter.closest('.topic_card');\n\n            if ($topicCard.length > 0) {\n                var cardHeight = $topicCard.outerHeight();\n                var margin = 20;\n\n                // Position in bottom-right corner with 20px margin\n                var rightPosition = margin;\n                var topPosition = cardHeight - $filter.outerHeight() - margin;\n\n                // Ensure minimum position values\n                if (topPosition < 0) {\n                    topPosition = margin;\n                }\n\n                // Apply the calculated position\n                $filter.css({\n                    'right': rightPosition + 'px',\n                    'top': topPosition + 'px',\n                    'position': 'absolute'\n                });\n            }\n        });\n    }\n\n    /**\n     * Initialize the positioning system\n     */\n    function init() {\n        // Initial adjustment after DOM is ready\n        $(document).ready(function() {\n            adjustTopicFilterPosition();\n        });\n\n        // Adjust on window resize with debouncing\n        var resizeTimeout;\n        $(window).on('resize', function() {\n            clearTimeout(resizeTimeout);\n            resizeTimeout = setTimeout(adjustTopicFilterPosition, 150);\n        });\n\n        // Adjust when new content is loaded (for dynamic content)\n        $(document).on('DOMNodeInserted', '.topic_card', function() {\n            setTimeout(adjustTopicFilterPosition, 100);\n        });\n\n        // Also adjust when images load (affects card height)\n        $(document).on('load', '.topic_card img', function() {\n            setTimeout(adjustTopicFilterPosition, 50);\n        });\n\n        // Adjust on orientation change (mobile devices)\n        $(window).on('orientationchange', function() {\n            setTimeout(adjustTopicFilterPosition, 300);\n        });\n    }\n\n    return {\n        init: init,\n        adjustPosition: adjustTopicFilterPosition\n    };\n});\n"], "names": ["define", "$", "adjustTopicFilterPosition", "each", "$filter", "this", "$topicCard", "closest", "length", "topPosition", "outerHeight", "css", "rightPosition", "init", "resizeTimeout", "document", "ready", "window", "on", "clearTimeout", "setTimeout", "adjustPosition"], "mappings": "AAMAA,yCAAO,CAAC,WAAW,SAASC,YAOfC,4BACLD,EAAE,mCAAmCE,MAAK,eAClCC,QAAUH,EAAEI,MACZC,WAAaF,QAAQG,QAAQ,kBAE7BD,WAAWE,OAAS,EAAG,KAMnBC,YALaH,WAAWI,cAKGN,QAAQM,cAJ1B,GAOTD,YAAc,IACdA,YARS,IAYbL,QAAQO,IAAI,OACCC,WACFH,YAAc,cACT,uBAsCrB,CACHI,oBAvBIC,cALJb,EAAEc,UAAUC,OAAM,WACdd,+BAKJD,EAAEgB,QAAQC,GAAG,UAAU,WACnBC,aAAaL,eACbA,cAAgBM,WAAWlB,0BAA2B,QAI1DD,EAAEc,UAAUG,GAAG,kBAAmB,eAAe,WAC7CE,WAAWlB,0BAA2B,QAI1CD,EAAEc,UAAUG,GAAG,OAAQ,mBAAmB,WACtCE,WAAWlB,0BAA2B,OAI1CD,EAAEgB,QAAQC,GAAG,qBAAqB,WAC9BE,WAAWlB,0BAA2B,SAM1CmB,eAAgBnB,0BAEvB"}
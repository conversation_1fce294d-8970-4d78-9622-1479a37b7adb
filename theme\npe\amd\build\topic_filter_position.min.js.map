{"version": 3, "file": "topic_filter_position.min.js", "sources": ["../src/topic_filter_position.js"], "sourcesContent": ["/**\n * Dynamic positioning for topic-col-filter elements in responsive design\n * Adjusts position based on container size for screens ≤799px\n */\n\ndefine(['jquery'], function($) {\n    'use strict';\n\n    /**\n     * Adjust topic filter position based on container size\n     */\n    function adjustTopicFilterPosition() {\n        // Only apply for screens ≤799px\n        if (window.innerWidth <= 799) {\n            $('.maticesTheme .topic-col-filter').each(function() {\n                var $filter = $(this);\n                var $container = $filter.closest('.wrapper_topic');\n\n                if ($container.length > 0) {\n                    var containerWidth = $container.outerWidth();\n                    var containerHeight = $container.outerHeight();\n\n                    // Calculate proportional position\n                    // Base position: right: 20px, top: 280px for a reference container\n                    var baseContainerWidth = 720; // Reference width\n                    var baseContainerHeight = 420; // Reference height\n                    var baseRight = 20;\n                    var baseTop = 280;\n\n                    // Calculate proportional values\n                    var proportionalRight = (baseRight / baseContainerWidth) * containerWidth;\n                    var proportionalTop = (baseTop / baseContainerHeight) * containerHeight;\n\n                    // Apply the calculated position\n                    $filter.css({\n                        'right': proportionalRight + 'px',\n                        'top': proportionalTop + 'px'\n                    });\n                }\n            });\n        } else {\n            // Reset to CSS defaults for larger screens\n            $('.maticesTheme .topic-col-filter').css({\n                'right': '',\n                'top': ''\n            });\n        }\n    }\n\n    /**\n     * Initialize the positioning system\n     */\n    function init() {\n        // Initial adjustment\n        adjustTopicFilterPosition();\n\n        // Adjust on window resize with debouncing\n        var resizeTimeout;\n        $(window).on('resize', function() {\n            clearTimeout(resizeTimeout);\n            resizeTimeout = setTimeout(adjustTopicFilterPosition, 150);\n        });\n\n        // Adjust when new content is loaded (for dynamic content)\n        $(document).on('DOMNodeInserted', '.topic_card', function() {\n            setTimeout(adjustTopicFilterPosition, 100);\n        });\n    }\n\n    return {\n        init: init,\n        adjustPosition: adjustTopicFilterPosition\n    };\n});\n"], "names": ["define", "$", "adjustTopicFilterPosition", "window", "innerWidth", "each", "$filter", "this", "$container", "closest", "length", "proportionalRight", "outerWidth", "proportionalTop", "outerHeight", "css", "init", "resizeTimeout", "on", "clearTimeout", "setTimeout", "document", "adjustPosition"], "mappings": "AAKAA,yCAAO,CAAC,WAAW,SAASC,YAMfC,4BAEDC,OAAOC,YAAc,IACrBH,EAAE,mCAAmCI,MAAK,eAClCC,QAAUL,EAAEM,MACZC,WAAaF,QAAQG,QAAQ,qBAE7BD,WAAWE,OAAS,EAAG,KAYnBC,kBAJY,GAFS,IALJH,WAAWI,aAY5BC,gBAJU,IAFY,IALJL,WAAWM,cAcjCR,QAAQS,IAAI,OACCJ,kBAAoB,SACtBE,gBAAkB,WAMrCZ,EAAE,mCAAmCc,IAAI,OAC5B,OACF,WAyBZ,CACHC,oBAbIC,cAHJf,4BAIAD,EAAEE,QAAQe,GAAG,UAAU,WACnBC,aAAaF,eACbA,cAAgBG,WAAWlB,0BAA2B,QAI1DD,EAAEoB,UAAUH,GAAG,kBAAmB,eAAe,WAC7CE,WAAWlB,0BAA2B,SAM1CoB,eAAgBpB,0BAEvB"}
{"version": 3, "file": "topic_filter_position.min.js", "sources": ["../src/topic_filter_position.js"], "sourcesContent": ["define(['jquery'], ($) => {\n    'use strict';\n\n    const MARGIN = 20;\n    const MARGIN_ALT = 155;\n\n    const adjustTopicFilterPosition = () => {\n        // Handle .topic-col-filter elements\n        $('.maticesTheme .topic-col-filter').each((_, element) => {\n            const $filter = $(element);\n            const $topicColImg = $filter.closest('.topic_card').find('.topic-col-img');\n\n            if ($topicColImg.length > 0) {\n                const imgHeight = $topicColImg.outerHeight();\n\n                // Position in bottom-right corner with 20px margin relative to topic-col-img\n                const rightPosition = MARGIN;\n                let topPosition = imgHeight - $filter.outerHeight() - MARGIN;\n\n                // Ensure minimum position values\n                if (topPosition < 0) {\n                    topPosition = MARGIN;\n                }\n\n                // Apply the calculated position\n                $filter.css({\n                    right: `${rightPosition}px`,\n                    top: `${topPosition}px`,\n                    position: 'absolute'\n                });\n            }\n        });\n\n        $('.maticesTheme .view-move-topic-low').each((_, element) => {\n            const $moveElement = $(element);\n            const $topicColImg = $moveElement.closest('.topic_card').find('.topic-col-img');\n\n            if ($topicColImg.length > 0) {\n                const imgHeight = $topicColImg.outerHeight();\n\n                // Position in bottom-right corner with 20px margin relative to topic-col-img\n                const rightPosition = MARGIN;\n                let topPosition = imgHeight - $moveElement.outerHeight() - MARGIN_ALT;\n\n                // Ensure minimum position values\n                if (topPosition < 0) {\n                    topPosition = MARGIN_ALT;\n                }\n\n                // Apply the calculated position\n                $moveElement.css({\n                    right: `${rightPosition}px`,\n                    top: `${topPosition}px`,\n                    position: 'absolute'\n                });\n            }\n        });\n    };\n\n    /**\n     * Initialize the positioning system\n     */\n    const init = () => {\n        // Initial adjustment after DOM is ready\n        $(document).ready(() => {\n            adjustTopicFilterPosition();\n        });\n\n        // Adjust on window resize with debouncing\n        let resizeTimeout;\n        $(window).on('resize', () => {\n            clearTimeout(resizeTimeout);\n            resizeTimeout = setTimeout(adjustTopicFilterPosition, 50);\n        });\n\n        // Adjust when new content is loaded (for dynamic content)\n        $(document).on('DOMNodeInserted', '.topic_card', () => {\n            setTimeout(adjustTopicFilterPosition, 100);\n        });\n\n        // Also adjust when images load (affects card height)\n        $(document).on('load', '.topic_card img', () => {\n            setTimeout(adjustTopicFilterPosition, 50);\n        });\n\n        // Adjust on orientation change (mobile devices)\n        $(window).on('orientationchange', () => {\n            setTimeout(adjustTopicFilterPosition, 300);\n        });\n    };\n\n    return {\n        init: init,\n        adjustPosition: adjustTopicFilterPosition\n    };\n});\n"], "names": ["define", "$", "adjustTopicFilterPosition", "each", "_", "element", "$filter", "$topicColImg", "closest", "find", "length", "rightPosition", "topPosition", "outerHeight", "css", "right", "top", "position", "$moveElement", "init", "resizeTimeout", "document", "ready", "window", "on", "clearTimeout", "setTimeout", "adjustPosition"], "mappings": "AAAAA,yCAAO,CAAC,WAAYC,UAMVC,0BAA4B,KAE9BD,EAAE,mCAAmCE,MAAK,CAACC,EAAGC,iBACpCC,QAAUL,EAAEI,SACZE,aAAeD,QAAQE,QAAQ,eAAeC,KAAK,qBAErDF,aAAaG,OAAS,EAAG,OAInBC,cAbH,OAcCC,YAJcL,aAAaM,cAIDP,QAAQO,cAdnC,GAiBCD,YAAc,IACdA,YAlBD,IAsBHN,QAAQQ,IAAI,CACRC,MAAQ,GAAEJ,kBACVK,IAAM,GAAEJ,gBACRK,SAAU,iBAKtBhB,EAAE,sCAAsCE,MAAK,CAACC,EAAGC,iBACvCa,aAAejB,EAAEI,SACjBE,aAAeW,aAAaV,QAAQ,eAAeC,KAAK,qBAE1DF,aAAaG,OAAS,EAAG,OAInBC,cAtCH,OAuCCC,YAJcL,aAAaM,cAIDK,aAAaL,cAtCpC,IAyCHD,YAAc,IACdA,YA1CG,KA8CPM,aAAaJ,IAAI,CACbC,MAAQ,GAAEJ,kBACVK,IAAM,GAAEJ,gBACRK,SAAU,wBAsCnB,CACHE,KA9BS,SAOLC,cALJnB,EAAEoB,UAAUC,OAAM,KACdpB,+BAKJD,EAAEsB,QAAQC,GAAG,UAAU,KACnBC,aAAaL,eACbA,cAAgBM,WAAWxB,0BAA2B,GAAtD,IAIJD,EAAEoB,UAAUG,GAAG,kBAAmB,eAAe,KAC7CE,WAAWxB,0BAA2B,IAAtC,IAIJD,EAAEoB,UAAUG,GAAG,OAAQ,mBAAmB,KACtCE,WAAWxB,0BAA2B,GAAtC,IAIJD,EAAEsB,QAAQC,GAAG,qBAAqB,KAC9BE,WAAWxB,0BAA2B,IAAtC,KAMJyB,eAAgBzB,0BAFpB"}
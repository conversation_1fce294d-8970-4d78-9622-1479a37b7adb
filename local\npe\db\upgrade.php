<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * This file contains datbase upgrade code that is called from local/npe/db/upgrade.php,
 *
 * @package    local_npe
 * @copyright  2021 SM
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */


defined('MOODLE_INTERNAL') || die();

use local_npe\db\records_npe;

/**
 * @throws ddl_exception
 * @throws upgrade_exception
 * @throws downgrade_exception
 */
function xmldb_local_npe_upgrade($oldversion) {
    global $DB;
    $dbman = $DB->get_manager(); // Loads ddl manager and xmldb classes.
    if ($oldversion < **********) {
        // Define table to be created.
        $table = new xmldb_table('npeplugins_enrolment_keys');
        // Adding fields to table quiz_sections.
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null, null);
        $table->add_field('enrolment', XMLDB_TYPE_CHAR, '10', null, XMLDB_NOTNULL, null, null, 'id');
        $table->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'enrolment');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'status');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'usermodified');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'timecreated');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }
        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < 2021071204) {
        // Define table to be created.
        $table = new xmldb_table('npeplugins_tag');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null, null);
        $table->add_field('tag', XMLDB_TYPE_CHAR, '50', null, XMLDB_NOTNULL, null, null, 'id');
        $table->add_field('color', XMLDB_TYPE_CHAR, '10', null, XMLDB_NOTNULL, null, null, 'tag');
        $table->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'color');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'courseid');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'usermodified');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'timecreated');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $tagindex = new xmldb_index('idx_npeplugins_tag_tag', XMLDB_INDEX_NOTUNIQUE, array('tag'));
        $courseindex = new xmldb_index('idx_npeplugins_tag_courseid', XMLDB_INDEX_NOTUNIQUE, array('courseid'));

        if (!$dbman->index_exists($table, $tagindex)) {
            $dbman->add_index($table, $tagindex);
        }

        if (!$dbman->index_exists($table, $courseindex)) {
            $dbman->add_index($table, $courseindex);
        }

        // Define table to be created.
        $table = new xmldb_table('npeplugins_team_tag');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null, null);
        $table->add_field('teamid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null, 'id');
        $table->add_field('tagid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null, 'teamid');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'tagid');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'usermodified');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'timecreated');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $teamid = new xmldb_index('idx_npeplugins_team_tag_teamid', XMLDB_INDEX_NOTUNIQUE, array('teamid'));
        $tagid = new xmldb_index('idx_npeplugins_team_tag_tagid', XMLDB_INDEX_NOTUNIQUE, array('tagid'));

        if (!$dbman->index_exists($table, $teamid)) {
            $dbman->add_index($table, $teamid);
        }

        if (!$dbman->index_exists($table, $tagid)) {
            $dbman->add_index($table, $tagid);
        }

        upgrade_plugin_savepoint(true, 2021071204, 'local', 'npe');
    }

    if ($oldversion < 2021082600) {

        //Rename previous table prefix.
        $table = new xmldb_table('npeplugins_enrolment_keys');
        if ($dbman->table_exists($table)) {
            $dbman->rename_table($table, 'npe_enrolment_keys');
        }
        $table = new xmldb_table('npeplugins_tag');
        if ($dbman->table_exists($table)) {
            $dbman->rename_table($table, 'npe_tag');
        }
        $table = new xmldb_table('npeplugins_team_tag');
        if ($dbman->table_exists($table)) {
            $dbman->rename_table($table, 'npe_team_tag');
        }

        // Define table to be created.
        $table = new xmldb_table('npe_product');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('name', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('level', XMLDB_TYPE_CHAR, '50', null, XMLDB_NOTNULL);
        $table->add_field('codproduct', XMLDB_TYPE_CHAR, '50', null, XMLDB_NOTNULL);
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $codproductindex = new xmldb_index('idx_npe_product_codproduct', XMLDB_INDEX_NOTUNIQUE, array('codproduct'));

        if (!$dbman->index_exists($table, $codproductindex)) {
            $dbman->add_index($table, $codproductindex);
        }

        // Define table to be created.
        $table = new xmldb_table('npe_activity');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('name', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('type', XMLDB_TYPE_CHAR, '50', null, XMLDB_NOTNULL);
        $table->add_field('sourceurl', XMLDB_TYPE_CHAR, '255');
        $table->add_field('productid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('activityclassificationid', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('producttopicid', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('defaultdelivery', XMLDB_TYPE_CHAR, '50');
        $table->add_field('isassignable', XMLDB_TYPE_INTEGER, '1');
        $table->add_field('showtostudent', XMLDB_TYPE_INTEGER, '1');
        $table->add_field('insequence', XMLDB_TYPE_INTEGER, '1');
        $table->add_field('invisor', XMLDB_TYPE_INTEGER, '1');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $productindex = new xmldb_index('idx_npe_activity_productid', XMLDB_INDEX_NOTUNIQUE, array('productid'));
        $classificationindex =
            new xmldb_index('idx_npe_activity_activityclassificationid', XMLDB_INDEX_NOTUNIQUE, array('activityclassificationid'));
        $producttopicindex = new xmldb_index('idx_npe_activity_producttopicid', XMLDB_INDEX_NOTUNIQUE, array('producttopicid'));

        if (!$dbman->index_exists($table, $productindex)) {
            $dbman->add_index($table, $productindex);
        }

        if (!$dbman->index_exists($table, $classificationindex)) {
            $dbman->add_index($table, $classificationindex);
        }

        if (!$dbman->index_exists($table, $producttopicindex)) {
            $dbman->add_index($table, $producttopicindex);
        }

        // Define table to be created.
        $table = new xmldb_table('npe_activity_classification');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('name', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('productid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $productindex = new xmldb_index('idx_npe_activity_classification_productid', XMLDB_INDEX_NOTUNIQUE, array('productid'));

        if (!$dbman->index_exists($table, $productindex)) {
            $dbman->add_index($table, $productindex);
        }

        // Define table to be created.
        $table = new xmldb_table('npe_product_topic');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('name', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('productid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('parentid', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $productindex = new xmldb_index('idx_npe_topic_productid', XMLDB_INDEX_NOTUNIQUE, array('productid'));

        if (!$dbman->index_exists($table, $productindex)) {
            $dbman->add_index($table, $productindex);
        }

        // Adding index.
        $parentindex = new xmldb_index('idx_npe_topic_parentid', XMLDB_INDEX_NOTUNIQUE, array('parentid'));

        if (!$dbman->index_exists($table, $parentindex)) {
            $dbman->add_index($table, $parentindex);
        }

        // Define table to be created.
        $table = new xmldb_table('npe_hidden_product_topic');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('producttopicid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $topicindex = new xmldb_index('idx_npe_hidden_topic_topicid', XMLDB_INDEX_NOTUNIQUE, array('producttopicid'));
        $courseidindex = new xmldb_index('idx_npe_hidden_topic_topicid', XMLDB_INDEX_NOTUNIQUE, array('courseid'));

        if (!$dbman->index_exists($table, $topicindex)) {
            $dbman->add_index($table, $topicindex);
        }

        if (!$dbman->index_exists($table, $courseidindex)) {
            $dbman->add_index($table, $courseidindex);
        }

        // Define table to be created.
        $table = new xmldb_table('npe_assign_activity');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('activityid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('title', XMLDB_TYPE_CHAR, '255');
        $table->add_field('comment', XMLDB_TYPE_CHAR, '255');
        $table->add_field('date', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timebegin', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timeend', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('deliverytype', XMLDB_TYPE_CHAR, '50');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $activityindex = new xmldb_index('idx_npe_assign_activity_activityid', XMLDB_INDEX_NOTUNIQUE, array('activityid'));
        $courseindex = new xmldb_index('idx_npe_assign_activity_courseid', XMLDB_INDEX_NOTUNIQUE, array('courseid'));

        if (!$dbman->index_exists($table, $activityindex)) {
            $dbman->add_index($table, $activityindex);
        }

        if (!$dbman->index_exists($table, $courseindex)) {
            $dbman->add_index($table, $courseindex);
        }

        // Define table to be created.
        $table = new xmldb_table('npe_assign_activity_singly');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('assignactivityid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('userid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $assignmentindex =
            new xmldb_index('idx_npe_assign_activity_assignactivityid', XMLDB_INDEX_NOTUNIQUE, array('assignactivityid'));
        $userindex = new xmldb_index('idx_npe_assign_activity_userid', XMLDB_INDEX_NOTUNIQUE, array('userid'));

        if (!$dbman->index_exists($table, $assignmentindex)) {
            $dbman->add_index($table, $assignmentindex);
        }

        if (!$dbman->index_exists($table, $userindex)) {
            $dbman->add_index($table, $userindex);
        }

        // Define table to be created.
        $table = new xmldb_table('npe_assign_activity_team');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('assignactivityid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('groupid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $assignmentindex =
            new xmldb_index('idx_npe_assign_activity_assignactivityid', XMLDB_INDEX_NOTUNIQUE, array('assignactivityid'));
        $groupindex = new xmldb_index('idx_npe_assign_activity_groupid', XMLDB_INDEX_NOTUNIQUE, array('groupid'));

        if (!$dbman->index_exists($table, $assignmentindex)) {
            $dbman->add_index($table, $assignmentindex);
        }

        if (!$dbman->index_exists($table, $groupindex)) {
            $dbman->add_index($table, $groupindex);
        }

        upgrade_plugin_savepoint(true, 2021082600, 'local', 'npe');
    }

    if ($oldversion < 2021091600) {

        $table = new xmldb_table('npe_assign_activity');
        $newfieldcomment = new xmldb_field('comment', XMLDB_TYPE_TEXT);

        // Conditionally launch add field grade.
        if ($dbman->field_exists($table, $newfieldcomment)) {
            $dbman->change_field_type($table, $newfieldcomment);
        }

        $table = new xmldb_table('npe_assign_activity_singly');
        // Adding fields
        $fieldtitle = new xmldb_field('title', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'userid');
        $fieldcomment = new xmldb_field('comment', XMLDB_TYPE_TEXT, null, null, null, null, null, 'title');
        $fielddate = new xmldb_field('date', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'comment');
        $fieldtimebegin = new xmldb_field('timebegin', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'date');
        $fieldtimeend = new xmldb_field('timeend', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'timebegin');

        // Conditionally launch add field grade.
        if (!$dbman->field_exists($table, $fieldtitle)) {
            $dbman->add_field($table, $fieldtitle);
        }
        if (!$dbman->field_exists($table, $fieldcomment)) {
            $dbman->add_field($table, $fieldcomment);
        }
        if (!$dbman->field_exists($table, $fielddate)) {
            $dbman->add_field($table, $fielddate);
        }
        if (!$dbman->field_exists($table, $fieldtimebegin)) {
            $dbman->add_field($table, $fieldtimebegin);
        }
        if (!$dbman->field_exists($table, $fieldtimeend)) {
            $dbman->add_field($table, $fieldtimeend);
        }

        $table = new xmldb_table('npe_assign_activity_team');
        // Adding fields
        $fieldtitle = new xmldb_field('title', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'groupid');
        $fieldcomment = new xmldb_field('comment', XMLDB_TYPE_TEXT, null, null, null, null, null, 'title');
        $fielddate = new xmldb_field('date', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'comment');
        $fieldtimebegin = new xmldb_field('timebegin', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'date');
        $fieldtimeend = new xmldb_field('timeend', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'timebegin');

        // Conditionally launch add field grade.
        if (!$dbman->field_exists($table, $fieldtitle)) {
            $dbman->add_field($table, $fieldtitle);
        }
        if (!$dbman->field_exists($table, $fieldcomment)) {
            $dbman->add_field($table, $fieldcomment);
        }
        if (!$dbman->field_exists($table, $fielddate)) {
            $dbman->add_field($table, $fielddate);
        }
        if (!$dbman->field_exists($table, $fieldtimebegin)) {
            $dbman->add_field($table, $fieldtimebegin);
        }
        if (!$dbman->field_exists($table, $fieldtimeend)) {
            $dbman->add_field($table, $fieldtimeend);
        }

        upgrade_plugin_savepoint(true, 2021091600, 'local', 'npe');
    }

    if ($oldversion < 2021091601) {

        // Activity
        $table = new xmldb_table('npe_activity');
        $classificationindex = new xmldb_index('mdl_npeacti_act_ix', XMLDB_INDEX_NOTUNIQUE, array('activityclassificationid'));
        if ($dbman->index_exists($table, $classificationindex)) {
            $dbman->drop_index($table, $classificationindex);
        }

        $fieldactivityclassificationid = new xmldb_field('activityclassificationid', XMLDB_TYPE_INTEGER, '10');
        if ($dbman->field_exists($table, $fieldactivityclassificationid)) {
            $dbman->rename_field($table, $fieldactivityclassificationid, 'activitycategoryid');
        }

        $categoryindex = new xmldb_index('idx_npe_activity_activitycategoryid', XMLDB_INDEX_NOTUNIQUE, array('activitycategoryid'));
        if (!$dbman->index_exists($table, $categoryindex)) {
            $dbman->add_index($table, $categoryindex);
        }

        //Activity Clasification (Category)
        $table = new xmldb_table('npe_activity_classification');
        if ($dbman->table_exists($table)) {
            $dbman->rename_table($table, 'npe_activity_category');
        }

        upgrade_plugin_savepoint(true, 2021091601, 'local', 'npe');
    }

    if ($oldversion < 2021091602) {

        //Activity Clasification (Category)
        $table = new xmldb_table('npe_assign_activity_singly');
        if ($dbman->table_exists($table)) {
            $dbman->rename_table($table, 'npe_assign_activity_student');
        }

        upgrade_plugin_savepoint(true, 2021091602, 'local', 'npe');
    }

    if ($oldversion < 2021091702) {

        $table = new xmldb_table('npe_activity');
        $field = new xmldb_field('clasification', XMLDB_TYPE_CHAR, '20', null, null, null, null, 'name');

        // Conditionally launch add field grade.
        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }

        $index = new xmldb_index('idx_npe_activity_clasification', XMLDB_INDEX_NOTUNIQUE, array('clasification'));
        if (!$dbman->index_exists($table, $index)) {
            $dbman->add_index($table, $index);
        }

        upgrade_plugin_savepoint(true, 2021091702, 'local', 'npe');
    }

    if ($oldversion < 2021100101) {

        // Define table to be created.
        $table = new xmldb_table('npe_team_metadata');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('teamid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('startingcolor', XMLDB_TYPE_CHAR, '10');
        $table->add_field('endingcolor', XMLDB_TYPE_CHAR, '10');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $teamid = new xmldb_index('idx_npe_team_metadata_teamid', XMLDB_INDEX_NOTUNIQUE, array('teamid'));

        if (!$dbman->index_exists($table, $teamid)) {
            $dbman->add_index($table, $teamid);
        }

        // Define table to be created.
        $table = new xmldb_table('npe_assign_activity_class');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('assignactivityid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('title', XMLDB_TYPE_CHAR, '255');
        $table->add_field('comment', XMLDB_TYPE_TEXT);
        $table->add_field('date', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timebegin', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timeend', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $assignmentindex =
            new xmldb_index('idx_npe_assign_activity_class_assignactivityid', XMLDB_INDEX_NOTUNIQUE, array('assignactivityid'));

        if (!$dbman->index_exists($table, $assignmentindex)) {
            $dbman->add_index($table, $assignmentindex);
        }

        $table = new xmldb_table('npe_assign_activity');

        $fieldtitle = new xmldb_field('title', XMLDB_TYPE_CHAR, '255');
        $fieldcomment = new xmldb_field('comment', XMLDB_TYPE_TEXT);
        $fielddate = new xmldb_field('date', XMLDB_TYPE_INTEGER, '10');
        $fieldtimebegin = new xmldb_field('timebegin', XMLDB_TYPE_INTEGER, '10');
        $fieldtimeend = new xmldb_field('timeend', XMLDB_TYPE_INTEGER, '10');

        if ($dbman->table_exists($table)) {
            if ($dbman->field_exists($table, $fieldtitle)) {
                $dbman->drop_field($table, $fieldtitle);
            }
            if ($dbman->field_exists($table, $fieldcomment)) {
                $dbman->drop_field($table, $fieldcomment);
            }
            if ($dbman->field_exists($table, $fielddate)) {
                $dbman->drop_field($table, $fielddate);
            }
            if ($dbman->field_exists($table, $fieldtimebegin)) {
                $dbman->drop_field($table, $fieldtimebegin);
            }
            if ($dbman->field_exists($table, $fieldtimeend)) {
                $dbman->drop_field($table, $fieldtimeend);
            }
        }

        upgrade_plugin_savepoint(true, 2021100101, 'local', 'npe');
    }

    if ($oldversion < 2021102701) {

        $table = new xmldb_table('npe_activity');
        $field = new xmldb_field('idnumber', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'clasification');

        // Conditionally launch add field grade.
        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }

        $index = new xmldb_index('idx_npe_activity_idnumber', XMLDB_INDEX_NOTUNIQUE, array('idnumber'));
        if (!$dbman->index_exists($table, $index)) {
            $dbman->add_index($table, $index);
        }

        // Define table to be created.
        $table = new xmldb_table('npe_activity_state');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('alias', XMLDB_TYPE_CHAR, '50');
        $table->add_field('name', XMLDB_TYPE_CHAR, '50');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $assignmentindex = new xmldb_index('idx_npe_activity_state_alias', XMLDB_INDEX_UNIQUE, array('alias'));

        if (!$dbman->index_exists($table, $assignmentindex)) {
            $dbman->add_index($table, $assignmentindex);
        }

        // Tables.
        $tablestudent = new xmldb_table('npe_assign_activity_student');
        $tableteam = new xmldb_table('npe_assign_activity_team');
        $tableclass = new xmldb_table('npe_assign_activity_class');

        // Fields.
        $fieldstatefeedback = new xmldb_field('feedback', XMLDB_TYPE_TEXT, '10', null, null, null, null, 'comment');
        $fieldstate = new xmldb_field('stateid', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'timeend');
        $fieldgrade = new xmldb_field('grade', XMLDB_TYPE_FLOAT, '10', null, null, null, null, 'stateid');
        $fieldgradedate = new xmldb_field('gradedate', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'grade');
        $fieldreopendate = new xmldb_field('deliverydate', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'gradedate');
        $fielddeliverydate = new xmldb_field('reopendate', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'deliverydate');
        $fielddone = new xmldb_field('done', XMLDB_TYPE_INTEGER, '1', null, null, null, 0, 'reopendate');
        $fieldusercreated = new xmldb_field('usercreated', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'done');

        // Conditionally launch add fields.
        if (!$dbman->field_exists($tablestudent, $fieldstatefeedback)) {
            $dbman->add_field($tablestudent, $fieldstatefeedback);
        }
        if (!$dbman->field_exists($tableteam, $fieldstatefeedback)) {
            $dbman->add_field($tableteam, $fieldstatefeedback);
        }
        if (!$dbman->field_exists($tableclass, $fieldstatefeedback)) {
            $dbman->add_field($tableclass, $fieldstatefeedback);
        }

        if (!$dbman->field_exists($tablestudent, $fieldstate)) {
            $dbman->add_field($tablestudent, $fieldstate);
        }
        if (!$dbman->field_exists($tableteam, $fieldstate)) {
            $dbman->add_field($tableteam, $fieldstate);
        }
        if (!$dbman->field_exists($tableclass, $fieldstate)) {
            $dbman->add_field($tableclass, $fieldstate);
        }

        if (!$dbman->field_exists($tablestudent, $fieldgrade)) {
            $dbman->add_field($tablestudent, $fieldgrade);
        }
        if (!$dbman->field_exists($tableteam, $fieldgrade)) {
            $dbman->add_field($tableteam, $fieldgrade);
        }
        if (!$dbman->field_exists($tableclass, $fieldgrade)) {
            $dbman->add_field($tableclass, $fieldgrade);
        }

        if (!$dbman->field_exists($tablestudent, $fieldgradedate)) {
            $dbman->add_field($tablestudent, $fieldgradedate);
        }
        if (!$dbman->field_exists($tableteam, $fieldgradedate)) {
            $dbman->add_field($tableteam, $fieldgradedate);
        }
        if (!$dbman->field_exists($tableclass, $fieldgradedate)) {
            $dbman->add_field($tableclass, $fieldgradedate);
        }

        if (!$dbman->field_exists($tablestudent, $fieldreopendate)) {
            $dbman->add_field($tablestudent, $fieldreopendate);
        }
        if (!$dbman->field_exists($tableteam, $fieldreopendate)) {
            $dbman->add_field($tableteam, $fieldreopendate);
        }
        if (!$dbman->field_exists($tableclass, $fieldreopendate)) {
            $dbman->add_field($tableclass, $fieldreopendate);
        }

        if (!$dbman->field_exists($tablestudent, $fielddeliverydate)) {
            $dbman->add_field($tablestudent, $fielddeliverydate);
        }
        if (!$dbman->field_exists($tableteam, $fielddeliverydate)) {
            $dbman->add_field($tableteam, $fielddeliverydate);
        }
        if (!$dbman->field_exists($tableclass, $fielddeliverydate)) {
            $dbman->add_field($tableclass, $fielddeliverydate);
        }

        if (!$dbman->field_exists($tablestudent, $fielddone)) {
            $dbman->add_field($tablestudent, $fielddone);
        }
        if (!$dbman->field_exists($tableteam, $fielddone)) {
            $dbman->add_field($tableteam, $fielddone);
        }
        if (!$dbman->field_exists($tableclass, $fielddone)) {
            $dbman->add_field($tableclass, $fielddone);
        }

        if (!$dbman->field_exists($tablestudent, $fieldusercreated)) {
            $dbman->add_field($tablestudent, $fieldusercreated);
        }
        if (!$dbman->field_exists($tableteam, $fieldusercreated)) {
            $dbman->add_field($tableteam, $fieldusercreated);
        }
        if (!$dbman->field_exists($tableclass, $fieldusercreated)) {
            $dbman->add_field($tableclass, $fieldusercreated);
        }

        // Adding index.
        $stateindex = new xmldb_index('idx_npe_assign_activity_stateid', XMLDB_INDEX_NOTUNIQUE, array('stateid'));

        if (!$dbman->index_exists($tablestudent, $stateindex)) {
            $dbman->add_index($tablestudent, $stateindex);
        }
        if (!$dbman->index_exists($tableteam, $stateindex)) {
            $dbman->add_index($tableteam, $stateindex);
        }
        if (!$dbman->index_exists($tableclass, $stateindex)) {
            $dbman->add_index($tableclass, $stateindex);
        }

        // Insert activity state records.
        records_npe::set_activity_state();
        // Update null stateid as pending.
        records_npe::update_activity_stateid();

        upgrade_plugin_savepoint(true, 2021102701, 'local', 'npe');
    }

    if ($oldversion < 2021112201) {
        records_npe::generate_fake_activity_idnumber();

        upgrade_plugin_savepoint(true, 2021112201, 'local', 'npe');
    }

    if ($oldversion < 2021121500) {
        $table = new xmldb_table('npe_product');
        $fieldshowhelp = new xmldb_field('showhelp', XMLDB_TYPE_INTEGER, '1', null, null, null, 0, 'codproduct');

        if (!$dbman->field_exists($table, $fieldshowhelp)) {
            $dbman->add_field($table, $fieldshowhelp);
        }

        upgrade_plugin_savepoint(true, 2021121500, 'local', 'npe');
    }

    if ($oldversion < 2022012402) {

        // Define table to be created.
        $table = new xmldb_table('npe_access');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('token', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('userid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('codproduct', XMLDB_TYPE_CHAR, '50', null, XMLDB_NOTNULL);
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $indextoken = new xmldb_index('idx_npe_access_token', XMLDB_INDEX_UNIQUE, array('token'));
        $indexuser = new xmldb_index('idx_npe_access_userid_codproduct', XMLDB_INDEX_UNIQUE, array('userid, codproduct'));

        if (!$dbman->index_exists($table, $indextoken)) {
            $dbman->add_index($table, $indextoken);
        }

        if (!$dbman->index_exists($table, $indexuser)) {
            $dbman->add_index($table, $indexuser);
        }

        // Crear Productos de prueba.
        $codproducts = ['ES332211', 'ES207394', 'ES207457'];
        foreach ($codproducts as $codproduct) {
            records_npe::create_mock_product($codproduct, 'Revuela - ' . $codproduct, '3EP');
        }

        upgrade_plugin_savepoint(true, 2022012402, 'local', 'npe');
    }

    if ($oldversion < 2022020900) {
        // Agregar foro general como actividad y asignarla a cursos ya creados.

        records_npe::create_mock_general_forum();

        upgrade_plugin_savepoint(true, 2022020900, 'local', 'npe');
    }

    if ($oldversion < 2022021500) {

        $table = new xmldb_table('npe_product');
        // Nuevos campos de la tabla producto.
        $fieldcountry = new xmldb_field('country', XMLDB_TYPE_CHAR, '10', null, null, null, null, 'showhelp');
        $fieldlanguage = new xmldb_field('language', XMLDB_TYPE_CHAR, '10', null, null, null, null, 'country');
        $fieldsubject = new xmldb_field('subject', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'language');
        $fieldcontentview = new xmldb_field('contentview', XMLDB_TYPE_CHAR, '10', null, null, null, null, 'subject');
        $fieldfamilylink = new xmldb_field('familylink', XMLDB_TYPE_INTEGER, '1', null, null, null, 0, 'contentview');
        $fieldfamilylicense = new xmldb_field('familylicense', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'familylink');
        $fieldicon = new xmldb_field('icon', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'familylicense');
        $fieldfrontpage = new xmldb_field('frontpage', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'icon');
        $fieldbackground = new xmldb_field('background', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'frontpage');
        $fieldisotipo = new xmldb_field('isotipo', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'background');
        $fieldstage = new xmldb_field('stage', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'isotipo');
        $fieldjson = new xmldb_field('json', XMLDB_TYPE_TEXT, null, null, null, null, null, 'stage');
        $fieldhash = new xmldb_field('jsonhash', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'json');

        // Conditionally launch add field grade.
        if (!$dbman->field_exists($table, $fieldcountry)) {
            $dbman->add_field($table, $fieldcountry);
        }

        if (!$dbman->field_exists($table, $fieldlanguage)) {
            $dbman->add_field($table, $fieldlanguage);
        }

        if (!$dbman->field_exists($table, $fieldsubject)) {
            $dbman->add_field($table, $fieldsubject);
        }

        if (!$dbman->field_exists($table, $fieldcontentview)) {
            $dbman->add_field($table, $fieldcontentview);
        }

        if (!$dbman->field_exists($table, $fieldfamilylink)) {
            $dbman->add_field($table, $fieldfamilylink);
        }

        if (!$dbman->field_exists($table, $fieldfamilylicense)) {
            $dbman->add_field($table, $fieldfamilylicense);
        }

        if (!$dbman->field_exists($table, $fieldicon)) {
            $dbman->add_field($table, $fieldicon);
        }

        if (!$dbman->field_exists($table, $fieldfrontpage)) {
            $dbman->add_field($table, $fieldfrontpage);
        }

        if (!$dbman->field_exists($table, $fieldbackground)) {
            $dbman->add_field($table, $fieldbackground);
        }

        if (!$dbman->field_exists($table, $fieldisotipo)) {
            $dbman->add_field($table, $fieldisotipo);
        }

        if (!$dbman->field_exists($table, $fieldstage)) {
            $dbman->add_field($table, $fieldstage);
        }

        if (!$dbman->field_exists($table, $fieldjson)) {
            $dbman->add_field($table, $fieldjson);
        }

        if (!$dbman->field_exists($table, $fieldhash)) {
            $dbman->add_field($table, $fieldhash);
        }

        // Define table to be created.
        $table = new xmldb_table('npe_product_topic_block');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('packerid', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('name', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('productid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $packerindex = new xmldb_index('idx_npe_product_topic_block_packerid', XMLDB_INDEX_NOTUNIQUE, array('packerid'));

        if (!$dbman->index_exists($table, $packerindex)) {
            $dbman->add_index($table, $packerindex);
        }

        // Adding index.
        $productindex = new xmldb_index('idx_npe_product_topic_block_productid', XMLDB_INDEX_NOTUNIQUE, array('productid'));

        if (!$dbman->index_exists($table, $productindex)) {
            $dbman->add_index($table, $productindex);
        }

        // Define table to be created.
        $table = new xmldb_table('npe_product_topic_area');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('packerid', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('name', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('productid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $packerindex = new xmldb_index('idx_npe_product_topic_area_packerid', XMLDB_INDEX_NOTUNIQUE, array('packerid'));

        if (!$dbman->index_exists($table, $packerindex)) {
            $dbman->add_index($table, $packerindex);
        }

        // Adding index.
        $productindex = new xmldb_index('idx_npe_product_topic_area_productid', XMLDB_INDEX_NOTUNIQUE, array('productid'));

        if (!$dbman->index_exists($table, $productindex)) {
            $dbman->add_index($table, $productindex);
        }

        // Agregar llaves foraneas e indices a la tabla
        $table = new xmldb_table('npe_product_topic');

        $fieldpackerid = new xmldb_field('packerid', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'id');
        $fielddigitalbook = new xmldb_field('digitalbook', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'productid');
        $fielddsdatype = new xmldb_field('sdatype', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'digitalbook');
        $fieldsdaimage = new xmldb_field('sdaimage', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'sdatype');
        $fieldblockid = new xmldb_field('blockid', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'sdaimage');
        $fieldareaid = new xmldb_field('areaid', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'blockid');

        // Conditionally launch add field grade.
        if (!$dbman->field_exists($table, $fieldpackerid)) {
            $dbman->add_field($table, $fieldpackerid);
        }

        // Conditionally launch add field grade.
        if (!$dbman->field_exists($table, $fielddigitalbook)) {
            $dbman->add_field($table, $fielddigitalbook);
        }

        // Conditionally launch add field grade.
        if (!$dbman->field_exists($table, $fielddsdatype)) {
            $dbman->add_field($table, $fielddsdatype);
        }

        // Conditionally launch add field grade.
        if (!$dbman->field_exists($table, $fieldsdaimage)) {
            $dbman->add_field($table, $fieldsdaimage);
        }

        // Conditionally launch add field grade.
        if (!$dbman->field_exists($table, $fieldblockid)) {
            $dbman->add_field($table, $fieldblockid);
        }

        if (!$dbman->field_exists($table, $fieldareaid)) {
            $dbman->add_field($table, $fieldareaid);
        }

        // Adding index.
        $packerindex = new xmldb_index('idx_npe_product_topic_packerid', XMLDB_INDEX_NOTUNIQUE, array('packerid'));

        if (!$dbman->index_exists($table, $packerindex)) {
            $dbman->add_index($table, $packerindex);
        }

        // Adding index.
        $blockindex = new xmldb_index('idx_npe_product_topic_blockid', XMLDB_INDEX_NOTUNIQUE, array('blockid'));

        if (!$dbman->index_exists($table, $blockindex)) {
            $dbman->add_index($table, $blockindex);
        }

        // Adding index.
        $packerindex = new xmldb_index('idx_npe_product_topic_areaid', XMLDB_INDEX_NOTUNIQUE, array('areaid'));

        if (!$dbman->index_exists($table, $packerindex)) {
            $dbman->add_index($table, $packerindex);
        }

        upgrade_plugin_savepoint(true, 2022021500, 'local', 'npe');
    }

    if ($oldversion < **********) {
        $table = new xmldb_table('npe_access');
        $fieldbackurl = new xmldb_field('backurl', XMLDB_TYPE_CHAR, '255', null, null, null, 0, 'codproduct');

        if (!$dbman->field_exists($table, $fieldbackurl)) {
            $dbman->add_field($table, $fieldbackurl);
        }

        records_npe::create_grandgroup();
        records_npe::check_general_forum_assign();

        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < **********) {

        // Define table to be created.
        $table = new xmldb_table('npe_user_license');
        // Adding fields.
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('userid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('productid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('licensetype', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('licenseexpiration', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $userid = new xmldb_index('idx_npe_user_license_userid', XMLDB_INDEX_NOTUNIQUE, array('userid'));

        if (!$dbman->index_exists($table, $userid)) {
            $dbman->add_index($table, $userid);
        }

        // Adding index.
        $userid = new xmldb_index('idx_npe_user_license_productid', XMLDB_INDEX_NOTUNIQUE, array('productid'));

        if (!$dbman->index_exists($table, $userid)) {
            $dbman->add_index($table, $userid);
        }

        $table = new xmldb_table('npe_access');
        $fieldbackurl = new xmldb_field('backurl', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'codproduct');

        if ($dbman->field_exists($table, $fieldbackurl)) {
            $dbman->change_field_default($table, $fieldbackurl);
        }

        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < **********) {

        $table = new xmldb_table('npe_activity_category');
        // Adding fields
        $fieldpackerid = new xmldb_field('packerid', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'id');
        $fieldcategory = new xmldb_field('category', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'packerid');
        $fieldsort = new xmldb_field('sort', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'name');

        // Conditionally launch add field grade.
        if (!$dbman->field_exists($table, $fieldpackerid)) {
            $dbman->add_field($table, $fieldpackerid);
        }
        if (!$dbman->field_exists($table, $fieldcategory)) {
            $dbman->add_field($table, $fieldcategory);
        }
        if (!$dbman->field_exists($table, $fieldsort)) {
            $dbman->add_field($table, $fieldsort);
        }

        // Adding index.
        $indexpackerid = new xmldb_index('idx_npe_activity_category_packerid', XMLDB_INDEX_NOTUNIQUE, array('packerid'));

        if (!$dbman->index_exists($table, $indexpackerid)) {
            $dbman->add_index($table, $indexpackerid);
        }

        // Adding index.
        $indexcategory = new xmldb_index('idx_npe_activity_category_category', XMLDB_INDEX_NOTUNIQUE, array('category'));

        if (!$dbman->index_exists($table, $indexcategory)) {
            $dbman->add_index($table, $indexcategory);
        }

        // Cambios en Tabla de actividad.
        $table = new xmldb_table('npe_activity');
        $fieldinvisor = new xmldb_field('invisor');
        $fieldsectionid =
            new xmldb_field('activitysectionid', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'activitycategoryid');
        $fieldtypelabel = new xmldb_field('typelabel', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'type');
        $fieldlicense = new xmldb_field('license', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'typelabel');
        $fieldkeyevidence = new xmldb_field('keyevidence', XMLDB_TYPE_INTEGER, '1', null, null, null, null, 'license');
        $fieldvisibility = new xmldb_field('visibility', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'keyevidence');
        $fieldvisibilitylabel = new xmldb_field('visibilitylabel', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'visibility');
        $fielddifficultylevel =
            new xmldb_field('difficultylevel', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'visibilitylabel');
        $fieldh5pconfiguration =
            new xmldb_field('h5pconfiguration', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'difficultylevel');
        $fieldprogexam = new xmldb_field('progexam', XMLDB_TYPE_INTEGER, '1', null, null, null, null, 'h5pconfiguration');

        if ($dbman->field_exists($table, $fieldinvisor)) {
            $dbman->drop_field($table, $fieldinvisor);
        }

        if (!$dbman->field_exists($table, $fieldsectionid)) {
            $dbman->add_field($table, $fieldsectionid);
        }

        if (!$dbman->field_exists($table, $fieldtypelabel)) {
            $dbman->add_field($table, $fieldtypelabel);
        }

        if (!$dbman->field_exists($table, $fieldlicense)) {
            $dbman->add_field($table, $fieldlicense);
        }

        if (!$dbman->field_exists($table, $fieldkeyevidence)) {
            $dbman->add_field($table, $fieldkeyevidence);
        }

        if (!$dbman->field_exists($table, $fieldvisibility)) {
            $dbman->add_field($table, $fieldvisibility);
        }

        if (!$dbman->field_exists($table, $fieldvisibilitylabel)) {
            $dbman->add_field($table, $fieldvisibilitylabel);
        }

        if (!$dbman->field_exists($table, $fielddifficultylevel)) {
            $dbman->add_field($table, $fielddifficultylevel);
        }

        if (!$dbman->field_exists($table, $fieldh5pconfiguration)) {
            $dbman->add_field($table, $fieldh5pconfiguration);
        }

        if (!$dbman->field_exists($table, $fieldprogexam)) {
            $dbman->add_field($table, $fieldprogexam);
        }

        // Define table to be created.
        $table = new xmldb_table('npe_activity_section');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null, null);
        $table->add_field('packerid', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL, null, null, 'id');
        $table->add_field('idview', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL, null, null, 'packerid');
        $table->add_field('section', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL, null, null, 'idview');
        $table->add_field('profile', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL, null, null, 'section');
        $table->add_field('license', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL, null, null, 'profile');
        $table->add_field('icon', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'license');
        $table->add_field('teachertitle', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'icon');
        $table->add_field('studenttitle', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'teachertitle');
        $table->add_field('sectionlabel', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'studenttitle');
        $table->add_field('productid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null, 'sectionlabel');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'productid');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'usermodified');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'timecreated');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $indexpacjerid = new xmldb_index('idx_npe_activity_section_packerid', XMLDB_INDEX_NOTUNIQUE, array('packerid'));
        $indexsection = new xmldb_index('idx_npe_activity_section_section', XMLDB_INDEX_NOTUNIQUE, array('section'));
        $indexproductid = new xmldb_index('idx_npeplugins_tag_productid', XMLDB_INDEX_NOTUNIQUE, array('productid'));

        if (!$dbman->index_exists($table, $indexpacjerid)) {
            $dbman->add_index($table, $indexpacjerid);
        }

        if (!$dbman->index_exists($table, $indexsection)) {
            $dbman->add_index($table, $indexsection);
        }

        if (!$dbman->index_exists($table, $indexproductid)) {
            $dbman->add_index($table, $indexproductid);
        }

        // Cambios en Tabla de producto.
        $table = new xmldb_table('npe_product');
        $fieldjson = new xmldb_field('json', XMLDB_TYPE_TEXT, null, null, null, null, null, 'stage');;
        $fieldjsonhash = new xmldb_field('jsonhash', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'productjson');

        $fieldrepositoryjson = new xmldb_field('repositoryjson', XMLDB_TYPE_TEXT, null, null, null, null, null, 'productjsonhash');
        $fieldrepositoryhash =
            new xmldb_field('repositoryjsonhash', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'repositoryjson');

        if ($dbman->field_exists($table, $fieldjson)) {
            $dbman->rename_field($table, $fieldjson, 'productjson');
        }

        if ($dbman->field_exists($table, $fieldjsonhash)) {
            $dbman->rename_field($table, $fieldjsonhash, 'productjsonhash');
        }

        if (!$dbman->field_exists($table, $fieldrepositoryjson)) {
            $dbman->add_field($table, $fieldrepositoryjson);
        }

        if (!$dbman->field_exists($table, $fieldrepositoryhash)) {
            $dbman->add_field($table, $fieldrepositoryhash);
        }

        // Cambios en Tabla de Unidades/temas.
        $table = new xmldb_table('npe_product_topic');
        $fieldtopicjson = new xmldb_field('topicjson', XMLDB_TYPE_TEXT, null, null, null, null, null, 'areaid');
        $fieldtopicjsonhash = new xmldb_field('topicjsonhash', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'topicjson');

        if (!$dbman->field_exists($table, $fieldtopicjson)) {
            $dbman->add_field($table, $fieldtopicjson);
        }

        if (!$dbman->field_exists($table, $fieldtopicjsonhash)) {
            $dbman->add_field($table, $fieldtopicjsonhash);
        }

        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < 2022032900) {
        // Define table to be created.
        $table = new xmldb_table('npe_ccaa');

        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('ccaaid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('name', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $indexccaa = new xmldb_index('idx_npe_ccaa', XMLDB_INDEX_UNIQUE, array('ccaaid'));

        if (!$dbman->index_exists($table, $indexccaa)) {
            $dbman->add_index($table, $indexccaa);
        }

        records_npe::create_ccaa();
        upgrade_plugin_savepoint(true, 2022032900, 'local', 'npe');
    }

    if ($oldversion < 2022033100) {
        // Cambios en Tabla de actividad.
        $table = new xmldb_table('npe_activity');
        $fieldn4title = new xmldb_field('n4title', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'name');
        $fieldactivitynum = new xmldb_field('activitynum', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'n4title');

        if (!$dbman->field_exists($table, $fieldn4title)) {
            $dbman->add_field($table, $fieldn4title);
        }

        if (!$dbman->field_exists($table, $fieldactivitynum)) {
            $dbman->add_field($table, $fieldactivitynum);
        }

        upgrade_plugin_savepoint(true, 2022033100, 'local', 'npe');
    }

    if ($oldversion < 2022040504) {

        // Define table to be created.
        $table = new xmldb_table('npe_product_topic_ccaa');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('packerid', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('ccaaid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding indexes.
        $producttopicid = new xmldb_index('idx_npe_producttopic_ccaa_packerid', XMLDB_INDEX_NOTUNIQUE, array('packerid'));
        $ccaaid = new xmldb_index('idx_npe_producttopic_ccaa_ccaaid', XMLDB_INDEX_NOTUNIQUE, array('ccaaid'));

        if (!$dbman->index_exists($table, $producttopicid)) {
            $dbman->add_index($table, $producttopicid);
        }
        if (!$dbman->index_exists($table, $ccaaid)) {
            $dbman->add_index($table, $ccaaid);
        }

        // Define table to be created.
        $table = new xmldb_table('npe_activity_ccaa');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('activityidnumber', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('ccaaid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding indexes.
        $activityid = new xmldb_index('idx_npe_activity_ccaa_activityidnumber', XMLDB_INDEX_NOTUNIQUE, array('activityidnumber'));
        $ccaaid = new xmldb_index('idx_npe_activity_ccaa_ccaaid', XMLDB_INDEX_NOTUNIQUE, array('ccaaid'));

        if (!$dbman->index_exists($table, $activityid)) {
            $dbman->add_index($table, $activityid);
        }
        if (!$dbman->index_exists($table, $ccaaid)) {
            $dbman->add_index($table, $ccaaid);
        }

        upgrade_plugin_savepoint(true, 2022040504, 'local', 'npe');
    }

    if ($oldversion < 2022042001) {
        // Cambios en Tabla de actividad.
        $table = new xmldb_table('npe_activity');
        $fieldactivitynum = new xmldb_field('activitynum', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'n4title');

        if ($dbman->field_exists($table, $fieldactivitynum)) {
            $dbman->change_field_type($table, $fieldactivitynum);
            $dbman->change_field_precision($table, $fieldactivitynum);
        }

        upgrade_plugin_savepoint(true, 2022042001, 'local', 'npe');
    }

    if ($oldversion < 2022042003) {
        // Cambios en Tabla de actividad.
        $table = new xmldb_table('npe_activity');
        $fieldtype = new xmldb_field('type', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'idnumber');
        $fieldtypeid = new xmldb_field('typeid', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'type');

        if ($dbman->field_exists($table, $fieldtype)) {
            $dbman->change_field_notnull($table, $fieldtype);
        }

        if (!$dbman->field_exists($table, $fieldtypeid)) {
            $dbman->add_field($table, $fieldtypeid);
        }

        upgrade_plugin_savepoint(true, 2022042003, 'local', 'npe');
    }

    if ($oldversion < **********) {

        $newnotifications = new xmldb_table('npe_notifications');
        // Adding fields to table .
        $newnotifications->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $newnotifications->add_field('type', XMLDB_TYPE_CHAR, '50', null, XMLDB_NOTNULL, null, null);
        $newnotifications->add_field('url', XMLDB_TYPE_TEXT, null, null, null, null, null);
        $newnotifications->add_field('params', XMLDB_TYPE_TEXT, null, null, null, null, null);
        $newnotifications->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, true, null, 0);
        $newnotifications->add_field('teamid', XMLDB_TYPE_INTEGER, '10', null, null, null, null);
        $newnotifications->add_field('uuid', XMLDB_TYPE_CHAR, '100', null, null, null, null);
        $newnotifications->add_field('count', XMLDB_TYPE_INTEGER, '7', null, XMLDB_NOTNULL, null, '1');
        $newnotifications->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $newnotifications->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        // Adding keys to table .
        $newnotifications->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));

        $notificationsuser = new xmldb_table('npe_notifications_user');
        $dbman = $DB->get_manager();
        if ($dbman->table_exists($notificationsuser)) {
            $dbman->drop_table($notificationsuser);
        }
        $notificationsuser->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $notificationsuser->add_field('notificationid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $notificationsuser->add_field('userid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $notificationsuser->add_field('viewed', XMLDB_TYPE_INTEGER, '1', null, XMLDB_NOTNULL, null, '0');
        $notificationsuser->add_field('fromdate', XMLDB_TYPE_INTEGER, '10', null, null, null, null);
        $notificationsuser->add_field('todate', XMLDB_TYPE_INTEGER, '10', null, null, null, null);
        $notificationsuser->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $notificationsuser->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');
        // Adding keys to table .
        $notificationsuser->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));

        // Conditionally launch create table.
        if (!$dbman->table_exists($newnotifications)) {
            $dbman->create_table($newnotifications);
        }
        $courseid = new xmldb_index('mdl_locanoti_cou_ix', XMLDB_INDEX_NOTUNIQUE, array('courseid'));
        if (!$dbman->index_exists($newnotifications, $courseid)) {
            $dbman->add_index($newnotifications, $courseid);
        }
        $uuid = new xmldb_index('mdl_locanoti_uid_ix', XMLDB_INDEX_NOTUNIQUE, array('uuid'));
        if (!$dbman->index_exists($newnotifications, $uuid)) {
            $dbman->add_index($newnotifications, $uuid);
        }
        // Conditionally launch create table .
        if (!$dbman->table_exists($notificationsuser)) {
            $dbman->create_table($notificationsuser);
        }
        $userid = new xmldb_index('mdl_locanoti_usr_ix', XMLDB_INDEX_NOTUNIQUE, array('userid'));
        if (!$dbman->index_exists($notificationsuser, $userid)) {
            $dbman->add_index($notificationsuser, $userid);
        }
        $fromdate = new xmldb_index('mdl_locanoti_fdt_ix', XMLDB_INDEX_NOTUNIQUE, array('fromdate'));
        if (!$dbman->index_exists($notificationsuser, $fromdate)) {
            $dbman->add_index($notificationsuser, $fromdate);
        }
        $todate = new xmldb_index('mdl_locanoti_tdt_ix', XMLDB_INDEX_NOTUNIQUE, array('todate'));
        if (!$dbman->index_exists($notificationsuser, $todate)) {
            $dbman->add_index($notificationsuser, $todate);
        }

        // Notifications savepoint reached.
        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < **********) {
        $table = new xmldb_table('npe_user_license');
        $fieldonboarding = new xmldb_field('onboarding', XMLDB_TYPE_INTEGER, '1', null, null, null, 0, 'licenseexpiration');

        if (!$dbman->field_exists($table, $fieldonboarding)) {
            $dbman->add_field($table, $fieldonboarding);
        }

        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < **********) {
        // Cambios en Tabla de Unidades/temas.
        $table = new xmldb_table('npe_product_topic');
        $fieldsdanumber = new xmldb_field('sdanumber', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'sdaimage');

        if (!$dbman->field_exists($table, $fieldsdanumber)) {
            $dbman->add_field($table, $fieldsdanumber);
        }

        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < **********) {
        // Cambios en Tabla de Actividades.
        $table = new xmldb_table('npe_activity');
        $mapperassignment = new xmldb_field('mapperassignment', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'h5pconfiguration');

        if (!$dbman->field_exists($table, $mapperassignment)) {
            $dbman->add_field($table, $mapperassignment);
        }

        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < **********) {
        $lastgroups = new xmldb_table('npe_last_groups_visited');
        $lastgroups->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $lastgroups->add_field('userid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $lastgroups->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $lastgroups->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        // Adding keys to table .
        $lastgroups->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));

        $dbman = $DB->get_manager();
        if ($dbman->table_exists($lastgroups)) {
            $dbman->drop_table($lastgroups);
        }
        if (!$dbman->table_exists($lastgroups)) {
            $dbman->create_table($lastgroups);
        }
        $courseid = new xmldb_index('mdl_lastgroups_cou_ix', XMLDB_INDEX_NOTUNIQUE, array('courseid'));
        if (!$dbman->index_exists($lastgroups, $courseid)) {
            $dbman->add_index($lastgroups, $courseid);
        }
        $userid = new xmldb_index('mdl_lastgroups_usr_ix', XMLDB_INDEX_NOTUNIQUE, array('userid'));
        if (!$dbman->index_exists($lastgroups, $userid)) {
            $dbman->add_index($lastgroups, $userid);
        }

        // Notifications savepoint reached.
        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < **********) {
        $table = new xmldb_table('npe_user_license');
        $fieldonboarding = new xmldb_field('cu', XMLDB_TYPE_INTEGER, '1', null, null, null, 1, 'onboarding');

        if (!$dbman->field_exists($table, $fieldonboarding)) {
            $dbman->add_field($table, $fieldonboarding);
        }

        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < **********) {
        $table = new xmldb_table('npe_activity');
        $fieldassignconfiguration = new xmldb_field('textanswertype', XMLDB_TYPE_CHAR, '2', null, null, null, null, 'h5pconfiguration');
        $fieldassignconfiguration_name = new xmldb_field('name', XMLDB_TYPE_CHAR, '255');

        if (!$dbman->field_exists($table, $fieldassignconfiguration)) {
            $dbman->add_field($table, $fieldassignconfiguration);
        }

        if ($dbman->field_exists($table, $fieldassignconfiguration_name)) {
            $dbman->change_field_notnull($table, $fieldassignconfiguration_name);
        }

        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < 2022063000) {
        records_npe::change_notifications_type_by_meetingroom();
        upgrade_plugin_savepoint(true, 2022063000, 'local', 'npe');
    }

    if ($oldversion < 2022070100) {
        records_npe::change_url_type_by_meetingroom();
        upgrade_plugin_savepoint(true, 2022070100, 'local', 'npe');
    }

    if ($oldversion < 2022070400) {
        $questions = new xmldb_table('npe_diary_questions');
        if ($dbman->table_exists($questions)) {
            $dbman->drop_table($questions);
        }
        $questions->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $questions->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $questions->add_field('topicid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $questions->add_field('questionid', XMLDB_TYPE_CHAR, '100', null, XMLDB_NOTNULL, null, null);
        $questions->add_field('label', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL, null, null);
        $questions->add_key('primary', XMLDB_KEY_PRIMARY, ['id']);
        $indexcourse = new xmldb_index('mdl_question_cou_ix', XMLDB_INDEX_NOTUNIQUE, array('courseid'));
        $indextopic = new xmldb_index('mdl_question_top_ix', XMLDB_INDEX_NOTUNIQUE, array('topicid'));
        if (!$dbman->table_exists($questions)) {
            $dbman->create_table($questions);
        }
        $dbman->add_index($questions, $indexcourse);
        $dbman->add_index($questions, $indextopic);

        //npe_student_journalothinking
        $journals = new xmldb_table('npe_student_journalothinking');
        if ($dbman->table_exists($journals)) {
            $dbman->drop_table($journals);
        }
        $journals->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $journals->add_field('userid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $journals->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $journals->add_field('topicid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $journals->add_field('questionid', XMLDB_TYPE_CHAR, '100', null, XMLDB_NOTNULL, null, null);
        $journals->add_field('value', XMLDB_TYPE_TEXT, null, null, XMLDB_NOTNULL, null, null);
        $journals->add_key('primary', XMLDB_KEY_PRIMARY, ['id']);
        $indexcourse = new xmldb_index('mdl_journalothinking_cou_ix', XMLDB_INDEX_NOTUNIQUE, array('courseid'));
        $indextopic = new xmldb_index('mdl_journalothinking_usr_ix', XMLDB_INDEX_NOTUNIQUE, array('userid'));
        $indexquestion = new xmldb_index('mdl_journalothinking_que_ix', XMLDB_INDEX_NOTUNIQUE, array('questionid'));
        if (!$dbman->table_exists($journals)) {
            $dbman->create_table($journals);
        }
        $dbman->add_index($journals, $indexcourse);
        $dbman->add_index($journals, $indextopic);
        $dbman->add_index($journals, $indexquestion);
        upgrade_plugin_savepoint(true, 2022070400, 'local', 'npe');
    }

    if ($oldversion < 2022070401) {
        $questions = new xmldb_table('npe_plan_questions');
        if ($dbman->table_exists($questions)) {
            $dbman->drop_table($questions);
        }
        $questions->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $questions->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $questions->add_field('topicid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $questions->add_field('questionid', XMLDB_TYPE_CHAR, '100', null, XMLDB_NOTNULL, null, null);
        $questions->add_field('label', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL, null, null);
        $questions->add_key('primary', XMLDB_KEY_PRIMARY, ['id']);
        $indexcourse = new xmldb_index('mdl_planquestion_cou_ix', XMLDB_INDEX_NOTUNIQUE, array('courseid'));
        $indextopic = new xmldb_index('mdl_planquestion_top_ix', XMLDB_INDEX_NOTUNIQUE, array('topicid'));
        if (!$dbman->table_exists($questions)) {
            $dbman->create_table($questions);
        }
        $dbman->add_index($questions, $indexcourse);
        $dbman->add_index($questions, $indextopic);

        //npe_student_plan
        $plans = new xmldb_table('npe_student_plan');
        if ($dbman->table_exists($plans)) {
            $dbman->drop_table($plans);
        }
        $plans->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $plans->add_field('userid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $plans->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $plans->add_field('topicid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $plans->add_field('questionid', XMLDB_TYPE_CHAR, '100', null, XMLDB_NOTNULL, null, null);
        $plans->add_field('value', XMLDB_TYPE_TEXT, null, null, XMLDB_NOTNULL, null, null);
        $plans->add_key('primary', XMLDB_KEY_PRIMARY, ['id']);
        $indexcourse = new xmldb_index('mdl_plan_cou_ix', XMLDB_INDEX_NOTUNIQUE, array('courseid'));
        $indextopic = new xmldb_index('mdl_plan_usr_ix', XMLDB_INDEX_NOTUNIQUE, array('userid'));
        $indexquestion = new xmldb_index('mdl_plan_que_ix', XMLDB_INDEX_NOTUNIQUE, array('questionid'));
        if (!$dbman->table_exists($plans)) {
            $dbman->create_table($plans);
        }
        $dbman->add_index($plans, $indexcourse);
        $dbman->add_index($plans, $indextopic);
        $dbman->add_index($plans, $indexquestion);
        upgrade_plugin_savepoint(true, 2022070401, 'local', 'npe');
    }

    if ($oldversion < **********) {
        $table = new xmldb_table('npe_student_objective');

        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $table->add_field('userid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('topicid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('objectiveid', XMLDB_TYPE_CHAR, '100', null, XMLDB_NOTNULL, null, null);
        $table->add_field('value', XMLDB_TYPE_CHAR, '100', null, XMLDB_NOTNULL, null, null);

        // Adding keys to table .
        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        $indexcourse = new xmldb_index('mdl_user_objectives_cou_ix', XMLDB_INDEX_NOTUNIQUE, array('courseid'));
        $indexuser = new xmldb_index('mdl_user_objectives_usr_ix', XMLDB_INDEX_NOTUNIQUE, array('userid'));
        $indexobjective = new xmldb_index('mdl_user_objectives_top_ix', XMLDB_INDEX_NOTUNIQUE, array('topicid'));
        $indextopic = new xmldb_index('mdl_user_objectives_obj_ix', XMLDB_INDEX_NOTUNIQUE, array('objectiveid'));

        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        if (!$dbman->index_exists($table, $indexcourse)) {
            $dbman->add_index($table, $indexcourse);
        }

        if (!$dbman->index_exists($table, $indexuser)) {
            $dbman->add_index($table, $indexuser);
        }

        if (!$dbman->index_exists($table, $indextopic)) {
            $dbman->add_index($table, $indextopic);
        }
        if (!$dbman->index_exists($table, $indexobjective)) {
            $dbman->add_index($table, $indexobjective);
        }
        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < **********) {
        $table = new xmldb_table('npe_user_license');
        $fieldonboardinggroups = new xmldb_field('onboardinggroups', XMLDB_TYPE_INTEGER, '1', null, null, null, 0, 'onboarding');

        if (!$dbman->field_exists($table, $fieldonboardinggroups)) {
            $dbman->add_field($table, $fieldonboardinggroups);
        }

        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < **********) {
        // Define table to be created.
        $table = new xmldb_table('npe_activity_n4');

        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('packerid', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('title', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('topicid', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('productid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $indexpackerid = new xmldb_index('idx_npe_activity_n4_packerid', XMLDB_INDEX_NOTUNIQUE, array('packerid'));
        $indextopicid = new xmldb_index('idx_npe_activity_n4_topicid', XMLDB_INDEX_NOTUNIQUE, array('topicid'));
        $indexproductid = new xmldb_index('idx_npe_activity_n4_productid', XMLDB_INDEX_NOTUNIQUE, array('productid'));

        if (!$dbman->index_exists($table, $indexpackerid)) {
            $dbman->add_index($table, $indexpackerid);
        }

        if (!$dbman->index_exists($table, $indextopicid)) {
            $dbman->add_index($table, $indextopicid);
        }

        if (!$dbman->index_exists($table, $indexproductid)) {
            $dbman->add_index($table, $indexproductid);
        }

        // Agregar campo en Tabla de Actividades.
        $activitytable = new xmldb_table('npe_activity');
        $fieldactivityn4id = new xmldb_field('activityn4id', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'activitysectionid');

        if (!$dbman->field_exists($activitytable, $fieldactivityn4id)) {
            $dbman->add_field($activitytable, $fieldactivityn4id);
        }

        // Eliminar campo en Tabla de Actividades.
        $fieldn4title = new xmldb_field('n4title', XMLDB_TYPE_CHAR, '255');

        if ($dbman->field_exists($activitytable, $fieldn4title)) {
            $dbman->drop_field($activitytable, $fieldn4title);
        }

        // Adding index.
        $indexactivityn4id = new xmldb_index('idx_npe_activity_n4id', XMLDB_INDEX_NOTUNIQUE, array('activityn4id'));

        if (!$dbman->index_exists($activitytable, $indexactivityn4id)) {
            $dbman->add_index($activitytable, $indexactivityn4id);
        }


        // Define table to be created.
        $table = new xmldb_table('npe_activity_grade_student');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('activityid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('userid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('checked', XMLDB_TYPE_INTEGER, '1', null, null, null,0);
        $table->add_field('grade', XMLDB_TYPE_FLOAT, '10, 2');
        $table->add_field('gradedate', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('deliverydate', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('reopendate', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $indexcourseid = new xmldb_index('idx_npe_student_activity_grade_courseid', XMLDB_INDEX_NOTUNIQUE, array('courseid'));
        $indexactivityid = new xmldb_index('idx_npe_student_activity_grade_activityid', XMLDB_INDEX_NOTUNIQUE, array('activityid'));
        $indexuserid = new xmldb_index('idx_npe_student_activity_grade_userid', XMLDB_INDEX_NOTUNIQUE, array('userid'));


        if (!$dbman->index_exists($table, $indexcourseid)) {
            $dbman->add_index($table, $indexcourseid);
        }

        if (!$dbman->index_exists($table, $indexactivityid)) {
            $dbman->add_index($table, $indexactivityid);
        }

        if (!$dbman->index_exists($table, $indexuserid)) {
            $dbman->add_index($table, $indexuserid);
        }


        // Define table to be created.
        $table = new xmldb_table('npe_activity_grade_team');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('activityid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('teamid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('checked', XMLDB_TYPE_INTEGER, '1', null, null, null,0);
        $table->add_field('grade', XMLDB_TYPE_FLOAT, '10, 2');
        $table->add_field('gradedate', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('deliverydate', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('reopendate', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $indexcourseid = new xmldb_index('idx_npe_student_activity_grade_courseid', XMLDB_INDEX_NOTUNIQUE, array('courseid'));
        $indexactivityid = new xmldb_index('idx_npe_student_activity_grade_activityid', XMLDB_INDEX_NOTUNIQUE, array('activityid'));
        $indexuserid = new xmldb_index('idx_npe_student_activity_grade_teamid', XMLDB_INDEX_NOTUNIQUE, array('teamid'));


        if (!$dbman->index_exists($table, $indexcourseid)) {
            $dbman->add_index($table, $indexcourseid);
        }

        if (!$dbman->index_exists($table, $indexactivityid)) {
            $dbman->add_index($table, $indexactivityid);
        }

        if (!$dbman->index_exists($table, $indexuserid)) {
            $dbman->add_index($table, $indexuserid);
        }


        // Define table to be created.
        $table = new xmldb_table('npe_activity_config');
        // Adding fields
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('activityid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('hidegrade', XMLDB_TYPE_INTEGER, '1');
        $table->add_field('showanswer', XMLDB_TYPE_INTEGER, '1');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $indexcourseid = new xmldb_index('idx_npe_activity_config_courseid', XMLDB_INDEX_NOTUNIQUE, array('courseid'));
        $indexactivityid = new xmldb_index('idx_npe_activity_config_activityid', XMLDB_INDEX_NOTUNIQUE, array('activityid'));


        if (!$dbman->index_exists($table, $indexcourseid)) {
            $dbman->add_index($table, $indexcourseid);
        }

        if (!$dbman->index_exists($table, $indexactivityid)) {
            $dbman->add_index($table, $indexactivityid);
        }


        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < 2022072601) {
        // Creación de tabla de feedbacks para estudiantes individuales
        $table = new xmldb_table('npe_activity_feedback_stud');

        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('activityid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('fromuserid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('touserid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('comment', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $activityindex = new xmldb_index('idx_npe_activity_feedback_activityid', XMLDB_INDEX_NOTUNIQUE, array('activityid'));
        $courseindex = new xmldb_index('idx_npe_activity_feedback_courseid', XMLDB_INDEX_NOTUNIQUE, array('courseid'));
        $fromuserid = new xmldb_index('idx_npe_activity_feedback_fromuserid', XMLDB_INDEX_NOTUNIQUE, array('fromuserid'));
        $touserid = new xmldb_index('idx_npe_activity_feedback_touserid', XMLDB_INDEX_NOTUNIQUE, array('touserid'));

        if (!$dbman->index_exists($table, $activityindex)) {
            $dbman->add_index($table, $activityindex);
        }

        if (!$dbman->index_exists($table, $courseindex)) {
            $dbman->add_index($table, $courseindex);
        }

        if (!$dbman->index_exists($table, $fromuserid)) {
            $dbman->add_index($table, $fromuserid);
        }

        if (!$dbman->index_exists($table, $touserid)) {
            $dbman->add_index($table, $touserid);
        }

        // Creación de tabla de feedbacks para grupos
        $table = new xmldb_table('npe_activity_feedback_team');

        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('activityid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('fromuserid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('toteamid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('comment', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $activityindex = new xmldb_index('idx_npe_activity_feedback_activityid', XMLDB_INDEX_NOTUNIQUE, array('activityid'));
        $courseindex = new xmldb_index('idx_npe_activity_feedback_courseid', XMLDB_INDEX_NOTUNIQUE, array('courseid'));
        $fromuserid = new xmldb_index('idx_npe_activity_feedback_fromuserid', XMLDB_INDEX_NOTUNIQUE, array('fromuserid'));
        $toteam = new xmldb_index('idx_npe_activity_feedback_toteamid', XMLDB_INDEX_NOTUNIQUE, array('toteamid'));

        if (!$dbman->index_exists($table, $activityindex)) {
            $dbman->add_index($table, $activityindex);
        }

        if (!$dbman->index_exists($table, $courseindex)) {
            $dbman->add_index($table, $courseindex);
        }

        if (!$dbman->index_exists($table, $fromuserid)) {
            $dbman->add_index($table, $fromuserid);
        }

        if (!$dbman->index_exists($table, $toteam)) {
            $dbman->add_index($table, $toteam);
        }

        // Creación de tabla de feedbacks para toda la clase
        $table = new xmldb_table('npe_activity_feedback_class');

        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('activityid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('fromuserid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('comment', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $activityindex = new xmldb_index('idx_npe_activity_feedback_activityid', XMLDB_INDEX_NOTUNIQUE, array('activityid'));
        $courseindex = new xmldb_index('idx_npe_activity_feedback_courseid', XMLDB_INDEX_NOTUNIQUE, array('courseid'));
        $fromuserid = new xmldb_index('idx_npe_activity_feedback_fromuserid', XMLDB_INDEX_NOTUNIQUE, array('fromuserid'));

        if (!$dbman->index_exists($table, $activityindex)) {
            $dbman->add_index($table, $activityindex);
        }

        if (!$dbman->index_exists($table, $courseindex)) {
            $dbman->add_index($table, $courseindex);
        }

        if (!$dbman->index_exists($table, $fromuserid)) {
            $dbman->add_index($table, $fromuserid);
        }

        upgrade_plugin_savepoint(true, 2022072601, 'local', 'npe');
    }

    if ($oldversion < 2022072602) {
        // Creación de tabla de feedbacks para estudiantes individuales
        $table = new xmldb_table('npe_portfolio_feedback');

        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('fromuserid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('touserid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('comment', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Adding index.
        $courseindex = new xmldb_index('idx_npe_portfolio_feedback_courseid', XMLDB_INDEX_NOTUNIQUE, array('courseid'));
        $fromuserid = new xmldb_index('idx_npe_portfolio_feedback_fromuserid', XMLDB_INDEX_NOTUNIQUE, array('fromuserid'));
        $touserid = new xmldb_index('idx_npe_portfolio_feedback_touserid', XMLDB_INDEX_NOTUNIQUE, array('touserid'));

        if (!$dbman->index_exists($table, $courseindex)) {
            $dbman->add_index($table, $courseindex);
        }

        if (!$dbman->index_exists($table, $fromuserid)) {
            $dbman->add_index($table, $fromuserid);
        }

        if (!$dbman->index_exists($table, $touserid)) {
            $dbman->add_index($table, $touserid);
        }

        upgrade_plugin_savepoint(true, 2022072602, 'local', 'npe');
    }

    if ($oldversion < 2022072805) {

        $table = new xmldb_table('npe_access');
        // Nuevos campos de la tabla producto.
        $fieldcourseid = new xmldb_field('courseid', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'backurl');

        // Conditionally launch add field grade.
        if (!$dbman->field_exists($table, $fieldcourseid)) {
            $dbman->add_field($table, $fieldcourseid);
        }

        // Remove index
        $indexuser = new xmldb_index('', XMLDB_INDEX_UNIQUE, array('userid', 'codproduct'));
        if ($dbman->index_exists($table, $indexuser)) {
            $dbman->drop_index($table, $indexuser);
        }

        // Adding index.
        $userindex = new xmldb_index('idx_npe_access_userid', XMLDB_INDEX_NOTUNIQUE, array('userid'));
        $codproductindex = new xmldb_index('idx_npe_access_codproductid', XMLDB_INDEX_NOTUNIQUE, array('codproduct'));
        $courseindex = new xmldb_index('idx_npe_access_courseid', XMLDB_INDEX_NOTUNIQUE, array('courseid'));

        if (!$dbman->index_exists($table, $userindex)) {
            $dbman->add_index($table, $userindex);
        }

        if (!$dbman->index_exists($table, $codproductindex)) {
            $dbman->add_index($table, $codproductindex);
        }

        if (!$dbman->index_exists($table, $courseindex)) {
            $dbman->add_index($table, $courseindex);
        }

        upgrade_plugin_savepoint(true, 2022072805, 'local', 'npe');
    }

    if ($oldversion < 2022082400) {

        // Define table to be created.
        $table = new xmldb_table('npe_activity');

        // Nuevos campos de la tabla producto.
        $fieldcourseid = new xmldb_field('filename', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'sourceurl');

        // Conditionally launch add field grade.
        if (!$dbman->field_exists($table, $fieldcourseid)) {
            $dbman->add_field($table, $fieldcourseid);
        }

        upgrade_plugin_savepoint(true, 2022082400, 'local', 'npe');
    }

    if ($oldversion < 2022082600){
        $table = new xmldb_table('npe_activity_feedback_stud');
        $newfieldcomment = new xmldb_field('comment', XMLDB_TYPE_TEXT);
        if ($dbman->field_exists($table, $newfieldcomment)) {
            $dbman->change_field_type($table, $newfieldcomment);
        }

        $table = new xmldb_table('npe_activity_feedback_team');
        $newfieldcomment = new xmldb_field('comment', XMLDB_TYPE_TEXT);
        if ($dbman->field_exists($table, $newfieldcomment)) {
            $dbman->change_field_type($table, $newfieldcomment);
        }

        $table = new xmldb_table('npe_activity_feedback_class');
        $newfieldcomment = new xmldb_field('comment', XMLDB_TYPE_TEXT);
        if ($dbman->field_exists($table, $newfieldcomment)) {
            $dbman->change_field_type($table, $newfieldcomment);
        }

        $table = new xmldb_table('npe_portfolio_feedback');
        $newfieldcomment = new xmldb_field('comment', XMLDB_TYPE_TEXT);
        if ($dbman->field_exists($table, $newfieldcomment)) {
            $dbman->change_field_type($table, $newfieldcomment);
        }
        upgrade_plugin_savepoint(true, 2022082600, 'local', 'npe');
    }

    if ($oldversion < 2022090600) {
        $table = new xmldb_table('npe_student_evidence');

        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('activityid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('userid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('title', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('description', XMLDB_TYPE_TEXT, null, null, XMLDB_NOTNULL);
        $table->add_field('attachment', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');
        // Adding keys to table .
        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        $indexactivity = new xmldb_index('npe_evidence_activity_act_ix', XMLDB_INDEX_NOTUNIQUE, array('courseid'));
        $indexcourse = new xmldb_index('npe_evidence_activity_cou_ix', XMLDB_INDEX_NOTUNIQUE, array('courseid'));
        $indexuser = new xmldb_index('npe_evidence_activity_usr_ix', XMLDB_INDEX_NOTUNIQUE, array('userid'));

        if ($dbman->table_exists($table)) {
            $dbman->drop_table($table);
        }
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        if (!$dbman->index_exists($table, $indexactivity)) {
            $dbman->add_index($table, $indexactivity);
        }

        if (!$dbman->index_exists($table, $indexcourse)) {
            $dbman->add_index($table, $indexcourse);
        }

        if (!$dbman->index_exists($table, $indexuser)) {
            $dbman->add_index($table, $indexuser);
        }

        $table = new xmldb_table('npe_activity');
        $iscustom = new xmldb_field('iscustom', XMLDB_TYPE_INTEGER, '1', null, null, null, 0, 'isassignable');

        if (!$dbman->field_exists($table, $iscustom)) {
            $dbman->add_field($table, $iscustom);
        }

        upgrade_plugin_savepoint(true, 2022090600, 'local', 'npe');
    }

    if ($oldversion < 2022110901) {

        // Define table to be created.
        $table = new xmldb_table('npe_activity_section');

        // Nuevos campos de la tabla producto.
        $fieldcourseid = new xmldb_field('enriching', XMLDB_TYPE_INTEGER, '1', null, XMLDB_NOTNULL, null, 0, 'productid');

        // Conditionally launch add field grade.
        if (!$dbman->field_exists($table, $fieldcourseid)) {
            $dbman->add_field($table, $fieldcourseid);
        }

        upgrade_plugin_savepoint(true, 2022110901, 'local', 'npe');
    }

    if ($oldversion < 2022122300) {

        $table = new xmldb_table('npe_product');
        $field = new xmldb_field('language', XMLDB_TYPE_CHAR, '30');

        if ($dbman->field_exists($table, $field)) {
            $dbman->change_field_precision($table, $field);
        }

        upgrade_plugin_savepoint(true, 2022122300, 'local', 'npe');
    }

    if ($oldversion < 2023011301) {

        $table = new xmldb_table('npe_product_topic_ccaa');
        $fieldproductid = new xmldb_field('productid', XMLDB_TYPE_INTEGER, '10');
        $fieldtimecreated = new xmldb_field('timecreated', XMLDB_TYPE_INTEGER, '10', null, null, null,time());
        $fieldtimemodified = new xmldb_field('timemodified', XMLDB_TYPE_INTEGER, '10', null, null, null, time());

        // Conditionally launch add field grade.
        if (!$dbman->field_exists($table, $fieldproductid)) {
            $dbman->add_field($table, $fieldproductid);
        }

        if (!$dbman->field_exists($table, $fieldtimecreated)) {
            $dbman->add_field($table, $fieldtimecreated);
        }

        if (!$dbman->field_exists($table, $fieldtimemodified)) {
            $dbman->add_field($table, $fieldtimemodified);
        }

        // Adding keys to table .
        $indexproductid = new xmldb_index('idx_npe_product_topic_ccaa_productid', XMLDB_INDEX_NOTUNIQUE, array('productid'));

        if (!$dbman->index_exists($table, $indexproductid)) {
            $dbman->add_index($table, $indexproductid);
        }


        $table = new xmldb_table('npe_activity_ccaa');
        $fieldproductid = new xmldb_field('productid', XMLDB_TYPE_INTEGER, '10');
        $fieldtimecreated = new xmldb_field('timecreated', XMLDB_TYPE_INTEGER, '10', null, null, null,time());
        $fieldtimemodified = new xmldb_field('timemodified', XMLDB_TYPE_INTEGER, '10', null, null, null,time());

        // Conditionally launch add field grade.
        if (!$dbman->field_exists($table, $fieldproductid)) {
            $dbman->add_field($table, $fieldproductid);
        }

        if (!$dbman->field_exists($table, $fieldtimecreated)) {
            $dbman->add_field($table, $fieldtimecreated);
        }

        if (!$dbman->field_exists($table, $fieldtimemodified)) {
            $dbman->add_field($table, $fieldtimemodified);
        }

        // Adding keys to table .
        $indexproductid = new xmldb_index('idx_npe_activity_ccaa_productid', XMLDB_INDEX_NOTUNIQUE, array('productid'));

        if (!$dbman->index_exists($table, $indexproductid)) {
            $dbman->add_index($table, $indexproductid);
        }

        $notificationsuser = new xmldb_table('npe_notifications_user');

        // Eliminar el campo userguid a notifications_user
        $fielduserguid = new xmldb_field('userguid', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL, null, null);
        if ($dbman->field_exists($notificationsuser, $fielduserguid)) {
            $dbman->drop_field($notificationsuser, $fielduserguid);
        }

        $table = new xmldb_table('npe_student_evidence');

        $indexactivity = new xmldb_index('npe_evidence_activity_act_ix', XMLDB_INDEX_NOTUNIQUE, array('activityid'));

        if (!$dbman->index_exists($table, $indexactivity)) {
            $dbman->add_index($table, $indexactivity);
        }

        $table = new xmldb_table('npe_product_topic_ccaa');
        $defaultproducttopictc = new xmldb_field('timecreated', XMLDB_TYPE_INTEGER, '10', null, null, null, null);
        if ($dbman->field_exists($table, $defaultproducttopictc)) {
            $dbman->change_field_default($table, $defaultproducttopictc);
        }

        $defaultproducttopictm = new xmldb_field('timemodified', XMLDB_TYPE_INTEGER, '10', null, null, null, null);
        if ($dbman->field_exists($table, $defaultproducttopictm)) {
            $dbman->change_field_default($table, $defaultproducttopictm);
        }

        $table = new xmldb_table('npe_activity_ccaa');
        $defaultactivityccaatc = new xmldb_field('timecreated', XMLDB_TYPE_INTEGER, '10', null, null, null, null);
        if ($dbman->field_exists($table, $defaultactivityccaatc)) {
            $dbman->change_field_default($table, $defaultactivityccaatc);
        }

        $defaultactivityccaatm = new xmldb_field('timemodified', XMLDB_TYPE_INTEGER, '10', null, null, null, null);
        if ($dbman->field_exists($table, $defaultactivityccaatm)) {
            $dbman->change_field_default($table, $defaultactivityccaatm);
        }

        // Cambios en Tabla de actividad.
        $table = new xmldb_table('npe_activity');
        $fieldorder = new xmldb_field('activityorder', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'insequence');

        if (!$dbman->field_exists($table, $fieldorder)) {
            $dbman->add_field($table, $fieldorder);
        }

        upgrade_plugin_savepoint(true, 2023011301, 'local', 'npe');
    }

    if ($oldversion < 2023020801) {
        $table = new xmldb_table('npe_student_evidence');
        $attachment = new xmldb_field('attachment', XMLDB_TYPE_INTEGER, '10');
        if ($dbman->field_exists($table, $attachment)) {
            $dbman->change_field_type($table, $attachment);
        }
        upgrade_plugin_savepoint(true, 2023020801, 'local', 'npe');
    }

    if ($oldversion < 2023021600) {
        // Cambios en Tabla de product_topic.
        $table = new xmldb_table('npe_product_topic');
        $fieldtopicorder = new xmldb_field('topicorder', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'parentid');

        if (!$dbman->field_exists($table, $fieldtopicorder)) {
            $dbman->add_field($table, $fieldtopicorder);
        }

        // Cambios en Tabla de product_topic_block.
        $table = new xmldb_table('npe_product_topic_block');
        $fieldtopicblockorder = new xmldb_field('topicblockorder', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'productid');

        if (!$dbman->field_exists($table, $fieldtopicblockorder)) {
            $dbman->add_field($table, $fieldtopicblockorder);
        }

        upgrade_plugin_savepoint(true, 2023021600, 'local', 'npe');
    }

    if ($oldversion < 2023021700) {
        $table = new xmldb_table('npe_activity');
        $lastupdatedate = new xmldb_field('lastupdatedate', XMLDB_TYPE_INTEGER, '10');

        if (!$dbman->field_exists($table, $lastupdatedate)) {
            $dbman->add_field($table, $lastupdatedate);
        }

        upgrade_plugin_savepoint(true, 2023021700, 'local', 'npe');
    }

    if ($oldversion < 2023022101) {
        // Cambios en Tabla de actividad.
        $table = new xmldb_table('npe_activity');
        $fieldcompetenceid = new xmldb_field('competenceid', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'activityorder');
        $fieldcriterionid = new xmldb_field('criterionid', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'competenceid');
        $fieldthemeid = new xmldb_field('themeid', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'criterionid');
        $fieldtransversekeyid = new xmldb_field('transversekeyid', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'themeid');

        if (!$dbman->field_exists($table, $fieldcompetenceid)) {
            $dbman->add_field($table, $fieldcompetenceid);
        }

        if (!$dbman->field_exists($table, $fieldcriterionid)) {
            $dbman->add_field($table, $fieldcriterionid);
        }

        if (!$dbman->field_exists($table, $fieldthemeid)) {
            $dbman->add_field($table, $fieldthemeid);
        }

        if (!$dbman->field_exists($table, $fieldtransversekeyid)) {
            $dbman->add_field($table, $fieldtransversekeyid);
        }

        upgrade_plugin_savepoint(true, 2023022101, 'local', 'npe');
    }

    if ($oldversion < 2023042000) {
        $table = new xmldb_table('npe_product_topic');
        $fieldtopicorder = new xmldb_field('topicorder', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'parentid');

        if ($dbman->field_exists($table, $fieldtopicorder)) {
            $dbman->change_field_type($table, $fieldtopicorder);
        }

        upgrade_plugin_savepoint(true, 2023042000, 'local', 'npe');
    }

    if ($oldversion < 2023042002) {
        // Correcciones de estructura de bbdd - TENPE2-298
        $tabletbo = new xmldb_table('npe_product_topic_block');
        $fieldtopicblockorder = new xmldb_field('topicblockorder', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'productid');
        if ($dbman->field_exists($tabletbo, $fieldtopicblockorder)) {
            $dbman->change_field_type($tabletbo, $fieldtopicblockorder);
        }

        $tablese = new xmldb_table('npe_student_evidence');
        $attachment = new xmldb_field('attachment', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null, 'description');
        if ($dbman->field_exists($tablese, $attachment)) {
            $dbman->change_field_type($tablese, $attachment);
        }

        $tableac = new xmldb_table('npe_activity');
        $fieldfilename = new xmldb_field('filename', XMLDB_TYPE_CHAR, '255');
        if ($dbman->field_exists($tableac, $fieldfilename)) {
            $dbman->drop_field($tableac, $fieldfilename);
        }

        $tableafs = new xmldb_table('npe_activity_feedback_stud');
        $commentafs = new xmldb_field('comment', XMLDB_TYPE_CHAR, '255');
        if ($dbman->field_exists($tableafs, $commentafs)) {
            $dbman->change_field_type($tableafs, $commentafs);
        }

        $tableaft = new xmldb_table('npe_activity_feedback_team');
        $commentaft = new xmldb_field('comment', XMLDB_TYPE_CHAR, '255');
        if ($dbman->field_exists($tableaft, $commentaft)) {
            $dbman->change_field_type($tableaft, $commentaft);
        }

        $tableafc = new xmldb_table('npe_activity_feedback_class');
        $commentafc = new xmldb_field('comment', XMLDB_TYPE_CHAR, '255');
        if ($dbman->field_exists($tableafc, $commentafc)) {
            $dbman->change_field_type($tableafc, $commentafc);
        }

        $tablepf = new xmldb_table('npe_portfolio_feedback');
        $commentpf = new xmldb_field('comment', XMLDB_TYPE_CHAR, '255');
        if ($dbman->field_exists($tablepf, $commentpf)) {
            $dbman->change_field_type($tablepf, $commentpf);
        }

        upgrade_plugin_savepoint(true, 2023042002, 'local', 'npe');
    }

    if ($oldversion < 2023051201) {
        // Cambios en Tabla de actividad.
        $table = new xmldb_table('npe_activity');
        $fieldorder = new xmldb_field('origin', XMLDB_TYPE_CHAR, '10', null, null, null, null, 'insequence');

        if (!$dbman->field_exists($table, $fieldorder)) {
            $dbman->add_field($table, $fieldorder);
        }

        upgrade_plugin_savepoint(true, 2023051201, 'local', 'npe');
    }

    if ($oldversion < 2023051600) {
        // Cambios en Tabla de actividad.
        $table = new xmldb_table('npe_activity');

        $fieldskillsid = new xmldb_field('skillsid', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'transversekeyid');
        if (!$dbman->field_exists($table, $fieldskillsid)) {
            $dbman->add_field($table, $fieldskillsid);
        }

        $fieldpedagogicalpurposesid= new xmldb_field('pedagogicalpurposesid', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'skillsid');
        if (!$dbman->field_exists($table, $fieldpedagogicalpurposesid)) {
            $dbman->add_field($table, $fieldpedagogicalpurposesid);
        }

        upgrade_plugin_savepoint(true, 2023051600, 'local', 'npe');
    }

    if ($oldversion < **********) {
        // Cambios en la tabla de claves de matriculacion
        $table = new xmldb_table('npe_enrolment_keys');
        $fieldexpired = new xmldb_field('expired', XMLDB_TYPE_INTEGER, '1', null, XMLDB_NOTNULL, null, 0, 'usermodified');
        if (!$dbman->field_exists($table, $fieldexpired)) {
            $dbman->add_field($table, $fieldexpired);
        }

        // Cambios en Tabla de licencias de usuario.
        $table = new xmldb_table('npe_user_license');
        $fielnoticenewcourse = new xmldb_field('noticenewcourse', XMLDB_TYPE_INTEGER, '1', null, null, null, 0, 'cu');
        if (!$dbman->field_exists($table, $fielnoticenewcourse)) {
            $dbman->add_field($table, $fielnoticenewcourse);
        }

        // Cambios en Tabla de actividad.
        $table = new xmldb_table('npe_activity');
        $fieldassessmentid = new xmldb_field('assessmentid', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'pedagogicalpurposesid');
        if (!$dbman->field_exists($table, $fieldassessmentid)) {
            $dbman->add_field($table, $fieldassessmentid);
        }
        $fieldlearninglevelid = new xmldb_field('learninglevelid', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'assessmentid');
        if (!$dbman->field_exists($table, $fieldlearninglevelid)) {
            $dbman->add_field($table, $fieldlearninglevelid);
        }

        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < 2023081602) {
        //Cambios en la tabla nperepository_teacher
        $tablert = new xmldb_table('nperepository_teacher');
        $descriptionrt = new xmldb_field('description', XMLDB_TYPE_TEXT);
        if ($dbman->field_exists($tablert, $descriptionrt)) {
            $dbman->change_field_type($tablert, $descriptionrt);
        }

        //Cambios en la tabla nperepository_teacher_uses
        $tablertu = new xmldb_table('nperepository_teacher_uses');
        $descriptionrtu = new xmldb_field('description', XMLDB_TYPE_TEXT);
        if ($dbman->field_exists($tablertu, $descriptionrtu)) {
            $dbman->change_field_type($tablertu, $descriptionrtu);
        }

        upgrade_plugin_savepoint(true, 2023081602, 'local', 'npe');
    }

    if ($oldversion < 2023101100) {
        // Cambios en Tabla de actividad.
        $table = new xmldb_table('npe_activity');
        $fieldtrackingactivitiesid = new xmldb_field('trackingactivitiesid', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'learninglevelid');
        if (!$dbman->field_exists($table, $fieldtrackingactivitiesid)) {
            $dbman->add_field($table, $fieldtrackingactivitiesid);
        }
        $fieldchallengesid= new xmldb_field('challengesid', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'trackingactivitiesid');
        if (!$dbman->field_exists($table, $fieldchallengesid)) {
            $dbman->add_field($table, $fieldchallengesid);
        }
        $fieldincontextid= new xmldb_field('incontextid', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'challengesid');
        if (!$dbman->field_exists($table, $fieldincontextid)) {
            $dbman->add_field($table, $fieldincontextid);
        }
        upgrade_plugin_savepoint(true, 2023101100, 'local', 'npe');
    }

    if ($oldversion < 2023111000) {
        // Cambios en Tabla de actividad.
        $table = new xmldb_table('npe_activity');
        $fieldtypeofactivityid = new xmldb_field('typeofactivityid', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'incontextid');
        if (!$dbman->field_exists($table, $fieldtypeofactivityid)) {
            $dbman->add_field($table, $fieldtypeofactivityid);
        }
        $fieldpresentationresourcesid= new xmldb_field('presentationresourcesid', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'typeofactivityid');
        if (!$dbman->field_exists($table, $fieldpresentationresourcesid)) {
            $dbman->add_field($table, $fieldpresentationresourcesid);
        }
        upgrade_plugin_savepoint(true, 2023111000, 'local', 'npe');
    }

    if ($oldversion < 2023122800) {
        // Cambios en tabla de assign de core.
        records_npe::add_index_to_assign();
        upgrade_plugin_savepoint(true, 2023122800, 'local', 'npe');
    }

    if ($oldversion < 2024012300) {
        // Cambios en Tabla de producto.
        $table = new xmldb_table('npe_product');
        $fieldtheme = new xmldb_field('theme', XMLDB_TYPE_CHAR, '30', null, null, null, 'defaultTheme', 'repositoryjsonhash');
        if (!$dbman->field_exists($table, $fieldtheme)) {
            $dbman->add_field($table, $fieldtheme);
        }
        upgrade_plugin_savepoint(true, 2024012300, 'local', 'npe');
    }

    if ($oldversion < 2024022700) {
        // Cambios en Tabla de activity (subcategorias).
        $table = new xmldb_table('npe_activity');
        $fieldtheme = new xmldb_field('subcategoryid', XMLDB_TYPE_CHAR, '50', null, null, null, null, 'presentationresourcesid');
        if (!$dbman->field_exists($table, $fieldtheme)) {
            $dbman->add_field($table, $fieldtheme);
        }
        upgrade_plugin_savepoint(true, 2024022700, 'local', 'npe');
    }

    if ($oldversion < **********) {
        // Cambios en Tabla de Unidades/temas.
        $table = new xmldb_table('npe_product_topic');
        $fieldtopicjsonhash = new xmldb_field('unittitle', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'topicorder');

        if (!$dbman->field_exists($table, $fieldtopicjsonhash)) {
            $dbman->add_field($table, $fieldtopicjsonhash);
        }

        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < **********) {
        // Cambios en Tabla de Producto.
        $table = new xmldb_table('npe_product');
        $fieldfamilyurl = new xmldb_field('familyurl', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'familylicense');
        if (!$dbman->field_exists($table, $fieldfamilyurl)) {
            $dbman->add_field($table, $fieldfamilyurl);
        }
        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < **********) {
        // Eliminar cualquier campo relacionado con la licencia de BBDD.
        $tableactivity = new xmldb_table('npe_activity');
        $fieldlicenseactivity = new xmldb_field('license');
        if ($dbman->field_exists($tableactivity, $fieldlicenseactivity)) {
            $dbman->drop_field($tableactivity, $fieldlicenseactivity);
        }

        $tableactivitysection = new xmldb_table('npe_activity_section');
        $fieldlicenseactivitysection = new xmldb_field('license');
        if ($dbman->field_exists($tableactivitysection, $fieldlicenseactivitysection)) {
            $dbman->drop_field($tableactivitysection, $fieldlicenseactivitysection);
        }

        $tableuserlicense = new xmldb_table('npe_user_license');
        $fieldlicensetype = new xmldb_field('licensetype');
        if ($dbman->field_exists($tableuserlicense, $fieldlicensetype)) {
            $dbman->drop_field($tableuserlicense, $fieldlicensetype);
        }

        $tableproduct = new xmldb_table('npe_product');
        $fieldfamilylicense = new xmldb_field('familylicense');

        if ($dbman->field_exists($tableproduct, $fieldfamilylicense)) {
            $dbman->drop_field($tableproduct, $fieldfamilylicense);
        }

        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < **********) {
        // Creación de tabla de opciones de curso.
        $table = new xmldb_table('npe_activity_subcategory');
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('packerid', XMLDB_TYPE_CHAR, '255');
        $table->add_field('subcategory', XMLDB_TYPE_CHAR, '50');
        $table->add_field('name', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('sort', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('productid', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');
        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }
        // Adding index.
        $idtindex = new xmldb_index('idx_npe_activity_subcategory_id', XMLDB_INDEX_UNIQUE, array('id'));
        if (!$dbman->index_exists($table, $idtindex)) {
            $dbman->add_index($table, $idtindex);
        }
        $indexpackerid = new xmldb_index('idx_npe_activity_subcategory_packerid', XMLDB_INDEX_NOTUNIQUE, array('packerid'));
        if (!$dbman->index_exists($table, $indexpackerid)) {
            $dbman->add_index($table, $indexpackerid);
        }
        $indexsubcategory = new xmldb_index('idx_npe_activity_subcategory_subcategory', XMLDB_INDEX_NOTUNIQUE, array('subcategory'));
        if (!$dbman->index_exists($table, $indexsubcategory)) {
            $dbman->add_index($table, $indexsubcategory);
        }
        $indexproductid = new xmldb_index('idx_npe_activity_subcategory_productid', XMLDB_INDEX_NOTUNIQUE, array('productid'));
        if (!$dbman->index_exists($table, $indexproductid)) {
            $dbman->add_index($table, $indexproductid);
        }

        //Rename previous table prefix.
        $table = new xmldb_table('npe_activity');
        $fieldactivitysubcategoryid = new xmldb_field('subcategoryid', XMLDB_TYPE_CHAR, '50');
        $fieldsubcategory = new xmldb_field('subcategory', XMLDB_TYPE_CHAR, '50');
        if ($dbman->field_exists($table, $fieldactivitysubcategoryid) && !$dbman->field_exists($table, $fieldsubcategory)) {
            $dbman->rename_field($table, $fieldactivitysubcategoryid, 'subcategory');
        }

        $fieldtheme = new xmldb_field('subcategoryid', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'subcategory');
        if (!$dbman->field_exists($table, $fieldtheme)) {
            $dbman->add_field($table, $fieldtheme);
        }

        $indexsubcategoryid = new xmldb_index('idx_npe_activity_subcategoryid', XMLDB_INDEX_NOTUNIQUE, array('subcategoryid'));
        if (!$dbman->index_exists($table, $indexsubcategoryid)) {
            $dbman->add_index($table, $indexsubcategoryid);
        }

        upgrade_plugin_savepoint(true, **********, 'local', 'npe');
    }

    if ($oldversion < 2025011000) {
        // Creación de tabla de opciones de curso.
        $table = new xmldb_table('npe_course_options');

        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('ccaa', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('codproduct', XMLDB_TYPE_CHAR, '50', null, XMLDB_NOTNULL);
        $table->add_field('educamoscourse', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, 0);
        $table->add_field('externalcourse', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, 0);
        $table->add_field('externalgroupname', XMLDB_TYPE_CHAR, '255');
        $table->add_field('marsupialcourse', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, 0);
        $table->add_field('marsupialgroupname', XMLDB_TYPE_CHAR, '255');
        $table->add_field('studentcourse', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, 0);
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        $courseindex = new xmldb_index('npe_course_options_cou_ix', XMLDB_INDEX_UNIQUE, array('courseid'));

        if (!$dbman->index_exists($table, $courseindex)) {
            $dbman->add_index($table, $courseindex);
        }

        upgrade_plugin_savepoint(true, 2025011000, 'local', 'npe');
    }

    if ($oldversion < 2025013100) {
        // Cambios en tabla de producto_topic.
        $table = new xmldb_table('npe_product_topic');
        $field = new xmldb_field('defaultviewer', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'digitalbook');
        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }
        upgrade_plugin_savepoint(true, 2025013100, 'local', 'npe');
    }
    
    if ($oldversion < 2025020100) {
        // Cambios en tabla de producto_topic.
        $table = new xmldb_table('npe_product_topic');
        $field = new xmldb_field('visibilityn2', XMLDB_TYPE_CHAR, '255', null, null, null, null, 'unittitle');
        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }
        upgrade_plugin_savepoint(true, 2025020100, 'local', 'npe');
    }

    if ($oldversion < 2025021200) {
        // Creación de tabla de para visibilidad de unidades.
        $table = new xmldb_table('npe_topic_teacher_perso');

        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE);
        $table->add_field('userid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL);
        $table->add_field('packerid', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL);
        $table->add_field('custom', XMLDB_TYPE_INTEGER, '1', null, XMLDB_NOTNULL, null, 0);
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        $userindex = new xmldb_index('idx_npe_topic_teacher_perso_userid', XMLDB_INDEX_NOTUNIQUE, array('userid'));

        if (!$dbman->index_exists($table, $userindex)) {
            $dbman->add_index($table, $userindex);
        }

        $courseindex = new xmldb_index('idx_npe_topic_teacher_perso_courseid', XMLDB_INDEX_NOTUNIQUE, array('courseid'));

        if (!$dbman->index_exists($table, $courseindex)) {
            $dbman->add_index($table, $courseindex);
        }

        $packerindex = new xmldb_index('idx_npe_topic_teacher_perso_packerid', XMLDB_INDEX_NOTUNIQUE, array('packerid'));

        if (!$dbman->index_exists($table, $packerindex)) {
            $dbman->add_index($table, $packerindex);
        }

        upgrade_plugin_savepoint(true, 2025021200, 'local', 'npe');
    }

    if ($oldversion < 2025051200) {
        unset_config('revuela_clientid');
        unset_config('revuela_clientsecret');
        unset_config('revuela_urlauth');
        unset_config('message_manager_clientid');
        unset_config('message_manager_clientsecret');
        unset_config('message_manager_urlauth');
        unset_config('api_imagenes_clientid', 'local_npe');
        unset_config('api_imagenes_clientsecret', 'local_npe');
        unset_config('api_imagenes_urlauth', 'local_npe');

        upgrade_plugin_savepoint(true, 2025051200,  'local', 'npe');
    }

    return true;
}

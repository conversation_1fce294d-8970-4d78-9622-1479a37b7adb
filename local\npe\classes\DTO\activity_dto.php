<?php

namespace local_npe\DTO;

use local_npe\base\stdclass_transform;
use local_npe\core\activity_core;

class activity_dto extends stdclass_transform {

    /** @var int */
    protected ?int $id;
    /** @var string */
    protected ?string $name = '';
    /** @var string */
    protected ?string $activitynum;
    /** @var string */
    protected string $clasification;
    /** @var bool */
    protected ?bool $keyevidence;
    /** @var string */
    protected ?string $visibility;
    /** @var string */
    protected ?string $visibilitylabel;
    /** @var string */
    protected ?string $difficultylevel;
    /** @var string */
    protected ?string $h5pconfiguration;
    /** @var string */
    protected ?string $mapperassignment;
    /** @var bool */
    protected ?bool $progexam;
    /** @var string */
    protected string $idnumber;
    /** @var  string */
    protected ?string $type;
    /** @var string */
    protected ?string $typeid;
    /** @var string */
    protected ?string $typelabel;
    /** @var string */
    protected ?string $license;
    /** @var string */
    protected ?string $sourceurl;
    /** @var string */
    protected ?string $filename;
    /** @var int */
    protected int $productid;
    protected ?string $codproduct;
    /** @var string */
    protected ?string $textanswertype;
    /** @var int */
    protected int $iscustom;
    /** @var string */
    protected ?string $activityorder;
    /** @var string */
    protected ?string $competenceid;
    /** @var string */
    protected ?string $criterionid;
    /** @var string */
    protected ?string $themeid;
    /** @var string */
    protected ?string $transversekeyid;
    protected ?string $skillsid;
    protected ?string $pedagogicalpurposesid;
    protected ?string $assessmentid;
    protected ?string $learninglevelid;
    protected ?string $trackingactivitiesid;
    protected ?string $challengesid;
    protected ?string $incontextid;
    protected ?string $typeofactivityid;
    protected ?string $presentationresourcesid;

    // Categoría de la actividad.
    /** @var int */
    protected ?int $activitycategoryid; // ID
    /** @var string */
    protected ?string $activitycategoryname; // NAME
    /** @var string */
    protected ?string $activitycategorypackerid; // PACKERID
    /** @var string */
    protected ?string $activitycategorycategory; // CATEGORY

    // Subcategoría de la actividad.
    /** @var int */
    protected ?int $subcategoryid; // ID
     /** @var string */
    protected ?string $subcategory; // SUBCATEGORY

    // Sección de la actividad.
    /** @var int */
    protected ?int $activitysectionid; // ID
    /** @var string */
    protected ?string $activitysectionlabel; // LABEL

    /** @var string */
    protected ?string $activitysection; // SECTION

    // Tema o Unidad a la que pertenece
    /** @var int */
    protected ?int $producttopicid; // ID
    /** @var string */
    protected ?string $producttopicname; // NAME
    /** @var string */
    protected ?string $producttopicpackerid; // PACKERID

    /** @var string */
    protected ?string $producttopicblockpackerid; // PACKERID

    /** @var int */
    protected ?int $activityn4id; // ID
    /** @var string */
    protected ?string $n4packerid; // PACKERID
    /** @var string */
    protected ?string $n4title; // TITLE

    /** @var string */
    protected ?string $defaultdelivery; // single, group, grandgroup
    /** @var bool $isassignable */
    protected bool $isassignable;
    /** @var bool $showtostudent */
    protected bool $showtostudent;
    /** @var bool $insequence */
    protected bool $insequence; // si la actividad está o no en la sequencia. Debe ser que algunas no vienen dentro de un tema dado (similar a los refuerzos y test de lectura que son libres de asignar)
    /** @var ?string $origin */
    protected ?string $origin = null;
    /** @var ?int $lastupdatedate */
    protected ?int $lastupdatedate = null;
    /** @var int timecreated */
    protected int $timecreated;
    /** @var int timemodified */
    protected int $timemodified;

    /** Los atributos privados no se exportan con el get_data() */

    private bool $pendingupdate = false;
    /** @var string[] $moodleactivities */
    private array $moodleactivities = [
        'assign',
        'forum',
        'generalforum',
    ];


    protected function get_alias(): array {
        return [
            'repositoryTitle' => 'name',
            'recommendedGroupingId' => 'defaultdelivery',
            'activityLogic' => 'type',
            'activityTypeId' => 'typeid',
            'activityTypeLabel' => 'typelabel',
            'behaviour' => 'clasification',
            'activity' => 'sourceurl',
            'resource' => 'sourceurl',
            'keyEvidenceId' => 'keyevidence',
            'difficultyLevelId' => 'difficultylevel',
            'order' => 'activityorder',
            'competenceId' => 'competenceid',
            'criterionId' => 'criterionid',
            'themeId' => 'themeid',
            'transverseKeyId' => 'transversekeyid',
            'skillsId' => 'skillsid',
            'pedagogicalPurposesId' => 'pedagogicalpurposesid',
            'assessmentId' => 'assessmentid',
            'learningLevelId' => 'learninglevelid',
            'trackingActivitiesId' => 'trackingactivitiesid',
            'challengesId' => 'challengesid',
            'inContextId' => 'incontextid',
            'subcategoryId' => 'subcategory',
        ];
    }

    /**
     * @param $v
     * @return int|null
     */
    protected function _prepare_id($v) {
        return is_numeric($v) ? $v : null;
    }

    /**
     * Truncar el activitynum si se pasa del tamaño establecido.
     *
     * @param $v
     * @return false|string
     */
    protected function _prepare_activitynum($v) {
        return is_string($v) && mb_strlen($v) > 50 ? mb_substr($v,0,50) : $v;
    }

    protected function _prepare_defaultdelivery($v) {
        $map = [
            'individual'    => 'single',
            'team'          => 'group',
            'bigGroup'      => 'grandgroup',
        ];
        return $map[$v] ?? $v;
    }

    protected function _prepare_sourceurl($v) {
        $this->filename = $v[0]->name ?? null;
        $cmurl = $v[0]->cm_url ?? null;
        return $cmurl ?: (is_string($v) ? $v : null);
    }

    protected function _prepare_name($v) {
        return empty($v) ? ' ' : $v;
    }

    /**
     * Se pasa a mayúscula y se eliminan espacios.
     *
     * @param $v
     * @return string
     */
    protected function _prepare_textanswertype($v) {
        return is_string($v) ? trim(mb_strtoupper($v)) : $v;
    }

    /**
     * Convierte a timestamp en caso necesario.
     *
     * @param $v
     * @return false|int
     */
    protected function _prepare_lastupdatedate($v) {
        if(empty($v)){
            return true;
        }
        return is_numeric($v) ? (int) $v : strtotime($v);
    }

    /**
     * @return int
     */
    public function get_id() {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function set_id(int $id): void {
        $this->id = $id;
    }

    /**
     * @return string
     */
    public function get_name() {
        if ($this->insequence) {
            $concat = $this->activitynum ? ', ' : '';
            $concatscore = !empty($this->name) && trim($this->name) !== '' ? ' - ' : '';
            if ($this->n4title) {
                return $this->n4title . $concat . $this->activitynum . $concatscore . $this->name;
            } else if ($this->activitynum) {
                return $this->activitynum . $concatscore . $this->name;
            }
        }
        return $this->name;
    }

    /**
     * @param string $name
     */
    public function set_name(string $name): void {
        $this->name = $name;
    }

    /**
     * @return int
     */
    public function get_activity_n4_id(): ?int {
        return $this->activityn4id;
    }

    /**
     * @param int $activityn4id
     */
    public function set_activity_n4_id(int $activityn4id): void {
        $this->activityn4id = $activityn4id;
    }

    /**
     * @return string
     */
    public function get_n4_packerid(): ?string {
        return $this->n4packerid;
    }

    /**
     * @return string
     */
    public function get_n4_title(): ?string {
        return $this->n4title;
    }

    /**
     * @return string|null
     */
    public function get_activity_num(): ?string {
        return $this->activitynum;
    }

    /**
     * @param string $activitynum
     */
    public function set_activity_num(string $activitynum): void {
        $this->activitynum = $activitynum;
    }

    /**
     * @return string|null
     */
    public function get_order(): ?string {
        return $this->activityorder;
    }

    /**
     * @param string|null $order
     */
    public function set_order(?string $order): void {
        $this->activityorder = $order;
    }

    /**
     * @return string|null
     */
    public function get_competence(): ?string {
        return $this->competenceid;
    }

    /**
     * @param string|null $competence
     */
    public function set_competence(?string $competence): void {
        $this->competenceid = $competence;
    }

    /**
     * @return string|null
     */
    public function get_criterion(): ?string {
        return $this->criterionid;
    }

    /**
     * @param string|null $criterion
     */
    public function set_criterion(?string $criterion): void {
        $this->criterionid= $criterion;
    }

    /**
     * @return string|null
     */
    public function get_theme(): ?string {
        return $this->themeid;
    }

    /**
     * @param string|null $themeid
     */
    public function set_theme(?string $themeid): void {
        $this->themeid = $themeid;
    }

    /**
     * @return string|null
     */
    public function get_transversekey(): ?string {
        return $this->transversekeyid;
    }

    /**
     * @param string|null $transversekeyid
     */
    public function set_transversekey(?string $transversekeyid): void {
        $this->transversekeyid = $transversekeyid;
    }

    /**
     * @return string|null
     */
    public function get_skill(): ?string {
        return $this->skillsid;
    }

    /**
     * @param string|null $skillsid
     */
    public function set_skills(?string $skillsid): void {
        $this->skillsid = $skillsid;
    }

    /**
     * @return string|null
     */
    public function get_pedagogical_purposes(): ?string {
        return $this->pedagogicalpurposesid;
    }

    /**
     * @param string|null $pedagogicalpurposesid
     */
    public function set_pedagogical_purposes(?string $pedagogicalpurposesid): void {
        $this->pedagogicalpurposesid = $pedagogicalpurposesid;
    }

    /**
     * @return string|null
     */
    public function get_assessment_id(): ?string {
        return $this->assessmentid;
    }
    /**
     * @param string|null $assessmentid
     */
    public function set_assessment_id(?string $assessmentid): void {
        $this->assessmentid = $assessmentid;
    }

    /**
     * @return string|null
     */
    public function get_learning_level_id(): ?string {
        return $this->learninglevelid;
    }
    /**
     * @param string|null $learninglevelid
     */
    public function set_learning_level_id(?string $learninglevelid): void {
        $this->learninglevelid = $learninglevelid;
    }

    /**
     * @return string|null
     */
    public function get_tracking_activities_id(): ?string {
        return $this->trackingactivitiesid;
    }
    /**
     * @param string|null $trackingactivitiesid
     */
    public function set_tracking_activities_id(?string $trackingactivitiesid): void {
        $this->trackingactivitiesid = $trackingactivitiesid;
    }

    /**
     * @return string|null
     */
    public function get_challenges_id(): ?string {
        return $this->challengesid;
    }
    /**
     * @param string|null $challengesid
     */
    public function set_challenges_id(?string $challengesid): void {
        $this->challengesid = $challengesid;
    }

    /**
     * @return string|null
     */
    public function get_in_context_id(): ?string {
        return $this->incontextid;
    }
    /**
     * @param string|null $incontextid
     */
    public function set_in_context_id(?string $incontextid): void {
        $this->incontextid = $incontextid;
    }

    /**
     * @return string
     */
    public function get_clasification(): ?string {
        return $this->clasification;
    }

    /**
     * @param string $clasification
     */
    public function set_clasification(string $clasification): void {
        $this->clasification = $clasification;
    }

    /**
     * @return string|null
     */
    public function get_type_of_activity_id(): ?string {
        return $this->typeofactivityid;
    }
    /**
     * @param string|null $typeofactivityid
     */
    public function set_type_of_activity_id(?string $typeofactivityid): void {
        $this->typeofactivityid = $typeofactivityid;
    }

    /**
     * @return string|null
     */
    public function get_presentation_resources_id(): ?string {
        return $this->presentationresourcesid;
    }
    /**
     * @param string|null $presentationresourcesid
     */
    public function set_presentation_resources_id(?string $presentationresourcesid): void {
        $this->presentationresourcesid = $presentationresourcesid;
    }

    /**
     * @return int|null
     */
    public function get_subcategory_id(): ?int {
        return $this->subcategoryid;
    }

    /**
     * @param int|null $subcategoryid
     */
    public function set_subcategory_id(?int $subcategoryid): void {
        $this->subcategoryid = $subcategoryid;
    }

    /**
     * @return string
     */
    public function get_activity_subcategory() {
        return $this->subcategory;
    }

    /**
     * @param string|null $subcategory
     */
    public function set_activity_subcategory(?string $subcategory): void {
        $this->subcategory = $subcategory;
    }

    /**
     * @return string
     */
    public function get_idnumber(): string {
        return $this->idnumber;
    }

    /**
     * @param string $idnumber
     */
    public function set_idnumber(string $idnumber): void {
        $this->idnumber = $idnumber;
    }

    /**
     * @return string|null
     */
    public function get_type(): ?string {
        return $this->type;
    }

    /**
     * @param string $type
     */
    public function set_type(string $type): void {
        $this->type = $type;
    }

    /**
     * @return string
     */
    public function get_type_id(): ?string {
        return $this->typeid;
    }

    /**
     * @param string $typeid
     */
    public function set_type_id(string $typeid): void {
        $this->typeid = $typeid;
    }

    /**
     * @return string
     */
    public function get_type_label(): ?string {
        return $this->typelabel;
    }

    /**
     * @param string $typelabel
     */
    public function set_type_label(string $typelabel): void {
        $this->typelabel = $typelabel;
    }

    /**
     * @return string|null
     */
    public function get_source_url() {
        return !empty($this->sourceurl) ? trim($this->sourceurl, '/') : null;
    }

    /**
     * @return string|null
     */
    public function get_datajson_url() {
        $ext = substr($this->sourceurl,-1) === '/' ? 'data.json' : null;
        return $ext !== null ? $this->sourceurl.$ext : null;
    }

    /**
     * @return string|null
     */
    public function get_filename(): ?string {
        return $this->filename;
    }

    /**
     * @param string $sourceurl
     */
    public function set_source_url(string $sourceurl): void {
        $this->sourceurl = $sourceurl;
    }

    /**
     * @return int
     */
    public function get_productid() {
        return $this->productid;
    }

    /**
     * @param int $productid
     */
    public function set_productid(int $productid): void {
        $this->productid = $productid;
    }

    /**
     * @return mixed
     */
    public function get_codproduct() {
        return $this->codproduct;
    }

    /**
     * @param string $codproduct
     */
    public function set_codproduct(string $codproduct): void {
        $this->codproduct = $codproduct;
    }

    /**
     * @return int
     */
    public function get_product_topic_block_packerid() {
        return $this->producttopicblockpackerid;
    }

    /**
     * @return int
     */
    public function get_activity_category_id() {
        return $this->activitycategoryid;
    }

    /**
     * @return int
     */
    public function get_activity_category_packerid() {
        return $this->activitycategorypackerid;
    }

    /**
     * @param int $activitycategoryid
     */
    public function set_activity_category_id(int $activitycategoryid): void {
        $this->activitycategoryid = $activitycategoryid;
    }

    /**
     * @return string
     */
    public function get_activity_category_name() {
        return $this->activitycategoryname;
    }

    /**
     * @param string $activitycategoryname
     */
    public function set_activity_category_name(string $activitycategoryname): void {
        $this->activitycategoryname = $activitycategoryname;
    }

    /**
     * @return string
     */
    public function get_activity_category_category() {
        return $this->activitycategorycategory;
    }

    /**
     * @return int
     */
    public function get_activity_section_id(): ?int {
        return $this->activitysectionid;
    }

    /**
     * @param int $activitysectionid
     */
    public function set_activity_section_id(int $activitysectionid): void {
        $this->activitysectionid = $activitysectionid;
    }

    /**
     * @return string
     */
    public function get_activity_section_label(): string {
        return $this->activitysectionlabel;
    }

    /**
     * @param string $activitysectionlabel
     */
    public function set_activity_section_label(string $activitysectionlabel): void {
        $this->activitysectionlabel = $activitysectionlabel;
    }

    /**
     * @return string|null
     */
    public function get_activity_section(): ?string {
        return $this->activitysection;
    }

    /**
     * @param string|null $activitysection
     */
    public function set_activity_section(?string $activitysection): void {
        $this->activitysection = $activitysection;
    }

    /**
     * @return int
     */
    public function get_product_topic_id() {
        return $this->producttopicid;
    }

    /**
     * @return int
     */
    public function get_product_topic_packerid() {
        return $this->producttopicpackerid;
    }

    /**
     * @param int $producttopicid
     */
    public function set_product_topic_id(int $producttopicid): void {
        $this->producttopicid = $producttopicid;
    }

    /**
     * @return string
     */
    public function get_product_topic_name() {
        return $this->producttopicname;
    }

    /**
     * @param string $producttopicname
     */
    public function set_product_topic_name(string $producttopicname): void {
        $this->producttopicname = $producttopicname;
    }

    /**
     * @return string
     */
    public function get_default_delivery() {
        return $this->defaultdelivery;
    }

    /**
     * @param string $defaultdelivery
     */
    public function set_default_delivery(string $defaultdelivery): void {
        $this->defaultdelivery = $defaultdelivery;
    }

    /**
     * @return bool
     */
    public function is_assignable() {
        return $this->isassignable;
    }

    /**
     * @param bool $isassignable
     */
    public function set_isassignable(bool $isassignable): void {
        $this->isassignable = $isassignable;
    }

    /**
     * @return bool
     */
    public function show_to_student() {
        return $this->showtostudent;
    }

    /**
     * @param bool $showtostudent
     */
    public function set_show_to_student(bool $showtostudent): void {
        $this->showtostudent = $showtostudent;
    }

    /**
     * @return bool
     */
    public function in_sequence() {
        return $this->insequence && (!$this->get_origin() || $this->get_origin() === activity_core::INSEQ);
    }

    /**
     * @param bool $insequence
     */
    public function set_in_sequence(bool $insequence): void {
        $this->insequence = $insequence;
    }

    /**
     * @return bool
     */
    public function get_origin() {
        return $this->origin;
    }

    /**
     * @param bool $origin
     */
    public function set_origin($origin): void {
        $this->origin = $origin;
    }

    /**
     * @return bool|null
     */
    public function get_key_evidence(): ?bool {
        return $this->keyevidence;
    }

    /**
     * @param bool $keyevidence
     */
    public function set_key_evidence(bool $keyevidence): void {
        $this->keyevidence = $keyevidence;
    }

    /**
     * @return string
     */
    public function get_visibility(): ?string {
        return $this->visibility;
    }

    /**
     * @param string $visibility
     */
    public function set_visibility(string $visibility): void {
        $this->visibility = $visibility;
    }

    /**
     * @return string
     */
    public function get_visibility_label(): ?string {
        return $this->visibilitylabel;
    }

    /**
     * @param string $visibilitylabel
     */
    public function set_visibility_label(string $visibilitylabel): void {
        $this->visibilitylabel = $visibilitylabel;
    }

    /**
     * @return string|null
     */
    public function get_difficultylevel(): ?string {
        return $this->difficultylevel;
    }

    /**
     * @param string $difficultylevel
     */
    public function set_difficultylevel(string $difficultylevel): void {
        $this->difficultylevel = $difficultylevel;
    }

    /**
     * @return string
     */
    public function get_h5p_configuration(): string {
        return $this->h5pconfiguration;
    }

    /**
     * @param string $h5pconfiguration
     */
    public function set_h5p_configuration(string $h5pconfiguration): void {
        $this->h5pconfiguration = $h5pconfiguration;
    }

    /**
     * @return string
     */
    public function get_mapperassignment(): ?string {
        return $this->mapperassignment;
    }

    /**
     * @return bool
     */
    public function is_progexam(): bool {
        return $this->progexam ?? true;
    }

    /**
     * @param bool $progexam
     */
    public function set_progexam(bool $progexam): void {
        $this->progexam = $progexam;
    }

    /**
     * @param string $license
     */
    public function set_license(string $license): void {
        $this->license = $license;
    }

    /**
     * @return ?string
     */
    public function get_text_answer_type(): ?string {
        return $this->textanswertype;
    }

    /**
     * @param string $textanswertype
     */
    public function set_text_answer_type(string $textanswertype): void {
        $this->textanswertype = $textanswertype;
    }

    /**
     * @return int|null
     */
    public function get_lastupdate_date(): ?int {
        return $this->lastupdatedate;
    }

    /**
     * @return bool
     */
    public function is_pending_update(): bool {
        return $this->pendingupdate;
    }

    /**
     * @param bool $pendingupdate
     */
    public function set_pending_update(bool $pendingupdate): void {
        $this->pendingupdate = $pendingupdate;
    }

    /**
     * @return bool
     */
    public function is_moodle_activity() {
        $type = $this->type ?? '';
        return in_array($type, $this->moodleactivities);
    }

    /**
     * @return int
     */
    public function is_custom(): int {
        return $this->iscustom;
    }

    /**
     * @param int $iscustom
     */
    public function set_is_custom(int $iscustom): void {
        $this->iscustom = $iscustom;
    }

    /**
     * @param int $timecreated
     */
    public function set_timecreated($timecreated) {
        $this->$timecreated = $timecreated;
    }

    /**
     * @param int $timemodified
     */
    public function set_timemodified($timemodified) {
        $this->$timemodified = $timemodified;
    }
}

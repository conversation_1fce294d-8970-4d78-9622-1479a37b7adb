{"version": 3, "file": "modal_confirm_multigrading.min.js", "sources": ["../src/modal_confirm_multigrading.js"], "sourcesContent": ["import $ from 'jquery';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\nimport EventListener from 'local_npe/event_listener';\n\nconst SELECTORS = {\n    HIDE: '.close',\n    CONFIRM: '[data-action=\"submit-grade\"]',\n};\n\nconst EVENTS = {\n    CONFIRMSUBMIT: 'npe:confirm-multigrading-submit'\n};\n\nlet registered = false;\n\nexport default class ModalConfirmMultigrading extends Modal {\n\n    static TYPE = 'local_npe/modal_confirm_multigrading';\n    static TEMPLATE = 'local_npe/commons/modal_confirm_multigrading';\n\n    constructor(root) {\n        super(root);\n    }\n\n    setData(random) {\n        this.random = random;\n    }\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.success-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CONFIRM, () => {\n            EventListener.shoutEvent(EVENTS.CONFIRMSUBMIT, {name: 'confirmmultigradingsubmit-' + this.random});\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.success-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n        });\n    }\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalConfirmMultigrading.TYPE, ModalConfirmMultigrading, ModalConfirmMultigrading.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_defineProperty", "r", "t", "i", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_j<PERSON>y", "_custom_interaction_events", "_modal", "_modal_registry", "_event_listener", "SELECTORS", "EVENTS", "registered", "ModalConfirmMultigrading", "Modal", "constructor", "root", "super", "setData", "random", "this", "registerEventListeners", "getModal", "on", "CustomEvents", "events", "activate", "getRoot", "removeClass", "$", "remove", "addClass", "EventListener", "shoutEvent", "name", "_exports", "ModalRegistry", "register", "TYPE", "TEMPLATE"], "mappings": "gQAIqD,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,gBAAAH,EAAAI,EAAAC,GAAAD,OAAAA,EAAA,SAAAC,GAAAC,IAAAA,EAAA,SAAAD,EAAAD,GAAAC,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAL,IAAAA,EAAAK,EAAAE,OAAAC,yBAAAR,EAAA,CAAA,IAAAM,EAAAN,EAAAS,KAAAJ,EAAAD,kCAAAE,EAAA,OAAAA,EAAAI,MAAAA,IAAAA,4EAAAN,EAAAO,OAAAC,QAAAP,EAAA,CAAAQ,CAAAR,EAAAC,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAQ,CAAAV,MAAAJ,EAAAe,OAAAC,eAAAhB,EAAAI,EAAAa,CAAAA,MAAAZ,EAAAa,YAAAC,EAAAA,cAAAC,EAAAA,cAAApB,EAAAI,GAAAC,EAAAL,CAAA,iFAJrDqB,QAAAtB,uBAAAsB,SACAC,2BAAAvB,uBAAAuB,4BACAC,OAAAxB,uBAAAwB,QACAC,gBAAAzB,uBAAAyB,iBACAC,gBAAA1B,uBAAA0B,iBAEA,MAAMC,eACI,SADJA,kBAEO,+BAGPC,qBACa,kCAGnB,IAAIC,YAAa,EAEF,MAAMC,iCAAiCC,OAAAA,QAKlDC,WAAAA,CAAYC,MACRC,MAAMD,KACV,CAEAE,OAAAA,CAAQC,QACJC,KAAKD,OAASA,MAClB,CAEAE,sBAAAA,GACIJ,MAAMI,uBAAuBD,MAE7BA,KAAKE,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUhB,gBAAgB,KAC7DU,KAAKO,UAAUC,YAAY,SAC3B,EAAAC,QAAAA,SAAE,QAAQD,YAAY,eACtB,EAAAC,iBAAE,kBAAkBC,UACpB,EAAAD,QAAC3C,SAAC,mBAAmB0C,YAAY,QAAQG,SAAS,OAAO,IAG7DX,KAAKE,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUhB,mBAAmB,KAChEsB,gBAAAA,QAAcC,WAAWtB,qBAAsB,CAACuB,KAAM,6BAA+Bd,KAAKD,SAC1FC,KAAKO,UAAUC,YAAY,SAC3B,EAAAC,QAAAA,SAAE,QAAQD,YAAY,eACtB,EAAAC,iBAAE,kBAAkBC,UACpB,EAAAD,QAAC3C,SAAC,mBAAmB0C,YAAY,QAAQG,SAAS,OAAO,GAEjE,EAMH,OALAI,SAAAjD,QAAA2B,yBAAA1B,gBA/BoB0B,yBAAwB,OAE3B,wCAAsC1B,gBAFnC0B,yBAAwB,WAGvB,gDA8BjBD,aACDwB,gBAAAA,QAAcC,SAASxB,yBAAyByB,KAAMzB,yBAA0BA,yBAAyB0B,UACzG3B,YAAa,GAChBuB,SAAAjD,OAAA"}
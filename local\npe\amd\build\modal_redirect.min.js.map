{"version": 3, "file": "modal_redirect.min.js", "sources": ["../src/modal_redirect.js"], "sourcesContent": ["import $ from 'jquery';\nimport Notification from 'core/notification';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\nimport Str from 'core/str';\n\nconst SELECTORS = {\n    MODALTITLE: '.modal-title',\n    MODALDESCRIPTION: '.modal-description',\n    HIDE: '.close',\n    CANCEL: '[data-action=\"hide\"]',\n    FINISH: '[data-action=\"finish\"]',\n};\n\nlet registered = false;\n\nexport default class ModalRedirect extends Modal {\n\n    static TYPE = 'local_npe/modal_redirect';\n    static TEMPLATE = 'local_npe/commons/modal_redirect';\n\n    constructor(root) {\n        super(root);\n    }\n\n    setData(redirectUrl, titleButtonKey, titleKey, descriptionKey, descriptionParam = null) {\n        this.getRoot().find(SELECTORS.FINISH).attr('data-url', redirectUrl);\n\n        var description = descriptionParam !== null\n            ? {key: description<PERSON>ey, component: 'local_npe', param: JSON.parse(descriptionParam)}\n            : {key: descriptionKey, component: 'local_npe'};\n\n        var titleButton = {key: titleButtonKey, component: 'local_npe'};\n        var title = {key: titleKey, component: 'local_npe'};\n        var requeststrings = [titleButton, title, description];\n\n        Str.get_strings(requeststrings).done((strings) => {\n            let titlebuttonstring = strings[0];\n            let titlestring = strings[1];\n            let descriptionstring = strings[2];\n            this.getRoot().find(SELECTORS.FINISH).html(titlebuttonstring);\n            this.getRoot().find(SELECTORS.MODALTITLE).html(titlestring);\n            if (descriptionKey) {\n                this.getRoot().find(SELECTORS.MODALDESCRIPTION).html(descriptionstring);\n            }\n        }).fail(Notification.exception);\n    }\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CANCEL, () => {\n            this.closeModal();\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {\n            this.closeModal();\n            window.location.href = this.getRoot().find(SELECTORS.FINISH).data('url');\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {\n            this.closeModal();\n        });\n    }\n\n    closeModal() {\n        this.getRoot().removeClass('show');\n        $('body').removeClass('modal-open');\n        $('.success-modal').remove();\n        $('.modal-backdrop').removeClass('show').addClass('hide');\n    }\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalRedirect.TYPE, ModalRedirect, ModalRedirect.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_defineProperty", "r", "t", "i", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_j<PERSON>y", "_notification", "_custom_interaction_events", "_modal", "_modal_registry", "_str", "SELECTORS", "registered", "ModalRedirect", "Modal", "constructor", "root", "super", "setData", "redirectUrl", "titleButtonKey", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "descriptionParam", "arguments", "length", "undefined", "this", "getRoot", "find", "attr", "requeststrings", "key", "component", "param", "JSON", "parse", "Str", "get_strings", "done", "strings", "titlebuttonstring", "titlestring", "descriptionstring", "html", "fail", "Notification", "exception", "registerEventListeners", "getModal", "on", "CustomEvents", "events", "activate", "closeModal", "window", "location", "href", "data", "removeClass", "$", "remove", "addClass", "_exports", "ModalRegistry", "register", "TYPE", "TEMPLATE"], "mappings": "2PAK2B,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,gBAAAH,EAAAI,EAAAC,GAAAD,OAAAA,EAAA,SAAAC,GAAAC,IAAAA,EAAA,SAAAD,EAAAD,GAAAC,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAL,IAAAA,EAAAK,EAAAE,OAAAC,yBAAAR,EAAA,CAAA,IAAAM,EAAAN,EAAAS,KAAAJ,EAAAD,kCAAAE,EAAA,OAAAA,EAAAI,MAAAA,IAAAA,4EAAAN,EAAAO,OAAAC,QAAAP,EAAA,CAAAQ,CAAAR,EAAAC,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAQ,CAAAV,MAAAJ,EAAAe,OAAAC,eAAAhB,EAAAI,EAAAa,CAAAA,MAAAZ,EAAAa,YAAAC,EAAAA,cAAAC,EAAAA,cAAApB,EAAAI,GAAAC,EAAAL,CAAA,iFAL3BqB,QAAAtB,uBAAAsB,SACAC,cAAAvB,uBAAAuB,eACAC,2BAAAxB,uBAAAwB,4BACAC,OAAAzB,uBAAAyB,QACAC,gBAAA1B,uBAAA0B,iBACAC,KAAA3B,uBAAA2B,MAEA,MAAMC,qBACU,eADVA,2BAEgB,qBAFhBA,eAGI,SAHJA,iBAIM,uBAJNA,iBAKM,yBAGZ,IAAIC,YAAa,EAEF,MAAMC,sBAAsBC,OAAAA,QAKvCC,WAAAA,CAAYC,MACRC,MAAMD,KACV,CAEAE,OAAAA,CAAQC,YAAaC,eAAgBC,SAAUC,gBAAyC,IAAzBC,iBAAgBC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,KAC9EG,KAAKC,UAAUC,KAAKlB,kBAAkBmB,KAAK,WAAYX,aAEvD,IAMIY,eAAiB,CAFH,CAACC,IAAKZ,eAAgBa,UAAW,aACvC,CAACD,IAAKX,SAAUY,UAAW,aALA,OAArBV,iBACZ,CAACS,IAAKV,eAAgBW,UAAW,YAAaC,MAAOC,KAAKC,MAAMb,mBAChE,CAACS,IAAKV,eAAgBW,UAAW,cAMvCI,KAAGnD,QAACoD,YAAYP,gBAAgBQ,MAAMC,UAClC,IAAIC,kBAAoBD,QAAQ,GAC5BE,YAAcF,QAAQ,GACtBG,kBAAoBH,QAAQ,GAChCb,KAAKC,UAAUC,KAAKlB,kBAAkBiC,KAAKH,mBAC3Cd,KAAKC,UAAUC,KAAKlB,sBAAsBiC,KAAKF,aAC3CpB,gBACAK,KAAKC,UAAUC,KAAKlB,4BAA4BiC,KAAKD,kBACzD,IACDE,KAAKC,cAAY5D,QAAC6D,UACzB,CAEAC,sBAAAA,GACI/B,MAAM+B,uBAAuBrB,MAE7BA,KAAKsB,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAU1C,kBAAkB,KAC/DgB,KAAK2B,YAAY,IAGrB3B,KAAKsB,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAU1C,kBAAkB,KAC/DgB,KAAK2B,aACLC,OAAOC,SAASC,KAAO9B,KAAKC,UAAUC,KAAKlB,kBAAkB+C,KAAK,MAAM,IAG5E/B,KAAKsB,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAU1C,gBAAgB,KAC7DgB,KAAK2B,YAAY,GAEzB,CAEAA,UAAAA,GACI3B,KAAKC,UAAU+B,YAAY,SAC3B,EAAAC,QAAAA,SAAE,QAAQD,YAAY,eACtB,EAAAC,iBAAE,kBAAkBC,UACpB,EAAAD,QAAC1E,SAAC,mBAAmByE,YAAY,QAAQG,SAAS,OACtD,EAMH,OALAC,SAAA7E,QAAA2B,cAAA1B,gBAvDoB0B,cAAa,OAEhB,4BAA0B1B,gBAFvB0B,cAAa,WAGZ,oCAsDjBD,aACDoD,gBAAAA,QAAcC,SAASpD,cAAcqD,KAAMrD,cAAeA,cAAcsD,UACxEvD,YAAa,GAChBmD,SAAA7E,OAAA"}
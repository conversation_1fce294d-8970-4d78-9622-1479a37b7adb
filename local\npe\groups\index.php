<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

use local_npe\access_control;
use local_npe\app;

require_once __DIR__ . '/../../../config.php';

require_login();

global $SESSION, $PAGE, $CFG;
$codproduct = required_param('codproduct', PARAM_ALPHANUMEXT);
$codproduct = \local_npe\product::clean_codproduct($codproduct);

access_control::check_product_access($codproduct, false);

$categorydto = \local_npe\product::get_instance($codproduct)->get_product_category();
if ($categorydto === null) {
    throw new moodle_exception('No se encuentra la categoría del producto');
}

app::get_session_helper()->set_session('codproduct', $codproduct);

$context = \context_coursecat::instance($categorydto->get_id());

$lang = \local_npe\app::get_product($codproduct)->get_product_data()->get_language();
force_current_language($lang);

$PAGE->set_context($context);
$PAGE->set_category_by_id($categorydto->get_id());

// Solo se permite el acceso a profesores SMA/SME/SEN (con limitaciones).
access_control::only_sma_and_sen_teacher();

/** @var \local_npe\helper\url $url */
$url = \local_npe\app::get_instance()->get(\local_npe\helper\url::class);

$title = get_string('titlepage_mygroups', 'local_npe');

// Traza de navegación.
\local_npe\app::get_navigation_trace()->set_trace(-1, $codproduct);
$product = app::get_product($codproduct);
$PAGE->set_url($url->get_groups_url($codproduct));
$PAGE->set_title($product->get_name());
$PAGE->set_heading($title);
$PAGE->set_pagelayout('base');
$output = $PAGE->get_renderer('local_npe');
echo $output->header();
/** @var \local_npe\output\mygroups $page */
$page = \local_npe\app::get_instance()->get(\local_npe\output\mygroups::class);
$page->set_codproduct($codproduct);
echo $output->render($page);
echo $output->footer();

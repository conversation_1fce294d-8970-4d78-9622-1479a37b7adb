<?php

namespace local_npe\exporter;

use core\external\exporter;
use local_npe\base\user\iuser;
use local_npe\config;
use local_npe\product;

class mygroups_exporter extends exporter {

    protected static function define_related() {
        return array(
            'currentuser' => iuser::class,
            'courses' => '\\local_npe\\Course[]',
            'product' => product::class,
            'config' => config::class
        );
    }

    protected static function define_other_properties() {
        return [
            'title' => [
                'type' => PARAM_RAW
            ],
            'level' => [
                'type' => PARAM_RAW
            ],
            'stage' => [
                'type' => PARAM_RAW
            ],
            'ccaa' => [
                'type' => PARAM_RAW
            ],
            'username' => [
                'type' => PARAM_RAW
            ],
            'iseducamos' => [
                'type' => PARAM_BOOL
            ],
            'issma' => [
                'type' => PARAM_BOOL
            ],
            'isteacher' => [
                'type' => PARAM_BOOL
            ],
            'hasgroups' => [
                'type' => PARAM_BOOL
            ],
            'hasmultiplegroups' => [
                'type' => PARAM_BOOL
            ],
            'limitreached' => [
                'type' => PARAM_BOOL
            ],
            'limitgroup' => [
                'type' => PARAM_INT
            ],
            'welcomeurl' => [
                'type' => PARAM_RAW
            ],
            'noticenewcourse' => [
                'type' => PARAM_BOOL
            ],
            'noticenewcourse_intime' => [
                'type' => PARAM_BOOL
            ],
            'groups' => [
                'multiple' => true,
                'type' => [
                    'id' => [
                        'type' => PARAM_INT
                    ],
                    'name' => [
                        'type' => PARAM_RAW
                    ],
                    'enrolmentkey' => [
                        'type' => PARAM_RAW
                    ],
                    'alertsharecode' => [
                        'type' => PARAM_BOOL
                    ],
                    'iscreated' => [
                        'type' => PARAM_BOOL
                    ],
                    'iscreator' => [
                        'type' => PARAM_BOOL
                    ],
                    'creator' => [
                        'type' => [
                            'name' => [
                                'type' => PARAM_RAW
                            ],
                            'picture' => [
                                'type' => PARAM_URL
                            ],
                        ]
                    ],
                    'students' => [
                        'type' => PARAM_INT
                    ],
                    'teachers' => [
                        'type' => PARAM_INT
                    ],
                    'expiration' => [
                        'type' => PARAM_RAW
                    ],
                    'linkstudent' => [
                        'type' => PARAM_RAW
                    ],
                    'linkteacher' => [
                        'type' => PARAM_RAW
                    ],
                    'linkcourse' => [
                        'type' => PARAM_RAW
                    ],
                    'linkmyclass' => [
                        'type' => PARAM_RAW
                    ],
                    'totalunreadnotifications' => [
                        'type' => PARAM_RAW
                    ],
                    'linkviewer' => [
                        'type' => PARAM_RAW
                    ]
                ]
            ],
            'floatingroup' => [
                'type' => PARAM_BOOL
            ],
            'floatingrouptitle' => [
                'type' => PARAM_RAW
            ],
            'floatingrouptab0' => [
                'type' => PARAM_RAW
            ],
            'floatingrouptab1' => [
                'type' => PARAM_RAW
            ],
            'itemstab0' => [
                'type' => PARAM_RAW
            ],
            'tab0type' => [
                'type' => PARAM_RAW
            ],
            'tab1type' => [
                'type' => PARAM_RAW
            ],
            'idtab0' => [
                'type' => PARAM_RAW
            ],
            'idtab1' => [
                'type' => PARAM_RAW
            ],
            'istabletst0' => [
                'type' => PARAM_RAW
            ],
            'istabletst1' => [
                'type' => PARAM_RAW
            ],
            'itemstab1' => [
                'type' => PARAM_RAW
            ],
            'showhelp' => [
                'type' => PARAM_BOOL,
            ],
            'breadcrumbs' => [
                'multiple' => true,
                'type' => [
                    'title' => [
                        'type' => PARAM_RAW
                    ],
                    'url' => [
                        'type' => PARAM_RAW
                    ],
                ]
            ],
            'isurlshared' => [
                'type' => PARAM_BOOL
            ],
            'logo' => [
                'type' => PARAM_RAW
            ],
            'helppanellink' => [
                'type' => PARAM_RAW
            ],
            'helppanellinklabel' => [
                'type' => PARAM_RAW
            ],
            'issmauser' => [
                'type' => PARAM_BOOL
            ],
            'hashelppanel' => [
                'type' => PARAM_BOOL
            ],
            'isdefaulttheme' => [
                'type' => PARAM_BOOL
            ],
            'isexternal' => [
                'type' => PARAM_BOOL
            ],
            'isnuancestheme' => [
                'type' => PARAM_BOOL
            ],
            'chatboturl' => [
                'type' => PARAM_RAW
            ],
        ];
    }

    /**
     * @param \renderer_base $output
     * @return array
     * @throws \dml_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     */
    protected function get_other_values(\renderer_base $output) {
        $mygroupsdata = new mygroup_data(
            $this->related['currentuser'],
            $this->related['product'],
            $this->related['config'],
            ...$this->related['courses']
        );
        $mygroupsdata->set_params();

        return $mygroupsdata->to_array();
    }
}

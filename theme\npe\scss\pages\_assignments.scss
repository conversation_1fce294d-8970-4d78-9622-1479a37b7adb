#page-local-npe-courses-assignments-index{
   overflow-y: auto;
   scrollbar-width: none;   /* Oculta en Firefox */

    @media only screen and (max-width: 799px) {
        #helpButton {
            display: none;
        }
    }
}

.assignments {
    padding-right: 0;

    .assignments-topbar {
        width: 100%;
        height: 40px;
        line-height: 40px;
        vertical-align: middle;
        background-color: $npe_dark-grey;
        color: $npe-white;
        padding-left: 40px;
        box-shadow: 0 3px 5px #0000001a;
        border-right: 1px solid $npe_dark-grey;
        z-index: 10;

        &.ini {
            margin: 20px 0 0 0;
            font-family: $ossemibold;
            font-size: 16px;
            color: $npe-white;
            padding-left: 30px;
            background-image: url([[pix:theme|arrow-white]]);
            background-repeat: no-repeat;
            background-size: 17px 17px;
            background-position-x: 15px;
            background-position-y: 12px;
            position: relative;

            .first {
                margin-left: 10px;
                padding-right: 20px;
                border-right: 1px solid white;
            }
        }
    }

    .assignments-content {
        width: 100%;
        min-height: 600px;
    }

    .assignments-activities {
        background-color: $npe_white;
        margin-bottom: 50px;

        .header-followup-assign{
            padding-left: 40px;
            padding-bottom: 20px;
            border-bottom: 4px solid #ededed;
            .typelabel{
                text-align: left;
                font-family: "Work Sans Light";
                font-size: 12px;
                color: #666
            }
            .subtitle-one{
                text-align: left;
                font-family: "Work Sans Regular";
                font-size: 14px;
                color: #666;
                padding-bottom: 20px;
            }
            .icons-row{
                text-align: left;
                display: inline-block;
                margin-bottom: 11px;
                .activity-icons{
                    position: inherit;
                }
            }
            .actions-row{
                display: flex;
                float: right;
                margin: 0 40px 0 0;
                flex-wrap: wrap;
                .assignments-followup-edit{
                    margin: 0 20px 0px 0;
                    float: unset;
                }
            }
            .row-buttons-followup{
                .noiconssubtitle{
                    display: inline-block;
                    width: 40%;
                }
                .noiconsrow{
                    display: inline-block;
                    float: right;
                    width: 50%;
                }
            }
            .nobuttons{
                margin-bottom: -11px;
            }
        }

        .npe-breadcrumb{
            margin-left: 16px;;
            margin-bottom: 0px;
        }

        .typelabel {
            display: block;
            text-align: center;
            font-size: 12px;
            font-style: italic;
        }

        .headandsearch {
            display: flex;

            .title {
                position: relative;
                font-family: $ossemibold;
                font-size: 20px;
                color: $npe_black;
                text-transform: uppercase;
                margin-bottom: 10px;
                margin-left: 40px;
                margin-right: 10px;

                .info_drop {
                    top: -40px;
                }
            }

            .subtitle-one {
                display: block;
                font-family: $oslight;
                font-size: 18px;
                color: $npe-dark-grey;
                margin-left: 40px;
            }

            .subtitle-two {
                display: block;
                font-family: $oslight;
                font-size: 18px;
                color: $npe-dark-grey;
                margin-left: 100px;
            }

            .icons-row {
                text-align: center;

                .activity-icons {
                    text-align: center;
                    position: relative;
                    top: 30px;

                    img {
                        width: 24px;
                        height: 24px;
                        background-color: #ededed;
                    }
                }
            }

            .actions-row {
                margin-bottom: 50px;
            }
            .icon-lupa {
                height: 36px;
                width: 36px;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-left: 10px;
                margin-top: 5px;
                border-radius: 8px;

                @media only screen and (max-width: 799px) {
                    margin-left: 0;
                    margin-top: 12px;
                    padding: 10px;
                }

                &:hover,
                &.active-icon {
                    background-color: $npe-grey-light-filters;
                }

                img {
                    height: 24px;
                    width: 24px;
                    position: relative;
                    left: 3px;
                    cursor: pointer;
                }
            }
            .assignments-search {
                display: none;
                width: 400px;
                height: 36px;
                margin: 5px 0 0 0;
                position: relative;
                left: -8px;

                @media only screen and (max-width: 799px) {
                    margin-top: 12px;
                }

                &.active-input {
                    display: inline-block;
                }

                input::placeholder {
                    font-style: italic;
                }

                input {
                    font-family: $osregular;
                    font-size: 14px;
                    font-style: normal;
                    color: $npe-dark-grey;
                    height: 36px;
                    background-color: $npe-grey-light-filters;
                    border-radius: 0 8px 8px 0;
                    padding-left: 5px;
                    border: 0;

                    &:focus {
                        box-shadow: none;
                    }
                }

                span {
                    position: absolute;
                    display: block;
                    top: 8px;
                    right: 10px;
                    width: 20px;
                    height: 20px;
                    border-radius: 50px;
                    background: $kbd-bg url([[pix:theme|close]]) no-repeat;
                    background-size: 10px 10px;
                    background-position-x: 5px;
                    background-position-y: 5px;
                    cursor: pointer;
                }
            }

            @media only screen and (max-width: 799px) {
                height: 60px;
            }
        }

        @media only screen and (max-width: 1024px) {
            .row-buttons-header,
            .sub-elements{
                display: grid;
            }
            .row-buttons-menu{
                width: 100%;
                padding-top: 15px;
                margin-bottom: -30px;
            }
            .assignments-actions {
                margin-left: 0px!important;
                margin-bottom: 30px!important;
            }
            .assignments-apply{
                margin-bottom: 20px!important;
            }
            .assignments-add{
            }

            .row-buttons-followup{
                .row-buttons-menu{
                    width: 100%;
                    padding-top: 0px;
                    margin-bottom: -10px;
                    display: inline-block;
                }
            }
            .assignments-search  {
                width: 520px;
                margin-left: 20px;
            }
        }

        @media only screen and (max-width: 799px) {

            .row-buttons-header{
                padding-bottom: 50px!important;
            }
            .assignments-actions,
            .assignments-apply,
            .gotofollowup{
                width: 40%!important;
                min-width: 170px;
            }

            .row-buttons-followup{
                .row-buttons-menu{
                    display: grid;
                }
                .assignments-followup-edit,
                #dotsheaderassignments{
                    margin-top: 20px!important;
                }
                .gotofollowup{
                    margin-top: 20px!important;
                }
            }
            .nobuttons{
                display: grid!important;
                padding-bottom: 11px;
            }
            .noiconsrow{
                width: 100% !important;
                display: grid!important;
                margin-top: -10px;
            }
            .assignments-search {
                margin-left: 20px;
                margin-bottom: 10px;
                width: calc(100% - 40px);
            }
        }

        .assignments-filter {
            @include filters_button();
        }

        @media only screen and (max-width: 1024px) {
            .assignments-filter {
                margin-right: 20px;
            }
        }
        @media only screen and (max-width: 799px) {
            .assignments-filter {
                margin-left: 20px;
                margin-bottom: 10px;
                float: left;
                width: calc(100% - 40px);
                text-align: left;
                padding-left: 50px;
            }
        }

        .assignments-filter-opacity-layer {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background: $npe-black;
            transition: opacity 350ms;
            opacity: .5;
            z-index: 9998;
        }

        #filterButton {
            @include new_filters(calc(100vh - 30px), -10px);
            
            &.te24151 {
                .modal-filter {
                    margin-left: 16px;
                    margin-bottom: 16px;
                    margin-top: 5px;
                }

                .modal-filter:not(.hidden) {

                    .clean-filters {
                        font-family: $ossemibold;
                        font-size: 14px;
                        color: $npe-black;
                        border: none;
                        background: $npe-white;
                        display: table-cell;
                        vertical-align: middle;
                        position: relative;
                        top: -2px;
                        width: 130px;
                        text-align: left;
                        padding-left: 25px;
                        margin-left: 10px;
        
                        &:before {
                            content: '';
                            position: absolute;
                            left: 0;
                            width: 20px;
                            height: 20px;
                            background-image: url([[pix:theme|trash-black]]);
                            background-repeat: no-repeat;
                            background-size: 20px 20px;
                        }
        
                        &.nofilters {
                            display: none;
                        }
                    }
                }
                  
                .dropdown {
                    position: relative;
                    display: inline-block;
                    margin-right: 8px;

                    &.categoryId {
                        display: inline-block;
                        @media only screen and (min-width: 799px) {
                            display: none!important;
                        }
                    }

                    &.subcategoryId {
                        display: none!important;
                    }

                    .dropbtn {
                        position: relative;
                        background-color: $npe-grey-light-filters;
                        color:$kbd-bg;
                        font-size: 14px;
                        font-family: $ossemibold;
                        border: none;
                        cursor: pointer;
                        height: 36px;
                        border-radius: 20px;
                        padding: .70rem 1.25rem;
                        padding-right: 35px;

                        i {
                            display: block;
                            width: 30px;
                            height: 40px;
                            position: absolute;
                            top: 0;
                            right: 0;
                            z-index: 10;
                            &:before {
                                display: none;
                            }
                            &:after {
                                content: '';
                                background-image: url([[pix:theme|chevron-down]]);
                                background-repeat: no-repeat;
                                background-size: 26px 23px;
                                position: absolute;
                                top: 8px;
                                width: 26px;
                                height: 23px;
                                right: 6px;
                                transition-duration: 0.5s;
                            }
                        }

                        &.open {
                            i {
                                &:before {
                                    display: none;
                                }
                                &:after {
                                    transition-duration: 0.5s;
                                    -webkit-transform: rotate(-180deg);
                                    -moz-transform: rotate(180deg);
                                    -ms-transform: rotate(180deg);
                                    -o-transform: rotate(180deg);
                                    transform: rotate(180deg);
                                }
                            }
                        }

                        &.bgf-c {
                            i {
                                &:after {
                                    transition-duration: 0s;
                                    -webkit-transform: rotate(0deg);
                                    -moz-transform: rotate(0deg);
                                    -ms-transform: rotate(0deg);
                                    -o-transform: rotate(0deg);
                                    transform: rotate(0deg);
                                }
                            }
                        }
                    }

                    .dropdown-content {
                        display: none;
                        position: absolute;
                        top: 48px;
                        background-color: $basic-color-a;
                        min-width: 160px;
                        overflow: auto;
                        box-shadow: 5px 5px 5px -2px rgba(0,0,0,.2);
                        z-index: 2;
                        list-style-type: none;
                        padding: 4px 8px 4px 8px;
                        border-radius: 8px;
                        border: 1px solid $npe-grey-alt;

                        @media only screen and (max-width: 799px) {
                            position: fixed;
                            top: 165px;
                        }

                        .custom_checkbox {
                            font-family: $osregular;
                            font-size: 14px;
                            position: initial;
                            padding: 0;

                            &:hover {
                                background: none;
                                color: initial;
                            }
                    
                            input {
                                padding: 0;
                                height: initial;
                                width: initial;
                                display: none;
                                cursor: pointer;
                    
                                &:checked+label::after {
                                    content: '';
                                    display: block;
                                    position: absolute;
                                    top: 8px;
                                    left: 5px;
                                    width: 6px;
                                    height: 10px;
                                    border: solid #00a559;
                                    border-width: 0 2px 2px 0;
                                    transform: rotate(45deg);
                                }
                    
                                &:checked+label::before {
                                    content: '';
                                    background-color: #86ff86;
                                    border-color: #00a559;
                                }
                            }

                            input[type=checkbox]:checked ~ span {
                                font-family: $ossemibold;
                            }
                    
                            label {
                                position: relative;
                                cursor: pointer;
                                color: #fff;
                                font-size: 20px;
                                display: inline;
                    
                                &:before {
                                    content: '';
                                    background-color: $npe_white;
                                    border: 1px solid #C0C0C0;
                                    padding: 7px;
                                    border-radius: 3px;
                                    display: inline-block;
                                    position: relative;
                                    vertical-align: middle;
                                    cursor: pointer;
                                    margin-right: 4px;
                                    transition: background-color 0.3s ease-in-out;
                                }
                            }
                    
                            span {
                                margin-bottom: 10px;
                                cursor: pointer;
                            }
                        }
                    }
                  }
                  .show {
                    display: block;
                }
            }
        }

        .assignments-filter-container {
            background-color: $npe-bg-color;
            border-top: 3px solid $npe-grey-alt;
            padding: 0 0 0 20px;
            @include filter-applied();

            .delete-filters {
                font-family: $ossemibold;
                font-size: 12px;
                color: $npe-black;
                text-decoration: underline;
                display: none;
                margin-top: 10px;
                cursor: pointer;

                &.show {
                    display: block;
                    width: fit-content;
                }
            }
        }

        @media only screen and (max-width: 799px) {
            #filterButton {
                margin-top: 15px;
                max-width: 100%;
                overflow-x: scroll;
                overflow-y: hidden;
                white-space: nowrap;
                -ms-overflow-style: none;  /* Internet Explorer 10+ */
                scrollbar-width: none;  /* Firefox */

                .modal-filter {
                    max-width: max-content;
                }
            }

            #filterButton::-webkit-scrollbar {
                display: none;
            }

            .assignments-filter-container {
                margin-top: 0px;
            }
        }

        @media only screen and (max-width: 1024px) {
            .assignments-filter-container {
                padding-left: 20px;
            }
        }

        .activities-container {
            background-color: $npe-bg-color;

            .npe-page-header-labels {
                @media only screen and (max-width: 799px) {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }

            .activities-list {
                background-color: $npe-white;
                padding: 20px 20px 20px 20px;

                .activities-type {
                    font-family: $ossemibold;
                    font-size: 18px;
                    color: $npe_black;

                    .count {
                        font-family: $oslight;
                        color: $npe_dark-grey;
                        padding-left: 10px;
                    }
                }

                .activities-subcats {
                    margin-top: 20px;
                    margin-bottom: 20px;

                    @media only screen and (max-width: 799px) {
                        
                    }

                    .subcat {
                        position: relative;
                        margin-right: 20px;
                        font-size: 14px;
                        cursor: pointer;
                        color: $npe-dark-grey;
                        background-color: $npe-grey-light-filters;
                        border: none;
                        border-radius: 8px;
                        padding: 4px;

                        i {
                            display: none;
                        }

                        &.selected {
                            padding-right: 25px;
                            pointer-events: none;

                            i {
                                display: block;
                                width: 30px;
                                height: 40px;
                                position: absolute;
                                top: 0;
                                right: 0;
                                z-index: 1;
                                cursor: pointer;
                                pointer-events: all;
    
                                &:after {
                                    content: '';
                                    background-repeat: no-repeat;
                                    right: 5px !important;
                                    top: 5px !important;
                                }
                            }
                        }
                    }
                }

                @media only screen and (max-width: 799px) {
                    .activities-subcats {
                        overflow: auto;
                        white-space: nowrap;
                        -ms-overflow-style: none;  /* Internet Explorer 10+ */
                        scrollbar-width: none;  /* Firefox */
                    }

                    .activities-subcats::-webkit-scrollbar {
                        display: none;
                    }
                }

                &.list {
                    &.hidden {
                        display: none !important;
                    }

                    .showmore,
                    .showless {
                        font-family: $osregular;
                        font-size: 14px;
                        color: $npe-white;
                        float: left;
                        margin-right: 20px;
                        margin-top: 20px;
                        cursor: pointer;
                        padding: 5px 25px 5px 10px;
                        border-radius: 20px;
                        background-repeat: no-repeat;
                        background-size: 16px 16px;
                        background-position-x: 70px;
                        background-position-y: 6px;
                        background-image: url([[pix:theme|chevron-right]]);
                    }
                }

                &.tablet {
                    &.hidden {
                        display: none !important;
                    }

                    .showmore,
                    .showless {
                        font-family: $osregular;
                        font-size: 14px;
                        color: $npe-white;
                        float: left;
                        margin-right: 20px;
                        margin-top: 20px;
                        cursor: pointer;
                        padding: 5px 25px 5px 10px;
                        border-radius: 20px;
                        background-repeat: no-repeat;
                        background-repeat: no-repeat;
                        background-size: 16px 16px;
                        background-position-x: 70px;
                        background-position-y: 6px;
                        background-image: url([[pix:theme|chevron-right]]);
                    }
                }

                &.cards {
                    &.hidden {
                        display: none !important;
                    }

                    .activities-header {
                        display: none;
                    }

                    .showmore,
                    .showless {
                        display: none !important;
                    }
                }

                &.sequence {
                    &.hidden {
                        display: none !important;
                    }

                    .activities-type {
                        display: none;
                    }

                    .showmore,
                    .showless {
                        display: none !important;
                    }
                }
            }

            @media only screen and (max-width: 1024px) {
                .activities-list {
                    padding: 20px 20px 20px 20px;
                }
            }
        }
    }

    &.edition {
        .title {
            margin: 10px 0 0;
            padding: 0;
            text-transform: none;
            font-size: 20px;
            text-transform: uppercase;

            &:before {
                all: unset;
            }
        }

        .subtitle-one {
            margin: 10px 0 0;
            font-size: 12px;
        }

        .assignments-actions {
            display: inline-block;
            width: 200px;
            height: 30px;
            border: 2px solid $npe-dark-grey;
            border-radius: 25px;
            margin: 0 20px 0px 40px;
            position: relative;
            font-family: $ossemibold;
            font-size: 12px;
            color: $npe-black;
            padding: 6px 8px 6px 8px;

            label {
                display: inline-block;
                width: 165px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }

            .actions-chevron-down {
                position: absolute;
                right: 5px;
                top: 5px;
                width: 20px;
                height: 20px;
                background-image: url([[pix:theme|chevron-down]]);
                background-repeat: no-repeat;
                background-size: 20px 20px;
                cursor: pointer;

                &._up {
                    transform: rotate(180deg);
                }
            }

            .assignments-actions-list {
                z-index: 10;
                position: absolute;
                left: 0;
                top: 35px;
                width: 195px;
                height: auto;
                padding-bottom: 20px;
                border-radius: 10px;
                background-color: $npe-white;
                box-shadow: 0 3px 6px #00000029;

                .actions-list-header {
                    margin-top: 20px;
                    margin-left: 10px;
                }

                .actions-list-item {
                    font-family: $osregular;
                    color: $npe-dark-grey;
                    margin-left: 10px;
                    margin-right: 40px;
                    cursor: pointer;
                }

                .actions-separator {
                    width: 90%;
                }
            }
        }

        .assignments-apply {
            display: inline-block;
            width: 62px;
            height: 30px;
            border-radius: 25px;
            margin: 0 20px 0px 0;
            position: relative;
            font-family: $ossemibold;
            font-size: 12px;
            color: $npe-white;
            text-align: center;
            padding: 6px 8px 6px 8px;
            cursor: pointer;

            &.disabled {
                opacity: .5;
                pointer-events: none;
            }
        }

        .gotofollowup {
            font-family: $ossemibold;
            font-size: 14px;
            display: inline-block;
            width: 170px;
            height: 40px;
            border-radius: 50px;
            color: $npe-white;
            margin: 0 20px 0px 0;
            position: relative;
            top: -15px;
            cursor: pointer;
            text-align: center;
            padding-top: 11px;

            a {
                color: $npe-white;

                :hover {
                    color: $npe-white;
                }
            }
        }

        .assignments-add {
            display: inline-block;
            width: 40px;
            height: 40px;
            border-radius: 50px;
            margin: 0 20px 0px 0;
            position: relative;
            background-image: url([[pix:theme|add_white]]);
            background-repeat: no-repeat;
            background-size: 16px 16px;
            background-position-x: 12px;
            background-position-y: 12px;
            cursor: pointer;
            .dots {
                background-size: 16px 16px;
            }
        }
        #dotsheaderassignments {
            margin: unset;
        }

        .header-user {
            display: inline-block;
            padding-left: 70px;
            position: relative;

            .order-assignments-users {
                position: absolute;
                bottom: 10px;
                margin-left: 10px;
                height: 32px;
                width: 32px;
                background-image: url([[pix:theme|order-user]]);
                border-radius: 8px;
                background-position: -2px -2px;
                cursor: pointer;

                &:hover {
                    background-color: $npe-grey-alt;
                }

                &.desc {
                    background-color: $npe-grey-alt;
                    background-image: url([[pix:theme|order-user-black]]);
                    background-position: center;
                    background-repeat: no-repeat;
                }
            }

            .select-all {
                text-decoration: underline;
                cursor: pointer;
            }
        }

        .header-date {
            display: inline-block;
        }

        .header-date-end {
            display: inline-block;
        }

        .header-comment {
            display: inline-block;
        }

        .header-grade-visibility-show {
            display: inline-block;
            top: -15px;
            margin-left: 20px;

            &:before {
                position: absolute;
                left: -7px;
                content: " ";
                width: 20px;
                height: 20px;
                background-color: $npe-grey-light;
                background-image: url([[pix:theme|eye-open]]);
                background-repeat: no-repeat;
                background-size: 16px auto;
                background-position: 2px 5px;
                border-radius: 50px;
            }

            .hide-all {
                cursor: pointer;
            }
        }

        .header-grade-visibility-hide {
            display: inline-block;
            top: -15px;
            margin-left: 20px;

            &:before {
                position: absolute;
                left: -7px;
                content: " ";
                width: 20px;
                height: 20px;
                background-color: $npe-black;
                background-image: url([[pix:theme|eye-off]]);
                background-repeat: no-repeat;
                background-size: 16px auto;
                background-position: 2px 2px;
                border-radius: 50px;
            }

            .hide-all {
                cursor: pointer;
            }
        }

        .activity-avatar {
            width: 40px;
            height: 40px;
            display: inline-block;
            position: relative;
            top: 10px;
            left: 35px;

            img {
                width: 40px;
                height: auto;
                border-radius: 50px;
            }
        }

        .activity-name {
            font-family: $ossemibold;
            font-size: 14px;
            color: $npe-black;
            display: inline-table;
            position: relative;
            top: 10px;
            left: 45px;
            height: 50px;

            @media only screen and (max-width: 1366px) {
               max-width: 190px;
            }

            &.hastag {
                top: -3px;
            }

            .tags {
                position: absolute;
                top: 20px;
            }
        }

        .activity-date {
            font-family: $osregular;
            font-size: 14px;
            color: $npe-dark-grey;
            display: flex;
            align-items: center;

            .delay {
                color: $npe-red-delay;
                display: contents;
            }
        }

        .activity-comment {
            max-width: 150px;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;

            span {
                width: 30px;
                height: 30px;
                border-radius: 5px;
                background-color: $npe-grey-alt;
                background-position: center;
                background-size: 51%;
                background-image: url([[pix:theme|comment_white]]);
                background-repeat: no-repeat;
            }

        }

        .feedback img {
            height: 60px;
            width: 22px;
            margin-left: 15px;
            opacity: .7;

            &:hover {
                opacity: 1;
            }
        }

        .activity-grade {
            display: flex;

            .grade-reviewed-checkbox {
                @include customcheckbox;
                position: relative;
                display: flex;

                label {
                    top: -5px;
                }

                span {
                    position: absolute;
                    top: 20px;
                    font-size: 12px;
                    left: -15px;
                }

                input[disabled] + label {
                    opacity: .2;
                }
            }

            .grade {
                margin-left: 36px;

                &.green {
                    @include gradebox(80px, 50px, true, false, false, false);
                }

                &.orange {
                    @include gradebox(80px, 50px, false, true, false, false);
                }

                &.red {
                    @include gradebox(80px, 50px, false, false, true, false);
                }

                &.grey {
                    @include gradebox(80px, 50px, false, false, false, true);
                }
            }
        }

        .no-assingment {
            width: 40%;
            margin: 0 auto;
            text-align: center;
            font-family: $osregular;

            h4 {
                font-family: $ossemibold;
                color: $npe-dark-grey;
                text-align: center;
                margin-top: 20px;
            }

            p {
                color: $npe-grey-light;
                padding: 20px;
            }
        }

        // FollowUp

        .assignments-followup-edit {
            display: inline-block;
            width: 40px;
            height: 40px;
            border-radius: 50px;
            margin: 0 40px 40px 0;
            position: relative;
            float: right;
            background-image: url([[pix:theme|gear_white]]);
            background-repeat: no-repeat;
            background-size: 30px 30px;
            background-position: center;
            cursor: pointer;
            transition-duration: 0.5s;
            transform: translateZ(0);

            &.open {
                position: relative;
                transition-duration: 0.5s;
                background-image: url([[pix:theme|plus_white]]);
                background-size: 20px;
                background-repeat: no-repeat;
                background-position: center;
                -webkit-transform: rotate(-90deg);
                -moz-transform: rotate(90deg);
                -ms-transform: rotate(90deg);
                -o-transform: rotate(90deg);
                transform: rotate(90deg);
            }
        }

        .followup-gear-options {
            position: absolute;
            margin-top: 50px;
            right: 20px;
            width: 206px;
            height: 150px;
            background: $npe-white 0 0 no-repeat padding-box;
            box-shadow: 0 5px 10px $npe-black-33;
            border-radius: 20px;
            z-index: 2000;
            height: fit-content;
            padding: 20px 0 0 20px;

            .followup-evidence-checkbox,
            .followup-answers-checkbox,
            .followup-visibility-checkbox {
                @include customcheckbox;
                position: relative;
                top: 0;
                left: 0;
                display: flex;
                margin-bottom: 20px;

                label {
                    top: 5px;
                }

                span {
                    font-size: 12px;
                    font-family: $osregular;
                    color: $npe-black;
                    width: 140px;
                    position: relative;
                    top: 3px;
                    left: -3px;
                }
            }
        }

        @media only screen and (max-width: 799px) {
            .followup-gear-options {
                position: absolute;
                margin-top: 70px;
                left: 20px;
                width: 206px;
                height: 150px;
                background: $npe-white 0 0 no-repeat padding-box;
                box-shadow: 0 5px 10px $npe-black-33;
                border-radius: 20px;
                z-index: 2000;
                height: fit-content;
                padding: 20px 0 0 20px;
            }
        }

        .header-edit-assign{
            .subtitle-header{
                display: inline;
                font-family: "Work Sans Light";
                font-size: 14px;
                color: #666;
                padding-left: 40px;
            }
            .row-buttons{
                padding-left: 0px;
                display: inline-block;
                vertical-align: top;
                max-width: 330px;
                padding-bottom: 15px;
            }
            .title{
                font-family: "Work Sans SemiBold";
                font-size: 20px;
                color: #000;
                text-transform: uppercase;
                padding-left: 40px;
            }
        }

        .row-buttons-header{
            padding-left: 40px;
            padding-top: 10px;
            border-bottom: 4px solid #ededed;
            padding-bottom: 20px;
            .icon{
                width: 17px;
                height: 18px;
                float: left;
                padding-top: 5px;
            }
            .assignments-actions{
                height: 40px;
                padding-top: 10px;
                font-size: 14px;
            }
            .assignments-apply{
                height: 40px;
                width: 86px;
                padding-top: 10px;
                font-size: 14px;
            }
            .actions-chevron-down{
                top: 10px;
            }
            .gotofollowup{
                top: -27px;
            }

            .sub-elements{
                .gotofollowup{
                    top: -17px;
                }
                .activity-icons{
                    display: inline-block;
                    height: 40px;
                    text-align: center;
                    position: relative;

                    img {
                        width: 30px;
                        height: 30px;
                        padding-top: 5px;
                    }
                }
            }
            .row-buttons-menu{
                float: right;
                display:  flex;
                flex-wrap: wrap;
                margin-right: 40px;
            }
        }

        #dotsheaderassignments {
            display: ruby-text-container;
            top: unset;
            margin: unset;
            .dots {
                height: 40px;
                width: 40px;
                background-size: 16px 16px;
            }
            .dropdown-item {
                word-break: unset;
                white-space: unset;
            }
            .dropdown-item:not(:last-child) {
                border-bottom: 1px solid #ededed;
            }
            .dropdown-menu .dropdown-item:last-child {
                border-bottom: 0 !important;
            }
        }
    }
}

.activity-icon {
    width: 40px;
    height: 50px;
    display: inline-block;
    background-repeat: no-repeat;
    background-size: auto 40px;
    background-position-x: 7px;
    background-position-y: 8px;

    &.ppt {
        background-image: url([[pix:theme|icontype/ppt]]);
    }

    &.pdf {
        background-image: url([[pix:theme|icontype/pdf]]);
    }

    &.mp4 {
        background-image: url([[pix:theme|icontype/mp4]]);
    }

    &.doc {
        background-image: url([[pix:theme|icontype/doc]]);
    }
}

.custom_checkbox {
    @include customcheckbox;
}

.icon-team {
    position: relative;
    content: " ";
    width: 40px;
    height: 40px;
    background-repeat: no-repeat;
    background-size: 25px auto;
    background-position: 8px 7px;
    border-radius: 50px;
    color: $npe-white;
    font-family: $ossemibold;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-transform: uppercase;

    &.typemodal {
        width: 60px;
        height: 60px;
        padding-top: 18px;
        font-size: 20px;
        display: inline-block;
        margin-bottom: 10px;
    }
}

.assignments-content {
    .activities-list.container-fluid {
        padding: 20px 40px;
        background: $basic-color-a;

        .activity-item.row {
            margin-top: 10px;
            border-radius: 20px;
            background: $basic-color-w;
            border: 1px solid $basic-color-b;
            position: relative;
        }
    }
}

.npe-header-assigments {
    font-size: 12px;
    color: $basic-color-c;
    position: relative;

    &.followup {
        top: 15px;
    }

    &.isresource {
        top: 0;
    }
}

.resource-card-container {
    padding: 40px;

    @include resourcecard();
}

#manage_assignment {
    .myassignments-content-form {
        .buttons {
            .npe-button-primary {
                &:disabled {
                    pointer-events: none;
                }
            }
        }
    }
}
{"version": 3, "file": "modal_success_assign_delivery.min.js", "sources": ["../src/modal_success_assign_delivery.js"], "sourcesContent": ["import $ from 'jquery';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\n\nconst SELECTORS = {\n    HIDE: '.close',\n    FINISH: '[data-action=\"cancel\"]',\n};\n\nlet registered = false;\n\nexport default class ModalSuccessAssignDelivery extends Modal {\n\n    static TYPE = 'local_npe/modal_success_assign_delivery';\n    static TEMPLATE = 'local_npe/assignments/modal_success_assign_delivery';\n\n    constructor(root) {\n        super(root);\n    }\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.success-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.success-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n        });\n    }\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalSuccessAssignDelivery.TYPE, ModalSuccessAssignDelivery, ModalSuccessAssignDelivery.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_defineProperty", "r", "t", "i", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_j<PERSON>y", "_custom_interaction_events", "_modal", "_modal_registry", "SELECTORS", "registered", "ModalSuccessAssignDelivery", "Modal", "constructor", "root", "super", "registerEventListeners", "this", "getModal", "on", "CustomEvents", "events", "activate", "getRoot", "removeClass", "$", "remove", "addClass", "_exports", "ModalRegistry", "register", "TYPE", "TEMPLATE"], "mappings": "wNAGgD,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,gBAAAH,EAAAI,EAAAC,GAAAD,OAAAA,EAAA,SAAAC,GAAAC,IAAAA,EAAA,SAAAD,EAAAD,GAAAC,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAL,IAAAA,EAAAK,EAAAE,OAAAC,yBAAAR,EAAA,CAAA,IAAAM,EAAAN,EAAAS,KAAAJ,EAAAD,kCAAAE,EAAA,OAAAA,EAAAI,MAAAA,IAAAA,4EAAAN,EAAAO,OAAAC,QAAAP,EAAA,CAAAQ,CAAAR,EAAAC,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAQ,CAAAV,MAAAJ,EAAAe,OAAAC,eAAAhB,EAAAI,EAAAa,CAAAA,MAAAZ,EAAAa,YAAAC,EAAAA,cAAAC,EAAAA,cAAApB,EAAAI,GAAAC,EAAAL,CAAA,iFAHhDqB,QAAAtB,uBAAAsB,SACAC,2BAAAvB,uBAAAuB,4BACAC,OAAAxB,uBAAAwB,QACAC,gBAAAzB,uBAAAyB,iBAEA,MAAMC,eACI,SADJA,iBAEM,yBAGZ,IAAIC,YAAa,EAEF,MAAMC,mCAAmCC,OAAAA,QAKpDC,WAAAA,CAAYC,MACRC,MAAMD,KACV,CAEAE,sBAAAA,GACID,MAAMC,uBAAuBC,MAE7BA,KAAKC,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUb,kBAAkB,KAC/DQ,KAAKM,UAAUC,YAAY,SAC3B,EAAAC,QAAAA,SAAE,QAAQD,YAAY,eACtB,EAAAC,iBAAE,kBAAkBC,UACpB,EAAAD,QAACvC,SAAC,mBAAmBsC,YAAY,QAAQG,SAAS,OAAO,IAG7DV,KAAKC,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUb,gBAAgB,KAC7DQ,KAAKM,UAAUC,YAAY,SAC3B,EAAAC,QAAAA,SAAE,QAAQD,YAAY,eACtB,EAAAC,iBAAE,kBAAkBC,UACpB,EAAAD,QAACvC,SAAC,mBAAmBsC,YAAY,QAAQG,SAAS,OAAO,GAEjE,EAMH,OALAC,SAAA1C,QAAAyB,2BAAAxB,gBA1BoBwB,2BAA0B,OAE7B,2CAAyCxB,gBAFtCwB,2BAA0B,WAGzB,uDAyBjBD,aACDmB,gBAAAA,QAAcC,SAASnB,2BAA2BoB,KAAMpB,2BAA4BA,2BAA2BqB,UAC/GtB,YAAa,GAChBkB,SAAA1C,OAAA"}
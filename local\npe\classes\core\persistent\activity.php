<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_npe\core\persistent;

use core\persistent;
use local_npe\core\activity_core;
use nperepository_teacher\persistent\repository;
use nperepository_teacher\persistent\repository_uses;
use function compact;
use function defined;
use const NULL_ALLOWED;
use const PARAM_BOOL;
use const PARAM_INT;
use const PARAM_RAW;

defined('MOODLE_INTERNAL') || die();

/**
 *
 */
class activity extends persistent {

    const TABLE = 'npe_activity';

    /**
     * @return array
     */
    protected static function define_properties() {
        return [
            'name' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'activitynum' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'activityorder' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'clasification' => [
                'type' => PARAM_RAW,
            ],
            'keyevidence' => [
                'type' => PARAM_BOOL,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'visibility' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'visibilitylabel' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'difficultylevel' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'h5pconfiguration' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'textanswertype' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'mapperassignment' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'progexam' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'license' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'idnumber' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'type' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'typeid' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'typelabel' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'sourceurl' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'filename' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'productid' => [
                'type' => PARAM_INT,
            ],
            'codproduct' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'activitycategoryid' => [
                'type' => PARAM_INT,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'activitycategoryname' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'activitycategorypackerid' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'activitycategorycategory' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'activitysectionid' => [
                'type' => PARAM_INT,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'activitysectionlabel' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'activitysection' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'producttopicid' => [
                'type' => PARAM_INT,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'producttopicname' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'producttopicpackerid' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'producttopicblockpackerid' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'defaultdelivery' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'isassignable' => [
                'type' => PARAM_BOOL,
                'default' => 1,
                'null' => NULL_ALLOWED,
            ],
            'showtostudent' => [
                'type' => PARAM_BOOL,
                'default' => 0,
                'null' => NULL_ALLOWED,
            ],
            'insequence' => [
                'type' => PARAM_BOOL,
                'default' => 0,
                'null' => NULL_ALLOWED,
            ],
            'origin' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'activityn4id' => [
                'type' => PARAM_INT,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            // Campos que no son de la tabla.
            'n4packerid' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'n4title' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'iscustom' => [
                'type' => PARAM_BOOL,
                'default' => 0,
                'null' => NULL_ALLOWED,
            ],
            'lastupdatedate' => [
                'type' => PARAM_INT,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'competenceid' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'criterionid' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'themeid' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'transversekeyid' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'skillsid' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'pedagogicalpurposesid' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'assessmentid' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'learninglevelid' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'trackingactivitiesid' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'challengesid' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'incontextid' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'typeofactivityid' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'presentationresourcesid' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'subcategoryid' => [
                'type' => PARAM_INT,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
            'subcategory' => [
                'type' => PARAM_RAW,
                'default' => null,
                'null' => NULL_ALLOWED,
            ],
        ];
    }

    /**
     * @param $productid
     * @param int|null $ccaa
     * @return activity[]
     * @throws \dml_exception
     */
    public static function get_activities($productid, ?int $ccaa = null): array {
        global $DB;

        $sql = self::get_base_activity_sql() . ' WHERE a.productid= :productid';
        $ccaa = product_topic::check_ccaa_variation($ccaa);
        $sql .= $ccaa !== null ?
            ' AND (ca.ccaaid = '.$ccaa.' OR ca.ccaaid IS NULL) AND (aca.ccaaid = '.$ccaa.' OR aca.ccaaid IS NULL)': '';
        $sql .= ' ORDER BY  pt.sdanumber, pt.name, a.activityorder, a.name';
        /** @var activity[] $persistents */
        $persistents = [];
        $recordset = $DB->get_recordset_sql($sql, ['productid' => $productid]);
        foreach ($recordset as $record) {
            $persistents[] = new static(0, $record);
        }
        $recordset->close();

        return $persistents;
    }

    /**
     * @param int ...$activityids
     * @return activity[]
     * @throws \coding_exception
     * @throws \dml_exception
     */
    public static function get_activityies_by_ids(int ...$activityids) {
        global $DB;

        [$insql, $inparams] = $DB->get_in_or_equal($activityids);
        $sql = self::get_base_activity_sql() . ' WHERE a.id '.$insql;
        $sql .= ' ORDER BY pt.name, a.activityorder, a.name';
        /** @var activity[] $persistents */
        $persistents = [];
        $recordset = $DB->get_recordset_sql($sql, $inparams);
        foreach ($recordset as $record) {
            $persistents[] = new static(0, $record);
        }
        $recordset->close();

        return $persistents;
    }

    /**
     * @param int $productid
     * @param int $courseid
     * @param int|null $ccaa
     * @return \local_npe\core\persistent\activity[]
     * @throws \dml_exception
     */
    public static function get_assigned_activities(int $productid, int $courseid, ?int $ccaa = null): array {
        global $DB;

        $sql = self::get_base_activity_sql();
        $sql .= ' INNER JOIN {' . assign_activity::TABLE . '} aa ON aa.activityid = a.id
                  WHERE a.productid= :productid AND aa.courseid = :courseid';
        $ccaa = product_topic::check_ccaa_variation($ccaa);
        $sql .= $ccaa !== null ?
            ' AND (ca.ccaaid = '.$ccaa.' OR ca.ccaaid IS NULL) AND (aca.ccaaid = '.$ccaa.' OR aca.ccaaid IS NULL)': '';
        $params = compact('productid', 'courseid');

        /** @var self[] $persistents */
        $persistents = [];
        $recordset = $DB->get_recordset_sql($sql, $params);
        foreach ($recordset as $record) {
            $persistents[] = new static(0, $record);
        }
        $recordset->close();

        return $persistents;
    }

    /**
     * @param int $courseid
     * @param int|null $sectionid
     * @param int|null $ccaa
     * @param int|null $topicid
     * @return activity[]
     * @throws \dml_exception
     */
    public static function get_repository_activities_by_course(
        int $courseid,
        ?int $sectionid = null,
        ?int $ccaa = null,
        ?int $topicid = null,
        ?bool $onlyactivities = false): array {
        global $DB;

        $sql = self::get_base_activity_sql() ;
        $sql .= ' INNER JOIN {' . repository_uses::TABLE . '} u ON u.activityid = a.id';
        $sql .= ' WHERE u.courseid = :courseid';
        $sql .= $sectionid !== null ? ' AND a.activitysectionid = :sectionid' : '';
        $sql .= $topicid !== null ? ' AND a.producttopicid = :topicid' : '';
        $sql .= $onlyactivities ? ' AND a.clasification = "' . activity_core::CLASIFICATION_ACTIVITY . '" ' : '';
        $ccaa = product_topic::check_ccaa_variation($ccaa);
        $sql .= $ccaa !== null ?
            ' AND (ca.ccaaid = '.$ccaa.' OR ca.ccaaid IS NULL) AND (aca.ccaaid = '.$ccaa.' OR aca.ccaaid IS NULL)': '';
        $params = compact('sectionid', 'courseid', 'topicid');
        /** @var activity[] $persistents */
        $persistents = [];

        $recordset = $DB->get_recordset_sql($sql, $params);
        foreach ($recordset as $record) {
            $persistents[] = new static(0, $record);
        }
        $recordset->close();

        return $persistents;
    }

    /**
     * Devuelve las actividades tipo evidencia asignadas a un alumno
     * @param int $productid
     * @param int $topicid
     * @return \local_npe\core\persistent\activity[]
     * @throws \dml_exception
     */
    public static function get_assigned_evidences_activities(int $productid, int $topicid, int $userid): array {
        global $DB;

        $sql = self::get_base_activity_sql();
        $sql .= ' INNER JOIN {' . assign_activity::TABLE . '} aa ON aa.activityid = a.id
                INNER JOIN {' . assign_activity_student::TABLE . '} aas ON aas.assignactivityid=aa.id
            WHERE a.productid = :productid
                AND a.producttopicid = :topicid
                AND a.keyevidence = 1
                AND a.iscustom = 0
                AND aas.userid = :userid
            ORDER BY a.name';

        $params = compact('productid', 'topicid', 'userid');

        /** @var self[] $persistents */
        $persistents = [];
        $recordset = $DB->get_recordset_sql($sql, $params);
        foreach ($recordset as $record) {
            $persistents[] = new static(0, $record);
        }
        $recordset->close();

        return $persistents;
    }

    /**
     * Devuelve las actividades tipo evidencia asignadas a un alumno
     * @param int $productid
     * @param int $topicid
     * @return \local_npe\core\persistent\activity[]
     * @throws \dml_exception
     */
    public static function get_assigned_student_evidences_activities(int $productid, int $topicid, int $userid): array {
        global $DB;

        $typeid = activity_core::ACTIVITY_TYPE_ID_STUDENT_EVIDENCE;

        $sql = self::get_base_activity_sql();
        $sql .= ' INNER JOIN {' . assign_activity::TABLE . '} aa ON aa.activityid = a.id
                INNER JOIN {' . assign_activity_student::TABLE . '} aas ON aas.assignactivityid=aa.id
            WHERE a.productid = :productid
                AND a.producttopicid = :topicid
                AND a.keyevidence = 1
                AND a.typeid = :typeid
                AND aas.userid = :userid
            ORDER BY a.name';

        $params = compact('productid', 'topicid', 'userid', 'typeid');

        /** @var self[] $persistents */
        $persistents = [];
        $recordset = $DB->get_recordset_sql($sql, $params);
        foreach ($recordset as $record) {
            $persistents[] = new static(0, $record);
        }
        $recordset->close();

        return $persistents;
    }


    /**
     * @param $productid
     * @param int $timemodified
     * @return activity[]
     * @throws \dml_exception
     */
    public static function get_activities_to_delete($productid, int $timemodified): array {
        global $DB;

        $sql = self::get_base_activity_sql() . ' WHERE a.productid= :productid AND a.timemodified < :timemodified AND a.iscustom<>1';

        /** @var activity[] $persistents */
        $persistents = [];

        $recordset = $DB->get_recordset_sql($sql, ['productid' => $productid, 'timemodified' => $timemodified]);
        foreach ($recordset as $record) {
            $persistents[] = new static(0, $record);
        }
        $recordset->close();

        return $persistents;
    }

    /**
     * @param $productid
     * @return activity[]
     * @throws \dml_exception
     */
    public static function get_orphaned_custom_activities($productid): array {
        global $DB;

        $sql =
            'SELECT a.* FROM {npe_activity} a' .
            ' INNER JOIN  {npe_assign_activity} aa ON aa.activityid = a.id' .
            ' LEFT JOIN {npe_product_topic} pt ON pt.id = a.producttopicid' .
            ' WHERE a.productid= :productid AND a.iscustom=1 AND pt.id IS NULL';

        /** @var activity[] $persistents */
        $persistents = [];

        $recordset = $DB->get_recordset_sql($sql, ['productid' => $productid]);
        foreach ($recordset as $record) {
            $persistents[] = new static(0, $record);
        }
        $recordset->close();

        return $persistents;
    }

    /**
     * @param $activityid
     * @param int|null $productid
     * @return activity|null
     * @throws \dml_exception
     */
    public static function get_activity($activityid, ?int $productid = null): ?activity {
        global $DB;

        $sql = self::get_base_activity_sql() . ' WHERE a.id= :activityid';
        $sql .= ($productid !== null ? ' AND p.id = ' . $productid : '');

        $recordset = $DB->get_recordset_sql($sql, ['activityid' => $activityid, 'productid' => $productid]);

        return ($recordset->valid()) ? new static(0, $recordset->current()) : null;
    }

    /**
     * @param string $idnumber
     * @param int $productid
     * @return activity|null
     * @throws \dml_exception
     */
    public static function get_activity_by_idnumber(string $idnumber, int $productid): ?activity {
        global $DB;

        $sql = self::get_base_activity_sql() . ' WHERE a.idnumber= :idnumber AND p.id = :productid';

        $recordset = $DB->get_recordset_sql($sql, ['idnumber' => $idnumber, 'productid' => $productid]);

        return ($recordset->valid()) ? new static(0, $recordset->current()) : null;
    }

    /**
     * @return string
     */
    private static function get_base_activity_sql(): string {
        return 'SELECT DISTINCT a.*,
                ac.name as activitycategoryname,
                ac.packerid as activitycategorypackerid,
                ac.category as activitycategorycategory,
                pt.name as producttopicname,
                pt.packerid as producttopicpackerid,
                ptb.packerid as producttopicblockpackerid,
                asec.sectionlabel as activitysectionlabel,
                asec.section as activitysection,
                n4.packerid as n4packerid,
                n4.title as n4title,
                p.codproduct
                FROM {' . static::TABLE . '} a
                INNER JOIN {' . product::TABLE . '} p ON p.id = a.productid
                LEFT JOIN {' . activity_category::TABLE . '} ac ON ac.id = a.activitycategoryid
                LEFT JOIN {' . product_topic::TABLE . '} pt ON pt.id = a.producttopicid AND pt.productid = p.id
                LEFT JOIN {' . product_topic_block::TABLE . '} ptb ON ptb.id = pt.blockid AND pt.id = a.producttopicid
                LEFT JOIN {' . product_topic_ccaa::TABLE . '} ca ON ca.packerid = pt.packerid AND ca.productid = p.id
                LEFT JOIN {' . activity_ccaa::TABLE . '} aca ON aca.activityidnumber = a.idnumber AND aca.productid = p.id
                LEFT JOIN {' . activity_section::TABLE . '} asec ON asec.id = a.activitysectionid AND p.id = asec.productid
                LEFT JOIN {' . activity_n4_persistent::TABLE . '} n4 ON n4.id = a.activityn4id';
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<XMLDB PATH="local/npe/db" VERSION="20210507" COMMENT="XMLDB file for Moodle local/npe"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:noNamespaceSchemaLocation="../../../lib/xmldb/xmldb.xsd"
>
    <TABLES>
        <TABLE NAME="npe_enrolment_keys" COMMENT="User enrolment keys">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="enrolment" TYPE="char" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="expired" TYPE="int" LENGTH="1" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_enrolment_keys_courseid" UNIQUE="false" FIELDS="courseid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_tag" COMMENT="Etiquetas para equipos">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="tag" TYPE="char" LENGTH="50" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="color" TYPE="char" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_tag_tag" UNIQUE="false" FIELDS="tag"/>
                <INDEX NAME="idx_npe_tag_courseid" UNIQUE="false" FIELDS="courseid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_team_tag" COMMENT="Relación entre Etiquetas y Equipos">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="teamid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="tagid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_team_tag_teamid" UNIQUE="false" FIELDS="teamid"/>
                <INDEX NAME="idx_npe_team_tag_tagid" UNIQUE="false" FIELDS="tagid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_team_metadata" COMMENT="Datos adicionales de los equipos.">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="teamid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="startingcolor" TYPE="char" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="endingcolor" TYPE="char" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_team_tag_teamid" UNIQUE="false" FIELDS="teamid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_product" COMMENT="Productos">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="name" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="level" TYPE="char" LENGTH="50" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="codproduct" TYPE="char" LENGTH="50" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="showhelp" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false" DEFAULT="0"/>
                <FIELD NAME="country" TYPE="char" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="language" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="subject" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="contentview" TYPE="char" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="familylink" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false" DEFAULT="0"/>
                <FIELD NAME="familyurl" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="icon" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="frontpage" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="background" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="isotipo" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="stage" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="productjson" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="productjsonhash" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="repositoryjson" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="repositoryjsonhash" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="theme" TYPE="char" LENGTH="30" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_product_codproduct" UNIQUE="false" FIELDS="codproduct"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_activity" COMMENT="Actividades del producto">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="name" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="activitynum" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="clasification" TYPE="char" LENGTH="20" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="idnumber" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="type" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="typeid" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="typelabel" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="keyevidence" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="visibility" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="visibilitylabel" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="difficultylevel" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="h5pconfiguration" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="textanswertype" TYPE="char" LENGTH="2" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="mapperassignment" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="progexam" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="sourceurl" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="productid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="activitycategoryid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="activitysectionid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="activityn4id" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="producttopicid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="defaultdelivery" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="isassignable" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="iscustom" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false" DEFAULT="0"/>
                <FIELD NAME="showtostudent" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="insequence" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="origin" TYPE="char" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="activityorder" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="competenceid" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="criterionid" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="themeid" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="transversekeyid" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="skillsid" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="pedagogicalpurposesid" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="assessmentid" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="learninglevelid" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="trackingactivitiesid" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="challengesid" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="incontextid" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="typeofactivityid" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="presentationresourcesid" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="subcategoryid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="subcategory" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="lastupdatedate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_activity_clasification" UNIQUE="false" FIELDS="clasification"/>
                <INDEX NAME="idx_npe_activity_idnumber" UNIQUE="false" FIELDS="idnumber"/>
                <INDEX NAME="idx_npe_activity_productid" UNIQUE="false" FIELDS="productid"/>
                <INDEX NAME="idx_npe_activity_activitycategoryid" UNIQUE="false" FIELDS="activitycategoryid"/>
                <INDEX NAME="idx_npe_activity_producttopicid" UNIQUE="false" FIELDS="producttopicid"/>
                <INDEX NAME="idx_npe_activity_activityn4id" UNIQUE="false" FIELDS="activityn4id"/>
                <INDEX NAME="idx_npe_activity_subcategoryid" UNIQUE="false" FIELDS="subcategoryid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_activity_category" COMMENT="Clasificación de las actividades">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="packerid" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="category" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="name" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="sort" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="productid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_activity_category_packerid" UNIQUE="false" FIELDS="packerid"/>
                <INDEX NAME="idx_npe_activity_category_category" UNIQUE="false" FIELDS="category"/>
                <INDEX NAME="idx_npe_activity_category_productid" UNIQUE="false" FIELDS="productid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_activity_section" COMMENT="Secciones de las actividades">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="packerid" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="idview" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="section" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="profile" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="icon" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="teachertitle" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="studenttitle" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="sectionlabel" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="productid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="enriching" TYPE="int" LENGTH="1" NOTNULL="true" SEQUENCE="false" DEFAULT="0"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_activity_section_packerid" UNIQUE="false" FIELDS="packerid"/>
                <INDEX NAME="idx_npe_activity_section_section" UNIQUE="false" FIELDS="section"/>
                <INDEX NAME="idx_npe_activity_section_productid" UNIQUE="false" FIELDS="productid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_product_topic" COMMENT="Temas/unidades del producto">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="packerid" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="name" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="productid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="digitalbook" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="defaultviewer" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="sdatype" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="sdaimage" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="sdanumber" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="blockid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="areaid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="topicjson" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="topicjsonhash" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="parentid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="topicorder" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="unittitle" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="visibilityn2" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_producttopic_packerid" UNIQUE="false" FIELDS="packerid"/>
                <INDEX NAME="idx_npe_producttopic_productid" UNIQUE="false" FIELDS="productid"/>
                <INDEX NAME="idx_npe_producttopic_blockid" UNIQUE="false" FIELDS="blockid"/>
                <INDEX NAME="idx_npe_producttopic_areaid" UNIQUE="false" FIELDS="areaid"/>
                <INDEX NAME="idx_npe_producttopic_parentid" UNIQUE="false" FIELDS="parentid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_product_topic_block" COMMENT="Bloques en que se agrupan los Temas/unidades">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="packerid" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="name" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="productid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="topicblockorder" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_producttopic_block_packerid" UNIQUE="false" FIELDS="packerid"/>
                <INDEX NAME="idx_npe_producttopic_block_productid" UNIQUE="false" FIELDS="productid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_product_topic_area" COMMENT="Areas en que se agrupan los Temas/unidades">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="packerid" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="name" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="productid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_producttopic_area_packerid" UNIQUE="false" FIELDS="packerid"/>
                <INDEX NAME="idx_npe_producttopic_area_productid" UNIQUE="false" FIELDS="productid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_hidden_product_topic" COMMENT="Temas ocultados por el profesor">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="producttopicid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_hidden_course_topic_producttopicid" UNIQUE="false" FIELDS="producttopicid"/>
                <INDEX NAME="idx_npe_hidden_course_topic_courseid" UNIQUE="false" FIELDS="courseid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_assign_activity" COMMENT="Asignar actividad">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="activityid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="deliverytype" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_assign_activity_activityid" UNIQUE="false" FIELDS="activityid"/>
                <INDEX NAME="idx_npe_assign_activity_courseid" UNIQUE="false" FIELDS="courseid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_assign_activity_student" COMMENT="Asignar actividad individualmente">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="assignactivityid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="title" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="comment" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="feedback" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="date" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timebegin" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timeend" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="stateid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="grade" TYPE="float" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="gradedate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="deliverydate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="reopendate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="done" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false" DEFAULT="0"/>
                <FIELD NAME="usercreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_assign_activity_user_assignactivityid" UNIQUE="false" FIELDS="assignactivityid"/>
                <INDEX NAME="idx_npe_assign_activity_user_userid" UNIQUE="false" FIELDS="userid"/>
                <INDEX NAME="idx_npe_assign_activity_user_stateid" UNIQUE="false" FIELDS="stateid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_assign_activity_team" COMMENT="Asignación grupal de actividad">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="assignactivityid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="groupid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="title" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="comment" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="feedback" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="date" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timebegin" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timeend" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="stateid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="grade" TYPE="float" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="gradedate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="deliverydate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="reopendate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="done" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false" DEFAULT="0"/>
                <FIELD NAME="usercreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_assign_activity_team_assignactivityid" UNIQUE="false" FIELDS="assignactivityid"/>
                <INDEX NAME="idx_npe_assign_activity_team_groupid" UNIQUE="false" FIELDS="groupid"/>
                <INDEX NAME="idx_npe_assign_activity_team_stateid" UNIQUE="false" FIELDS="stateid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_assign_activity_class" COMMENT="Asignación de la actividad para todos los estudiantes">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="assignactivityid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="title" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="comment" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="feedback" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="date" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timebegin" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timeend" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="stateid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="grade" TYPE="float" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="gradedate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="deliverydate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="reopendate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="done" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false" DEFAULT="0"/>
                <FIELD NAME="usercreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_assign_activity_class_assignactivityid" UNIQUE="false" FIELDS="assignactivityid"/>
                <INDEX NAME="idx_npe_assign_activity_class_stateid" UNIQUE="false" FIELDS="stateid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_access" COMMENT="Tabla temporal para almacenar los accesos a productos asociados a un token">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="token" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="codproduct" TYPE="char" LENGTH="50" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="backurl" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_access_token" UNIQUE="true" FIELDS="token"/>
                <INDEX NAME="idx_npe_access_userid" UNIQUE="false" FIELDS="userid"/>
                <INDEX NAME="idx_npe_access_codproduct" UNIQUE="false" FIELDS="codproduct"/>
                <INDEX NAME="idx_npe_access_courseid" UNIQUE="false" FIELDS="courseid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_activity_state" COMMENT="Estados en los que puede estar la actividad">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="alias" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="name" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_activity_status_alias" UNIQUE="true" FIELDS="alias"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_user_license" COMMENT="Datos de las licencias de los usuarios">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="productid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="licenseexpiration" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="onboarding" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false" DEFAULT="0"/>
                <FIELD NAME="onboardinggroups" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false" DEFAULT="0"/>
                <FIELD NAME="noticenewcourse" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false" DEFAULT="0"/>
                <FIELD NAME="cu" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false" DEFAULT="1"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_user_license_userid" UNIQUE="false" FIELDS="userid"/>
                <INDEX NAME="idx_npe_user_license_productid" UNIQUE="false" FIELDS="productid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_notifications" COMMENT="Notificationes">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="type" TYPE="char" LENGTH="50" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="url" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="params" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
                <FIELD NAME="teamid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="uuid" TYPE="char" LENGTH="100" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="count" TYPE="int" LENGTH="7" NOTNULL="true" DEFAULT="1" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="mdl_locanoti_cou_ix" UNIQUE="false" FIELDS="courseid"/>
                <INDEX NAME="mdl_locanoti_uid_ix" UNIQUE="false" FIELDS="uuid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_notifications_user" COMMENT="Notifications users">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="notificationid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="fromdate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="todate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="viewed" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="mdl_locanoti_usr_ix" UNIQUE="false" FIELDS="userid"/>
                <INDEX NAME="mdl_locanoti_fdt_ix" UNIQUE="false" FIELDS="fromdate"/>
                <INDEX NAME="mdl_locanoti_tdt_ix" UNIQUE="false" FIELDS="todate"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_ccaa" COMMENT="Comunidades autónomas">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="ccaaid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="name" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_ccaa" UNIQUE="false" FIELDS="ccaaid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_product_topic_ccaa" COMMENT="Datos de las licencias de los usuarios">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="packerid" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="ccaaid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="productid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_producttopic_ccaa_packerid" UNIQUE="false" FIELDS="packerid"/>
                <INDEX NAME="idx_npe_producttopic_ccaa_ccaaid" UNIQUE="false" FIELDS="ccaaid"/>
                <INDEX NAME="idx_npe_producttopic_ccaa_productid" UNIQUE="false" FIELDS="productid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_activity_ccaa" COMMENT="Datos de las licencias de los usuarios">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="activityidnumber" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="ccaaid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="productid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_activity_ccaa_activityidnumber" UNIQUE="false" FIELDS="activityidnumber"/>
                <INDEX NAME="idx_npe_activity_ccaa_ccaaid" UNIQUE="false" FIELDS="ccaaid"/>
                <INDEX NAME="idx_npe_activity_ccaa_productid" UNIQUE="false" FIELDS="productid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_last_groups_visited" COMMENT="Histórico de grupos visitados por usuario">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="mdl_lastgroups_cou_ix" UNIQUE="false" FIELDS="courseid"/>
                <INDEX NAME="mdl_lastgroups_usr_ix" UNIQUE="false" FIELDS="userid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_activity_n4" COMMENT="Agrupación de actividades en la unidad">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="packerid" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="title" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="topicid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="productid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_producttopic_area_packerid" UNIQUE="false" FIELDS="packerid"/>
                <INDEX NAME="idx_npe_producttopic_area_topicid" UNIQUE="false" FIELDS="topicid"/>
                <INDEX NAME="idx_npe_producttopic_area_productid" UNIQUE="false" FIELDS="productid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_activity_grade_student" COMMENT="Calificaciones de las actividades por estudiantes">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="activityid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="checked" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false" DEFAULT="0"/>
                <FIELD NAME="grade" TYPE="float" LENGTH="10" NOTNULL="false" SEQUENCE="false" DECIMALS="2"/>
                <FIELD NAME="gradedate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="deliverydate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="reopendate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_student_activity_grade_courseid" UNIQUE="false" FIELDS="courseid"/>
                <INDEX NAME="idx_npe_student_activity_grade_activityid" UNIQUE="false" FIELDS="activityid"/>
                <INDEX NAME="idx_npe_student_activity_grade_userid" UNIQUE="false" FIELDS="userid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_activity_grade_team" COMMENT="Calificaciones de las actividades por equipos">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="activityid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="teamid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="checked" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false" DEFAULT="0"/>
                <FIELD NAME="grade" TYPE="float" LENGTH="10" NOTNULL="false" SEQUENCE="false" DECIMALS="2"/>
                <FIELD NAME="gradedate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="deliverydate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="reopendate" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_student_activity_grade_courseid" UNIQUE="false" FIELDS="courseid"/>
                <INDEX NAME="idx_npe_student_activity_grade_activityid" UNIQUE="false" FIELDS="activityid"/>
                <INDEX NAME="idx_npe_student_activity_grade_teamid" UNIQUE="false" FIELDS="teamid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_activity_config" COMMENT="Configuraciones de las actividades hechas por el Profe.">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="activityid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="hidegrade" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="showanswer" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_activity_config_courseid" UNIQUE="false" FIELDS="courseid"/>
                <INDEX NAME="idx_npe_activity_config_activityid" UNIQUE="false" FIELDS="activityid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_student_objective" COMMENT="Valor de objetivos de portfolio por estudiante">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="topicid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="objectiveid" TYPE="char" LENGTH="100" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="value" TYPE="char" LENGTH="100" NOTNULL="true" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="mdl_objectives_cou_ix" UNIQUE="false" FIELDS="courseid"/>
                <INDEX NAME="mdl_objectives_usr_ix" UNIQUE="false" FIELDS="userid"/>
                <INDEX NAME="mdl_objectives_top_ix" UNIQUE="false" FIELDS="topicid"/>
                <INDEX NAME="mdl_objectives_obj_ix" UNIQUE="false" FIELDS="objectiveid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_diary_questions" COMMENT="Preguntas del Diario de pensar">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="topicid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="questionid" TYPE="char" LENGTH="100" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="label" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="mdl_question_cou_ix" UNIQUE="false" FIELDS="courseid"/>
                <INDEX NAME="mdl_question_top_ix" UNIQUE="false" FIELDS="topicid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_student_journalothinking" COMMENT="Diario de pensar en portfolio de estudiante">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="topicid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="questionid" TYPE="char" LENGTH="100" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="value" TYPE="text" NOTNULL="true" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="mdl_journalothinking_cou_ix" UNIQUE="false" FIELDS="courseid"/>
                <INDEX NAME="mdl_journalothinking_usr_ix" UNIQUE="false" FIELDS="userid"/>
                <INDEX NAME="mdl_journalothinking_que_ix" UNIQUE="false" FIELDS="questionid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_plan_questions" COMMENT="Preguntas del Paln de Mejora">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="topicid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="questionid" TYPE="char" LENGTH="100" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="label" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="mdl_planquestion_cou_ix" UNIQUE="false" FIELDS="courseid"/>
                <INDEX NAME="mdl_planquestion_top_ix" UNIQUE="false" FIELDS="topicid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_student_plan" COMMENT="Plan de mejora en portfolio de estudiante">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="topicid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="questionid" TYPE="char" LENGTH="100" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="value" TYPE="text" NOTNULL="true" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="mdl_plan_cou_ix" UNIQUE="false" FIELDS="courseid"/>
                <INDEX NAME="mdl_plan_usr_ix" UNIQUE="false" FIELDS="userid"/>
                <INDEX NAME="mdl_plan_que_ix" UNIQUE="false" FIELDS="questionid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_activity_feedback_stud" COMMENT="Feedbacks de una actividad individual">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="activityid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="fromuserid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="touserid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="comment" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="npe_evidence_activity_cou_ix" UNIQUE="false" FIELDS="courseid"/>
                <INDEX NAME="npe_evidence_activity_act_ix" UNIQUE="false" FIELDS="activityid"/>
                <INDEX NAME="idx_npe_activity_feedback_activityid" UNIQUE="false" FIELDS="activityid"/>
                <INDEX NAME="idx_npe_activity_feedback_courseid" UNIQUE="false" FIELDS="courseid"/>
                <INDEX NAME="idx_npe_activity_feedback_fromuserid" UNIQUE="false" FIELDS="fromuserid"/>
                <INDEX NAME="idx_npe_activity_feedback_touserid" UNIQUE="false" FIELDS="touserid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_activity_feedback_team" COMMENT="Feedbacks de una actividad individual">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="activityid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="fromuserid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="toteamid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="comment" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_activity_feedback_activityid" UNIQUE="false" FIELDS="activityid"/>
                <INDEX NAME="idx_npe_activity_feedback_courseid" UNIQUE="false" FIELDS="courseid"/>
                <INDEX NAME="idx_npe_activity_feedback_fromuserid" UNIQUE="false" FIELDS="fromuserid"/>
                <INDEX NAME="idx_npe_activity_feedback_toteamid" UNIQUE="false" FIELDS="toteamid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_activity_feedback_class" COMMENT="Feedbacks de una actividad individual">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="activityid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="fromuserid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="comment" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_activity_feedback_activityid" UNIQUE="false" FIELDS="activityid"/>
                <INDEX NAME="idx_npe_activity_feedback_courseid" UNIQUE="false" FIELDS="courseid"/>
                <INDEX NAME="idx_npe_activity_feedback_fromuserid" UNIQUE="false" FIELDS="fromuserid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_portfolio_feedback" COMMENT="Feedbacks del portfolio">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="fromuserid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="touserid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="comment" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_portfolio_feedback_courseid" UNIQUE="false" FIELDS="courseid"/>
                <INDEX NAME="idx_npe_portfolio_feedback_fromuserid" UNIQUE="false" FIELDS="fromuserid"/>
                <INDEX NAME="idx_npe_portfolio_feedback_touserid" UNIQUE="false" FIELDS="touserid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_student_evidence" COMMENT="Agregar actividad evidencia del estudiante">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="activityid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="title" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="description" TYPE="text" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="attachment" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="npe_evidence_activity_act_ix" UNIQUE="false" FIELDS="activityid"/>
                <INDEX NAME="npe_evidence_activity_cou_ix" UNIQUE="false" FIELDS="courseid"/>
                <INDEX NAME="npe_evidence_activity_usr_ix" UNIQUE="false" FIELDS="userid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_course_options" COMMENT="Creación de tabla de opciones de curso">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="ccaa" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="codproduct" TYPE="char" LENGTH="50" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="educamoscourse" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" DEFAULT="0"/>
                <FIELD NAME="externalcourse" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" DEFAULT="0"/>
                <FIELD NAME="externalgroupname" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="marsupialcourse" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" DEFAULT="0"/>
                <FIELD NAME="marsupialgroupname" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="studentcourse" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" DEFAULT="0"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="npe_course_options_cou_ix" UNIQUE="true" FIELDS="courseid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_activity_subcategory" COMMENT="Creación de tabla de subcategorias">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="packerid" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="subcategory" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="name" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="sort" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="productid" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_activity_subcategory_id" UNIQUE="true" FIELDS="id"/>
                <INDEX NAME="idx_npe_activity_subcategory_packerid" UNIQUE="false" FIELDS="packerid"/>
                <INDEX NAME="idx_npe_activity_subcategory_subcategory" UNIQUE="false" FIELDS="subcategory"/>
                <INDEX NAME="idx_npe_activity_subcategory_productid" UNIQUE="false" FIELDS="productid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="npe_topic_teacher_perso" COMMENT="User enrolment keys">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="packerid" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
                <FIELD NAME="custom" TYPE="int" LENGTH="1" NOTNULL="true" SEQUENCE="false" DEFAULT="0"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_npe_topic_teacher_perso_userid" UNIQUE="false" FIELDS="userid"/>
                <INDEX NAME="idx_npe_topic_teacher_perso_courseid" UNIQUE="false" FIELDS="courseid"/>
            </INDEXES>
        </TABLE>
    </TABLES>
</XMLDB>

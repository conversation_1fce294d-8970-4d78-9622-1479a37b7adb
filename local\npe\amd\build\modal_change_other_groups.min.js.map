{"version": 3, "file": "modal_change_other_groups.min.js", "sources": ["../src/modal_change_other_groups.js"], "sourcesContent": ["import $ from 'jquery';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\n\nconst SELECTORS = {\n    HEADER: '[data-region=\"header\"]',\n    NAMEGROUPTOPICS: '#namegrouptopics',\n    BUTTONCOPYTOPICS: '.buttoncopytopics',\n};\n\nlet registered = false;\n\nexport default class ModalChangeOtherGroups extends Modal {\n    static TYPE = 'local_npe/Modal_Change_Other_Groups';\n    static TEMPLATE = 'local_npe/courses/modal_change_other_groups';\n\n    constructor(root) {\n        super(root);\n    }\n\n    setData(nameGroup, havemoregroups) {\n        if (havemoregroups === 1) {\n            this.getRoot().find(SELECTORS.BUTTONCOPYTOPICS).hide();\n        }\n        this.getRoot().find(SELECTORS.NAMEGROUPTOPICS).text(nameGroup);\n    }\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {\n            this.getRoot().removeClass('show');\n            $('.change-other-groups-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {\n            this.getRoot().find(SELECTORS.HEADER).show();\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CANCELBUTTON, () => {\n            $('.change-other-groups-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n            $('body').removeClass('modal-open');\n        });\n    }\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalChangeOtherGroups.TYPE, ModalChangeOtherGroups, ModalChangeOtherGroups.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_defineProperty", "r", "t", "i", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_j<PERSON>y", "_custom_interaction_events", "_modal", "_modal_registry", "SELECTORS", "HEADER", "NAMEGROUPTOPICS", "BUTTONCOPYTOPICS", "registered", "ModalChangeOtherGroups", "Modal", "constructor", "root", "super", "setData", "nameGroup", "havemoregroups", "this", "getRoot", "find", "hide", "text", "registerEventListeners", "getModal", "on", "CustomEvents", "events", "activate", "FINISH", "removeClass", "$", "remove", "addClass", "HIDE", "show", "CANCELBUTTON", "_exports", "ModalRegistry", "register", "TYPE", "TEMPLATE"], "mappings": "oNAGgD,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,gBAAAH,EAAAI,EAAAC,GAAAD,OAAAA,EAAA,SAAAC,GAAAC,IAAAA,EAAA,SAAAD,EAAAD,GAAAC,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAL,IAAAA,EAAAK,EAAAE,OAAAC,yBAAAR,EAAA,CAAA,IAAAM,EAAAN,EAAAS,KAAAJ,EAAAD,kCAAAE,EAAA,OAAAA,EAAAI,MAAAA,IAAAA,4EAAAN,EAAAO,OAAAC,QAAAP,EAAA,CAAAQ,CAAAR,EAAAC,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAQ,CAAAV,MAAAJ,EAAAe,OAAAC,eAAAhB,EAAAI,EAAAa,CAAAA,MAAAZ,EAAAa,YAAAC,EAAAA,cAAAC,EAAAA,cAAApB,EAAAI,GAAAC,EAAAL,CAAA,iFAHhDqB,QAAAtB,uBAAAsB,SACAC,2BAAAvB,uBAAAuB,4BACAC,OAAAxB,uBAAAwB,QACAC,gBAAAzB,uBAAAyB,iBAEA,MAAMC,UAAY,CACdC,OAAQ,yBACRC,gBAAiB,mBACjBC,iBAAkB,qBAGtB,IAAIC,YAAa,EAEF,MAAMC,+BAA+BC,OAAAA,QAIhDC,WAAAA,CAAYC,MACRC,MAAMD,KACV,CAEAE,OAAAA,CAAQC,UAAWC,gBACQ,IAAnBA,gBACAC,KAAKC,UAAUC,KAAKf,UAAUG,kBAAkBa,OAEpDH,KAAKC,UAAUC,KAAKf,UAAUE,iBAAiBe,KAAKN,UACxD,CAEAO,sBAAAA,GACIT,MAAMS,uBAAuBL,MAE7BA,KAAKM,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUvB,UAAUwB,QAAQ,KAC/DX,KAAKC,UAAUW,YAAY,SAC3B,EAAAC,iBAAE,8BAA8BC,UAChC,EAAAD,QAACjD,SAAC,mBAAmBgD,YAAY,QAAQG,SAAS,OAAO,IAG7Df,KAAKM,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUvB,UAAU6B,MAAM,KAC7DhB,KAAKC,UAAUC,KAAKf,UAAUC,QAAQ6B,MAAM,IAGhDjB,KAAKM,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUvB,UAAU+B,cAAc,MACrE,EAAAL,iBAAE,8BAA8BC,UAChC,EAAAD,QAACjD,SAAC,mBAAmBgD,YAAY,QAAQG,SAAS,SAClD,EAAAF,QAAAA,SAAE,QAAQD,YAAY,aAAa,GAE3C,EAMH,OALAO,SAAAvD,QAAA4B,uBAAA3B,gBAlCoB2B,uBAAsB,OACzB,uCAAqC3B,gBADlC2B,uBAAsB,WAErB,+CAkCjBD,aACD6B,gBAAAA,QAAcC,SAAS7B,uBAAuB8B,KAAM9B,uBAAwBA,uBAAuB+B,UACnGhC,YAAa,GAChB4B,SAAAvD,OAAA"}
{"version": 3, "file": "modal_join_group.min.js", "sources": ["../src/modal_join_group.js"], "sourcesContent": ["import $ from 'jquery';\nimport Notification from 'core/notification';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\nimport Ajax from 'core/ajax';\nimport { get_strings as getStrings } from 'core/str';\n\nconst SELECTORS = {\n    MODALBOX: '.modalbox',\n    JOIN_BUTTON: '[data-action=\"JOIN\"]',\n    SAVE_BUTTON: '[data-action=\"save\"]',\n    INPUT: '#groupcode',\n    CONTAINER: '[data-region=\"modal-container\"]',\n    RESULT: '[data-region=\"result\"]',\n    ERROR: '.error',\n    HIDE: '.close',\n    PREVIOUS: '.previous',\n    CUSTOM: '.custom-text',\n    CONGRATSTITLE: '.congrats',\n    CONGRATSTEXT: '.congrats_text',\n    FINISH: '[data-action=\"finish\"]',\n    LEVELINFO: '.levelinfo',\n};\n\nconst SERVICES = {\n    JOINGROUP: 'local_npe_join_group'\n};\n\nlet registered = false;\n\nexport default class ModalJoinGroup extends Modal {\n\n    static TYPE = 'local_npe/modal_join_group';\n    static TEMPLATE = 'local_npe/groups/mygroups_modal_join_group';\n\n    #courseid;\n    #level;\n    #name;\n    #newcourse = false;\n    #courseurl = '';\n\n    constructor(root) {\n        super(root);\n    }\n\n    setData(level, name, courseid) {\n        this.#level = level;\n        this.#name = name;\n        this.#courseid = courseid;\n\n        getStrings(\n            [\n                {key: 'joincoursestudenttext', component: 'local_npe', param: name},\n                {key: 'joincourseteachertext', component: 'local_npe', param: name}\n            ]\n        ).done((strings) => {\n            if ($(SELECTORS.JOIN_BUTTON).data('student') == true) {\n                this.getRoot().find(SELECTORS.CUSTOM).html(strings[0]);\n            } else {\n                this.getRoot().find(SELECTORS.CUSTOM).html(strings[1]);\n            }\n        });\n\n        this.getRoot().find(SELECTORS.LEVELINFO).html(level + '. ' + name);\n    }\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.JOIN_BUTTON, () => {\n            this.getRoot().find(SELECTORS.LEVELINFO).html(this.#level + '. ' + this.#name);\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {\n            this.getRoot().find(SELECTORS.CONTAINER).show();\n            this.getTitle().show();\n            this.getRoot().find(SELECTORS.RESULT).hide();\n            this.getRoot().find(SELECTORS.ERROR).hide();\n            this.getRoot().find(SELECTORS.INPUT).removeClass('error');\n            this.getRoot().removeClass('show');\n            this.getRoot().find(SELECTORS.INPUT).val('');\n            this.getRoot().removeClass('show');\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.SAVE_BUTTON, () => {\n            this.ajaxCall();\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {\n            this.getRoot().removeClass('show');\n            $('.group-join-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n            if (this.#newcourse) {\n                window.location.replace(this.#courseurl);\n            } else {\n                location.reload();\n            }\n        });\n\n        this.eventMaxChar();\n    }\n\n    ajaxResult(response) {\n        if (response.course !== undefined) {\n            let name = response.course.name;\n            if ($(SELECTORS.JOIN_BUTTON).data('student') == true) {\n                this.#newcourse = response.course.id;\n                this.#courseurl = response.course.url;\n            }\n\n            getStrings([\n                {key: 'joincourseoktitle', component: 'local_npe', param: name},\n                {key: 'joincourseokteachertext', component: 'local_npe', param: name},\n                {key: 'joincourseokstudenttext', component: 'local_npe', param: name},\n            ]).done((strings) => {\n                $(SELECTORS.HIDE).attr('data-action', 'finish');\n                this.getRoot().find(SELECTORS.PREVIOUS).hide();\n                this.getRoot().find(SELECTORS.CONTAINER).hide();\n                this.getTitle().hide();\n                this.getRoot().find(SELECTORS.INPUT).removeClass('error');\n                this.getRoot().find(SELECTORS.ERROR).hide();\n                this.getRoot().find(SELECTORS.MODALBOX).animate({height: '340px'}, 200);\n                if ($(SELECTORS.JOIN_BUTTON).data('student') == false) {\n                    this.getRoot().find(SELECTORS.CONGRATSTITLE).html(strings[0]);\n                    this.getRoot().find(SELECTORS.CONGRATSTEXT).html(strings[1]);\n                } else {\n                    this.getRoot().find(SELECTORS.CONGRATSTITLE).html(strings[0]);\n                    this.getRoot().find(SELECTORS.CONGRATSTEXT).html(strings[2]);\n                }\n                this.getRoot().find(SELECTORS.RESULT).show();\n                this.getFooter().hide();\n            });\n        } else {\n            this.getRoot().find(SELECTORS.INPUT).addClass('error');\n            this.getRoot().find(SELECTORS.ERROR).html(response.error.errordes);\n            this.getRoot().find(SELECTORS.ERROR).show();\n            this.getRoot().find(SELECTORS.INPUT).show();\n        }\n    }\n\n    ajaxError(ex) {\n        Notification.exception({message: ex});\n    }\n\n    ajaxCall() {\n        const promises = Ajax.call([{\n            methodname: SERVICES.JOINGROUP,\n            args: {enrolmentkey: this.getRoot().find(SELECTORS.INPUT).val(), courseid: this.#courseid}\n        }]);\n\n        promises[0].done((response) => {\n            this.ajaxResult(response);\n        }).fail((ex) => {\n            this.ajaxError(ex);\n        });\n    }\n\n    eventMaxChar() {\n        this.getRoot().find(SELECTORS.INPUT).on('keyup', (event) => {\n            let text = $(event.currentTarget).val();\n            let length = text.length;\n            if (length > 0) {\n                const format = /[ `!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?~·]/;\n                $(SELECTORS.SAVE_BUTTON).prop('disabled', format.test(text));\n            } else {\n                $(SELECTORS.SAVE_BUTTON).prop('disabled', true);\n            }\n        });\n    }\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalJoinGroup.TYPE, ModalJoinGroup, ModalJoinGroup.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_classPrivateFieldInitSpec", "t", "a", "has", "TypeError", "_checkPrivateRedeclaration", "set", "_defineProperty", "r", "i", "Symbol", "toPrimitive", "call", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_classPrivateFieldGet", "s", "get", "_assert<PERSON>lassBrand", "_classPrivateFieldSet", "n", "arguments", "length", "_j<PERSON>y", "_notification", "_custom_interaction_events", "_modal", "_modal_registry", "_ajax", "SELECTORS", "SERVICES", "registered", "_courseid", "WeakMap", "_level", "_name", "_newcourse", "_courseurl", "ModalJoinGroup", "Modal", "constructor", "root", "super", "this", "setData", "level", "name", "courseid", "getStrings", "key", "component", "param", "done", "strings", "$", "data", "getRoot", "find", "html", "registerEventListeners", "getModal", "on", "CustomEvents", "events", "activate", "show", "getTitle", "hide", "removeClass", "val", "ajaxCall", "remove", "addClass", "window", "location", "replace", "reload", "eventMaxChar", "ajaxResult", "response", "undefined", "course", "id", "url", "attr", "animate", "height", "getFooter", "error", "errordes", "ajaxError", "ex", "Notification", "exception", "message", "Ajax", "methodname", "args", "enrolmentkey", "fail", "event", "text", "currentTarget", "format", "prop", "test", "_exports", "ModalRegistry", "register", "TYPE", "TEMPLATE"], "mappings": "+QAK6B,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,2BAAAH,EAAAI,EAAAC,IAAA,SAAAL,EAAAI,GAAA,GAAAA,EAAAE,IAAAN,GAAA,MAAA,IAAAO,UAAA,iEAAA,EAAAC,CAAAR,EAAAI,GAAAA,EAAAK,IAAAT,EAAAK,EAAA,CAAA,SAAAK,gBAAAV,EAAAW,EAAAP,GAAAO,OAAAA,EAAA,SAAAP,GAAAQ,IAAAA,EAAA,SAAAR,EAAAO,GAAAP,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAJ,IAAAA,EAAAI,EAAAS,OAAAC,yBAAAd,EAAA,CAAA,IAAAY,EAAAZ,EAAAe,KAAAX,EAAAO,kCAAAC,EAAA,OAAAA,EAAAL,MAAAA,IAAAA,4EAAAI,EAAAK,OAAAC,QAAAb,EAAA,CAAAc,CAAAd,EAAAQ,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAO,CAAAR,MAAAX,EAAAoB,OAAAC,eAAArB,EAAAW,EAAAW,CAAAA,MAAAlB,EAAAmB,YAAAC,EAAAA,cAAAC,EAAAA,cAAAzB,EAAAW,GAAAP,EAAAJ,CAAA,CAAA,SAAA0B,sBAAAC,EAAAtB,GAAAsB,OAAAA,EAAAC,IAAAC,kBAAAF,EAAAtB,GAAA,CAAA,SAAAyB,sBAAAH,EAAAtB,EAAAM,UAAAgB,EAAAlB,IAAAoB,kBAAAF,EAAAtB,GAAAM,GAAAA,CAAA,CAAA,SAAAkB,kBAAA7B,EAAAI,EAAA2B,GAAA,GAAA,mBAAA/B,EAAAA,IAAAI,EAAAJ,EAAAM,IAAAF,GAAA,OAAA4B,UAAAC,OAAA,EAAA7B,EAAA2B,EAAA,MAAA,IAAAxB,UAAA,gDAAA,iFAL7B2B,QAAAnC,uBAAAmC,SACAC,cAAApC,uBAAAoC,eACAC,2BAAArC,uBAAAqC,4BACAC,OAAAtC,uBAAAsC,QACAC,gBAAAvC,uBAAAuC,iBACAC,MAAAxC,uBAAAwC,OAGA,MAAMC,mBACQ,YADRA,sBAEW,uBAFXA,sBAGW,uBAHXA,gBAIK,aAJLA,oBAKS,kCALTA,iBAMM,yBANNA,gBAOK,SAPLA,eAQI,SARJA,mBASQ,YATRA,iBAUM,eAVNA,wBAWa,YAXbA,uBAYY,iBAZZA,iBAaM,yBAbNA,oBAcS,aAGTC,mBACS,uBAGf,IAAIC,YAAa,EAAM,IAAAC,cAAAC,QAAAC,WAAAD,QAAAE,UAAAF,QAAAG,eAAAH,QAAAI,eAAAJ,QAER,MAAMK,uBAAuBC,OAAAA,QAWxCC,WAAAA,CAAYC,MACRC,MAAMD,MAPVjD,gCAAAwC,eAAS,GACTxC,gCAAA0C,YAAM,GACN1C,gCAAA2C,WAAK,GACL3C,2BAAAmD,KAAAP,YAAa,GACb5C,2BAAAmD,KAAAN,WAAa,GAIb,CAEAO,OAAAA,CAAQC,MAAOC,KAAMC,UACjB5B,sBAAKe,OAALS,KAAcE,OACd1B,sBAAKgB,MAALQ,KAAaG,MACb3B,sBAAKa,UAALW,KAAiBI,WAEjB,EAAAC,KAAAA,aACI,CACI,CAACC,IAAK,wBAAyBC,UAAW,YAAaC,MAAOL,MAC9D,CAACG,IAAK,wBAAyBC,UAAW,YAAaC,MAAOL,QAEpEM,MAAMC,UAC4C,IAA5C,EAAAC,QAAAA,SAAEzB,uBAAuB0B,KAAK,WAC9BZ,KAAKa,UAAUC,KAAK5B,kBAAkB6B,KAAKL,QAAQ,IAEnDV,KAAKa,UAAUC,KAAK5B,kBAAkB6B,KAAKL,QAAQ,GACvD,IAGJV,KAAKa,UAAUC,KAAK5B,qBAAqB6B,KAAKb,MAAQ,KAAOC,KACjE,CAEAa,sBAAAA,GACIjB,MAAMiB,uBAAuBhB,MAE7BA,KAAKiB,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUnC,uBAAuB,KACpEc,KAAKa,UAAUC,KAAK5B,qBAAqB6B,KAAK3C,sBAAKmB,OAALS,MAAc,KAAO5B,sBAAKoB,MAALQ,MAAW,IAGlFA,KAAKiB,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUnC,gBAAgB,KAC7Dc,KAAKa,UAAUC,KAAK5B,qBAAqBoC,OACzCtB,KAAKuB,WAAWD,OAChBtB,KAAKa,UAAUC,KAAK5B,kBAAkBsC,OACtCxB,KAAKa,UAAUC,KAAK5B,iBAAiBsC,OACrCxB,KAAKa,UAAUC,KAAK5B,iBAAiBuC,YAAY,SACjDzB,KAAKa,UAAUY,YAAY,QAC3BzB,KAAKa,UAAUC,KAAK5B,iBAAiBwC,IAAI,IACzC1B,KAAKa,UAAUY,YAAY,OAAO,IAGtCzB,KAAKiB,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUnC,uBAAuB,KACpEc,KAAK2B,UAAU,IAGnB3B,KAAKiB,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUnC,kBAAkB,KAC/Dc,KAAKa,UAAUY,YAAY,SAC3B,EAAAd,iBAAE,qBAAqBiB,UACvB,EAAAjB,QAAC/D,SAAC,mBAAmB6E,YAAY,QAAQI,SAAS,QAC9CzD,sBAAKqB,WAALO,MACA8B,OAAOC,SAASC,QAAQ5D,sBAAKsB,WAALM,OAExB+B,SAASE,QACb,IAGJjC,KAAKkC,cACT,CAEAC,UAAAA,CAAWC,UACP,QAAwBC,IAApBD,SAASE,OAAsB,CAC/B,IAAInC,KAAOiC,SAASE,OAAOnC,KACqB,IAA5C,EAAAQ,QAAAA,SAAEzB,uBAAuB0B,KAAK,aAC9BpC,sBAAKiB,WAALO,KAAkBoC,SAASE,OAAOC,IAClC/D,sBAAKkB,WAALM,KAAkBoC,SAASE,OAAOE,OAGtC,EAAAnC,KAAAA,aAAW,CACP,CAACC,IAAK,oBAAqBC,UAAW,YAAaC,MAAOL,MAC1D,CAACG,IAAK,0BAA2BC,UAAW,YAAaC,MAAOL,MAChE,CAACG,IAAK,0BAA2BC,UAAW,YAAaC,MAAOL,QACjEM,MAAMC,WACL,EAAAC,QAAC/D,SAACsC,gBAAgBuD,KAAK,cAAe,UACtCzC,KAAKa,UAAUC,KAAK5B,oBAAoBsC,OACxCxB,KAAKa,UAAUC,KAAK5B,qBAAqBsC,OACzCxB,KAAKuB,WAAWC,OAChBxB,KAAKa,UAAUC,KAAK5B,iBAAiBuC,YAAY,SACjDzB,KAAKa,UAAUC,KAAK5B,iBAAiBsC,OACrCxB,KAAKa,UAAUC,KAAK5B,oBAAoBwD,QAAQ,CAACC,OAAQ,SAAU,KACnB,IAA5C,EAAAhC,QAAAA,SAAEzB,uBAAuB0B,KAAK,YAC9BZ,KAAKa,UAAUC,KAAK5B,yBAAyB6B,KAAKL,QAAQ,IAC1DV,KAAKa,UAAUC,KAAK5B,wBAAwB6B,KAAKL,QAAQ,MAEzDV,KAAKa,UAAUC,KAAK5B,yBAAyB6B,KAAKL,QAAQ,IAC1DV,KAAKa,UAAUC,KAAK5B,wBAAwB6B,KAAKL,QAAQ,KAE7DV,KAAKa,UAAUC,KAAK5B,kBAAkBoC,OACtCtB,KAAK4C,YAAYpB,MAAM,GAE/B,MACIxB,KAAKa,UAAUC,KAAK5B,iBAAiB2C,SAAS,SAC9C7B,KAAKa,UAAUC,KAAK5B,iBAAiB6B,KAAKqB,SAASS,MAAMC,UACzD9C,KAAKa,UAAUC,KAAK5B,iBAAiBoC,OACrCtB,KAAKa,UAAUC,KAAK5B,iBAAiBoC,MAE7C,CAEAyB,SAAAA,CAAUC,IACNC,cAAYrG,QAACsG,UAAU,CAACC,QAASH,IACrC,CAEArB,QAAAA,GACqByB,MAAAA,QAAK3F,KAAK,CAAC,CACxB4F,WAAYlE,mBACZmE,KAAM,CAACC,aAAcvD,KAAKa,UAAUC,KAAK5B,iBAAiBwC,MAAOtB,SAAUhC,sBAAKiB,UAALW,UAGtE,GAAGS,MAAM2B,WACdpC,KAAKmC,WAAWC,SAAS,IAC1BoB,MAAMR,KACLhD,KAAK+C,UAAUC,GAAG,GAE1B,CAEAd,YAAAA,GACIlC,KAAKa,UAAUC,KAAK5B,iBAAiBgC,GAAG,SAAUuC,QAC9C,IAAIC,MAAO,EAAA/C,QAAAA,SAAE8C,MAAME,eAAejC,MAElC,GADagC,KAAK/E,OACL,EAAG,CACZ,MAAMiF,OAAS,6CACf,EAAAjD,iBAAEzB,uBAAuB2E,KAAK,WAAYD,OAAOE,KAAKJ,MAC1D,MACI,EAAA/C,QAAC/D,SAACsC,uBAAuB2E,KAAK,YAAY,EAC9C,GAER,EAMH,OALAE,SAAAnH,QAAA+C,eAAAvC,gBA3IoBuC,eAAc,OAEjB,8BAA4BvC,gBAFzBuC,eAAc,WAGb,8CA0IjBP,aACD4E,gBAAAA,QAAcC,SAAStE,eAAeuE,KAAMvE,eAAgBA,eAAewE,UAC3E/E,YAAa,GAChB2E,SAAAnH,OAAA"}
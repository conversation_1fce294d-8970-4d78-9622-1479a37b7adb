{{!
    @template theme_webbook/header

    Header

    Context variables required for this template:

    * output_user_menu | output.user_menu : HTML strinfied that represents the header
    * params:
          homeurl: site home URL


    Example context (json):
    {
        output_user_menu | output.user_menu : "a example HTML stringfied"
        params {
          'homeurl': 'http://evasm/my'
        }
    }

}}
{{#smheader}}
<header class="header__wrapper header_common educamosheader" id="nd_header_wrapper">
    <div class="header">
        <img
                class="icon"
                src="{{ logourl }}"
                alt="Logo SM"
                tabindex="0">
        {{!
        user_menu
        content for Savia old pages
        }}
        {{# output_user_menu }}
            {{# notinbookpage }}
                {{{ output_user_menu }}}
            {{/ notinbookpage }}
            {{^ notinbookpage }}
                {{> local_header_common/educamosusermenu }}
            {{/ notinbookpage }}
        {{/ output_user_menu }}

        {{! content for Savia new pages  }}
        {{^ output_user_menu }}
            {{> local_header_common/educamosusermenu}}

        {{/ output_user_menu }}
    </div>
</header>
{{> theme_npe/megamenu }}
<div id="npe-helpdock">
</div>
{{/smheader}}
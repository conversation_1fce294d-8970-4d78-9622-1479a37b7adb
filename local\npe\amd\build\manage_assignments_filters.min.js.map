{"version": 3, "file": "manage_assignments_filters.min.js", "sources": ["../src/manage_assignments_filters.js"], "sourcesContent": ["/* eslint-disable complexity */\ndefine(['jquery', 'jqueryui', 'local_npe/datepicker', 'local_npe/event_listener'],\n    function ($) {\n\n        var filtersArray = {};\n        let SELECTORS = {\n            ROOT: '.myassignments',\n            HEADERMENU: '.header-menu',\n            HEADERBAR: '#nd_header_wrapper',\n            MEGAMENUMOBILE: '.button-mobile-megamenu',\n            SEARCH: '#search',\n            CLEAN: '.clean',\n            CLEANALL: '.clean-filters',\n            DELETEFILTERS: '.delete-filters',\n            FILTER: '.assignments-filter',\n            FILTERCONTAINER: '.assignments-filter-container',\n            FILTERCONTAINERITEM: '.assignments-filter-container .assignments-filter-selected',\n            FILTERCLOSE: '#filterButton .close',\n            OPACITYLAYER: '.assignments-filter-opacity-layer',\n            CARD: '.card-header h5',\n            FILTERCHECKBOX: '.custom_checkbox input',\n            FILTERZONE: '.filters-zone',\n            FILTERZONEITEM: '.filters-zone .assignments-filter-selected',\n            REMOVEFILTER: '.clean-filter-selected',\n            ACTIVITYLIST: '.activities-list',\n            SHOWMOREITEMS: '.showmoreitems',\n            SHOWLESSITEMS: '.showlessitems',\n            FILTERTITLE: '.filter-title',\n            ACTIVITIESCONTAINER: '.activities-container.ac4filters',\n            SIDEMENUITEM: '.sidebar-menu a',\n            SUBCATEGORYINPUT: '.cc-subcat input',\n            SUBCATEGORY: '.subcat',\n            SUBCATEGORYRESTORE: '.cc-subcat i'\n        };\n\n        /**\n         * @param {String} filter_topics\n         * @param {String} filter_categories\n         */\n        function ManageAssignmentsFilter(filter_topics, filter_categories) {\n            ManageAssignmentsFilter.prototype.constructor = ManageAssignmentsFilter;\n            ManageAssignmentsFilter.prototype.root = null;\n            ManageAssignmentsFilter.prototype.filter_topics = filter_topics;\n            ManageAssignmentsFilter.prototype.filter_categories = filter_categories;\n\n            this.init();\n        }\n\n        ManageAssignmentsFilter.prototype.init = function () {\n            var that = this;\n            this.root = $(SELECTORS.ROOT);\n            this.searchParams = new URLSearchParams(window.location.search);\n            this.sectionSelected = that.defaultSection();\n            this.filtersArray = that.prepareFilters();\n            this.activitiesCounter = 0;\n            this.mainFilter = [];\n            this.filterController = [];\n            this.filterControllerClicks = {};\n            this.sesskeyCourseid = '';\n            this.sessionCheck();\n            this.categoryDropValue = '';\n            this.categoryDropValueReload = '';\n            this.subCatAdded = false;\n            this.subCatDelete = false;\n            that.subCatDeleted = false;\n\n            that.resetControlller = false;\n            that.toggleDropdownFilter();\n            that.chooseFilters();\n            that.checkFilter();\n            that.openFilters();\n            that.changeSection();\n            this.subFilterManagement();\n\n            this.filtersStorage = localStorage.getItem(this.sesskeyCourseid) ?? '{}';\n\n            if (this.filtersStorage !== null && this.filtersStorage.length !== 0 && this.filtersStorage !== '{}') {\n                that.filterControllerClicks = JSON.parse(this.filtersStorage);\n            }\n\n            // Eliminar filtro de Categoría.\n            $('.filtercategory .clean-filter-selected').click(function () {\n                var categoryid = $(this).data('categoryid');\n                let index = that.filter_categories.indexOf(categoryid);\n                if (index > -1) {\n                    that.filter_categories.splice(index, 1);\n                }\n                $(this).parent().remove();\n                that.filters();\n            });\n\n            // Eliminar filtro de Unidad/Tema.\n            $('.filtertopic .clean-filter-selected').click(function () {\n                let topicid = $(this).data('topicid');\n                let index = that.filter_topics.indexOf(topicid);\n                if (index > -1) {\n                    that.filter_topics.splice(index, 1);\n                }\n                $(this).parent().remove();\n                that.filters();\n            });\n\n            // Eliminar filtro de actividades para asignar.\n            $('.filtertoassign .clean-filter-selected').click(function () {\n                that.filter_toassign = 0;\n                $(this).parent().remove();\n                that.filters();\n            });\n\n            window.onload = () => {\n                if (!that.getFiltersFromURL()) {\n                    that.cleanAllFilters();\n                    that.applyFiltersSaved();\n                }\n            };\n\n            that.manageActivities();\n        };\n\n        ManageAssignmentsFilter.prototype.sessionCheck = function () {\n            let urlSesskey = $('#logout_link').attr('href');\n            let sesskey = urlSesskey.substring(urlSesskey.lastIndexOf('=') + 1);\n            let courseid = this.searchParams.get('courseid');\n            this.sesskeyCourseid = sesskey + '-' + courseid;\n            // Si el usuario esta en una nueva sesion o es un usuario diferente, borramos los datos anteriores\n            if (null === localStorage.getItem(this.sesskeyCourseid)) {\n                localStorage.clear();\n            }\n        };\n\n        ManageAssignmentsFilter.prototype.filters = function () {\n            var that = this;\n\n            // Modificación: Limitar el alcance del filtro a la sección actual\n            $('.' + that.sectionSelected).find('.activity-item').each(function () {\n                let topicid = $(this).data('sequenceid');\n                let categoryid = $(this).data('categoryid');\n                let toassign = $(this).data('toassign');\n\n                // Extracción de la información.\n                let bytype = $(this).data('type');\n                let bydelivery = $(this).data('recommendedgroupingid');\n                let bydificulty = $(this).data('difficultylevelid');\n                let bykeyevidence = $(this).data('keyevidenceid');\n                let byblock = $(this).data('blockid');\n                let bycompetence = $(this).data('competenceid');\n                let bycriterion = $(this).data('criterionid');\n                let bytheme = $(this).data('themeid');\n                let bytransversekey = $(this).data('transversekeyid');\n                let byskills = $(this).data('skillsid');\n                let bypedagogicalpurposes = $(this).data('pedagogicalpurposesid');\n                let byassessment = $(this).data('assessmentid');\n                let bylearninglevel = $(this).data('learninglevelid');\n                let bytrackingactivitiesid = $(this).data('trackingactivitiesid');\n                let bychallengesid = $(this).data('challengesid');\n                let byincontextid = $(this).data('incontextid');\n                let bytypeofactivityid = $(this).data('typeofactivityid');\n                let bypresentationresourcesid = $(this).data('presentationresourcesid');\n                let bysubcategoryid = $(this).data('subcategoryid');\n\n\n                // Control.\n                let hidebytype = false;\n                let hidebydelivery = false;\n                let hidebysequence = false; // Añadir la extracción para hacerlo funcionar.\n                let hidebydificulty = false;\n                let hidebykeyevidence = false;\n                let hidebycompetence = false;\n                let hidebycriterion = false;\n                let hidebytheme = false;\n                let hidebytransversekey = false;\n                let hidebyskills = false;\n                let hidebypedagogicalpurposes = false;\n                let hidebyassessment = false;\n                let hidebylearninglevel = false;\n                let hidebyblock = false;\n                let hidebytoassign = (that.filter_toassign && !toassign);\n                let hidebytrackingactivitiesid = false;\n                let hidebychallengesid = false;\n                let hidebyincontextid = false;\n                let hidetypeofactivityid = false;\n                let hidepresentationresourcesid = false;\n                let hidesubcategorysid = false;\n\n                let hidebytopic = ((that.filter_topics.length > 0 && that.filter_topics.indexOf(topicid) === -1) ||\n                    (that.filtersArray['sequenceId'] !== undefined\n                        && that.filtersArray['sequenceId'].length > 0\n                        && that.filtersArray['sequenceId'].indexOf(topicid) === -1));\n                let hidebycategory = (that.filter_categories.length > 0 && that.filter_categories.indexOf(categoryid) === -1)\n                    || (that.filtersArray['categoryId'] !== undefined\n                        && that.filtersArray['categoryId'].length > 0\n                        && that.filtersArray['categoryId'].indexOf(categoryid) === -1);\n\n                if (that.filtersArray.hasOwnProperty('activityTypeId')) {\n                    hidebytype = (that.filtersArray['activityTypeId'].length > 0\n                        && that.filtersArray['activityTypeId'].indexOf(bytype) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('difficultyLevelId')) {\n                    hidebydificulty = (that.filtersArray['difficultyLevelId'].length > 0\n                        && that.filtersArray['difficultyLevelId'].indexOf(bydificulty) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('recommendedGroupingId')) {\n                    hidebydelivery = (that.filtersArray['recommendedGroupingId'].length > 0\n                        && that.filtersArray['recommendedGroupingId'].indexOf(bydelivery) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('keyEvidenceId')) {\n                    hidebykeyevidence = (that.filtersArray['keyEvidenceId'].length > 0\n                        && that.filtersArray['keyEvidenceId'].indexOf(bykeyevidence) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('blockId')) {\n                    hidebyblock = (that.filtersArray['blockId'].length > 0\n                        && that.filtersArray['blockId'].indexOf(byblock) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('competenceId')) {\n                    hidebycompetence = (that.filtersArray['competenceId'].length > 0\n                        && that.filtersArray['competenceId'].indexOf(bycompetence) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('criterionId')) {\n                    hidebycriterion = (that.filtersArray['criterionId'].length > 0\n                        && that.filtersArray['criterionId'].indexOf(bycriterion) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('themeId')) {\n                    hidebytheme = (that.filtersArray['themeId'].length > 0\n                        && that.filtersArray['themeId'].indexOf(bytheme) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('transverseKeyId')) {\n                    hidebytransversekey = (that.filtersArray['transverseKeyId'].length > 0\n                        && that.filtersArray['transverseKeyId'].indexOf(bytransversekey) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('skillsId')) {\n                    hidebyskills = (that.filtersArray['skillsId'].length > 0\n                        && that.filtersArray['skillsId'].indexOf(byskills) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('pedagogicalPurposesId')) {\n                    hidebypedagogicalpurposes = (that.filtersArray['pedagogicalPurposesId'].length > 0\n                        && that.filtersArray['pedagogicalPurposesId'].indexOf(bypedagogicalpurposes) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('assessmentId')) {\n                    hidebyassessment = (that.filtersArray['assessmentId'].length > 0\n                        && that.filtersArray['assessmentId'].indexOf(byassessment) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('learningLevelId')) {\n                    hidebylearninglevel = (that.filtersArray['learningLevelId'].length > 0\n                        && that.filtersArray['learningLevelId'].indexOf(bylearninglevel) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('trackingActivitiesId')) {\n                    hidebytrackingactivitiesid = (that.filtersArray['trackingActivitiesId'].length > 0\n                        && that.filtersArray['trackingActivitiesId'].indexOf(bytrackingactivitiesid) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('challengesId')) {\n                    hidebychallengesid = (that.filtersArray['challengesId'].length > 0\n                        && that.filtersArray['challengesId'].indexOf(bychallengesid) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('inContextId')) {\n                    hidebyincontextid = (that.filtersArray['inContextId'].length > 0\n                        && that.filtersArray['inContextId'].indexOf(byincontextid) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('typeOfActivityId')) {\n                    hidetypeofactivityid = (that.filtersArray['typeOfActivityId'].length > 0\n                        && that.filtersArray['typeOfActivityId'].indexOf(bytypeofactivityid) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('presentationResourcesId')) {\n                    hidepresentationresourcesid = (that.filtersArray['presentationResourcesId'].length > 0\n                        && that.filtersArray['presentationResourcesId'].indexOf(bypresentationresourcesid) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('subcategoryId')) {\n                    hidesubcategorysid = (that.filtersArray['subcategoryId'].length > 0\n                        && that.filtersArray['subcategoryId'].indexOf(bysubcategoryid) === -1);\n                }\n\n                if (hidebytopic || hidebycategory || hidebytoassign || hidebyblock || hidebycompetence || hidebycriterion ||\n                    hidebytheme || hidebytransversekey || hidebytype || hidebydelivery || hidebysequence || hidebydificulty ||\n                    hidebykeyevidence || hidebyskills || hidebypedagogicalpurposes || hidebyassessment || hidebylearninglevel ||\n                    hidebytrackingactivitiesid || hidebychallengesid || hidebyincontextid || hidetypeofactivityid ||\n                    hidepresentationresourcesid || hidesubcategorysid) {\n                    $(this).addClass('hidden');\n                    $(this).hide();\n                } else {\n                    $(this).removeClass('hidden');\n                    $(this).show();\n                }\n            });\n            $('#search').keyup();\n        };\n\n        ManageAssignmentsFilter.prototype.applyColorButton = function (data, add) {\n            var dataParent = $(data).closest('.dropdown');\n            if (add) {\n                $(dataParent).addClass('selected');\n                $(dataParent).find('.dropbtn').addClass('bgf-c');\n            } else {\n                $(dataParent).removeClass('selected');\n                $(dataParent).find('.dropbtn').removeClass('bgf-c');\n            }\n        };\n\n        ManageAssignmentsFilter.prototype.toggleDropdownFilter = function () {\n            var openId = '';\n            var that = this;\n            $('.dropbtn i').on('click', function (e) {\n                if ($(this).parent().hasClass('bgf-c')) {\n                    $(\"ul[data-id =\" + $(this).parent().attr('id') + \"] \" + SELECTORS.FILTERCHECKBOX + ':checked')\n                        .each(function () {\n                            // Eliminamos los filtros para esa categoria.\n                            var id = $(this).attr('id');\n                            var filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid));\n                            var transitionalVars = filtersFromStorage[that.sectionSelected];\n                            transitionalVars = $.grep(transitionalVars, function (value) {\n                                return value !== id;\n                            });\n                            filtersFromStorage[that.sectionSelected] = transitionalVars;\n                            localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, filtersFromStorage)));\n                            $(this).click();\n                            $(\"[data-id =\" + $(this).attr('id') + \"]\").parent().remove();\n                        });\n                    $(this).parent().removeClass('bgf-c');\n                    e.preventDefault();\n                    return; // Do nothing\n                }\n            });\n\n            $('.dropbtn').on('click', function (e) {\n                $(this).toggleClass('open');\n                var id = $(this).attr('id');\n\n                if (openId && openId !== $(this).attr('id')) {\n                    $(\"ul[data-id =\" + openId + \"]\").hide();\n                    $('#' + openId).removeClass('open');\n                }\n                openId = id;\n                $(\"ul[data-id =\" + id + \"]\").toggle();\n                e.stopPropagation();\n            });\n        };\n\n        ManageAssignmentsFilter.prototype.checkFilter = function () {\n            var that = this;\n\n            if (that.filtersStorage) {\n                that.filterControllerClicks[that.sectionSelected] = JSON.parse(that.filtersStorage);\n            }\n\n            $(SELECTORS.FILTERCHECKBOX).on('click', function () {\n                var id = $(this).data('id');\n                var uniquekey = $(this).attr('id');\n                var uniquekeySub = '';\n                var filtertype = $(this).data('filtertype');\n                var label = $(this).data('label');\n                var filtered = true;\n                var checked = false;\n                var idselected = '#' + $(this).attr('id') + '-label';\n                var idselectedsub = '#' + $(this).attr('id') + '-sub';\n                var filterSelector = that.sectionSelected + '-' + filtertype;\n\n                that.applyColorButton(idselected, true);\n\n                if ($(this).prop('checked')) {\n                    that.filtersArray[filtertype].push(id);\n                    label = $(this).data('label');\n\n                    if (that.categoryDropValueReload && filtertype === 'subcategoryId') {\n                        uniquekeySub = $('#' + that.categoryDropValueReload).closest('li').data('filtersuddrop');\n                    }\n\n                    // No añadimos el tab de subcategorias y tampoco si ya existe\n                    if (filtertype !== 'subcategoryId' &&\n                        $('.assignments-filter-container').find(`[data-id='${uniquekey}']`).length === 0\n                    ) {\n                        $(SELECTORS.FILTERCONTAINER).prepend(\n                            '<div class=\"filtercategory assignments-filter-selected border-c\" data-checkid=\"' + id + '\">' +\n                            '<div class=\"filter-text fc-c\">' + label + '</div>' +\n                            '<div class=\"clean-filter-selected bg-c\" data-label=\"' + label + '\" data-category=\"' + id + '\" ' +\n                            'data-id=\"' + uniquekey + '\" ' + 'data-idSub=\"' + uniquekeySub + '\" ' + 'data-filtertype=\"' +\n                            filtertype + '\" ' + 'data-filterselector=\"' + filterSelector + '\"></div>' + '</div>'\n                        );\n                    }\n\n                    $(SELECTORS.DELETEFILTERS).addClass('show');\n                    checked = true;\n\n                    // Rellenamos los clicks\n                    // Solo aplicamos en este punto el tag si no es una subacategoria\n                    if (!that.subCatAdded) {\n                        if (that.filterControllerClicks !== null\n                            && that.filterControllerClicks[that.sectionSelected] === undefined) {\n                            that.filterControllerClicks[that.sectionSelected] = [];\n                        }\n                        if (that.filterControllerClicks !== null &&\n                            $.inArray($(this).attr('id'), that.filterControllerClicks[that.sectionSelected]) === -1) {\n                            that.filterControllerClicks[that.sectionSelected].push($(this).attr('id'));\n                            localStorage.setItem(that.sesskeyCourseid,\n                                JSON.stringify(Object.assign({}, that.filterControllerClicks)));\n                        }\n                    }\n                    // Resetamos la variable con control de subfiltro para que siga el flujo normal\n                    that.subCatAdded = false;\n\n                    $('#' + $(this).attr('id') + '-label').addClass('selected');\n                    $(idselectedsub).prop('checked', true);\n                } else {\n                    let removeItem = $(this).attr('id');\n                    if (that.subCatDelete) {\n                        removeItem = that.categoryDropValueReload ?? that.categoryDropValue + '-subcategoryId-' + id + '-sub';\n                        that.subCatDelete = false;\n                    }\n                    that.filterControllerClicks[that.sectionSelected] = $.grep(\n                        that.filterControllerClicks[that.sectionSelected]\n                        , function (value) {\n                            return value !== removeItem;\n                        });\n                    let key = that.filtersArray[filtertype].indexOf(id);\n                    that.filtersArray[filtertype].splice(key, 1);\n                    $(SELECTORS.FILTERZONE).find(`[data-checkid='${id}']`).remove();\n                    $(SELECTORS.FILTERCONTAINER).find(`[data-checkid='${id}']`).remove();\n\n                    if ($(SELECTORS.FILTERCONTAINERITEM).length < 1) {\n                        $(SELECTORS.DELETEFILTERS).removeClass('show');\n                        filtered = false;\n                        filtertype = null;\n                        that.applyColorButton(idselected, false);\n                    }\n\n                    // Guardamos los nuevos datos en el storage\n                    var idAlt = $(this).data('filter') === 'subcategoryId' ? that.categoryDropValueReload : $(this).attr('id');\n\n                    var filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid));\n                    var transitionalVars = filtersFromStorage[that.sectionSelected];\n                    transitionalVars = $.grep(transitionalVars, function (value) {\n                        return value !== idAlt;\n                    });\n                    filtersFromStorage[that.sectionSelected] = transitionalVars;\n\n                    that.mainFilter[that.sectionSelected] = '';\n                    localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, filtersFromStorage)));\n\n                    // Cambiamos el mainFilter en caso de que sea necesario\n                    if (that.filterControllerClicks !== undefined\n                        && that.filterControllerClicks[that.sectionSelected] !== undefined\n                        && that.filterControllerClicks[that.sectionSelected][0] !== undefined) {\n                        var nextMainFilter = that.filterControllerClicks[that.sectionSelected][0].split('-', 2).join('-');\n                        that.mainFilter[that.sectionSelected] = nextMainFilter;\n                    }\n                    if ($(this).data('filter') === 'categoryId'\n                        && $('.subcat[data-category~=\"' + $(this).attr('id') +'\"]').siblings('input').next().hasClass('selected')) {\n                            that.cleanAllFilters(true);\n                            $('.dropbtn').removeClass('bgf-c');\n                            $('.subcat').removeClass('selected bgf-c');\n                            $('#search').click();\n                    }\n\n                    $(idselected).toggleClass('selected');\n                }\n\n                if (that.filterControllerClicks[that.sectionSelected] === '') {\n                    that.mainFilter[that.sectionSelected] = '';\n                }\n\n                $('.subcat').show();\n                that.filters();\n                that.manageActivities(filtered, filtertype, checked);\n            });\n\n            $(document).on('click', SELECTORS.REMOVEFILTER, function () {\n                if ($(this).data('filtertype') === 'categoryId'\n                    && $(SELECTORS.FILTERCONTAINER).find(`[data-filtertype='subcategoryId']`).length > 0) {\n                    var restore = $(this).data('id');\n                    $(\".restore[data-categoryid-restore='\" + restore + \"']\").click();\n                    return;\n                }\n\n\n                // Al eliminar un filtro, recuperamos datos de Storage y los modificamos con la nueva selección.\n                var id = $(this).data('id');\n                var idSub = $(this).data('idsub');\n                var filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid));\n                var transitionalVars = filtersFromStorage[that.sectionSelected];\n                transitionalVars = $.grep(transitionalVars, function (value) {\n                    return value !== id;\n                });\n                filtersFromStorage[that.sectionSelected] = transitionalVars;\n                localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, filtersFromStorage)));\n\n                if (idSub) {\n                    that.subCatDelete = true;\n                    // Desmarcamos los checks\n                    $('#' + idSub + '-sub').click();\n                } else {\n                    // Desmarcamos los checks\n                    $('#' + id).click();\n                }\n\n                if ($(SELECTORS.FILTERCONTAINERITEM).length < 1) {\n                    $(SELECTORS.DELETEFILTERS).removeClass('show');\n                }\n\n                // Comprobamos si ese tipo de filtro está vacio\n                var filterSelector = 'heading-' + $(this).data('filterselector');\n                var filtercounter = $('#' + $(this).data('filterselector')).find('.custom_checkbox span.selected').length;\n                if (filtercounter < 1) {\n                    $('#' + filterSelector).removeClass('bgf-c');\n                }\n            });\n\n            $(SELECTORS.CLEANALL).on('click', function () {\n                that.cleanAllFilters(true);\n                $('.dropbtn').removeClass('bgf-c');\n                $('.subcat').removeClass('selected bgf-c');\n                $('#search').click();\n            });\n        };\n\n        ManageAssignmentsFilter.prototype.cleanAllFilters = function (cleanClicks = false) {\n            var that = this;\n            $(SELECTORS.SEARCH).val('');\n            $(SELECTORS.FILTERCHECKBOX).each(function () {\n                $(this).prop('checked', false);\n                var idselected = '#' + $(this).attr('id') + '-label';\n                $(idselected).removeClass('selected');\n            });\n\n            $(SELECTORS.FILTERCONTAINERITEM).each(function () {\n                $(this).remove();\n            });\n\n            $(SELECTORS.FILTERZONEITEM).each(function () {\n                $(this).remove();\n            });\n\n            $(SELECTORS.DELETEFILTERS).removeClass('show');\n            that.setToDefault();\n            that.manageActivities();\n\n            if (cleanClicks) {\n                that.mainFilter[that.sectionSelected] = '';\n                // Al eliminar todos los filtros, recuperamos datos de Storage y los vaciamos.\n                that.cleanStorageForCurrentSection();\n                that.filterControllerClicks[that.sectionSelected] = [];\n                $('.activities-subcats').show();\n                $('.subcat').show();\n                $('.collapse-units-dropdown .topic-item').show();\n                $('.subcats-dropdown .list-header').removeClass('bg-d');\n                $('#categories-' + that.sectionSelected).find('li.bgf-c.active').removeClass('bgf-c active');\n            }\n        };\n\n        ManageAssignmentsFilter.prototype.cleanStorageForCurrentSection = function () {\n            var that = this;\n            var filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid)) ?? [];\n            filtersFromStorage[that.sectionSelected] = [];\n            that.filterControllerClicks[that.sectionSelected] = [];\n            localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, that.filterControllerClicks)));\n        };\n\n        ManageAssignmentsFilter.prototype.openFilters = function () {\n            $(SELECTORS.FILTER).on('click', function () {\n                $(SELECTORS.OPACITYLAYER).toggle();\n                $(SELECTORS.HEADERMENU).addClass('z0');\n                $(SELECTORS.HEADERBAR).addClass('z0');\n                $(SELECTORS.MEGAMENUMOBILE).addClass('z0');\n            });\n            $(SELECTORS.CARD).on('click', function () {\n                $(this).find('.rotate').toggleClass(\"down\");\n                $(this).toggleClass('bold');\n            });\n            $(SELECTORS.FILTERCLOSE).on('click', function () {\n                $(SELECTORS.OPACITYLAYER).toggle();\n                $(SELECTORS.HEADERMENU).removeClass('z0');\n                $(SELECTORS.HEADERBAR).removeClass('z0');\n                $(SELECTORS.MEGAMENUMOBILE).removeClass('z0');\n            });\n            $('body').click(function (event) {\n                if ($(event.target).attr('id') === 'closeSubcats' || $(event.target).hasClass('results')) {\n                    $('.subcats-dropdown i.rotate').toggleClass('down');\n                    $('.collapse-units-dropdown').removeClass('show');\n                }\n\n                if ($('.collapse-units-dropdown').is(':visible')) {\n                    var container = Array.from(document.querySelectorAll('.subcatsdropdown')).filter(s =>\n                        window.getComputedStyle(s).getPropertyValue('display') != 'none');\n                    container = $(container).find('.subcats-dropdown')[0];\n\n                    var containerSub = document.querySelectorAll('.collapse-units-dropdown.collapse.show .list-body')[0];\n                    document.addEventListener('click', function (event) {\n                        if (container\n                            && container !== event.target\n                            && !container.contains(event.target)\n                            && $(containerSub).is(':visible')) {\n                            $('.subcats-dropdown i.rotate').toggleClass('down');\n                            $('.collapse-units-dropdown').removeClass('show');\n                        }\n                    });\n                }\n            });\n        };\n\n        ManageAssignmentsFilter.prototype.setToDefault = function () {\n            var that = this;\n            $('.activity-item').each(function () {\n                $(this).show();\n                $(this).removeClass('hidden').removeClass('filtered');\n            });\n            $(SELECTORS.SEARCH).val('');\n            that.prepareFilters();\n            var isVisible = 0;\n            $('.activity-item').each(function () {\n                if ($(this).is(\":visible\")) {\n                    isVisible++;\n                }\n            });\n            if (isVisible === 0 && !($('.assignments-filter-selected').length > 0)) {\n                $('.noresultssearch').hide();\n                $('.noresultsfilter').hide();\n                $('.noresultsassignments').show();\n            } else if (isVisible === 0 && ($('.assignments-filter-selected').length > 0)) {\n                $('.noresultssearch').hide();\n                $('.noresultsfilter').show();\n                $('.noresultsassignments').hide();\n            } else if (isVisible !== 0) {\n                $('.noresultsassignments').hide();\n                $('.noresultsfilter').hide();\n                $('.noresultssearch').hide();\n            }\n            that.prepareFilters();\n        };\n\n        ManageAssignmentsFilter.prototype.prepareFilters = function () {\n            $(SELECTORS.FILTERTITLE).each(function () {\n                var filter = $(this).find('.custom_checkbox input');\n                filter.each(function () {\n                    filtersArray[$(this).data('filtertype')] = [];\n                });\n            });\n            return filtersArray;\n        };\n\n        ManageAssignmentsFilter.prototype.chooseFilters = function () {\n            var that = this;\n            let section;\n\n            // Initial filters.\n            if ($('.sidebar-menu').is(\":hidden\")) {\n                let searchParams = that.searchParams;\n                section = searchParams.get('section');\n            } else {\n                section = $('.icon_menu.section.active a').data('section');\n            }\n\n            let visibleFilterbutton = false;\n            $('.modal-filter').each(function () {\n                if ($(this).data('section') === section) {\n                    $(this).removeClass('hidden');\n                    if ($(this).children().hasClass('dropdown')) {\n                        visibleFilterbutton = true;\n                    }\n                }\n            });\n            if (visibleFilterbutton == false) {\n                $(SELECTORS.FILTER).hide();\n            }\n\n            // Change filters when section has changed.\n            $('.icon_menu.section').on('click', function () {\n                // Removed filters applied.\n                that.cleanAllFilters(false);\n                var newSection = $(this).find('a').data('section');\n                $('.modal-filter').each(function () {\n                    if ($(this).data('section') === newSection) {\n                        $(this).removeClass('hidden');\n                    } else if (!$(this).hasClass('hidden')) {\n                        $(this).addClass('hidden');\n                    }\n                });\n            });\n\n        };\n\n        ManageAssignmentsFilter.prototype.getFiltersFromURL = function () {\n            var that = this;\n            let urlParams = that.searchParams;\n            let section = urlParams.get('section');\n            let sequenceId = urlParams.get('topicid');\n            let filters = JSON.parse($('#filterbyitem').val());\n\n            if (sequenceId !== null || filters.length > 0) {\n                that.cleanStorageForCurrentSection();\n            } else {\n                return false;\n            }\n            if (sequenceId !== null) {\n                let filterSeq = section + '-sequenceId-' + sequenceId;\n                that.filterControllerClicks[that.sectionSelected].push(filterSeq);\n            }\n\n            if (filters.length > 0) {\n                $(filters).each(function () {\n                    let filter = section + '-' + this.key + '-' + this.value;\n                    that.filterControllerClicks[that.sectionSelected].push(filter);\n                });\n            }\n\n            // Save filters to storage\n            localStorage.setItem(that.sesskeyCourseid,\n                JSON.stringify(Object.assign({}, that.filterControllerClicks)));\n        };\n\n        ManageAssignmentsFilter.prototype.defaultSection = function () {\n            var that = this;\n            var section;\n            if (!this.sectionSelected) {\n                let urlParams = that.searchParams;\n                section = urlParams.get('section') ?? 'actAndRec';\n            }\n            return section;\n        };\n\n        ManageAssignmentsFilter.prototype.changeSection = function () {\n            var that = this;\n            $(SELECTORS.SIDEMENUITEM).on('click', function () {\n                that.sectionSelected = $(this).data('section');\n                that.resetControlller = false;\n                that.filterControllerClicks = that.filterControllerClicks ?? '{}';\n\n                if (that.filterControllerClicks[that.sectionSelected] !== undefined) {\n                    that.applyFiltersSaved();\n                }\n                that.filterControllerClicks[that.sectionSelected] = [];\n                if ($.isEmptyObject(that.filtersStorage)) {\n                    localStorage.setItem(that.sesskeyCourseid,\n                        JSON.stringify(Object.assign({}, that.filterControllerClicks[that.sectionSelected])));\n                }\n            });\n        };\n\n        ManageAssignmentsFilter.prototype.applyFiltersSaved = function () {\n            var that = this;\n            $(document).ready(function () {\n                if (that.filterControllerClicks !== null) {\n                    $('.subcat').removeClass('selected bgf-c');\n                    $('.dropbtn').removeClass('bgf-c');\n                    $.each(that.filterControllerClicks[that.sectionSelected], function (key, value) {\n                        $('#' + value).prop('checked', false).click();\n                        // Con id, buscamos el data-filter y lo pulsamos para aplicar las clases en la subcategoría\n                        $('.cc-subcat [data-filter~=\"' + value + '\"]').prop('checked', true).next().addClass('selected bgf-c');\n                    });\n                }\n            });\n        };\n\n        ManageAssignmentsFilter.prototype.manageActivities = function (\n            filtered = false,\n            filterType = null,\n            checkedUpper = null) {\n            var that = this;\n            var activities = $('.activities-list[data-section=\"' + this.sectionSelected + '\"] .activity-item');\n            var checked = checkedUpper;\n            that.filterController[that.sectionSelected] = [];\n\n            // Calculamos las actividades visibles;\n            if (!filtered) {\n                that.activitiesCounter = $(activities).length;\n            } else {\n                that.activitiesCounter = $(activities).filter(\":visible\").length;\n                activities = $(activities).filter(\":visible\");\n                that.mainFilter[that.sectionSelected] = !that.mainFilter[that.sectionSelected]\n                    ? that.sectionSelected + '-' + filterType\n                    : that.mainFilter[that.sectionSelected];\n            }\n\n            // Si el filtro primario es category y el secundario subcategory, hacemos el cambio.\n            const clicks = that.filterControllerClicks[that.sectionSelected];\n            let currentFilter;\n            if (clicks != undefined) {\n                var currentFilterCat = clicks.findIndex(function (item) {\n                    return (item.indexOf('subcategoryId') != -1);\n                });\n\n                currentFilter = clicks.findIndex(function (item) {\n                    return (item.indexOf('subcategoryId') == -1 && item.indexOf('categoryId') == -1);\n                });\n\n                if (currentFilterCat != -1 && currentFilter == -1 && clicks.length > 0) {\n                    that.mainFilter[that.sectionSelected] = that.sectionSelected + '-subcategoryId';\n                }\n            }\n            $('#filter-counter').html('(' + that.activitiesCounter + ')');\n\n            if (checked === false) {\n                that.filterController[that.sectionSelected] = [];\n                checked = true;\n            }\n            $(activities).each(function () {\n                $.each($(this).data(), function (key, value) {\n                    key = key === 'type' ? 'activitytypeid' : key;\n                    if (that.filterController[that.sectionSelected][key] === undefined || !filtered) {\n                        that.filterController[that.sectionSelected][key] = [];\n                    }\n                    if (checked && filtered && $.inArray(value, that.filterController[that.sectionSelected][key]) === -1) {\n                        that.filterController[that.sectionSelected][key].push(value);\n                        checked = true;\n                        that.resetControlller = true;\n                    }\n                });\n            });\n            checked = false;\n            // Construimos el objeto de controls de filtros\n            var filterTypeDropdown = $('.modal-filter .dropdown');\n            $(filterTypeDropdown).each(function () {\n                var dropdown = this;\n                var checkboxes = $(this).find('input');\n\n                // Primero hago todas visibles\n                $(checkboxes).each(function () {\n                    $(this).parent().show();\n                    $(this).closest('li').show();\n                });\n\n                // Ahora compruebo cual tengo que ocultar\n                let filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid));\n                let transitionalVars = filtersFromStorage !== null ? filtersFromStorage[that.sectionSelected] : '{}';\n                $('#collapse-units-dropdown-' + that.sectionSelected + $(this).data('id') + ' .topic-item').show();\n                $(checkboxes).each(function () {\n                    var check = this;\n                    var id = $(this).data('id'); // Value\n                    var fullId = $(this).attr('id');\n                    var type = $(this).data('filter'); // Key\n                    var parent = $(this).attr('id').split('-', 2).join('-');\n                    var subCatToHide = $(check).closest('li .custom_checkbox input').attr('id');\n                    var previoussuddrop = subCatToHide.includes(that.sectionSelected + '-subcategoryId')\n                        ? '[data-section~=\"' + that.sectionSelected + '\"] [data-previoussuddrop~=\"' + subCatToHide + '\"]'\n                        : '[data-section~=\"' + that.sectionSelected + '\"] [data-filtersuddrop~=\"' + subCatToHide + '\"]';\n\n                    $(previoussuddrop).show();\n\n                    if (filtered && checkedUpper && parent !== that.mainFilter[that.sectionSelected]\n                        && $.inArray(id, that.filterController[that.sectionSelected][type.toLowerCase()]) === -1) {\n                        $(check).parent().hide();\n                        transitionalVars = $.grep(transitionalVars, function (value) {\n                            return value !== fullId;\n                        });\n                        filtersFromStorage[that.sectionSelected] = transitionalVars;\n                        $(check).closest('li').hide();\n                        $('[data-section~=\"' + that.sectionSelected + '\"] [data-filter~=\"' + subCatToHide + '\"]').hide();\n                        if (that.sectionSelected + '-' + type !== that.mainFilter[that.sectionSelected]) {\n                            $(previoussuddrop).hide();\n                        }\n                    }\n                    if (filtered && checkedUpper && parent === that.mainFilter[that.sectionSelected]\n                        && parent !== that.sectionSelected + '-' + filterType\n                        && $.inArray(id, that.filterController[that.sectionSelected][type.toLowerCase()]) === -1) {\n                        var toDelete = $('[data-previoussuddrop~=\"' + subCatToHide + '\"]').data('filtersuddrop') + '-sub';\n                        if ($(check).prop('checked') === true) {\n                            $(check).prop('checked', false);\n                            $('#' + fullId + '-label').removeClass('selected');\n                            $('[data-id=\"' + fullId + '\"]').parent().remove();\n                            $('#' + toDelete).prop('checked', false);\n                        }\n                        transitionalVars = $.grep(transitionalVars, function (value) {\n                            return value !== toDelete;\n                        });\n                        filtersFromStorage[that.sectionSelected] = transitionalVars;\n                        $(check).closest('li').hide();\n\n                        if (that.sectionSelected + '-' + type !== that.mainFilter[that.sectionSelected]) {\n                            $(previoussuddrop).hide();\n                        }\n                    }\n                    if (filtered && !checkedUpper && parent !== that.mainFilter[that.sectionSelected]\n                        && $.inArray(id, that.filterController[that.sectionSelected][type.toLowerCase()]) === -1) {\n                        $(check).parent().hide();\n                        transitionalVars = $.grep(transitionalVars, function (value) {\n                            return value !== fullId;\n                        });\n                        filtersFromStorage[that.sectionSelected] = transitionalVars;\n                        $(check).closest('li').hide();\n                        $('[data-section~=\"' + that.sectionSelected + '\"] [data-filter~=\"' + subCatToHide + '\"]').hide();\n                        if (that.sectionSelected + '-' + type !== that.mainFilter[that.sectionSelected]) {\n                            $(previoussuddrop).hide();\n                        }\n                    }\n                });\n                that.filterControllerClicks = filtersFromStorage;\n                localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, filtersFromStorage)));\n\n                var visible = false;\n                var customCheckboxes = $(this).find('.custom_checkbox');\n\n                $(customCheckboxes).each(function () {\n                    if ($(this).css('display') !== 'none') {\n                        visible = true;\n                    }\n                    return true;\n                });\n\n                if (!visible) {\n                    $(dropdown).hide();\n                } else {\n                    $(dropdown).show();\n                }\n            });\n\n            if ($('.assignments-filter-selected').length > 0) {\n                $(SELECTORS.CLEANALL).removeClass('nofilters');\n            } else {\n                $(SELECTORS.CLEANALL).addClass('nofilters');\n            }\n\n            // Control de las subcategorias a la hora de mostrarla en las diferentes secciones.\n            // Solo haremos esta comprobacion en caso de que haya algun filtro aplicado.\n            if (that.filterControllerClicks[that.sectionSelected] !== undefined) {\n                $('.activities-list[data-section=\"' + that.sectionSelected + '\"]').filter(':visible').each(function () {\n                    var section = this;\n                    var cat = $(this).data('category');\n                    $('#subcatsdropdown-' + that.sectionSelected + '-' + cat + ' .subcat').filter(':visible').each(function () {\n                        var subcat = $(this).data('subcategory');\n                        var counter = $(section).find('.activity-item')\n                            .filter('[data-subcategoryid=\"' + subcat + '\"]')\n                            .not('.hidden').length;\n                        var count = $(this).find('counter');\n                        if (counter === 0) {\n                            $(this).hide();\n                        } else {\n                            count.html('(' + counter + ')');\n                            $(this).show();\n                        }\n                    });\n                });\n            }\n        };\n\n        // Gestión de subfilters\n\n        ManageAssignmentsFilter.prototype.cleanStorageForSubcategories = function (subcategories) {\n            var that = this;\n\n            // Se quitan de los clicks actuales, los filtros relacionados con las subcategorias.\n            var filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid)) ?? [];\n            filtersFromStorage[that.sectionSelected] = [];\n            var temp = that.filterControllerClicks[that.sectionSelected].filter(\n                val => !subcategories.includes(val)\n            );\n            // Se hace limpieza total de los filtros\n            that.cleanAllFilters(true);\n            that.filterControllerClicks[that.sectionSelected] = temp;\n            localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, that.filterControllerClicks)));\n\n            // Se aplican los filtros no relacionados con las subcategorias.\n            that.applyFiltersSaved();\n        };\n\n        ManageAssignmentsFilter.prototype.checkSubcategoriesDropdown = function () {\n            var that = this;\n            var subcatsdropdown = $('.' + that.sectionSelected + ' .subcatsdropdown');\n            $(subcatsdropdown).each(function () {\n                if ($(this).css('display') === 'block') {\n                    var subcatsdropdownVisible = $(this);\n                    var checker = true;\n                    $(this).find('li').each(function () {\n                        if ($(this).css('display') == 'list-item') {\n                            checker = false;\n                        }\n                    });\n                    if (checker) {\n                        subcatsdropdownVisible.hide();\n                    } else {\n                        subcatsdropdownVisible.show();\n                    }\n                }\n            });\n        };\n        ManageAssignmentsFilter.prototype.showSubcategoriesDropdown = function () {\n            var that = this;\n            var filtercounter = $('.' + that.sectionSelected).find(SELECTORS.SUBCATEGORYINPUT + ':checked').length;\n            var parent = '.' + that.sectionSelected + ' .subcats-dropdown .list-header';\n            if (filtercounter > 0) {\n                filtercounter = filtercounter >= 10 ? '+9' : filtercounter;\n                $(parent).addClass('bg-d');\n                $('.' + that.sectionSelected + ' .filter-active').text(filtercounter);\n            } else {\n                $(parent).removeClass('bg-d');\n                $('.' + that.sectionSelected + ' .filter-active').text('x');\n            }\n        };\n\n        ManageAssignmentsFilter.prototype.subFilterManagement = function () {\n            var that = this;\n\n            $('.subcats-dropdown .list-header').on('click', function () {\n                var id = $(this).attr('id');\n                $('#' + id + ' i.rotate').toggleClass('down');\n            });\n\n            $('.controls .results').on('click', function () {\n                $('.subcats-dropdown i.rotate').toggleClass('down');\n                $('#collapse-units-dropdown').removeClass('show');\n            });\n\n            $(SELECTORS.SUBCATEGORYINPUT).on('click', function () {\n                //Rellenamos los clicks\n                if (!that.subCatDeleted) {\n                    if (that.filterControllerClicks !== null && that.filterControllerClicks[that.sectionSelected] === undefined\n                        && $(this).data('filtertype') === 'subcategoryId') {\n                        that.filterControllerClicks[that.sectionSelected] = [];\n                    }\n                    if ($(this).data('filtertype') === 'subcategoryId' && that.filterControllerClicks !== null &&\n                        $.inArray($(this).attr('id'), that.filterControllerClicks[that.sectionSelected]) === -1) {\n                        that.filterControllerClicks[that.sectionSelected].push($(this)[0].id);\n                        localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, that.filterControllerClicks)));\n                    }\n                } else {\n                    that.subCatDeleted = false;\n                }\n\n                // Controlamos que no se añadan los tags de subcategoria mediante esta variable.\n                that.subCatAdded = true;\n\n                //that.showSubcategoriesDropdown(parent);\n            });\n\n            $(SELECTORS.SUBCATEGORYRESTORE).on('click', function () {\n                var idSubCatClose = $(this).data('subfilterclose');\n                var idSubCat = $(this).data('subfilterid');\n                var filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid));\n                var transitionalVars = filtersFromStorage[that.sectionSelected];\n                transitionalVars = $.grep(transitionalVars, function (value) {\n                    return (value !== idSubCatClose && value !== idSubCat);\n                });\n                filtersFromStorage[that.sectionSelected] = transitionalVars;\n\n                that.mainFilter[that.sectionSelected] = '';\n                localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, filtersFromStorage)));\n                var label = ($(this).parent()[0]);\n                $(label).removeClass('selected');\n                that.subCatDeleted = true;\n            });\n\n            $(SELECTORS.SUBCATEGORY).on('click', function () {\n                var filterId = '#' + that.sectionSelected + '-subcategoryId-' + $(this).data('subcategory');\n                var filterCatId = '#' + $(this).data('category');\n                that.categoryDropValue = $(this).data('category-drop');\n                $(filterId).click();\n                if ($(filterCatId).prop('checked') === false) {\n                    $(filterCatId).click();\n                }\n                if (!that.subCatDeleted) {\n                    $(this).addClass('selected bgf-c');\n                }\n            });\n        };\n\n        return ManageAssignmentsFilter;\n    });\n\n\n"], "names": ["define", "$", "filtersArray", "SELECTORS", "ManageAssignmentsFilter", "filter_topics", "filter_categories", "prototype", "constructor", "root", "this", "init", "that", "searchParams", "URLSearchParams", "window", "location", "search", "sectionSelected", "defaultSection", "prepareFilters", "activitiesCounter", "mainFilter", "filterController", "filterControllerClicks", "sesskeyCourseid", "<PERSON><PERSON><PERSON><PERSON>", "categoryDropValue", "categoryDropValueReload", "subCatAdded", "subCatDelete", "subCatDeleted", "resetControlller", "toggleDropdownFilter", "chooseFilters", "checkFilter", "openFilters", "changeSection", "subFilterManagement", "filtersStorage", "localStorage", "getItem", "length", "JSON", "parse", "click", "categoryid", "data", "index", "indexOf", "splice", "parent", "remove", "filters", "topicid", "filter_toassign", "onload", "getFiltersFromURL", "cleanAllFilters", "applyFiltersSaved", "manageActivities", "urlSesskey", "attr", "sesskey", "substring", "lastIndexOf", "courseid", "get", "clear", "find", "each", "toassign", "bytype", "bydelivery", "bydificulty", "bykeyevidence", "byblock", "bycompetence", "bycriterion", "bytheme", "bytransversekey", "byskills", "bypedagogicalpurposes", "byassessment", "bylearninglevel", "bytrackingactivitiesid", "bychallengesid", "byincontextid", "bytypeofactivityid", "bypresentationresourcesid", "<PERSON><PERSON>b<PERSON><PERSON><PERSON><PERSON>", "hidebytype", "hidebydelivery", "hidebydificulty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hidebycompetence", "hidebycriterion", "hidebytheme", "hidebytransversekey", "<PERSON><PERSON><PERSON><PERSON>", "hidebypedagogicalpurposes", "hidebyassessment", "hidebylearninglevel", "hidebyblock", "hidebytoassign", "hidebytrackingactivitiesid", "hidebychallengesid", "hidebyincontextid", "hidetypeofactivityid", "hidepresentationresourcesid", "hidesubcategorys<PERSON>", "hidebytopic", "undefined", "hidebycategory", "hasOwnProperty", "addClass", "hide", "removeClass", "show", "keyup", "applyColorButton", "add", "dataParent", "closest", "openId", "on", "e", "hasClass", "id", "filtersFromStorage", "transitionalVars", "grep", "value", "setItem", "stringify", "Object", "assign", "preventDefault", "toggleClass", "toggle", "stopPropagation", "uniquekey", "uniquekeySub", "filtertype", "label", "filtered", "checked", "idselected", "idselectedsub", "filterSelector", "prop", "push", "prepend", "inArray", "removeItem", "key", "idAlt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "split", "join", "siblings", "next", "document", "restore", "idSub", "cleanClicks", "arguments", "val", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cleanStorageForCurrentSection", "event", "target", "is", "container", "Array", "from", "querySelectorAll", "filter", "s", "getComputedStyle", "getPropertyValue", "containerSub", "addEventListener", "contains", "isVisible", "section", "visibleFilterbutton", "children", "newSection", "urlParams", "sequenceId", "filterSeq", "isEmptyObject", "ready", "filterType", "checkedUpper", "activities", "clicks", "currentFilter", "currentFilterCat", "findIndex", "item", "html", "filterTypeDropdown", "checkboxes", "fullId", "type", "subCatToHide", "previoussuddrop", "includes", "toLowerCase", "toDelete", "visible", "customCheckboxes", "css", "cat", "subcat", "counter", "not", "count", "cleanStorageForSubcategories", "subcategories", "temp", "checkSubcategoriesDropdown", "subcatsdropdown", "subcatsdropdownVisible", "checker", "showSubcategoriesDropdown", "filtercounter", "text", "idSubCatClose", "idSubCat", "filterId", "filterCatId"], "mappings": "AACAA,OAAO,uCAAA,CAAC,SAAU,WAAY,uBAAwB,6BAClD,SAAUC,GAEN,IAAIC,aAAe,CAAA,EACnB,IAAIC,eACM,iBADNA,qBAEY,eAFZA,oBAGW,qBAHXA,yBAIgB,0BAJhBA,iBAKQ,UALRA,mBAOU,iBAPVA,wBAQe,kBARfA,iBASQ,sBATRA,0BAUiB,gCAVjBA,8BAWqB,6DAXrBA,sBAYa,uBAZbA,uBAac,oCAbdA,eAcM,kBAdNA,yBAegB,yBAfhBA,qBAgBY,gBAhBZA,yBAiBgB,6CAjBhBA,uBAkBc,yBAlBdA,sBAsBa,gBAtBbA,uBAwBc,kBAxBdA,2BAyBkB,mBAzBlBA,sBA0Ba,UA1BbA,6BA2BoB,eAOxB,SAASC,wBAAwBC,cAAeC,mBAC5CF,wBAAwBG,UAAUC,YAAcJ,wBAChDA,wBAAwBG,UAAUE,KAAO,KACzCL,wBAAwBG,UAAUF,cAAgBA,cAClDD,wBAAwBG,UAAUD,kBAAoBA,kBAEtDI,KAAKC,MACT,CA6/BA,OA3/BAP,wBAAwBG,UAAUI,KAAO,WACrC,IAAIC,KAAOF,KACXA,KAAKD,KAAOR,EAAEE,gBACdO,KAAKG,aAAe,IAAIC,gBAAgBC,OAAOC,SAASC,QACxDP,KAAKQ,gBAAkBN,KAAKO,iBAC5BT,KAAKR,aAAeU,KAAKQ,iBACzBV,KAAKW,kBAAoB,EACzBX,KAAKY,WAAa,GAClBZ,KAAKa,iBAAmB,GACxBb,KAAKc,uBAAyB,GAC9Bd,KAAKe,gBAAkB,GACvBf,KAAKgB,eACLhB,KAAKiB,kBAAoB,GACzBjB,KAAKkB,wBAA0B,GAC/BlB,KAAKmB,aAAc,EACnBnB,KAAKoB,cAAe,EACpBlB,KAAKmB,eAAgB,EAErBnB,KAAKoB,kBAAmB,EACxBpB,KAAKqB,uBACLrB,KAAKsB,gBACLtB,KAAKuB,cACLvB,KAAKwB,cACLxB,KAAKyB,gBACL3B,KAAK4B,sBAEL5B,KAAK6B,eAAiBC,aAAaC,QAAQ/B,KAAKe,kBAAoB,KAExC,OAAxBf,KAAK6B,gBAA0D,IAA/B7B,KAAK6B,eAAeG,QAAwC,OAAxBhC,KAAK6B,iBACzE3B,KAAKY,uBAAyBmB,KAAKC,MAAMlC,KAAK6B,iBAIlDtC,EAAE,0CAA0C4C,OAAM,WAC9C,IAAIC,WAAa7C,EAAES,MAAMqC,KAAK,cAC9B,IAAIC,MAAQpC,KAAKN,kBAAkB2C,QAAQH,YACvCE,OAAS,GACTpC,KAAKN,kBAAkB4C,OAAOF,MAAO,GAEzC/C,EAAES,MAAMyC,SAASC,SACjBxC,KAAKyC,SACT,IAGApD,EAAE,uCAAuC4C,OAAM,WAC3C,IAAIS,QAAUrD,EAAES,MAAMqC,KAAK,WACvBC,MAAQpC,KAAKP,cAAc4C,QAAQK,SACnCN,OAAS,GACTpC,KAAKP,cAAc6C,OAAOF,MAAO,GAErC/C,EAAES,MAAMyC,SAASC,SACjBxC,KAAKyC,SACT,IAGApD,EAAE,0CAA0C4C,OAAM,WAC9CjC,KAAK2C,gBAAkB,EACvBtD,EAAES,MAAMyC,SAASC,SACjBxC,KAAKyC,SACT,IAEAtC,OAAOyC,OAAS,KACP5C,KAAK6C,sBACN7C,KAAK8C,kBACL9C,KAAK+C,oBACT,EAGJ/C,KAAKgD,oBAGTxD,wBAAwBG,UAAUmB,aAAe,WAC7C,IAAImC,WAAa5D,EAAE,gBAAgB6D,KAAK,QACpCC,QAAUF,WAAWG,UAAUH,WAAWI,YAAY,KAAO,GAC7DC,SAAWxD,KAAKG,aAAasD,IAAI,YACrCzD,KAAKe,gBAAkBsC,QAAU,IAAMG,SAEnC,OAAS1B,aAAaC,QAAQ/B,KAAKe,kBACnCe,aAAa4B,SAIrBhE,wBAAwBG,UAAU8C,QAAU,WACxC,IAAIzC,KAAOF,KAGXT,EAAE,IAAMW,KAAKM,iBAAiBmD,KAAK,kBAAkBC,MAAK,WACtD,IAAIhB,QAAUrD,EAAES,MAAMqC,KAAK,cACvBD,WAAa7C,EAAES,MAAMqC,KAAK,cAC1BwB,SAAWtE,EAAES,MAAMqC,KAAK,YAGxByB,OAASvE,EAAES,MAAMqC,KAAK,QACtB0B,WAAaxE,EAAES,MAAMqC,KAAK,yBAC1B2B,YAAczE,EAAES,MAAMqC,KAAK,qBAC3B4B,cAAgB1E,EAAES,MAAMqC,KAAK,iBAC7B6B,QAAU3E,EAAES,MAAMqC,KAAK,WACvB8B,aAAe5E,EAAES,MAAMqC,KAAK,gBAC5B+B,YAAc7E,EAAES,MAAMqC,KAAK,eAC3BgC,QAAU9E,EAAES,MAAMqC,KAAK,WACvBiC,gBAAkB/E,EAAES,MAAMqC,KAAK,mBAC/BkC,SAAWhF,EAAES,MAAMqC,KAAK,YACxBmC,sBAAwBjF,EAAES,MAAMqC,KAAK,yBACrCoC,aAAelF,EAAES,MAAMqC,KAAK,gBAC5BqC,gBAAkBnF,EAAES,MAAMqC,KAAK,mBAC/BsC,uBAAyBpF,EAAES,MAAMqC,KAAK,wBACtCuC,eAAiBrF,EAAES,MAAMqC,KAAK,gBAC9BwC,cAAgBtF,EAAES,MAAMqC,KAAK,eAC7ByC,mBAAqBvF,EAAES,MAAMqC,KAAK,oBAClC0C,0BAA4BxF,EAAES,MAAMqC,KAAK,2BACzC2C,gBAAkBzF,EAAES,MAAMqC,KAAK,iBAI/B4C,YAAa,EACbC,gBAAiB,EAEjBC,iBAAkB,EAClBC,mBAAoB,EACpBC,kBAAmB,EACnBC,iBAAkB,EAClBC,aAAc,EACdC,qBAAsB,EACtBC,cAAe,EACfC,2BAA4B,EAC5BC,kBAAmB,EACnBC,qBAAsB,EACtBC,aAAc,EACdC,eAAkB5F,KAAK2C,kBAAoBgB,SAC3CkC,4BAA6B,EAC7BC,oBAAqB,EACrBC,mBAAoB,EACpBC,sBAAuB,EACvBC,6BAA8B,EAC9BC,oBAAqB,EAErBC,YAAgBnG,KAAKP,cAAcqC,OAAS,IAA8C,IAAzC9B,KAAKP,cAAc4C,QAAQK,eACvC0D,IAApCpG,KAAKV,aAAyB,YACxBU,KAAKV,aAAyB,WAAEwC,OAAS,IACa,IAAtD9B,KAAKV,aAAyB,WAAE+C,QAAQK,SAC/C2D,eAAkBrG,KAAKN,kBAAkBoC,OAAS,IAAqD,IAAhD9B,KAAKN,kBAAkB2C,QAAQH,kBAC9CkE,IAApCpG,KAAKV,aAAyB,YAC3BU,KAAKV,aAAyB,WAAEwC,OAAS,IACgB,IAAzD9B,KAAKV,aAAyB,WAAE+C,QAAQH,YAE/ClC,KAAKV,aAAagH,eAAe,oBACjCvB,WAAc/E,KAAKV,aAA6B,eAAEwC,OAAS,IACK,IAAzD9B,KAAKV,aAA6B,eAAE+C,QAAQuB,SAGnD5D,KAAKV,aAAagH,eAAe,uBACjCrB,gBAAmBjF,KAAKV,aAAgC,kBAAEwC,OAAS,IACK,IAAjE9B,KAAKV,aAAgC,kBAAE+C,QAAQyB,cAGtD9D,KAAKV,aAAagH,eAAe,2BACjCtB,eAAkBhF,KAAKV,aAAoC,sBAAEwC,OAAS,IACK,IAApE9B,KAAKV,aAAoC,sBAAE+C,QAAQwB,aAG1D7D,KAAKV,aAAagH,eAAe,mBACjCpB,kBAAqBlF,KAAKV,aAA4B,cAAEwC,OAAS,IACK,IAA/D9B,KAAKV,aAA4B,cAAE+C,QAAQ0B,gBAGlD/D,KAAKV,aAAagH,eAAe,aACjCX,YAAe3F,KAAKV,aAAsB,QAAEwC,OAAS,IACK,IAAnD9B,KAAKV,aAAsB,QAAE+C,QAAQ2B,UAG5ChE,KAAKV,aAAagH,eAAe,kBACjCnB,iBAAoBnF,KAAKV,aAA2B,aAAEwC,OAAS,IACK,IAA7D9B,KAAKV,aAA2B,aAAE+C,QAAQ4B,eAGjDjE,KAAKV,aAAagH,eAAe,iBACjClB,gBAAmBpF,KAAKV,aAA0B,YAAEwC,OAAS,IACK,IAA3D9B,KAAKV,aAA0B,YAAE+C,QAAQ6B,cAGhDlE,KAAKV,aAAagH,eAAe,aACjCjB,YAAerF,KAAKV,aAAsB,QAAEwC,OAAS,IACK,IAAnD9B,KAAKV,aAAsB,QAAE+C,QAAQ8B,UAG5CnE,KAAKV,aAAagH,eAAe,qBACjChB,oBAAuBtF,KAAKV,aAA8B,gBAAEwC,OAAS,IACK,IAAnE9B,KAAKV,aAA8B,gBAAE+C,QAAQ+B,kBAGpDpE,KAAKV,aAAagH,eAAe,cACjCf,aAAgBvF,KAAKV,aAAuB,SAAEwC,OAAS,IACK,IAArD9B,KAAKV,aAAuB,SAAE+C,QAAQgC,WAG7CrE,KAAKV,aAAagH,eAAe,2BACjCd,0BAA6BxF,KAAKV,aAAoC,sBAAEwC,OAAS,IACK,IAA/E9B,KAAKV,aAAoC,sBAAE+C,QAAQiC,wBAG1DtE,KAAKV,aAAagH,eAAe,kBACjCb,iBAAoBzF,KAAKV,aAA2B,aAAEwC,OAAS,IACK,IAA7D9B,KAAKV,aAA2B,aAAE+C,QAAQkC,eAGjDvE,KAAKV,aAAagH,eAAe,qBACjCZ,oBAAuB1F,KAAKV,aAA8B,gBAAEwC,OAAS,IACK,IAAnE9B,KAAKV,aAA8B,gBAAE+C,QAAQmC,kBAGpDxE,KAAKV,aAAagH,eAAe,0BACjCT,2BAA8B7F,KAAKV,aAAmC,qBAAEwC,OAAS,IACK,IAA/E9B,KAAKV,aAAmC,qBAAE+C,QAAQoC,yBAGzDzE,KAAKV,aAAagH,eAAe,kBACjCR,mBAAsB9F,KAAKV,aAA2B,aAAEwC,OAAS,IACK,IAA/D9B,KAAKV,aAA2B,aAAE+C,QAAQqC,iBAGjD1E,KAAKV,aAAagH,eAAe,iBACjCP,kBAAqB/F,KAAKV,aAA0B,YAAEwC,OAAS,IACK,IAA7D9B,KAAKV,aAA0B,YAAE+C,QAAQsC,gBAGhD3E,KAAKV,aAAagH,eAAe,sBACjCN,qBAAwBhG,KAAKV,aAA+B,iBAAEwC,OAAS,IACO,IAAvE9B,KAAKV,aAA+B,iBAAE+C,QAAQuC,qBAGrD5E,KAAKV,aAAagH,eAAe,6BACjCL,4BAA+BjG,KAAKV,aAAsC,wBAAEwC,OAAS,IACO,IAArF9B,KAAKV,aAAsC,wBAAE+C,QAAQwC,4BAG5D7E,KAAKV,aAAagH,eAAe,mBACjCJ,mBAAsBlG,KAAKV,aAA4B,cAAEwC,OAAS,IACM,IAAjE9B,KAAKV,aAA4B,cAAE+C,QAAQyC,kBAGlDqB,aAAeE,gBAAkBT,gBAAkBD,aAAeR,kBAAoBC,iBACtFC,aAAeC,qBAAuBP,YAAcC,gBAAoCC,iBACxFC,mBAAqBK,cAAgBC,2BAA6BC,kBAAoBC,qBACtFG,4BAA8BC,oBAAsBC,mBAAqBC,sBACzEC,6BAA+BC,oBAC/B7G,EAAES,MAAMyG,SAAS,UACjBlH,EAAES,MAAM0G,SAERnH,EAAES,MAAM2G,YAAY,UACpBpH,EAAES,MAAM4G,OAEhB,IACArH,EAAE,WAAWsH,SAGjBnH,wBAAwBG,UAAUiH,iBAAmB,SAAUzE,KAAM0E,KACjE,IAAIC,WAAazH,EAAE8C,MAAM4E,QAAQ,aAC7BF,KACAxH,EAAEyH,YAAYP,SAAS,YACvBlH,EAAEyH,YAAYrD,KAAK,YAAY8C,SAAS,WAExClH,EAAEyH,YAAYL,YAAY,YAC1BpH,EAAEyH,YAAYrD,KAAK,YAAYgD,YAAY,WAInDjH,wBAAwBG,UAAU0B,qBAAuB,WACrD,IAAI2F,OAAS,GACThH,KAAOF,KACXT,EAAE,cAAc4H,GAAG,SAAS,SAAUC,GAClC,GAAI7H,EAAES,MAAMyC,SAAS4E,SAAS,SAiB1B,OAhBA9H,EAAE,eAAiBA,EAAES,MAAMyC,SAASW,KAAK,MAAQ,KAAO3D,yBAA2B,YAC9EmE,MAAK,WAEF,IAAI0D,GAAK/H,EAAES,MAAMoD,KAAK,MAClBmE,mBAAqBtF,KAAKC,MAAMJ,aAAaC,QAAQ7B,KAAKa,kBAC1DyG,iBAAmBD,mBAAmBrH,KAAKM,iBAC/CgH,iBAAmBjI,EAAEkI,KAAKD,kBAAkB,SAAUE,OAClD,OAAOA,QAAUJ,EACrB,IACAC,mBAAmBrH,KAAKM,iBAAmBgH,iBAC3C1F,aAAa6F,QAAQzH,KAAKa,gBAAiBkB,KAAK2F,UAAUC,OAAOC,OAAO,CAAA,EAAIP,sBAC5EhI,EAAES,MAAMmC,QACR5C,EAAE,aAAeA,EAAES,MAAMoD,KAAK,MAAQ,KAAKX,SAASC,QACxD,IACJnD,EAAES,MAAMyC,SAASkE,YAAY,cAC7BS,EAAEW,gBAGV,IAEAxI,EAAE,YAAY4H,GAAG,SAAS,SAAUC,GAChC7H,EAAES,MAAMgI,YAAY,QACpB,IAAIV,GAAK/H,EAAES,MAAMoD,KAAK,MAElB8D,QAAUA,SAAW3H,EAAES,MAAMoD,KAAK,QAClC7D,EAAE,eAAiB2H,OAAS,KAAKR,OACjCnH,EAAE,IAAM2H,QAAQP,YAAY,SAEhCO,OAASI,GACT/H,EAAE,eAAiB+H,GAAK,KAAKW,SAC7Bb,EAAEc,iBACN,KAGJxI,wBAAwBG,UAAU4B,YAAc,WAC5C,IAAIvB,KAAOF,KAEPE,KAAK2B,iBACL3B,KAAKY,uBAAuBZ,KAAKM,iBAAmByB,KAAKC,MAAMhC,KAAK2B,iBAGxEtC,EAAEE,0BAA0B0H,GAAG,SAAS,WACpC,IAAIG,GAAK/H,EAAES,MAAMqC,KAAK,MAClB8F,UAAY5I,EAAES,MAAMoD,KAAK,MACzBgF,aAAe,GACfC,WAAa9I,EAAES,MAAMqC,KAAK,cAC1BiG,MAAQ/I,EAAES,MAAMqC,KAAK,SACrBkG,UAAW,EACXC,SAAU,EACVC,WAAa,IAAMlJ,EAAES,MAAMoD,KAAK,MAAQ,SACxCsF,cAAgB,IAAMnJ,EAAES,MAAMoD,KAAK,MAAQ,OAC3CuF,eAAiBzI,KAAKM,gBAAkB,IAAM6H,WAIlD,GAFAnI,KAAK4G,iBAAiB2B,YAAY,GAE9BlJ,EAAES,MAAM4I,KAAK,WACb1I,KAAKV,aAAa6I,YAAYQ,KAAKvB,IACnCgB,MAAQ/I,EAAES,MAAMqC,KAAK,SAEjBnC,KAAKgB,yBAA0C,kBAAfmH,aAChCD,aAAe7I,EAAE,IAAMW,KAAKgB,yBAAyB+F,QAAQ,MAAM5E,KAAK,kBAIzD,kBAAfgG,YAC+E,IAA/E9I,EAAE,iCAAiCoE,KAAK,aAAawE,eAAenG,QAEpEzC,EAAEE,2BAA2BqJ,QACzB,kFAAoFxB,GAApF,mCACmCgB,MADnC,6DAEyDA,MAAQ,oBAAsBhB,GAFvF,cAGca,UAHd,iBAGkDC,aAHlD,sBAIAC,WAJA,0BAI8CM,eAJ9C,kBAQRpJ,EAAEE,yBAAyBgH,SAAS,QACpC+B,SAAU,EAILtI,KAAKiB,cAC8B,OAAhCjB,KAAKY,6BACoDwF,IAAtDpG,KAAKY,uBAAuBZ,KAAKM,mBACpCN,KAAKY,uBAAuBZ,KAAKM,iBAAmB,IAEpB,OAAhCN,KAAKY,yBACiF,IAAtFvB,EAAEwJ,QAAQxJ,EAAES,MAAMoD,KAAK,MAAOlD,KAAKY,uBAAuBZ,KAAKM,oBAC/DN,KAAKY,uBAAuBZ,KAAKM,iBAAiBqI,KAAKtJ,EAAES,MAAMoD,KAAK,OACpEtB,aAAa6F,QAAQzH,KAAKa,gBACtBkB,KAAK2F,UAAUC,OAAOC,OAAO,CAAA,EAAI5H,KAAKY,4BAIlDZ,KAAKiB,aAAc,EAEnB5B,EAAE,IAAMA,EAAES,MAAMoD,KAAK,MAAQ,UAAUqD,SAAS,YAChDlH,EAAEmJ,eAAeE,KAAK,WAAW,OAC9B,CACH,IAAII,WAAazJ,EAAES,MAAMoD,KAAK,MAC1BlD,KAAKkB,eACL4H,WAAa9I,KAAKgB,yBAA2BhB,KAAKe,kBAAoB,kBAAoBqG,GAAK,OAC/FpH,KAAKkB,cAAe,GAExBlB,KAAKY,uBAAuBZ,KAAKM,iBAAmBjB,EAAEkI,KAClDvH,KAAKY,uBAAuBZ,KAAKM,kBAC/B,SAAUkH,OACR,OAAOA,QAAUsB,UACrB,IACJ,IAAIC,IAAM/I,KAAKV,aAAa6I,YAAY9F,QAAQ+E,IAChDpH,KAAKV,aAAa6I,YAAY7F,OAAOyG,IAAK,GAC1C1J,EAAEE,sBAAsBkE,KAAK,kBAAkB2D,QAAQ5E,SACvDnD,EAAEE,2BAA2BkE,KAAK,kBAAkB2D,QAAQ5E,SAExDnD,EAAEE,+BAA+BuC,OAAS,IAC1CzC,EAAEE,yBAAyBkH,YAAY,QACvC4B,UAAW,EACXF,WAAa,KACbnI,KAAK4G,iBAAiB2B,YAAY,IAItC,IAAIS,MAAmC,kBAA3B3J,EAAES,MAAMqC,KAAK,UAAgCnC,KAAKgB,wBAA0B3B,EAAES,MAAMoD,KAAK,MAEjGmE,mBAAqBtF,KAAKC,MAAMJ,aAAaC,QAAQ7B,KAAKa,kBAC1DyG,iBAAmBD,mBAAmBrH,KAAKM,iBAU/C,GATAgH,iBAAmBjI,EAAEkI,KAAKD,kBAAkB,SAAUE,OAClD,OAAOA,QAAUwB,KACrB,IACA3B,mBAAmBrH,KAAKM,iBAAmBgH,iBAE3CtH,KAAKU,WAAWV,KAAKM,iBAAmB,GACxCsB,aAAa6F,QAAQzH,KAAKa,gBAAiBkB,KAAK2F,UAAUC,OAAOC,OAAO,CAAA,EAAIP,2BAGxCjB,IAAhCpG,KAAKY,6BACoDwF,IAAtDpG,KAAKY,uBAAuBZ,KAAKM,uBACwB8F,IAAzDpG,KAAKY,uBAAuBZ,KAAKM,iBAAiB,GAAkB,CACvE,IAAI2I,eAAiBjJ,KAAKY,uBAAuBZ,KAAKM,iBAAiB,GAAG4I,MAAM,IAAK,GAAGC,KAAK,KAC7FnJ,KAAKU,WAAWV,KAAKM,iBAAmB2I,cAC5C,CAC+B,eAA3B5J,EAAES,MAAMqC,KAAK,WACV9C,EAAE,2BAA6BA,EAAES,MAAMoD,KAAK,MAAO,MAAMkG,SAAS,SAASC,OAAOlC,SAAS,cAC1FnH,KAAK8C,iBAAgB,GACrBzD,EAAE,YAAYoH,YAAY,SAC1BpH,EAAE,WAAWoH,YAAY,kBACzBpH,EAAE,WAAW4C,SAGrB5C,EAAEkJ,YAAYT,YAAY,WAC9B,CAE0D,KAAtD9H,KAAKY,uBAAuBZ,KAAKM,mBACjCN,KAAKU,WAAWV,KAAKM,iBAAmB,IAG5CjB,EAAE,WAAWqH,OACb1G,KAAKyC,UACLzC,KAAKgD,iBAAiBqF,SAAUF,WAAYG,QAChD,IAEAjJ,EAAEiK,UAAUrC,GAAG,QAAS1H,wBAAwB,WAC5C,GAAmC,eAA/BF,EAAES,MAAMqC,KAAK,eACV9C,EAAEE,2BAA2BkE,KAAK,qCAAqC3B,OAAS,EADvF,CAEI,IAAIyH,QAAUlK,EAAES,MAAMqC,KAAK,MAC3B9C,EAAE,qCAAuCkK,QAAU,MAAMtH,OAE7D,KALA,CASA,IAAImF,GAAK/H,EAAES,MAAMqC,KAAK,MAClBqH,MAAQnK,EAAES,MAAMqC,KAAK,SACrBkF,mBAAqBtF,KAAKC,MAAMJ,aAAaC,QAAQ7B,KAAKa,kBAC1DyG,iBAAmBD,mBAAmBrH,KAAKM,iBAC/CgH,iBAAmBjI,EAAEkI,KAAKD,kBAAkB,SAAUE,OAClD,OAAOA,QAAUJ,EACrB,IACAC,mBAAmBrH,KAAKM,iBAAmBgH,iBAC3C1F,aAAa6F,QAAQzH,KAAKa,gBAAiBkB,KAAK2F,UAAUC,OAAOC,OAAO,CAAA,EAAIP,sBAExEmC,OACAxJ,KAAKkB,cAAe,EAEpB7B,EAAE,IAAMmK,MAAQ,QAAQvH,SAGxB5C,EAAE,IAAM+H,IAAInF,QAGZ5C,EAAEE,+BAA+BuC,OAAS,GAC1CzC,EAAEE,yBAAyBkH,YAAY,QAI3C,IAAIgC,eAAiB,WAAapJ,EAAES,MAAMqC,KAAK,kBAC3B9C,EAAE,IAAMA,EAAES,MAAMqC,KAAK,mBAAmBsB,KAAK,kCAAkC3B,OAC/E,GAChBzC,EAAE,IAAMoJ,gBAAgBhC,YAAY,QA/BxC,CAiCJ,IAEApH,EAAEE,oBAAoB0H,GAAG,SAAS,WAC9BjH,KAAK8C,iBAAgB,GACrBzD,EAAE,YAAYoH,YAAY,SAC1BpH,EAAE,WAAWoH,YAAY,kBACzBpH,EAAE,WAAW4C,OACjB,KAGJzC,wBAAwBG,UAAUmD,gBAAkB,WAA+B,IAArB2G,YAAWC,UAAA5H,OAAA,QAAAsE,IAAAsD,UAAA,IAAAA,UAAA,GAErErK,EAAEE,kBAAkBoK,IAAI,IACxBtK,EAAEE,0BAA0BmE,MAAK,WAC7BrE,EAAES,MAAM4I,KAAK,WAAW,GACxB,IAAIH,WAAa,IAAMlJ,EAAES,MAAMoD,KAAK,MAAQ,SAC5C7D,EAAEkJ,YAAY9B,YAAY,WAC9B,IAEApH,EAAEE,+BAA+BmE,MAAK,WAClCrE,EAAES,MAAM0C,QACZ,IAEAnD,EAAEE,0BAA0BmE,MAAK,WAC7BrE,EAAES,MAAM0C,QACZ,IAEAnD,EAAEE,yBAAyBkH,YAAY,QAhB5B3G,KAiBN8J,eAjBM9J,KAkBNkD,mBAEDyG,cApBO3J,KAqBFY,WArBEZ,KAqBcQ,iBAAmB,GArBjCR,KAuBF+J,gCAvBE/J,KAwBFc,uBAxBEd,KAwB0BQ,iBAAmB,GACpDjB,EAAE,uBAAuBqH,OACzBrH,EAAE,WAAWqH,OACbrH,EAAE,wCAAwCqH,OAC1CrH,EAAE,kCAAkCoH,YAAY,QAChDpH,EAAE,eA7BKS,KA6BiBQ,iBAAiBmD,KAAK,mBAAmBgD,YAAY,kBAIrFjH,wBAAwBG,UAAUkK,8BAAgC,YAErC9H,KAAKC,MAAMJ,aAAaC,QADtC/B,KACmDe,mBAAqB,IADxEf,KAEaQ,iBAAmB,GAFhCR,KAGNc,uBAHMd,KAGsBQ,iBAAmB,GACpDsB,aAAa6F,QAJF3H,KAIee,gBAAiBkB,KAAK2F,UAAUC,OAAOC,OAAO,CAAA,EAJ7D9H,KAIsEc,2BAGrFpB,wBAAwBG,UAAU6B,YAAc,WAC5CnC,EAAEE,kBAAkB0H,GAAG,SAAS,WAC5B5H,EAAEE,wBAAwBwI,SAC1B1I,EAAEE,sBAAsBgH,SAAS,MACjClH,EAAEE,qBAAqBgH,SAAS,MAChClH,EAAEE,0BAA0BgH,SAAS,KACzC,IACAlH,EAAEE,gBAAgB0H,GAAG,SAAS,WAC1B5H,EAAES,MAAM2D,KAAK,WAAWqE,YAAY,QACpCzI,EAAES,MAAMgI,YAAY,OACxB,IACAzI,EAAEE,uBAAuB0H,GAAG,SAAS,WACjC5H,EAAEE,wBAAwBwI,SAC1B1I,EAAEE,sBAAsBkH,YAAY,MACpCpH,EAAEE,qBAAqBkH,YAAY,MACnCpH,EAAEE,0BAA0BkH,YAAY,KAC5C,IACApH,EAAE,QAAQ4C,OAAM,SAAU6H,OAMtB,IALmC,iBAA/BzK,EAAEyK,MAAMC,QAAQ7G,KAAK,OAA4B7D,EAAEyK,MAAMC,QAAQ5C,SAAS,cAC1E9H,EAAE,8BAA8ByI,YAAY,QAC5CzI,EAAE,4BAA4BoH,YAAY,SAG1CpH,EAAE,4BAA4B2K,GAAG,YAAa,CAC9C,IAAIC,UAAYC,MAAMC,KAAKb,SAASc,iBAAiB,qBAAqBC,QAAOC,GACnB,QAA1DnK,OAAOoK,iBAAiBD,GAAGE,iBAAiB,aAChDP,UAAY5K,EAAE4K,WAAWxG,KAAK,qBAAqB,GAEnD,IAAIgH,aAAenB,SAASc,iBAAiB,qDAAqD,GAClGd,SAASoB,iBAAiB,SAAS,SAAUZ,OACrCG,WACGA,YAAcH,MAAMC,SACnBE,UAAUU,SAASb,MAAMC,SAC1B1K,EAAEoL,cAAcT,GAAG,cACtB3K,EAAE,8BAA8ByI,YAAY,QAC5CzI,EAAE,4BAA4BoH,YAAY,QAElD,GACJ,CACJ,KAGJjH,wBAAwBG,UAAUiK,aAAe,WAE7CvK,EAAE,kBAAkBqE,MAAK,WACrBrE,EAAES,MAAM4G,OACRrH,EAAES,MAAM2G,YAAY,UAAUA,YAAY,WAC9C,IACApH,EAAEE,kBAAkBoK,IAAI,IALb7J,KAMNU,iBACL,IAAIoK,UAAY,EAChBvL,EAAE,kBAAkBqE,MAAK,WACjBrE,EAAES,MAAMkK,GAAG,aACXY,WAER,IACkB,IAAdA,WAAqBvL,EAAE,gCAAgCyC,OAAS,EAI3C,IAAd8I,WAAoBvL,EAAE,gCAAgCyC,OAAS,GACtEzC,EAAE,oBAAoBmH,OACtBnH,EAAE,oBAAoBqH,OACtBrH,EAAE,yBAAyBmH,QACN,IAAdoE,YACPvL,EAAE,yBAAyBmH,OAC3BnH,EAAE,oBAAoBmH,OACtBnH,EAAE,oBAAoBmH,SAVtBnH,EAAE,oBAAoBmH,OACtBnH,EAAE,oBAAoBmH,OACtBnH,EAAE,yBAAyBqH,QAhBpB5G,KA0BNU,kBAGThB,wBAAwBG,UAAUa,eAAiB,WAO/C,OANAnB,EAAEE,uBAAuBmE,MAAK,WACbrE,EAAES,MAAM2D,KAAK,0BACnBC,MAAK,WACRpE,aAAaD,EAAES,MAAMqC,KAAK,eAAiB,EAC/C,GACJ,IACO7C,cAGXE,wBAAwBG,UAAU2B,cAAgB,WAC9C,IAAItB,KAAOF,KACX,IAAI+K,QAGJ,GAAIxL,EAAE,iBAAiB2K,GAAG,WAAY,CAClC,IAAI/J,aAAeD,KAAKC,aACxB4K,QAAU5K,aAAasD,IAAI,UAC/B,MACIsH,QAAUxL,EAAE,+BAA+B8C,KAAK,WAGpD,IAAI2I,qBAAsB,EAC1BzL,EAAE,iBAAiBqE,MAAK,WAChBrE,EAAES,MAAMqC,KAAK,aAAe0I,UAC5BxL,EAAES,MAAM2G,YAAY,UAChBpH,EAAES,MAAMiL,WAAW5D,SAAS,cAC5B2D,qBAAsB,GAGlC,IAC2B,GAAvBA,qBACAzL,EAAEE,kBAAkBiH,OAIxBnH,EAAE,sBAAsB4H,GAAG,SAAS,WAEhCjH,KAAK8C,iBAAgB,GACrB,IAAIkI,WAAa3L,EAAES,MAAM2D,KAAK,KAAKtB,KAAK,WACxC9C,EAAE,iBAAiBqE,MAAK,WAChBrE,EAAES,MAAMqC,KAAK,aAAe6I,WAC5B3L,EAAES,MAAM2G,YAAY,UACZpH,EAAES,MAAMqH,SAAS,WACzB9H,EAAES,MAAMyG,SAAS,SAEzB,GACJ,KAIJ/G,wBAAwBG,UAAUkD,kBAAoB,WAClD,IAAI7C,KAAOF,KACX,IAAImL,UAAYjL,KAAKC,aACjB4K,QAAUI,UAAU1H,IAAI,WACxB2H,WAAaD,UAAU1H,IAAI,WAC3Bd,QAAUV,KAAKC,MAAM3C,EAAE,iBAAiBsK,OAE5C,KAAmB,OAAfuB,YAAuBzI,QAAQX,OAAS,GAGxC,OAAO,EAEX,GAJI9B,KAAK6J,gCAIU,OAAfqB,WAAqB,CACrB,IAAIC,UAAYN,QAAU,eAAiBK,WAC3ClL,KAAKY,uBAAuBZ,KAAKM,iBAAiBqI,KAAKwC,UAC3D,CAEI1I,QAAQX,OAAS,GACjBzC,EAAEoD,SAASiB,MAAK,WACZ,IAAI2G,OAASQ,QAAU,IAAM/K,KAAKiJ,IAAM,IAAMjJ,KAAK0H,MACnDxH,KAAKY,uBAAuBZ,KAAKM,iBAAiBqI,KAAK0B,OAC3D,IAIJzI,aAAa6F,QAAQzH,KAAKa,gBACtBkB,KAAK2F,UAAUC,OAAOC,OAAO,CAAA,EAAI5H,KAAKY,2BAG9CpB,wBAAwBG,UAAUY,eAAiB,WAC/C,IACIsK,QACJ,IAAK/K,KAAKQ,gBAAiB,CAEvBuK,QAJO/K,KAGcG,aACDsD,IAAI,YAAc,WAC1C,CACA,OAAOsH,SAGXrL,wBAAwBG,UAAU8B,cAAgB,WAC9C,IAAIzB,KAAOF,KACXT,EAAEE,wBAAwB0H,GAAG,SAAS,WAClCjH,KAAKM,gBAAkBjB,EAAES,MAAMqC,KAAK,WACpCnC,KAAKoB,kBAAmB,EACxBpB,KAAKY,uBAAyBZ,KAAKY,wBAA0B,UAEHwF,IAAtDpG,KAAKY,uBAAuBZ,KAAKM,kBACjCN,KAAK+C,oBAET/C,KAAKY,uBAAuBZ,KAAKM,iBAAmB,GAChDjB,EAAE+L,cAAcpL,KAAK2B,iBACrBC,aAAa6F,QAAQzH,KAAKa,gBACtBkB,KAAK2F,UAAUC,OAAOC,OAAO,CAAE,EAAE5H,KAAKY,uBAAuBZ,KAAKM,mBAE9E,KAGJd,wBAAwBG,UAAUoD,kBAAoB,WAClD,IAAI/C,KAAOF,KACXT,EAAEiK,UAAU+B,OAAM,WACsB,OAAhCrL,KAAKY,yBACLvB,EAAE,WAAWoH,YAAY,kBACzBpH,EAAE,YAAYoH,YAAY,SAC1BpH,EAAEqE,KAAK1D,KAAKY,uBAAuBZ,KAAKM,kBAAkB,SAAUyI,IAAKvB,OACrEnI,EAAE,IAAMmI,OAAOkB,KAAK,WAAW,GAAOzG,QAEtC5C,EAAE,6BAA+BmI,MAAQ,MAAMkB,KAAK,WAAW,GAAMW,OAAO9C,SAAS,iBACzF,IAER,KAGJ/G,wBAAwBG,UAAUqD,iBAAmB,WAG5B,IAFrBqF,SAAQqB,UAAA5H,OAAA,QAAAsE,IAAAsD,UAAA,IAAAA,UAAA,GACR4B,WAAU5B,UAAA5H,OAAA,QAAAsE,IAAAsD,UAAA,GAAAA,UAAA,GAAG,KACb6B,aAAY7B,UAAA5H,OAAA,QAAAsE,IAAAsD,UAAA,GAAAA,UAAA,GAAG,KACf,IAAI1J,KAAOF,KACP0L,WAAanM,EAAE,kCAAoCS,KAAKQ,gBAAkB,qBAC1EgI,QAAUiD,aACdvL,KAAKW,iBAAiBX,KAAKM,iBAAmB,GAGzC+H,UAGDrI,KAAKS,kBAAoBpB,EAAEmM,YAAYnB,OAAO,YAAYvI,OAC1D0J,WAAanM,EAAEmM,YAAYnB,OAAO,YAClCrK,KAAKU,WAAWV,KAAKM,iBAAoBN,KAAKU,WAAWV,KAAKM,iBAExDN,KAAKU,WAAWV,KAAKM,iBADrBN,KAAKM,gBAAkB,IAAMgL,YALnCtL,KAAKS,kBAAoBpB,EAAEmM,YAAY1J,OAU3C,MAAM2J,OAASzL,KAAKY,uBAAuBZ,KAAKM,iBAChD,IAAIoL,cACJ,GAActF,MAAVqF,OAAqB,CACrB,IAAIE,iBAAmBF,OAAOG,WAAU,SAAUC,MAC9C,OAA0C,GAAlCA,KAAKxJ,QAAQ,gBACzB,IAEAqJ,cAAgBD,OAAOG,WAAU,SAAUC,MACvC,OAA0C,GAAlCA,KAAKxJ,QAAQ,mBAAyD,GAA/BwJ,KAAKxJ,QAAQ,aAChE,KAEyB,GAArBsJ,mBAA4C,GAAlBD,eAAuBD,OAAO3J,OAAS,IACjE9B,KAAKU,WAAWV,KAAKM,iBAAmBN,KAAKM,gBAAkB,iBAEvE,CACAjB,EAAE,mBAAmByM,KAAK,IAAM9L,KAAKS,kBAAoB,MAEzC,IAAZ6H,UACAtI,KAAKW,iBAAiBX,KAAKM,iBAAmB,GAC9CgI,SAAU,GAEdjJ,EAAEmM,YAAY9H,MAAK,WACfrE,EAAEqE,KAAKrE,EAAES,MAAMqC,QAAQ,SAAU4G,IAAKvB,OAClCuB,IAAc,SAARA,IAAiB,iBAAmBA,SACe3C,IAArDpG,KAAKW,iBAAiBX,KAAKM,iBAAiByI,MAAuBV,WACnErI,KAAKW,iBAAiBX,KAAKM,iBAAiByI,KAAO,IAEnDT,SAAWD,WAAoF,IAAxEhJ,EAAEwJ,QAAQrB,MAAOxH,KAAKW,iBAAiBX,KAAKM,iBAAiByI,QACpF/I,KAAKW,iBAAiBX,KAAKM,iBAAiByI,KAAKJ,KAAKnB,OACtDc,SAAU,EACVtI,KAAKoB,kBAAmB,EAEhC,GACJ,IACAkH,SAAU,EAEV,IAAIyD,mBAAqB1M,EAAE,2BAC3BA,EAAE0M,oBAAoBrI,MAAK,WACvB,IACIsI,WAAa3M,EAAES,MAAM2D,KAAK,SAG9BpE,EAAE2M,YAAYtI,MAAK,WACfrE,EAAES,MAAMyC,SAASmE,OACjBrH,EAAES,MAAMiH,QAAQ,MAAML,MAC1B,IAGA,IAAIW,mBAAqBtF,KAAKC,MAAMJ,aAAaC,QAAQ7B,KAAKa,kBAC1DyG,iBAA0C,OAAvBD,mBAA8BA,mBAAmBrH,KAAKM,iBAAmB,KAChGjB,EAAE,4BAA8BW,KAAKM,gBAAkBjB,EAAES,MAAMqC,KAAK,MAAQ,gBAAgBuE,OAC5FrH,EAAE2M,YAAYtI,MAAK,WACf,IACI0D,GAAK/H,EAAES,MAAMqC,KAAK,MAClB8J,OAAS5M,EAAES,MAAMoD,KAAK,MACtBgJ,KAAO7M,EAAES,MAAMqC,KAAK,UACpBI,OAASlD,EAAES,MAAMoD,KAAK,MAAMgG,MAAM,IAAK,GAAGC,KAAK,KAC/CgD,aAAe9M,EALPS,MAKgBiH,QAAQ,6BAA6B7D,KAAK,MAClEkJ,gBAAkBD,aAAaE,SAASrM,KAAKM,gBAAkB,kBAC7D,mBAAqBN,KAAKM,gBAAkB,8BAAgC6L,aAAe,KAC3F,mBAAqBnM,KAAKM,gBAAkB,4BAA8B6L,aAAe,KAiB/F,GAfA9M,EAAE+M,iBAAiB1F,OAEf2B,UAAYkD,cAAgBhJ,SAAWvC,KAAKU,WAAWV,KAAKM,mBAC2B,IAApFjB,EAAEwJ,QAAQzB,GAAIpH,KAAKW,iBAAiBX,KAAKM,iBAAiB4L,KAAKI,kBAClEjN,EAdQS,MAcCyC,SAASiE,OAClBc,iBAAmBjI,EAAEkI,KAAKD,kBAAkB,SAAUE,OAClD,OAAOA,QAAUyE,MACrB,IACA5E,mBAAmBrH,KAAKM,iBAAmBgH,iBAC3CjI,EAnBQS,MAmBCiH,QAAQ,MAAMP,OACvBnH,EAAE,mBAAqBW,KAAKM,gBAAkB,qBAAuB6L,aAAe,MAAM3F,OACtFxG,KAAKM,gBAAkB,IAAM4L,OAASlM,KAAKU,WAAWV,KAAKM,kBAC3DjB,EAAE+M,iBAAiB5F,QAGvB6B,UAAYkD,cAAgBhJ,SAAWvC,KAAKU,WAAWV,KAAKM,kBACzDiC,SAAWvC,KAAKM,gBAAkB,IAAMgL,aAC4C,IAApFjM,EAAEwJ,QAAQzB,GAAIpH,KAAKW,iBAAiBX,KAAKM,iBAAiB4L,KAAKI,gBAAwB,CAC1F,IAAIC,SAAWlN,EAAE,2BAA6B8M,aAAe,MAAMhK,KAAK,iBAAmB,QAC1D,IAA7B9C,EA7BIS,MA6BK4I,KAAK,aACdrJ,EA9BIS,MA8BK4I,KAAK,WAAW,GACzBrJ,EAAE,IAAM4M,OAAS,UAAUxF,YAAY,YACvCpH,EAAE,aAAe4M,OAAS,MAAM1J,SAASC,SACzCnD,EAAE,IAAMkN,UAAU7D,KAAK,WAAW,IAEtCpB,iBAAmBjI,EAAEkI,KAAKD,kBAAkB,SAAUE,OAClD,OAAOA,QAAU+E,QACrB,IACAlF,mBAAmBrH,KAAKM,iBAAmBgH,iBAC3CjI,EAvCQS,MAuCCiH,QAAQ,MAAMP,OAEnBxG,KAAKM,gBAAkB,IAAM4L,OAASlM,KAAKU,WAAWV,KAAKM,kBAC3DjB,EAAE+M,iBAAiB5F,MAE3B,CACI6B,WAAakD,cAAgBhJ,SAAWvC,KAAKU,WAAWV,KAAKM,mBAC0B,IAApFjB,EAAEwJ,QAAQzB,GAAIpH,KAAKW,iBAAiBX,KAAKM,iBAAiB4L,KAAKI,kBAClEjN,EA/CQS,MA+CCyC,SAASiE,OAClBc,iBAAmBjI,EAAEkI,KAAKD,kBAAkB,SAAUE,OAClD,OAAOA,QAAUyE,MACrB,IACA5E,mBAAmBrH,KAAKM,iBAAmBgH,iBAC3CjI,EApDQS,MAoDCiH,QAAQ,MAAMP,OACvBnH,EAAE,mBAAqBW,KAAKM,gBAAkB,qBAAuB6L,aAAe,MAAM3F,OACtFxG,KAAKM,gBAAkB,IAAM4L,OAASlM,KAAKU,WAAWV,KAAKM,kBAC3DjB,EAAE+M,iBAAiB5F,OAG/B,IACAxG,KAAKY,uBAAyByG,mBAC9BzF,aAAa6F,QAAQzH,KAAKa,gBAAiBkB,KAAK2F,UAAUC,OAAOC,OAAO,CAAA,EAAIP,sBAE5E,IAAImF,SAAU,EACVC,iBAAmBpN,EAAES,MAAM2D,KAAK,oBAEpCpE,EAAEoN,kBAAkB/I,MAAK,WAIrB,MAH+B,SAA3BrE,EAAES,MAAM4M,IAAI,aACZF,SAAU,IAEP,CACX,IAEKA,QAGDnN,EAzFWS,MAyFC4G,OAFZrH,EAvFWS,MAuFC0G,MAIpB,IAEInH,EAAE,gCAAgCyC,OAAS,EAC3CzC,EAAEE,oBAAoBkH,YAAY,aAElCpH,EAAEE,oBAAoBgH,SAAS,kBAKuBH,IAAtDpG,KAAKY,uBAAuBZ,KAAKM,kBACjCjB,EAAE,kCAAoCW,KAAKM,gBAAkB,MAAM+J,OAAO,YAAY3G,MAAK,WACvF,IAAImH,QAAU/K,KACV6M,IAAMtN,EAAES,MAAMqC,KAAK,YACvB9C,EAAE,oBAAsBW,KAAKM,gBAAkB,IAAMqM,IAAM,YAAYtC,OAAO,YAAY3G,MAAK,WAC3F,IAAIkJ,OAASvN,EAAES,MAAMqC,KAAK,eACtB0K,QAAUxN,EAAEwL,SAASpH,KAAK,kBACzB4G,OAAO,wBAA0BuC,OAAS,MAC1CE,IAAI,WAAWhL,OAChBiL,MAAQ1N,EAAES,MAAM2D,KAAK,WACT,IAAZoJ,QACAxN,EAAES,MAAM0G,QAERuG,MAAMjB,KAAK,IAAMe,QAAU,KAC3BxN,EAAES,MAAM4G,OAEhB,GACJ,KAMRlH,wBAAwBG,UAAUqN,6BAA+B,SAAUC,gBAI9ClL,KAAKC,MAAMJ,aAAaC,QAHtC/B,KAGmDe,mBAAqB,IAHxEf,KAIaQ,iBAAmB,GAC3C,IAAI4M,KALOpN,KAKKc,uBALLd,KAKiCQ,iBAAiB+J,QACzDV,MAAQsD,cAAcZ,SAAS1C,OANxB7J,KASNgD,iBAAgB,GATVhD,KAUNc,uBAVMd,KAUsBQ,iBAAmB4M,KACpDtL,aAAa6F,QAXF3H,KAWee,gBAAiBkB,KAAK2F,UAAUC,OAAOC,OAAO,CAAA,EAX7D9H,KAWsEc,0BAXtEd,KAcNiD,qBAGTvD,wBAAwBG,UAAUwN,2BAA6B,WAC3D,IACIC,gBAAkB/N,EAAE,IADbS,KACwBQ,gBAAkB,qBACrDjB,EAAE+N,iBAAiB1J,MAAK,WACpB,GAA+B,UAA3BrE,EAAES,MAAM4M,IAAI,WAAwB,CACpC,IAAIW,uBAAyBhO,EAAES,MAC3BwN,SAAU,EACdjO,EAAES,MAAM2D,KAAK,MAAMC,MAAK,WACU,aAA1BrE,EAAES,MAAM4M,IAAI,aACZY,SAAU,EAElB,IACIA,QACAD,uBAAuB7G,OAEvB6G,uBAAuB3G,MAE/B,CACJ,KAEJlH,wBAAwBG,UAAU4N,0BAA4B,WAC1D,IACIC,cAAgBnO,EAAE,IADXS,KACsBQ,iBAAiBmD,KAAKlE,2BAA6B,YAAYuC,OAC5FS,OAAS,IAFFzC,KAEaQ,gBAAkB,kCACtCkN,cAAgB,GAChBA,cAAgBA,eAAiB,GAAK,KAAOA,cAC7CnO,EAAEkD,QAAQgE,SAAS,QACnBlH,EAAE,IANKS,KAMMQ,gBAAkB,mBAAmBmN,KAAKD,iBAEvDnO,EAAEkD,QAAQkE,YAAY,QACtBpH,EAAE,IATKS,KASMQ,gBAAkB,mBAAmBmN,KAAK,OAI/DjO,wBAAwBG,UAAU+B,oBAAsB,WACpD,IAAI1B,KAAOF,KAEXT,EAAE,kCAAkC4H,GAAG,SAAS,WAC5C,IAAIG,GAAK/H,EAAES,MAAMoD,KAAK,MACtB7D,EAAE,IAAM+H,GAAK,aAAaU,YAAY,OAC1C,IAEAzI,EAAE,sBAAsB4H,GAAG,SAAS,WAChC5H,EAAE,8BAA8ByI,YAAY,QAC5CzI,EAAE,4BAA4BoH,YAAY,OAC9C,IAEApH,EAAEE,4BAA4B0H,GAAG,SAAS,WAEjCjH,KAAKmB,cAWNnB,KAAKmB,eAAgB,GAVe,OAAhCnB,KAAKY,6BAAyFwF,IAAtDpG,KAAKY,uBAAuBZ,KAAKM,kBACvC,kBAA/BjB,EAAES,MAAMqC,KAAK,gBAChBnC,KAAKY,uBAAuBZ,KAAKM,iBAAmB,IAErB,kBAA/BjB,EAAES,MAAMqC,KAAK,eAAqE,OAAhCnC,KAAKY,yBAC+B,IAAtFvB,EAAEwJ,QAAQxJ,EAAES,MAAMoD,KAAK,MAAOlD,KAAKY,uBAAuBZ,KAAKM,oBAC/DN,KAAKY,uBAAuBZ,KAAKM,iBAAiBqI,KAAKtJ,EAAES,MAAM,GAAGsH,IAClExF,aAAa6F,QAAQzH,KAAKa,gBAAiBkB,KAAK2F,UAAUC,OAAOC,OAAO,CAAA,EAAI5H,KAAKY,4BAOzFZ,KAAKiB,aAAc,CAGvB,IAEA5B,EAAEE,8BAA8B0H,GAAG,SAAS,WACxC,IAAIyG,cAAgBrO,EAAES,MAAMqC,KAAK,kBAC7BwL,SAAWtO,EAAES,MAAMqC,KAAK,eACxBkF,mBAAqBtF,KAAKC,MAAMJ,aAAaC,QAAQ7B,KAAKa,kBAC1DyG,iBAAmBD,mBAAmBrH,KAAKM,iBAC/CgH,iBAAmBjI,EAAEkI,KAAKD,kBAAkB,SAAUE,OAClD,OAAQA,QAAUkG,eAAiBlG,QAAUmG,QACjD,IACAtG,mBAAmBrH,KAAKM,iBAAmBgH,iBAE3CtH,KAAKU,WAAWV,KAAKM,iBAAmB,GACxCsB,aAAa6F,QAAQzH,KAAKa,gBAAiBkB,KAAK2F,UAAUC,OAAOC,OAAO,CAAA,EAAIP,sBAC5E,IAAIe,MAAS/I,EAAES,MAAMyC,SAAS,GAC9BlD,EAAE+I,OAAO3B,YAAY,YACrBzG,KAAKmB,eAAgB,CACzB,IAEA9B,EAAEE,uBAAuB0H,GAAG,SAAS,WACjC,IAAI2G,SAAW,IAAM5N,KAAKM,gBAAkB,kBAAoBjB,EAAES,MAAMqC,KAAK,eACzE0L,YAAc,IAAMxO,EAAES,MAAMqC,KAAK,YACrCnC,KAAKe,kBAAoB1B,EAAES,MAAMqC,KAAK,iBACtC9C,EAAEuO,UAAU3L,SAC2B,IAAnC5C,EAAEwO,aAAanF,KAAK,YACpBrJ,EAAEwO,aAAa5L,QAEdjC,KAAKmB,eACN9B,EAAES,MAAMyG,SAAS,iBAEzB,KAGG/G,uBACX"}
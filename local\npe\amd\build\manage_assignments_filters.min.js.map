{"version": 3, "file": "manage_assignments_filters.min.js", "sources": ["../src/manage_assignments_filters.js"], "sourcesContent": ["/* eslint-disable complexity */\ndefine(['jquery', 'jqueryui', 'local_npe/datepicker', 'local_npe/event_listener'],\n    function ($) {\n\n        var filtersArray = {};\n        let SELECTORS = {\n            ROOT: '.myassignments',\n            HEADERMENU: '.header-menu',\n            HEADERBAR: '#nd_header_wrapper',\n            MEGAMENUMOBILE: '.button-mobile-megamenu',\n            SEARCH: '#search',\n            CLEAN: '.clean',\n            CLEANALL: '.clean-filters',\n            DELETEFILTERS: '.delete-filters',\n            FILTER: '.assignments-filter',\n            FILTERCONTAINER: '.assignments-filter-container',\n            FILTERCONTAINERITEM: '.assignments-filter-container .assignments-filter-selected',\n            FILTERCLOSE: '#filterButton .close',\n            OPACITYLAYER: '.assignments-filter-opacity-layer',\n            CARD: '.card-header h5',\n            FILTERCHECKBOX: '.custom_checkbox input',\n            FILTERZONE: '.filters-zone',\n            FILTERZONEITEM: '.filters-zone .assignments-filter-selected',\n            REMOVEFILTER: '.clean-filter-selected',\n            ACTIVITYLIST: '.activities-list',\n            SHOWMOREITEMS: '.showmoreitems',\n            SHOWLESSITEMS: '.showlessitems',\n            FILTERTITLE: '.filter-title',\n            ACTIVITIESCONTAINER: '.activities-container.ac4filters',\n            SIDEMENUITEM: '.sidebar-menu a',\n            SUBCATEGORYINPUT: '.cc-subcat input',\n            SUBCATEGORY: '.subcat',\n            SUBCATEGORYRESTORE: '.cc-subcat i'\n        };\n\n        /**\n         * @param {String} filter_topics\n         * @param {String} filter_categories\n         */\n        function ManageAssignmentsFilter(filter_topics, filter_categories) {\n            ManageAssignmentsFilter.prototype.constructor = ManageAssignmentsFilter;\n            ManageAssignmentsFilter.prototype.root = null;\n            ManageAssignmentsFilter.prototype.filter_topics = filter_topics;\n            ManageAssignmentsFilter.prototype.filter_categories = filter_categories;\n\n            this.init();\n        }\n\n        ManageAssignmentsFilter.prototype.init = function () {\n            var that = this;\n            this.root = $(SELECTORS.ROOT);\n            this.searchParams = new URLSearchParams(window.location.search);\n            this.sectionSelected = that.defaultSection();\n            this.filtersArray = that.prepareFilters();\n            this.activitiesCounter = 0;\n            this.mainFilter = [];\n            this.filterController = [];\n            this.filterControllerClicks = {};\n            this.sesskeyCourseid = '';\n            let urlSesskey = $('#logout_link').attr('href');\n            if (urlSesskey){\n                this.sessionCheck(urlSesskey);\n\n            }\n            this.categoryDropValue = '';\n            this.categoryDropValueReload = '';\n            this.subCatAdded = false;\n            this.subCatDelete = false;\n            that.subCatDeleted = false;\n\n            that.resetControlller = false;\n            that.toggleDropdownFilter();\n            that.chooseFilters();\n            that.checkFilter();\n            that.openFilters();\n            that.changeSection();\n            this.subFilterManagement();\n\n            this.filtersStorage = localStorage.getItem(this.sesskeyCourseid) ?? '{}';\n\n            if (this.filtersStorage !== null && this.filtersStorage.length !== 0 && this.filtersStorage !== '{}') {\n                that.filterControllerClicks = JSON.parse(this.filtersStorage);\n            }\n\n            // Eliminar filtro de Categoría.\n            $('.filtercategory .clean-filter-selected').click(function () {\n                var categoryid = $(this).data('categoryid');\n                let index = that.filter_categories.indexOf(categoryid);\n                if (index > -1) {\n                    that.filter_categories.splice(index, 1);\n                }\n                $(this).parent().remove();\n                that.filters();\n            });\n\n            // Eliminar filtro de Unidad/Tema.\n            $('.filtertopic .clean-filter-selected').click(function () {\n                let topicid = $(this).data('topicid');\n                let index = that.filter_topics.indexOf(topicid);\n                if (index > -1) {\n                    that.filter_topics.splice(index, 1);\n                }\n                $(this).parent().remove();\n                that.filters();\n            });\n\n            // Eliminar filtro de actividades para asignar.\n            $('.filtertoassign .clean-filter-selected').click(function () {\n                that.filter_toassign = 0;\n                $(this).parent().remove();\n                that.filters();\n            });\n\n            window.onload = () => {\n                if (!that.getFiltersFromURL()) {\n                    that.cleanAllFilters();\n                    that.applyFiltersSaved();\n                }\n            };\n\n            that.manageActivities();\n        };\n        ManageAssignmentsFilter.prototype.sessionCheck = function (urlSesskey) {\n            let sesskey = urlSesskey.substring(urlSesskey.lastIndexOf('=') + 1);\n            let courseid = this.searchParams.get('courseid');\n            this.sesskeyCourseid = sesskey + '-' + courseid;\n            // Si el usuario esta en una nueva sesion o es un usuario diferente, borramos los datos anteriores\n            if (null === localStorage.getItem(this.sesskeyCourseid)) {\n                localStorage.clear();\n            }\n        };\n\n        ManageAssignmentsFilter.prototype.filters = function () {\n            var that = this;\n\n            // Modificación: Limitar el alcance del filtro a la sección actual\n            $('.' + that.sectionSelected).find('.activity-item').each(function () {\n                let topicid = $(this).data('sequenceid');\n                let categoryid = $(this).data('categoryid');\n                let toassign = $(this).data('toassign');\n\n                // Extracción de la información.\n                let bytype = $(this).data('type');\n                let bydelivery = $(this).data('recommendedgroupingid');\n                let bydificulty = $(this).data('difficultylevelid');\n                let bykeyevidence = $(this).data('keyevidenceid');\n                let byblock = $(this).data('blockid');\n                let bycompetence = $(this).data('competenceid');\n                let bycriterion = $(this).data('criterionid');\n                let bytheme = $(this).data('themeid');\n                let bytransversekey = $(this).data('transversekeyid');\n                let byskills = $(this).data('skillsid');\n                let bypedagogicalpurposes = $(this).data('pedagogicalpurposesid');\n                let byassessment = $(this).data('assessmentid');\n                let bylearninglevel = $(this).data('learninglevelid');\n                let bytrackingactivitiesid = $(this).data('trackingactivitiesid');\n                let bychallengesid = $(this).data('challengesid');\n                let byincontextid = $(this).data('incontextid');\n                let bytypeofactivityid = $(this).data('typeofactivityid');\n                let bypresentationresourcesid = $(this).data('presentationresourcesid');\n                let bysubcategoryid = $(this).data('subcategoryid');\n\n\n                // Control.\n                let hidebytype = false;\n                let hidebydelivery = false;\n                let hidebysequence = false; // Añadir la extracción para hacerlo funcionar.\n                let hidebydificulty = false;\n                let hidebykeyevidence = false;\n                let hidebycompetence = false;\n                let hidebycriterion = false;\n                let hidebytheme = false;\n                let hidebytransversekey = false;\n                let hidebyskills = false;\n                let hidebypedagogicalpurposes = false;\n                let hidebyassessment = false;\n                let hidebylearninglevel = false;\n                let hidebyblock = false;\n                let hidebytoassign = (that.filter_toassign && !toassign);\n                let hidebytrackingactivitiesid = false;\n                let hidebychallengesid = false;\n                let hidebyincontextid = false;\n                let hidetypeofactivityid = false;\n                let hidepresentationresourcesid = false;\n                let hidesubcategorysid = false;\n\n                let hidebytopic = ((that.filter_topics.length > 0 && that.filter_topics.indexOf(topicid) === -1) ||\n                    (that.filtersArray['sequenceId'] !== undefined\n                        && that.filtersArray['sequenceId'].length > 0\n                        && that.filtersArray['sequenceId'].indexOf(topicid) === -1));\n                let hidebycategory = (that.filter_categories.length > 0 && that.filter_categories.indexOf(categoryid) === -1)\n                    || (that.filtersArray['categoryId'] !== undefined\n                        && that.filtersArray['categoryId'].length > 0\n                        && that.filtersArray['categoryId'].indexOf(categoryid) === -1);\n\n                if (that.filtersArray.hasOwnProperty('activityTypeId')) {\n                    hidebytype = (that.filtersArray['activityTypeId'].length > 0\n                        && that.filtersArray['activityTypeId'].indexOf(bytype) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('difficultyLevelId')) {\n                    hidebydificulty = (that.filtersArray['difficultyLevelId'].length > 0\n                        && that.filtersArray['difficultyLevelId'].indexOf(bydificulty) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('recommendedGroupingId')) {\n                    hidebydelivery = (that.filtersArray['recommendedGroupingId'].length > 0\n                        && that.filtersArray['recommendedGroupingId'].indexOf(bydelivery) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('keyEvidenceId')) {\n                    hidebykeyevidence = (that.filtersArray['keyEvidenceId'].length > 0\n                        && that.filtersArray['keyEvidenceId'].indexOf(bykeyevidence) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('blockId')) {\n                    hidebyblock = (that.filtersArray['blockId'].length > 0\n                        && that.filtersArray['blockId'].indexOf(byblock) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('competenceId')) {\n                    hidebycompetence = (that.filtersArray['competenceId'].length > 0\n                        && that.filtersArray['competenceId'].indexOf(bycompetence) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('criterionId')) {\n                    hidebycriterion = (that.filtersArray['criterionId'].length > 0\n                        && that.filtersArray['criterionId'].indexOf(bycriterion) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('themeId')) {\n                    hidebytheme = (that.filtersArray['themeId'].length > 0\n                        && that.filtersArray['themeId'].indexOf(bytheme) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('transverseKeyId')) {\n                    hidebytransversekey = (that.filtersArray['transverseKeyId'].length > 0\n                        && that.filtersArray['transverseKeyId'].indexOf(bytransversekey) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('skillsId')) {\n                    hidebyskills = (that.filtersArray['skillsId'].length > 0\n                        && that.filtersArray['skillsId'].indexOf(byskills) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('pedagogicalPurposesId')) {\n                    hidebypedagogicalpurposes = (that.filtersArray['pedagogicalPurposesId'].length > 0\n                        && that.filtersArray['pedagogicalPurposesId'].indexOf(bypedagogicalpurposes) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('assessmentId')) {\n                    hidebyassessment = (that.filtersArray['assessmentId'].length > 0\n                        && that.filtersArray['assessmentId'].indexOf(byassessment) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('learningLevelId')) {\n                    hidebylearninglevel = (that.filtersArray['learningLevelId'].length > 0\n                        && that.filtersArray['learningLevelId'].indexOf(bylearninglevel) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('trackingActivitiesId')) {\n                    hidebytrackingactivitiesid = (that.filtersArray['trackingActivitiesId'].length > 0\n                        && that.filtersArray['trackingActivitiesId'].indexOf(bytrackingactivitiesid) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('challengesId')) {\n                    hidebychallengesid = (that.filtersArray['challengesId'].length > 0\n                        && that.filtersArray['challengesId'].indexOf(bychallengesid) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('inContextId')) {\n                    hidebyincontextid = (that.filtersArray['inContextId'].length > 0\n                        && that.filtersArray['inContextId'].indexOf(byincontextid) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('typeOfActivityId')) {\n                    hidetypeofactivityid = (that.filtersArray['typeOfActivityId'].length > 0\n                        && that.filtersArray['typeOfActivityId'].indexOf(bytypeofactivityid) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('presentationResourcesId')) {\n                    hidepresentationresourcesid = (that.filtersArray['presentationResourcesId'].length > 0\n                        && that.filtersArray['presentationResourcesId'].indexOf(bypresentationresourcesid) === -1);\n                }\n\n                if (that.filtersArray.hasOwnProperty('subcategoryId')) {\n                    hidesubcategorysid = (that.filtersArray['subcategoryId'].length > 0\n                        && that.filtersArray['subcategoryId'].indexOf(bysubcategoryid) === -1);\n                }\n\n                if (hidebytopic || hidebycategory || hidebytoassign || hidebyblock || hidebycompetence || hidebycriterion ||\n                    hidebytheme || hidebytransversekey || hidebytype || hidebydelivery || hidebysequence || hidebydificulty ||\n                    hidebykeyevidence || hidebyskills || hidebypedagogicalpurposes || hidebyassessment || hidebylearninglevel ||\n                    hidebytrackingactivitiesid || hidebychallengesid || hidebyincontextid || hidetypeofactivityid ||\n                    hidepresentationresourcesid || hidesubcategorysid) {\n                    $(this).addClass('hidden');\n                    $(this).hide();\n                } else {\n                    $(this).removeClass('hidden');\n                    $(this).show();\n                }\n            });\n            $('#search').keyup();\n        };\n\n        ManageAssignmentsFilter.prototype.applyColorButton = function (data, add) {\n            var dataParent = $(data).closest('.dropdown');\n            if (add) {\n                $(dataParent).addClass('selected');\n                $(dataParent).find('.dropbtn').addClass('bgf-c');\n            } else {\n                $(dataParent).removeClass('selected');\n                $(dataParent).find('.dropbtn').removeClass('bgf-c');\n            }\n        };\n\n        ManageAssignmentsFilter.prototype.toggleDropdownFilter = function () {\n            var openId = '';\n            var that = this;\n            $('.dropbtn i').on('click', function (e) {\n                if ($(this).parent().hasClass('bgf-c')) {\n                    $(\"ul[data-id =\" + $(this).parent().attr('id') + \"] \" + SELECTORS.FILTERCHECKBOX + ':checked')\n                        .each(function () {\n                            // Eliminamos los filtros para esa categoria.\n                            var id = $(this).attr('id');\n                            var filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid));\n                            var transitionalVars = filtersFromStorage[that.sectionSelected];\n                            transitionalVars = $.grep(transitionalVars, function (value) {\n                                return value !== id;\n                            });\n                            filtersFromStorage[that.sectionSelected] = transitionalVars;\n                            localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, filtersFromStorage)));\n                            $(this).click();\n                            $(\"[data-id =\" + $(this).attr('id') + \"]\").parent().remove();\n                        });\n                    $(this).parent().removeClass('bgf-c');\n                    e.preventDefault();\n                    return; // Do nothing\n                }\n            });\n\n            $('.dropbtn').on('click', function (e) {\n                $(this).toggleClass('open');\n                var id = $(this).attr('id');\n\n                if (openId && openId !== $(this).attr('id')) {\n                    $(\"ul[data-id =\" + openId + \"]\").hide();\n                    $('#' + openId).removeClass('open');\n                }\n                openId = id;\n                $(\"ul[data-id =\" + id + \"]\").toggle();\n                e.stopPropagation();\n            });\n        };\n\n        ManageAssignmentsFilter.prototype.checkFilter = function () {\n            var that = this;\n\n            if (that.filtersStorage) {\n                that.filterControllerClicks[that.sectionSelected] = JSON.parse(that.filtersStorage);\n            }\n\n            $(SELECTORS.FILTERCHECKBOX).on('click', function () {\n                var id = $(this).data('id');\n                var uniquekey = $(this).attr('id');\n                var uniquekeySub = '';\n                var filtertype = $(this).data('filtertype');\n                var label = $(this).data('label');\n                var filtered = true;\n                var checked = false;\n                var idselected = '#' + $(this).attr('id') + '-label';\n                var idselectedsub = '#' + $(this).attr('id') + '-sub';\n                var filterSelector = that.sectionSelected + '-' + filtertype;\n\n                that.applyColorButton(idselected, true);\n\n                if ($(this).prop('checked')) {\n                    that.filtersArray[filtertype].push(id);\n                    label = $(this).data('label');\n\n                    if (that.categoryDropValueReload && filtertype === 'subcategoryId') {\n                        uniquekeySub = $('#' + that.categoryDropValueReload).closest('li').data('filtersuddrop');\n                    }\n\n                    // No añadimos el tab de subcategorias y tampoco si ya existe\n                    if (filtertype !== 'subcategoryId' &&\n                        $('.assignments-filter-container').find(`[data-id='${uniquekey}']`).length === 0\n                    ) {\n                        $(SELECTORS.FILTERCONTAINER).prepend(\n                            '<div class=\"filtercategory assignments-filter-selected border-c\" data-checkid=\"' + id + '\">' +\n                            '<div class=\"filter-text fc-c\">' + label + '</div>' +\n                            '<div class=\"clean-filter-selected bg-c\" data-label=\"' + label + '\" data-category=\"' + id + '\" ' +\n                            'data-id=\"' + uniquekey + '\" ' + 'data-idSub=\"' + uniquekeySub + '\" ' + 'data-filtertype=\"' +\n                            filtertype + '\" ' + 'data-filterselector=\"' + filterSelector + '\"></div>' + '</div>'\n                        );\n                    }\n\n                    $(SELECTORS.DELETEFILTERS).addClass('show');\n                    checked = true;\n\n                    // Rellenamos los clicks\n                    // Solo aplicamos en este punto el tag si no es una subacategoria\n                    if (!that.subCatAdded) {\n                        if (that.filterControllerClicks !== null\n                            && that.filterControllerClicks[that.sectionSelected] === undefined) {\n                            that.filterControllerClicks[that.sectionSelected] = [];\n                        }\n                        if (that.filterControllerClicks !== null &&\n                            $.inArray($(this).attr('id'), that.filterControllerClicks[that.sectionSelected]) === -1) {\n                            that.filterControllerClicks[that.sectionSelected].push($(this).attr('id'));\n                            localStorage.setItem(that.sesskeyCourseid,\n                                JSON.stringify(Object.assign({}, that.filterControllerClicks)));\n                        }\n                    }\n                    // Resetamos la variable con control de subfiltro para que siga el flujo normal\n                    that.subCatAdded = false;\n\n                    $('#' + $(this).attr('id') + '-label').addClass('selected');\n                    $(idselectedsub).prop('checked', true);\n                } else {\n                    let removeItem = $(this).attr('id');\n                    if (that.subCatDelete) {\n                        removeItem = that.categoryDropValueReload ?? that.categoryDropValue + '-subcategoryId-' + id + '-sub';\n                        that.subCatDelete = false;\n                    }\n                    that.filterControllerClicks[that.sectionSelected] = $.grep(\n                        that.filterControllerClicks[that.sectionSelected]\n                        , function (value) {\n                            return value !== removeItem;\n                        });\n                    let key = that.filtersArray[filtertype].indexOf(id);\n                    that.filtersArray[filtertype].splice(key, 1);\n                    $(SELECTORS.FILTERZONE).find(`[data-checkid='${id}']`).remove();\n                    $(SELECTORS.FILTERCONTAINER).find(`[data-checkid='${id}']`).remove();\n\n                    if ($(SELECTORS.FILTERCONTAINERITEM).length < 1) {\n                        $(SELECTORS.DELETEFILTERS).removeClass('show');\n                        filtered = false;\n                        filtertype = null;\n                        that.applyColorButton(idselected, false);\n                    }\n\n                    // Guardamos los nuevos datos en el storage\n                    var idAlt = $(this).data('filter') === 'subcategoryId' ? that.categoryDropValueReload : $(this).attr('id');\n\n                    var filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid));\n                    var transitionalVars = filtersFromStorage[that.sectionSelected];\n                    transitionalVars = $.grep(transitionalVars, function (value) {\n                        return value !== idAlt;\n                    });\n                    filtersFromStorage[that.sectionSelected] = transitionalVars;\n\n                    that.mainFilter[that.sectionSelected] = '';\n                    localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, filtersFromStorage)));\n\n                    // Cambiamos el mainFilter en caso de que sea necesario\n                    if (that.filterControllerClicks !== undefined\n                        && that.filterControllerClicks[that.sectionSelected] !== undefined\n                        && that.filterControllerClicks[that.sectionSelected][0] !== undefined) {\n                        var nextMainFilter = that.filterControllerClicks[that.sectionSelected][0].split('-', 2).join('-');\n                        that.mainFilter[that.sectionSelected] = nextMainFilter;\n                    }\n                    if ($(this).data('filter') === 'categoryId'\n                        && $('.subcat[data-category~=\"' + $(this).attr('id') +'\"]').siblings('input').next().hasClass('selected')) {\n                            that.cleanAllFilters(true);\n                            $('.dropbtn').removeClass('bgf-c');\n                            $('.subcat').removeClass('selected bgf-c');\n                            $('#search').click();\n                    }\n\n                    $(idselected).toggleClass('selected');\n                }\n\n                if (that.filterControllerClicks[that.sectionSelected] === '') {\n                    that.mainFilter[that.sectionSelected] = '';\n                }\n\n                $('.subcat').show();\n                that.filters();\n                that.manageActivities(filtered, filtertype, checked);\n            });\n\n            $(document).on('click', SELECTORS.REMOVEFILTER, function () {\n                if ($(this).data('filtertype') === 'categoryId'\n                    && $(SELECTORS.FILTERCONTAINER).find(`[data-filtertype='subcategoryId']`).length > 0) {\n                    var restore = $(this).data('id');\n                    $(\".restore[data-categoryid-restore='\" + restore + \"']\").click();\n                    return;\n                }\n\n\n                // Al eliminar un filtro, recuperamos datos de Storage y los modificamos con la nueva selección.\n                var id = $(this).data('id');\n                var idSub = $(this).data('idsub');\n                var filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid));\n                var transitionalVars = filtersFromStorage[that.sectionSelected];\n                transitionalVars = $.grep(transitionalVars, function (value) {\n                    return value !== id;\n                });\n                filtersFromStorage[that.sectionSelected] = transitionalVars;\n                localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, filtersFromStorage)));\n\n                if (idSub) {\n                    that.subCatDelete = true;\n                    // Desmarcamos los checks\n                    $('#' + idSub + '-sub').click();\n                } else {\n                    // Desmarcamos los checks\n                    $('#' + id).click();\n                }\n\n                if ($(SELECTORS.FILTERCONTAINERITEM).length < 1) {\n                    $(SELECTORS.DELETEFILTERS).removeClass('show');\n                }\n\n                // Comprobamos si ese tipo de filtro está vacio\n                var filterSelector = 'heading-' + $(this).data('filterselector');\n                var filtercounter = $('#' + $(this).data('filterselector')).find('.custom_checkbox span.selected').length;\n                if (filtercounter < 1) {\n                    $('#' + filterSelector).removeClass('bgf-c');\n                }\n            });\n\n            $(SELECTORS.CLEANALL).on('click', function () {\n                that.cleanAllFilters(true);\n                $('.dropbtn').removeClass('bgf-c');\n                $('.subcat').removeClass('selected bgf-c');\n                $('#search').click();\n            });\n        };\n\n        ManageAssignmentsFilter.prototype.cleanAllFilters = function (cleanClicks = false) {\n            var that = this;\n            $(SELECTORS.SEARCH).val('');\n            $(SELECTORS.FILTERCHECKBOX).each(function () {\n                $(this).prop('checked', false);\n                var idselected = '#' + $(this).attr('id') + '-label';\n                $(idselected).removeClass('selected');\n            });\n\n            $(SELECTORS.FILTERCONTAINERITEM).each(function () {\n                $(this).remove();\n            });\n\n            $(SELECTORS.FILTERZONEITEM).each(function () {\n                $(this).remove();\n            });\n\n            $(SELECTORS.DELETEFILTERS).removeClass('show');\n            that.setToDefault();\n            that.manageActivities();\n\n            if (cleanClicks) {\n                that.mainFilter[that.sectionSelected] = '';\n                // Al eliminar todos los filtros, recuperamos datos de Storage y los vaciamos.\n                that.cleanStorageForCurrentSection();\n                that.filterControllerClicks[that.sectionSelected] = [];\n                $('.activities-subcats').show();\n                $('.subcat').show();\n                $('.collapse-units-dropdown .topic-item').show();\n                $('.subcats-dropdown .list-header').removeClass('bg-d');\n                $('#categories-' + that.sectionSelected).find('li.bgf-c.active').removeClass('bgf-c active');\n            }\n        };\n\n        ManageAssignmentsFilter.prototype.cleanStorageForCurrentSection = function () {\n            var that = this;\n            var filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid)) ?? [];\n            filtersFromStorage[that.sectionSelected] = [];\n            that.filterControllerClicks[that.sectionSelected] = [];\n            localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, that.filterControllerClicks)));\n        };\n\n        ManageAssignmentsFilter.prototype.openFilters = function () {\n            $(SELECTORS.FILTER).on('click', function () {\n                $(SELECTORS.OPACITYLAYER).toggle();\n                $(SELECTORS.HEADERMENU).addClass('z0');\n                $(SELECTORS.HEADERBAR).addClass('z0');\n                $(SELECTORS.MEGAMENUMOBILE).addClass('z0');\n            });\n            $(SELECTORS.CARD).on('click', function () {\n                $(this).find('.rotate').toggleClass(\"down\");\n                $(this).toggleClass('bold');\n            });\n            $(SELECTORS.FILTERCLOSE).on('click', function () {\n                $(SELECTORS.OPACITYLAYER).toggle();\n                $(SELECTORS.HEADERMENU).removeClass('z0');\n                $(SELECTORS.HEADERBAR).removeClass('z0');\n                $(SELECTORS.MEGAMENUMOBILE).removeClass('z0');\n            });\n            $('body').click(function (event) {\n                if ($(event.target).attr('id') === 'closeSubcats' || $(event.target).hasClass('results')) {\n                    $('.subcats-dropdown i.rotate').toggleClass('down');\n                    $('.collapse-units-dropdown').removeClass('show');\n                }\n\n                if ($('.collapse-units-dropdown').is(':visible')) {\n                    var container = Array.from(document.querySelectorAll('.subcatsdropdown')).filter(s =>\n                        window.getComputedStyle(s).getPropertyValue('display') != 'none');\n                    container = $(container).find('.subcats-dropdown')[0];\n\n                    var containerSub = document.querySelectorAll('.collapse-units-dropdown.collapse.show .list-body')[0];\n                    document.addEventListener('click', function (event) {\n                        if (container\n                            && container !== event.target\n                            && !container.contains(event.target)\n                            && $(containerSub).is(':visible')) {\n                            $('.subcats-dropdown i.rotate').toggleClass('down');\n                            $('.collapse-units-dropdown').removeClass('show');\n                        }\n                    });\n                }\n            });\n        };\n\n        ManageAssignmentsFilter.prototype.setToDefault = function () {\n            var that = this;\n            $('.activity-item').each(function () {\n                $(this).show();\n                $(this).removeClass('hidden').removeClass('filtered');\n            });\n            $(SELECTORS.SEARCH).val('');\n            that.prepareFilters();\n            var isVisible = 0;\n            $('.activity-item').each(function () {\n                if ($(this).is(\":visible\")) {\n                    isVisible++;\n                }\n            });\n            if (isVisible === 0 && !($('.assignments-filter-selected').length > 0)) {\n                $('.noresultssearch').hide();\n                $('.noresultsfilter').hide();\n                $('.noresultsassignments').show();\n            } else if (isVisible === 0 && ($('.assignments-filter-selected').length > 0)) {\n                $('.noresultssearch').hide();\n                $('.noresultsfilter').show();\n                $('.noresultsassignments').hide();\n            } else if (isVisible !== 0) {\n                $('.noresultsassignments').hide();\n                $('.noresultsfilter').hide();\n                $('.noresultssearch').hide();\n            }\n            that.prepareFilters();\n        };\n\n        ManageAssignmentsFilter.prototype.prepareFilters = function () {\n            $(SELECTORS.FILTERTITLE).each(function () {\n                var filter = $(this).find('.custom_checkbox input');\n                filter.each(function () {\n                    filtersArray[$(this).data('filtertype')] = [];\n                });\n            });\n            return filtersArray;\n        };\n\n        ManageAssignmentsFilter.prototype.chooseFilters = function () {\n            var that = this;\n            let section;\n\n            // Initial filters.\n            if ($('.sidebar-menu').is(\":hidden\")) {\n                let searchParams = that.searchParams;\n                section = searchParams.get('section');\n            } else {\n                section = $('.icon_menu.section.active a').data('section');\n            }\n\n            let visibleFilterbutton = false;\n            $('.modal-filter').each(function () {\n                if ($(this).data('section') === section) {\n                    $(this).removeClass('hidden');\n                    if ($(this).children().hasClass('dropdown')) {\n                        visibleFilterbutton = true;\n                    }\n                }\n            });\n            if (visibleFilterbutton == false) {\n                $(SELECTORS.FILTER).hide();\n            }\n\n            // Change filters when section has changed.\n            $('.icon_menu.section').on('click', function () {\n                // Removed filters applied.\n                that.cleanAllFilters(false);\n                var newSection = $(this).find('a').data('section');\n                $('.modal-filter').each(function () {\n                    if ($(this).data('section') === newSection) {\n                        $(this).removeClass('hidden');\n                    } else if (!$(this).hasClass('hidden')) {\n                        $(this).addClass('hidden');\n                    }\n                });\n            });\n\n        };\n\n        ManageAssignmentsFilter.prototype.getFiltersFromURL = function () {\n            var that = this;\n            let urlParams = that.searchParams;\n            let section = urlParams.get('section');\n            let sequenceId = urlParams.get('topicid');\n            let filters = JSON.parse($('#filterbyitem').val());\n\n            if (sequenceId !== null || filters.length > 0) {\n                that.cleanStorageForCurrentSection();\n            } else {\n                return false;\n            }\n            if (sequenceId !== null) {\n                let filterSeq = section + '-sequenceId-' + sequenceId;\n                that.filterControllerClicks[that.sectionSelected].push(filterSeq);\n            }\n\n            if (filters.length > 0) {\n                $(filters).each(function () {\n                    let filter = section + '-' + this.key + '-' + this.value;\n                    that.filterControllerClicks[that.sectionSelected].push(filter);\n                });\n            }\n\n            // Save filters to storage\n            localStorage.setItem(that.sesskeyCourseid,\n                JSON.stringify(Object.assign({}, that.filterControllerClicks)));\n        };\n\n        ManageAssignmentsFilter.prototype.defaultSection = function () {\n            var that = this;\n            var section;\n            if (!this.sectionSelected) {\n                let urlParams = that.searchParams;\n                section = urlParams.get('section') ?? 'actAndRec';\n            }\n            return section;\n        };\n\n        ManageAssignmentsFilter.prototype.changeSection = function () {\n            var that = this;\n            $(SELECTORS.SIDEMENUITEM).on('click', function () {\n                that.sectionSelected = $(this).data('section');\n                that.resetControlller = false;\n                that.filterControllerClicks = that.filterControllerClicks ?? '{}';\n\n                if (that.filterControllerClicks[that.sectionSelected] !== undefined) {\n                    that.applyFiltersSaved();\n                }\n                that.filterControllerClicks[that.sectionSelected] = [];\n                if ($.isEmptyObject(that.filtersStorage)) {\n                    localStorage.setItem(that.sesskeyCourseid,\n                        JSON.stringify(Object.assign({}, that.filterControllerClicks[that.sectionSelected])));\n                }\n            });\n        };\n\n        ManageAssignmentsFilter.prototype.applyFiltersSaved = function () {\n            var that = this;\n            $(document).ready(function () {\n                if (that.filterControllerClicks !== null) {\n                    $('.subcat').removeClass('selected bgf-c');\n                    $('.dropbtn').removeClass('bgf-c');\n                    $.each(that.filterControllerClicks[that.sectionSelected], function (key, value) {\n                        $('#' + value).prop('checked', false).click();\n                        // Con id, buscamos el data-filter y lo pulsamos para aplicar las clases en la subcategoría\n                        $('.cc-subcat [data-filter~=\"' + value + '\"]').prop('checked', true).next().addClass('selected bgf-c');\n                    });\n                }\n            });\n        };\n\n        ManageAssignmentsFilter.prototype.manageActivities = function (\n            filtered = false,\n            filterType = null,\n            checkedUpper = null) {\n            var that = this;\n            var activities = $('.activities-list[data-section=\"' + this.sectionSelected + '\"] .activity-item');\n            var checked = checkedUpper;\n            that.filterController[that.sectionSelected] = [];\n\n            // Calculamos las actividades visibles;\n            if (!filtered) {\n                that.activitiesCounter = $(activities).length;\n            } else {\n                that.activitiesCounter = $(activities).filter(\":visible\").length;\n                activities = $(activities).filter(\":visible\");\n                that.mainFilter[that.sectionSelected] = !that.mainFilter[that.sectionSelected]\n                    ? that.sectionSelected + '-' + filterType\n                    : that.mainFilter[that.sectionSelected];\n            }\n\n            // Si el filtro primario es category y el secundario subcategory, hacemos el cambio.\n            const clicks = that.filterControllerClicks[that.sectionSelected];\n            let currentFilter;\n            if (clicks != undefined) {\n                var currentFilterCat = clicks.findIndex(function (item) {\n                    return (item.indexOf('subcategoryId') != -1);\n                });\n\n                currentFilter = clicks.findIndex(function (item) {\n                    return (item.indexOf('subcategoryId') == -1 && item.indexOf('categoryId') == -1);\n                });\n\n                if (currentFilterCat != -1 && currentFilter == -1 && clicks.length > 0) {\n                    that.mainFilter[that.sectionSelected] = that.sectionSelected + '-subcategoryId';\n                }\n            }\n            $('#filter-counter').html('(' + that.activitiesCounter + ')');\n\n            if (checked === false) {\n                that.filterController[that.sectionSelected] = [];\n                checked = true;\n            }\n            $(activities).each(function () {\n                $.each($(this).data(), function (key, value) {\n                    key = key === 'type' ? 'activitytypeid' : key;\n                    if (that.filterController[that.sectionSelected][key] === undefined || !filtered) {\n                        that.filterController[that.sectionSelected][key] = [];\n                    }\n                    if (checked && filtered && $.inArray(value, that.filterController[that.sectionSelected][key]) === -1) {\n                        that.filterController[that.sectionSelected][key].push(value);\n                        checked = true;\n                        that.resetControlller = true;\n                    }\n                });\n            });\n            checked = false;\n            // Construimos el objeto de controls de filtros\n            var filterTypeDropdown = $('.modal-filter .dropdown');\n            $(filterTypeDropdown).each(function () {\n                var dropdown = this;\n                var checkboxes = $(this).find('input');\n\n                // Primero hago todas visibles\n                $(checkboxes).each(function () {\n                    $(this).parent().show();\n                    $(this).closest('li').show();\n                });\n\n                // Ahora compruebo cual tengo que ocultar\n                let filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid));\n                let transitionalVars = filtersFromStorage !== null ? filtersFromStorage[that.sectionSelected] : '{}';\n                $('#collapse-units-dropdown-' + that.sectionSelected + $(this).data('id') + ' .topic-item').show();\n                $(checkboxes).each(function () {\n                    var check = this;\n                    var id = $(this).data('id'); // Value\n                    var fullId = $(this).attr('id');\n                    var type = $(this).data('filter'); // Key\n                    var parent = $(this).attr('id').split('-', 2).join('-');\n                    var subCatToHide = $(check).closest('li .custom_checkbox input').attr('id');\n                    var previoussuddrop = subCatToHide.includes(that.sectionSelected + '-subcategoryId')\n                        ? '[data-section~=\"' + that.sectionSelected + '\"] [data-previoussuddrop~=\"' + subCatToHide + '\"]'\n                        : '[data-section~=\"' + that.sectionSelected + '\"] [data-filtersuddrop~=\"' + subCatToHide + '\"]';\n\n                    $(previoussuddrop).show();\n\n                    if (filtered && checkedUpper && parent !== that.mainFilter[that.sectionSelected]\n                        && $.inArray(id, that.filterController[that.sectionSelected][type.toLowerCase()]) === -1) {\n                        $(check).parent().hide();\n                        transitionalVars = $.grep(transitionalVars, function (value) {\n                            return value !== fullId;\n                        });\n                        filtersFromStorage[that.sectionSelected] = transitionalVars;\n                        $(check).closest('li').hide();\n                        $('[data-section~=\"' + that.sectionSelected + '\"] [data-filter~=\"' + subCatToHide + '\"]').hide();\n                        if (that.sectionSelected + '-' + type !== that.mainFilter[that.sectionSelected]) {\n                            $(previoussuddrop).hide();\n                        }\n                    }\n                    if (filtered && checkedUpper && parent === that.mainFilter[that.sectionSelected]\n                        && parent !== that.sectionSelected + '-' + filterType\n                        && $.inArray(id, that.filterController[that.sectionSelected][type.toLowerCase()]) === -1) {\n                        var toDelete = $('[data-previoussuddrop~=\"' + subCatToHide + '\"]').data('filtersuddrop') + '-sub';\n                        if ($(check).prop('checked') === true) {\n                            $(check).prop('checked', false);\n                            $('#' + fullId + '-label').removeClass('selected');\n                            $('[data-id=\"' + fullId + '\"]').parent().remove();\n                            $('#' + toDelete).prop('checked', false);\n                        }\n                        transitionalVars = $.grep(transitionalVars, function (value) {\n                            return value !== toDelete;\n                        });\n                        filtersFromStorage[that.sectionSelected] = transitionalVars;\n                        $(check).closest('li').hide();\n\n                        if (that.sectionSelected + '-' + type !== that.mainFilter[that.sectionSelected]) {\n                            $(previoussuddrop).hide();\n                        }\n                    }\n                    if (filtered && !checkedUpper && parent !== that.mainFilter[that.sectionSelected]\n                        && $.inArray(id, that.filterController[that.sectionSelected][type.toLowerCase()]) === -1) {\n                        $(check).parent().hide();\n                        transitionalVars = $.grep(transitionalVars, function (value) {\n                            return value !== fullId;\n                        });\n                        filtersFromStorage[that.sectionSelected] = transitionalVars;\n                        $(check).closest('li').hide();\n                        $('[data-section~=\"' + that.sectionSelected + '\"] [data-filter~=\"' + subCatToHide + '\"]').hide();\n                        if (that.sectionSelected + '-' + type !== that.mainFilter[that.sectionSelected]) {\n                            $(previoussuddrop).hide();\n                        }\n                    }\n                });\n                that.filterControllerClicks = filtersFromStorage;\n                localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, filtersFromStorage)));\n\n                var visible = false;\n                var customCheckboxes = $(this).find('.custom_checkbox');\n\n                $(customCheckboxes).each(function () {\n                    if ($(this).css('display') !== 'none') {\n                        visible = true;\n                    }\n                    return true;\n                });\n\n                if (!visible) {\n                    $(dropdown).hide();\n                } else {\n                    $(dropdown).show();\n                }\n            });\n\n            if ($('.assignments-filter-selected').length > 0) {\n                $(SELECTORS.CLEANALL).removeClass('nofilters');\n            } else {\n                $(SELECTORS.CLEANALL).addClass('nofilters');\n            }\n\n            // Control de las subcategorias a la hora de mostrarla en las diferentes secciones.\n            // Solo haremos esta comprobacion en caso de que haya algun filtro aplicado.\n            if (that.filterControllerClicks[that.sectionSelected] !== undefined) {\n                $('.activities-list[data-section=\"' + that.sectionSelected + '\"]').filter(':visible').each(function () {\n                    var section = this;\n                    var cat = $(this).data('category');\n                    $('#subcatsdropdown-' + that.sectionSelected + '-' + cat + ' .subcat').filter(':visible').each(function () {\n                        var subcat = $(this).data('subcategory');\n                        var counter = $(section).find('.activity-item')\n                            .filter('[data-subcategoryid=\"' + subcat + '\"]')\n                            .not('.hidden').length;\n                        var count = $(this).find('counter');\n                        if (counter === 0) {\n                            $(this).hide();\n                        } else {\n                            count.html('(' + counter + ')');\n                            $(this).show();\n                        }\n                    });\n                });\n            }\n        };\n\n        // Gestión de subfilters\n\n        ManageAssignmentsFilter.prototype.cleanStorageForSubcategories = function (subcategories) {\n            var that = this;\n\n            // Se quitan de los clicks actuales, los filtros relacionados con las subcategorias.\n            var filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid)) ?? [];\n            filtersFromStorage[that.sectionSelected] = [];\n            var temp = that.filterControllerClicks[that.sectionSelected].filter(\n                val => !subcategories.includes(val)\n            );\n            // Se hace limpieza total de los filtros\n            that.cleanAllFilters(true);\n            that.filterControllerClicks[that.sectionSelected] = temp;\n            localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, that.filterControllerClicks)));\n\n            // Se aplican los filtros no relacionados con las subcategorias.\n            that.applyFiltersSaved();\n        };\n\n        ManageAssignmentsFilter.prototype.checkSubcategoriesDropdown = function () {\n            var that = this;\n            var subcatsdropdown = $('.' + that.sectionSelected + ' .subcatsdropdown');\n            $(subcatsdropdown).each(function () {\n                if ($(this).css('display') === 'block') {\n                    var subcatsdropdownVisible = $(this);\n                    var checker = true;\n                    $(this).find('li').each(function () {\n                        if ($(this).css('display') == 'list-item') {\n                            checker = false;\n                        }\n                    });\n                    if (checker) {\n                        subcatsdropdownVisible.hide();\n                    } else {\n                        subcatsdropdownVisible.show();\n                    }\n                }\n            });\n        };\n        ManageAssignmentsFilter.prototype.showSubcategoriesDropdown = function () {\n            var that = this;\n            var filtercounter = $('.' + that.sectionSelected).find(SELECTORS.SUBCATEGORYINPUT + ':checked').length;\n            var parent = '.' + that.sectionSelected + ' .subcats-dropdown .list-header';\n            if (filtercounter > 0) {\n                filtercounter = filtercounter >= 10 ? '+9' : filtercounter;\n                $(parent).addClass('bg-d');\n                $('.' + that.sectionSelected + ' .filter-active').text(filtercounter);\n            } else {\n                $(parent).removeClass('bg-d');\n                $('.' + that.sectionSelected + ' .filter-active').text('x');\n            }\n        };\n\n        ManageAssignmentsFilter.prototype.subFilterManagement = function () {\n            var that = this;\n\n            $('.subcats-dropdown .list-header').on('click', function () {\n                var id = $(this).attr('id');\n                $('#' + id + ' i.rotate').toggleClass('down');\n            });\n\n            $('.controls .results').on('click', function () {\n                $('.subcats-dropdown i.rotate').toggleClass('down');\n                $('#collapse-units-dropdown').removeClass('show');\n            });\n\n            $(SELECTORS.SUBCATEGORYINPUT).on('click', function () {\n                //Rellenamos los clicks\n                if (!that.subCatDeleted) {\n                    if (that.filterControllerClicks !== null && that.filterControllerClicks[that.sectionSelected] === undefined\n                        && $(this).data('filtertype') === 'subcategoryId') {\n                        that.filterControllerClicks[that.sectionSelected] = [];\n                    }\n                    if ($(this).data('filtertype') === 'subcategoryId' && that.filterControllerClicks !== null &&\n                        $.inArray($(this).attr('id'), that.filterControllerClicks[that.sectionSelected]) === -1) {\n                        that.filterControllerClicks[that.sectionSelected].push($(this)[0].id);\n                        localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, that.filterControllerClicks)));\n                    }\n                } else {\n                    that.subCatDeleted = false;\n                }\n\n                // Controlamos que no se añadan los tags de subcategoria mediante esta variable.\n                that.subCatAdded = true;\n\n                //that.showSubcategoriesDropdown(parent);\n            });\n\n            $(SELECTORS.SUBCATEGORYRESTORE).on('click', function () {\n                var idSubCatClose = $(this).data('subfilterclose');\n                var idSubCat = $(this).data('subfilterid');\n                var filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid));\n                var transitionalVars = filtersFromStorage[that.sectionSelected];\n                transitionalVars = $.grep(transitionalVars, function (value) {\n                    return (value !== idSubCatClose && value !== idSubCat);\n                });\n                filtersFromStorage[that.sectionSelected] = transitionalVars;\n\n                that.mainFilter[that.sectionSelected] = '';\n                localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, filtersFromStorage)));\n                var label = ($(this).parent()[0]);\n                $(label).removeClass('selected');\n                that.subCatDeleted = true;\n            });\n\n            $(SELECTORS.SUBCATEGORY).on('click', function () {\n                var filterId = '#' + that.sectionSelected + '-subcategoryId-' + $(this).data('subcategory');\n                var filterCatId = '#' + $(this).data('category');\n                that.categoryDropValue = $(this).data('category-drop');\n                $(filterId).click();\n                if ($(filterCatId).prop('checked') === false) {\n                    $(filterCatId).click();\n                }\n                if (!that.subCatDeleted) {\n                    $(this).addClass('selected bgf-c');\n                }\n            });\n        };\n\n        return ManageAssignmentsFilter;\n    });\n\n\n"], "names": ["define", "$", "filtersArray", "SELECTORS", "ManageAssignmentsFilter", "filter_topics", "filter_categories", "prototype", "constructor", "root", "init", "that", "this", "searchParams", "URLSearchParams", "window", "location", "search", "sectionSelected", "defaultSection", "prepareFilters", "activitiesCounter", "mainFilter", "filterController", "filterControllerClicks", "sesskeyCourseid", "urlSesskey", "attr", "<PERSON><PERSON><PERSON><PERSON>", "categoryDropValue", "categoryDropValueReload", "subCatAdded", "subCatDelete", "subCatDeleted", "resetControlller", "toggleDropdownFilter", "chooseFilters", "checkFilter", "openFilters", "changeSection", "subFilterManagement", "filtersStorage", "localStorage", "getItem", "length", "JSON", "parse", "click", "categoryid", "data", "index", "indexOf", "splice", "parent", "remove", "filters", "topicid", "filter_toassign", "onload", "getFiltersFromURL", "cleanAllFilters", "applyFiltersSaved", "manageActivities", "sesskey", "substring", "lastIndexOf", "courseid", "get", "clear", "find", "each", "toassign", "bytype", "bydelivery", "bydificulty", "bykeyevidence", "byblock", "bycompetence", "bycriterion", "bytheme", "bytransversekey", "byskills", "bypedagogicalpurposes", "byassessment", "bylearninglevel", "bytrackingactivitiesid", "bychallengesid", "byincontextid", "bytypeofactivityid", "bypresentationresourcesid", "<PERSON><PERSON>b<PERSON><PERSON><PERSON><PERSON>", "hidebytype", "hidebydelivery", "hidebydificulty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hidebycompetence", "hidebycriterion", "hidebytheme", "hidebytransversekey", "<PERSON><PERSON><PERSON><PERSON>", "hidebypedagogicalpurposes", "hidebyassessment", "hidebylearninglevel", "hidebyblock", "hidebytoassign", "hidebytrackingactivitiesid", "hidebychallengesid", "hidebyincontextid", "hidetypeofactivityid", "hidepresentationresourcesid", "hidesubcategorys<PERSON>", "hidebytopic", "undefined", "hidebycategory", "hasOwnProperty", "addClass", "hide", "removeClass", "show", "keyup", "applyColorButton", "add", "dataParent", "closest", "openId", "on", "e", "hasClass", "id", "filtersFromStorage", "transitionalVars", "grep", "value", "setItem", "stringify", "Object", "assign", "preventDefault", "toggleClass", "toggle", "stopPropagation", "uniquekey", "uniquekeySub", "filtertype", "label", "filtered", "checked", "idselected", "idselectedsub", "filterSelector", "prop", "push", "prepend", "inArray", "removeItem", "key", "idAlt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "split", "join", "siblings", "next", "document", "restore", "idSub", "cleanClicks", "val", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cleanStorageForCurrentSection", "event", "target", "is", "container", "Array", "from", "querySelectorAll", "filter", "s", "getComputedStyle", "getPropertyValue", "containerSub", "addEventListener", "contains", "isVisible", "section", "visibleFilterbutton", "children", "newSection", "urlParams", "sequenceId", "filterSeq", "isEmptyObject", "ready", "filterType", "checkedUpper", "activities", "clicks", "currentFilter", "currentFilterCat", "findIndex", "item", "html", "filterTypeDropdown", "checkboxes", "fullId", "type", "subCatToHide", "previoussuddrop", "includes", "toLowerCase", "toDelete", "visible", "customCheckboxes", "css", "cat", "subcat", "counter", "not", "count", "cleanStorageForSubcategories", "subcategories", "temp", "checkSubcategoriesDropdown", "subcatsdropdown", "subcatsdropdownVisible", "checker", "showSubcategoriesDropdown", "filtercounter", "text", "idSubCatClose", "idSubCat", "filterId", "filterCatId"], "mappings": "AACAA,8CAAO,CAAC,SAAU,WAAY,uBAAwB,6BAClD,SAAUC,OAEFC,aAAe,OACfC,eACM,iBADNA,qBAEY,eAFZA,oBAGW,qBAHXA,yBAIgB,0BAJhBA,iBAKQ,UALRA,mBAOU,iBAPVA,wBAQe,kBARfA,iBASQ,sBATRA,0BAUiB,gCAVjBA,8BAWqB,6DAXrBA,sBAYa,uBAZbA,uBAac,oCAbdA,eAcM,kBAdNA,yBAegB,yBAfhBA,qBAgBY,gBAhBZA,yBAiBgB,6CAjBhBA,uBAkBc,yBAlBdA,sBAsBa,gBAtBbA,uBAwBc,kBAxBdA,2BAyBkB,mBAzBlBA,sBA0Ba,UA1BbA,6BA2BoB,wBAOfC,wBAAwBC,cAAeC,mBAC5CF,wBAAwBG,UAAUC,YAAcJ,wBAChDA,wBAAwBG,UAAUE,KAAO,KACzCL,wBAAwBG,UAAUF,cAAgBA,cAClDD,wBAAwBG,UAAUD,kBAAoBA,uBAEjDI,cAGTN,wBAAwBG,UAAUG,KAAO,qCACjCC,KAAOC,UACNH,KAAOR,EAAEE,qBACTU,aAAe,IAAIC,gBAAgBC,OAAOC,SAASC,aACnDC,gBAAkBP,KAAKQ,sBACvBjB,aAAeS,KAAKS,sBACpBC,kBAAoB,OACpBC,WAAa,QACbC,iBAAmB,QACnBC,uBAAyB,QACzBC,gBAAkB,OACnBC,WAAazB,EAAE,gBAAgB0B,KAAK,QACpCD,iBACKE,aAAaF,iBAGjBG,kBAAoB,QACpBC,wBAA0B,QAC1BC,aAAc,OACdC,cAAe,EACpBrB,KAAKsB,eAAgB,EAErBtB,KAAKuB,kBAAmB,EACxBvB,KAAKwB,uBACLxB,KAAKyB,gBACLzB,KAAK0B,cACL1B,KAAK2B,cACL3B,KAAK4B,qBACAC,2BAEAC,6CAAiBC,aAAaC,QAAQ/B,KAAKa,wEAAoB,KAExC,OAAxBb,KAAK6B,gBAA0D,IAA/B7B,KAAK6B,eAAeG,QAAwC,OAAxBhC,KAAK6B,iBACzE9B,KAAKa,uBAAyBqB,KAAKC,MAAMlC,KAAK6B,iBAIlDxC,EAAE,0CAA0C8C,OAAM,eAC1CC,WAAa/C,EAAEW,MAAMqC,KAAK,kBAC1BC,MAAQvC,KAAKL,kBAAkB6C,QAAQH,YACvCE,OAAS,GACTvC,KAAKL,kBAAkB8C,OAAOF,MAAO,GAEzCjD,EAAEW,MAAMyC,SAASC,SACjB3C,KAAK4C,aAITtD,EAAE,uCAAuC8C,OAAM,eACvCS,QAAUvD,EAAEW,MAAMqC,KAAK,WACvBC,MAAQvC,KAAKN,cAAc8C,QAAQK,SACnCN,OAAS,GACTvC,KAAKN,cAAc+C,OAAOF,MAAO,GAErCjD,EAAEW,MAAMyC,SAASC,SACjB3C,KAAK4C,aAITtD,EAAE,0CAA0C8C,OAAM,WAC9CpC,KAAK8C,gBAAkB,EACvBxD,EAAEW,MAAMyC,SAASC,SACjB3C,KAAK4C,aAGTxC,OAAO2C,OAAS,KACP/C,KAAKgD,sBACNhD,KAAKiD,kBACLjD,KAAKkD,sBAIblD,KAAKmD,oBAET1D,wBAAwBG,UAAUqB,aAAe,SAAUF,gBACnDqC,QAAUrC,WAAWsC,UAAUtC,WAAWuC,YAAY,KAAO,GAC7DC,SAAWtD,KAAKC,aAAasD,IAAI,iBAChC1C,gBAAkBsC,QAAU,IAAMG,SAEnC,OAASxB,aAAaC,QAAQ/B,KAAKa,kBACnCiB,aAAa0B,SAIrBhE,wBAAwBG,UAAUgD,QAAU,eACpC5C,KAAOC,KAGXX,EAAE,IAAMU,KAAKO,iBAAiBmD,KAAK,kBAAkBC,MAAK,eAClDd,QAAUvD,EAAEW,MAAMqC,KAAK,cACvBD,WAAa/C,EAAEW,MAAMqC,KAAK,cAC1BsB,SAAWtE,EAAEW,MAAMqC,KAAK,YAGxBuB,OAASvE,EAAEW,MAAMqC,KAAK,QACtBwB,WAAaxE,EAAEW,MAAMqC,KAAK,yBAC1ByB,YAAczE,EAAEW,MAAMqC,KAAK,qBAC3B0B,cAAgB1E,EAAEW,MAAMqC,KAAK,iBAC7B2B,QAAU3E,EAAEW,MAAMqC,KAAK,WACvB4B,aAAe5E,EAAEW,MAAMqC,KAAK,gBAC5B6B,YAAc7E,EAAEW,MAAMqC,KAAK,eAC3B8B,QAAU9E,EAAEW,MAAMqC,KAAK,WACvB+B,gBAAkB/E,EAAEW,MAAMqC,KAAK,mBAC/BgC,SAAWhF,EAAEW,MAAMqC,KAAK,YACxBiC,sBAAwBjF,EAAEW,MAAMqC,KAAK,yBACrCkC,aAAelF,EAAEW,MAAMqC,KAAK,gBAC5BmC,gBAAkBnF,EAAEW,MAAMqC,KAAK,mBAC/BoC,uBAAyBpF,EAAEW,MAAMqC,KAAK,wBACtCqC,eAAiBrF,EAAEW,MAAMqC,KAAK,gBAC9BsC,cAAgBtF,EAAEW,MAAMqC,KAAK,eAC7BuC,mBAAqBvF,EAAEW,MAAMqC,KAAK,oBAClCwC,0BAA4BxF,EAAEW,MAAMqC,KAAK,2BACzCyC,gBAAkBzF,EAAEW,MAAMqC,KAAK,iBAI/B0C,YAAa,EACbC,gBAAiB,EAEjBC,iBAAkB,EAClBC,mBAAoB,EACpBC,kBAAmB,EACnBC,iBAAkB,EAClBC,aAAc,EACdC,qBAAsB,EACtBC,cAAe,EACfC,2BAA4B,EAC5BC,kBAAmB,EACnBC,qBAAsB,EACtBC,aAAc,EACdC,eAAkB7F,KAAK8C,kBAAoBc,SAC3CkC,4BAA6B,EAC7BC,oBAAqB,EACrBC,mBAAoB,EACpBC,sBAAuB,EACvBC,6BAA8B,EAC9BC,oBAAqB,EAErBC,YAAgBpG,KAAKN,cAAcuC,OAAS,IAA8C,IAAzCjC,KAAKN,cAAc8C,QAAQK,eACvCwD,IAApCrG,KAAKT,aAAL,YACMS,KAAKT,aAAL,WAAgC0C,OAAS,IACa,IAAtDjC,KAAKT,aAAL,WAAgCiD,QAAQK,SAC/CyD,eAAkBtG,KAAKL,kBAAkBsC,OAAS,IAAqD,IAAhDjC,KAAKL,kBAAkB6C,QAAQH,kBAC9CgE,IAApCrG,KAAKT,aAAL,YACGS,KAAKT,aAAL,WAAgC0C,OAAS,IACgB,IAAzDjC,KAAKT,aAAL,WAAgCiD,QAAQH,YAE/CrC,KAAKT,aAAagH,eAAe,oBACjCvB,WAAchF,KAAKT,aAAL,eAAoC0C,OAAS,IACK,IAAzDjC,KAAKT,aAAL,eAAoCiD,QAAQqB,SAGnD7D,KAAKT,aAAagH,eAAe,uBACjCrB,gBAAmBlF,KAAKT,aAAL,kBAAuC0C,OAAS,IACK,IAAjEjC,KAAKT,aAAL,kBAAuCiD,QAAQuB,cAGtD/D,KAAKT,aAAagH,eAAe,2BACjCtB,eAAkBjF,KAAKT,aAAL,sBAA2C0C,OAAS,IACK,IAApEjC,KAAKT,aAAL,sBAA2CiD,QAAQsB,aAG1D9D,KAAKT,aAAagH,eAAe,mBACjCpB,kBAAqBnF,KAAKT,aAAL,cAAmC0C,OAAS,IACK,IAA/DjC,KAAKT,aAAL,cAAmCiD,QAAQwB,gBAGlDhE,KAAKT,aAAagH,eAAe,aACjCX,YAAe5F,KAAKT,aAAL,QAA6B0C,OAAS,IACK,IAAnDjC,KAAKT,aAAL,QAA6BiD,QAAQyB,UAG5CjE,KAAKT,aAAagH,eAAe,kBACjCnB,iBAAoBpF,KAAKT,aAAL,aAAkC0C,OAAS,IACK,IAA7DjC,KAAKT,aAAL,aAAkCiD,QAAQ0B,eAGjDlE,KAAKT,aAAagH,eAAe,iBACjClB,gBAAmBrF,KAAKT,aAAL,YAAiC0C,OAAS,IACK,IAA3DjC,KAAKT,aAAL,YAAiCiD,QAAQ2B,cAGhDnE,KAAKT,aAAagH,eAAe,aACjCjB,YAAetF,KAAKT,aAAL,QAA6B0C,OAAS,IACK,IAAnDjC,KAAKT,aAAL,QAA6BiD,QAAQ4B,UAG5CpE,KAAKT,aAAagH,eAAe,qBACjChB,oBAAuBvF,KAAKT,aAAL,gBAAqC0C,OAAS,IACK,IAAnEjC,KAAKT,aAAL,gBAAqCiD,QAAQ6B,kBAGpDrE,KAAKT,aAAagH,eAAe,cACjCf,aAAgBxF,KAAKT,aAAL,SAA8B0C,OAAS,IACK,IAArDjC,KAAKT,aAAL,SAA8BiD,QAAQ8B,WAG7CtE,KAAKT,aAAagH,eAAe,2BACjCd,0BAA6BzF,KAAKT,aAAL,sBAA2C0C,OAAS,IACK,IAA/EjC,KAAKT,aAAL,sBAA2CiD,QAAQ+B,wBAG1DvE,KAAKT,aAAagH,eAAe,kBACjCb,iBAAoB1F,KAAKT,aAAL,aAAkC0C,OAAS,IACK,IAA7DjC,KAAKT,aAAL,aAAkCiD,QAAQgC,eAGjDxE,KAAKT,aAAagH,eAAe,qBACjCZ,oBAAuB3F,KAAKT,aAAL,gBAAqC0C,OAAS,IACK,IAAnEjC,KAAKT,aAAL,gBAAqCiD,QAAQiC,kBAGpDzE,KAAKT,aAAagH,eAAe,0BACjCT,2BAA8B9F,KAAKT,aAAL,qBAA0C0C,OAAS,IACK,IAA/EjC,KAAKT,aAAL,qBAA0CiD,QAAQkC,yBAGzD1E,KAAKT,aAAagH,eAAe,kBACjCR,mBAAsB/F,KAAKT,aAAL,aAAkC0C,OAAS,IACK,IAA/DjC,KAAKT,aAAL,aAAkCiD,QAAQmC,iBAGjD3E,KAAKT,aAAagH,eAAe,iBACjCP,kBAAqBhG,KAAKT,aAAL,YAAiC0C,OAAS,IACK,IAA7DjC,KAAKT,aAAL,YAAiCiD,QAAQoC,gBAGhD5E,KAAKT,aAAagH,eAAe,sBACjCN,qBAAwBjG,KAAKT,aAAL,iBAAsC0C,OAAS,IACO,IAAvEjC,KAAKT,aAAL,iBAAsCiD,QAAQqC,qBAGrD7E,KAAKT,aAAagH,eAAe,6BACjCL,4BAA+BlG,KAAKT,aAAL,wBAA6C0C,OAAS,IACO,IAArFjC,KAAKT,aAAL,wBAA6CiD,QAAQsC,4BAG5D9E,KAAKT,aAAagH,eAAe,mBACjCJ,mBAAsBnG,KAAKT,aAAL,cAAmC0C,OAAS,IACM,IAAjEjC,KAAKT,aAAL,cAAmCiD,QAAQuC,kBAGlDqB,aAAeE,gBAAkBT,gBAAkBD,aAAeR,kBAAoBC,iBACtFC,aAAeC,qBAAuBP,YAAcC,gBAAoCC,iBACxFC,mBAAqBK,cAAgBC,2BAA6BC,kBAAoBC,qBACtFG,4BAA8BC,oBAAsBC,mBAAqBC,sBACzEC,6BAA+BC,oBAC/B7G,EAAEW,MAAMuG,SAAS,UACjBlH,EAAEW,MAAMwG,SAERnH,EAAEW,MAAMyG,YAAY,UACpBpH,EAAEW,MAAM0G,WAGhBrH,EAAE,WAAWsH,SAGjBnH,wBAAwBG,UAAUiH,iBAAmB,SAAUvE,KAAMwE,SAC7DC,WAAazH,EAAEgD,MAAM0E,QAAQ,aAC7BF,KACAxH,EAAEyH,YAAYP,SAAS,YACvBlH,EAAEyH,YAAYrD,KAAK,YAAY8C,SAAS,WAExClH,EAAEyH,YAAYL,YAAY,YAC1BpH,EAAEyH,YAAYrD,KAAK,YAAYgD,YAAY,WAInDjH,wBAAwBG,UAAU4B,qBAAuB,eACjDyF,OAAS,GACTjH,KAAOC,KACXX,EAAE,cAAc4H,GAAG,SAAS,SAAUC,MAC9B7H,EAAEW,MAAMyC,SAAS0E,SAAS,gBAC1B9H,EAAE,eAAiBA,EAAEW,MAAMyC,SAAS1B,KAAK,MAAQ,KAAOxB,yBAA2B,YAC9EmE,MAAK,eAEE0D,GAAK/H,EAAEW,MAAMe,KAAK,MAClBsG,mBAAqBpF,KAAKC,MAAMJ,aAAaC,QAAQhC,KAAKc,kBAC1DyG,iBAAmBD,mBAAmBtH,KAAKO,iBAC/CgH,iBAAmBjI,EAAEkI,KAAKD,kBAAkB,SAAUE,cAC3CA,QAAUJ,MAErBC,mBAAmBtH,KAAKO,iBAAmBgH,iBAC3CxF,aAAa2F,QAAQ1H,KAAKc,gBAAiBoB,KAAKyF,UAAUC,OAAOC,OAAO,GAAIP,sBAC5EhI,EAAEW,MAAMmC,QACR9C,EAAE,aAAeA,EAAEW,MAAMe,KAAK,MAAQ,KAAK0B,SAASC,YAE5DrD,EAAEW,MAAMyC,SAASgE,YAAY,cAC7BS,EAAEW,oBAKVxI,EAAE,YAAY4H,GAAG,SAAS,SAAUC,GAChC7H,EAAEW,MAAM8H,YAAY,YAChBV,GAAK/H,EAAEW,MAAMe,KAAK,MAElBiG,QAAUA,SAAW3H,EAAEW,MAAMe,KAAK,QAClC1B,EAAE,eAAiB2H,OAAS,KAAKR,OACjCnH,EAAE,IAAM2H,QAAQP,YAAY,SAEhCO,OAASI,GACT/H,EAAE,eAAiB+H,GAAK,KAAKW,SAC7Bb,EAAEc,sBAIVxI,wBAAwBG,UAAU8B,YAAc,eACxC1B,KAAOC,KAEPD,KAAK8B,iBACL9B,KAAKa,uBAAuBb,KAAKO,iBAAmB2B,KAAKC,MAAMnC,KAAK8B,iBAGxExC,EAAEE,0BAA0B0H,GAAG,SAAS,eAChCG,GAAK/H,EAAEW,MAAMqC,KAAK,MAClB4F,UAAY5I,EAAEW,MAAMe,KAAK,MACzBmH,aAAe,GACfC,WAAa9I,EAAEW,MAAMqC,KAAK,cAC1B+F,MAAQ/I,EAAEW,MAAMqC,KAAK,SACrBgG,UAAW,EACXC,SAAU,EACVC,WAAa,IAAMlJ,EAAEW,MAAMe,KAAK,MAAQ,SACxCyH,cAAgB,IAAMnJ,EAAEW,MAAMe,KAAK,MAAQ,OAC3C0H,eAAiB1I,KAAKO,gBAAkB,IAAM6H,cAElDpI,KAAK6G,iBAAiB2B,YAAY,GAE9BlJ,EAAEW,MAAM0I,KAAK,WACb3I,KAAKT,aAAa6I,YAAYQ,KAAKvB,IACnCgB,MAAQ/I,EAAEW,MAAMqC,KAAK,SAEjBtC,KAAKmB,yBAA0C,kBAAfiH,aAChCD,aAAe7I,EAAE,IAAMU,KAAKmB,yBAAyB6F,QAAQ,MAAM1E,KAAK,kBAIzD,kBAAf8F,YAC+E,IAA/E9I,EAAE,iCAAiCoE,yBAAkBwE,iBAAejG,QAEpE3C,EAAEE,2BAA2BqJ,QACzB,kFAAoFxB,GAApF,mCACmCgB,MADnC,6DAEyDA,MAAQ,oBAAsBhB,GAFvF,cAGca,UAHd,iBAGkDC,aAHlD,sBAIAC,WAJA,0BAI8CM,eAJ9C,kBAQRpJ,EAAEE,yBAAyBgH,SAAS,QACpC+B,SAAU,EAILvI,KAAKoB,cAC8B,OAAhCpB,KAAKa,6BACoDwF,IAAtDrG,KAAKa,uBAAuBb,KAAKO,mBACpCP,KAAKa,uBAAuBb,KAAKO,iBAAmB,IAEpB,OAAhCP,KAAKa,yBACiF,IAAtFvB,EAAEwJ,QAAQxJ,EAAEW,MAAMe,KAAK,MAAOhB,KAAKa,uBAAuBb,KAAKO,oBAC/DP,KAAKa,uBAAuBb,KAAKO,iBAAiBqI,KAAKtJ,EAAEW,MAAMe,KAAK,OACpEe,aAAa2F,QAAQ1H,KAAKc,gBACtBoB,KAAKyF,UAAUC,OAAOC,OAAO,GAAI7H,KAAKa,4BAIlDb,KAAKoB,aAAc,EAEnB9B,EAAE,IAAMA,EAAEW,MAAMe,KAAK,MAAQ,UAAUwF,SAAS,YAChDlH,EAAEmJ,eAAeE,KAAK,WAAW,OAC9B,KACCI,WAAazJ,EAAEW,MAAMe,KAAK,mCAC1BhB,KAAKqB,aACL0H,yCAAa/I,KAAKmB,+EAA2BnB,KAAKkB,kBAAoB,kBAAoBmG,GAAK,OAC/FrH,KAAKqB,cAAe,EAExBrB,KAAKa,uBAAuBb,KAAKO,iBAAmBjB,EAAEkI,KAClDxH,KAAKa,uBAAuBb,KAAKO,kBAC/B,SAAUkH,cACDA,QAAUsB,kBAErBC,IAAMhJ,KAAKT,aAAa6I,YAAY5F,QAAQ6E,IAChDrH,KAAKT,aAAa6I,YAAY3F,OAAOuG,IAAK,GAC1C1J,EAAEE,sBAAsBkE,8BAAuB2D,UAAQ1E,SACvDrD,EAAEE,2BAA2BkE,8BAAuB2D,UAAQ1E,SAExDrD,EAAEE,+BAA+ByC,OAAS,IAC1C3C,EAAEE,yBAAyBkH,YAAY,QACvC4B,UAAW,EACXF,WAAa,KACbpI,KAAK6G,iBAAiB2B,YAAY,QAIlCS,MAAmC,kBAA3B3J,EAAEW,MAAMqC,KAAK,UAAgCtC,KAAKmB,wBAA0B7B,EAAEW,MAAMe,KAAK,MAEjGsG,mBAAqBpF,KAAKC,MAAMJ,aAAaC,QAAQhC,KAAKc,kBAC1DyG,iBAAmBD,mBAAmBtH,KAAKO,oBAC/CgH,iBAAmBjI,EAAEkI,KAAKD,kBAAkB,SAAUE,cAC3CA,QAAUwB,SAErB3B,mBAAmBtH,KAAKO,iBAAmBgH,iBAE3CvH,KAAKW,WAAWX,KAAKO,iBAAmB,GACxCwB,aAAa2F,QAAQ1H,KAAKc,gBAAiBoB,KAAKyF,UAAUC,OAAOC,OAAO,GAAIP,2BAGxCjB,IAAhCrG,KAAKa,6BACoDwF,IAAtDrG,KAAKa,uBAAuBb,KAAKO,uBACwB8F,IAAzDrG,KAAKa,uBAAuBb,KAAKO,iBAAiB,GAAkB,KACnE2I,eAAiBlJ,KAAKa,uBAAuBb,KAAKO,iBAAiB,GAAG4I,MAAM,IAAK,GAAGC,KAAK,KAC7FpJ,KAAKW,WAAWX,KAAKO,iBAAmB2I,eAEb,eAA3B5J,EAAEW,MAAMqC,KAAK,WACVhD,EAAE,2BAA6BA,EAAEW,MAAMe,KAAK,MAAO,MAAMqI,SAAS,SAASC,OAAOlC,SAAS,cAC1FpH,KAAKiD,iBAAgB,GACrB3D,EAAE,YAAYoH,YAAY,SAC1BpH,EAAE,WAAWoH,YAAY,kBACzBpH,EAAE,WAAW8C,SAGrB9C,EAAEkJ,YAAYT,YAAY,YAG4B,KAAtD/H,KAAKa,uBAAuBb,KAAKO,mBACjCP,KAAKW,WAAWX,KAAKO,iBAAmB,IAG5CjB,EAAE,WAAWqH,OACb3G,KAAK4C,UACL5C,KAAKmD,iBAAiBmF,SAAUF,WAAYG,YAGhDjJ,EAAEiK,UAAUrC,GAAG,QAAS1H,wBAAwB,cACT,eAA/BF,EAAEW,MAAMqC,KAAK,eACVhD,EAAEE,2BAA2BkE,0CAA0CzB,OAAS,OAC/EuH,QAAUlK,EAAEW,MAAMqC,KAAK,MAC3BhD,EAAE,qCAAuCkK,QAAU,MAAMpH,iBAMzDiF,GAAK/H,EAAEW,MAAMqC,KAAK,MAClBmH,MAAQnK,EAAEW,MAAMqC,KAAK,SACrBgF,mBAAqBpF,KAAKC,MAAMJ,aAAaC,QAAQhC,KAAKc,kBAC1DyG,iBAAmBD,mBAAmBtH,KAAKO,iBAC/CgH,iBAAmBjI,EAAEkI,KAAKD,kBAAkB,SAAUE,cAC3CA,QAAUJ,MAErBC,mBAAmBtH,KAAKO,iBAAmBgH,iBAC3CxF,aAAa2F,QAAQ1H,KAAKc,gBAAiBoB,KAAKyF,UAAUC,OAAOC,OAAO,GAAIP,sBAExEmC,OACAzJ,KAAKqB,cAAe,EAEpB/B,EAAE,IAAMmK,MAAQ,QAAQrH,SAGxB9C,EAAE,IAAM+H,IAAIjF,QAGZ9C,EAAEE,+BAA+ByC,OAAS,GAC1C3C,EAAEE,yBAAyBkH,YAAY,YAIvCgC,eAAiB,WAAapJ,EAAEW,MAAMqC,KAAK,kBAC3BhD,EAAE,IAAMA,EAAEW,MAAMqC,KAAK,mBAAmBoB,KAAK,kCAAkCzB,OAC/E,GAChB3C,EAAE,IAAMoJ,gBAAgBhC,YAAY,aAI5CpH,EAAEE,oBAAoB0H,GAAG,SAAS,WAC9BlH,KAAKiD,iBAAgB,GACrB3D,EAAE,YAAYoH,YAAY,SAC1BpH,EAAE,WAAWoH,YAAY,kBACzBpH,EAAE,WAAW8C,YAIrB3C,wBAAwBG,UAAUqD,gBAAkB,eAAUyG,wEACtD1J,KAAOC,KACXX,EAAEE,kBAAkBmK,IAAI,IACxBrK,EAAEE,0BAA0BmE,MAAK,WAC7BrE,EAAEW,MAAM0I,KAAK,WAAW,OACpBH,WAAa,IAAMlJ,EAAEW,MAAMe,KAAK,MAAQ,SAC5C1B,EAAEkJ,YAAY9B,YAAY,eAG9BpH,EAAEE,+BAA+BmE,MAAK,WAClCrE,EAAEW,MAAM0C,YAGZrD,EAAEE,0BAA0BmE,MAAK,WAC7BrE,EAAEW,MAAM0C,YAGZrD,EAAEE,yBAAyBkH,YAAY,QACvC1G,KAAK4J,eACL5J,KAAKmD,mBAEDuG,cACA1J,KAAKW,WAAWX,KAAKO,iBAAmB,GAExCP,KAAK6J,gCACL7J,KAAKa,uBAAuBb,KAAKO,iBAAmB,GACpDjB,EAAE,uBAAuBqH,OACzBrH,EAAE,WAAWqH,OACbrH,EAAE,wCAAwCqH,OAC1CrH,EAAE,kCAAkCoH,YAAY,QAChDpH,EAAE,eAAiBU,KAAKO,iBAAiBmD,KAAK,mBAAmBgD,YAAY,kBAIrFjH,wBAAwBG,UAAUiK,8BAAgC,gDAErC3H,KAAKC,MAAMJ,aAAaC,QADtC/B,KACmDa,qDAAqB,IADxEb,KAEaM,iBAAmB,GAFhCN,KAGNY,uBAHMZ,KAGsBM,iBAAmB,GACpDwB,aAAa2F,QAJFzH,KAIea,gBAAiBoB,KAAKyF,UAAUC,OAAOC,OAAO,GAJ7D5H,KAIsEY,2BAGrFpB,wBAAwBG,UAAU+B,YAAc,WAC5CrC,EAAEE,kBAAkB0H,GAAG,SAAS,WAC5B5H,EAAEE,wBAAwBwI,SAC1B1I,EAAEE,sBAAsBgH,SAAS,MACjClH,EAAEE,qBAAqBgH,SAAS,MAChClH,EAAEE,0BAA0BgH,SAAS,SAEzClH,EAAEE,gBAAgB0H,GAAG,SAAS,WAC1B5H,EAAEW,MAAMyD,KAAK,WAAWqE,YAAY,QACpCzI,EAAEW,MAAM8H,YAAY,WAExBzI,EAAEE,uBAAuB0H,GAAG,SAAS,WACjC5H,EAAEE,wBAAwBwI,SAC1B1I,EAAEE,sBAAsBkH,YAAY,MACpCpH,EAAEE,qBAAqBkH,YAAY,MACnCpH,EAAEE,0BAA0BkH,YAAY,SAE5CpH,EAAE,QAAQ8C,OAAM,SAAU0H,WACa,iBAA/BxK,EAAEwK,MAAMC,QAAQ/I,KAAK,OAA4B1B,EAAEwK,MAAMC,QAAQ3C,SAAS,cAC1E9H,EAAE,8BAA8ByI,YAAY,QAC5CzI,EAAE,4BAA4BoH,YAAY,SAG1CpH,EAAE,4BAA4B0K,GAAG,YAAa,KAC1CC,UAAYC,MAAMC,KAAKZ,SAASa,iBAAiB,qBAAqBC,QAAOC,GACnB,QAA1DlK,OAAOmK,iBAAiBD,GAAGE,iBAAiB,aAChDP,UAAY3K,EAAE2K,WAAWvG,KAAK,qBAAqB,OAE/C+G,aAAelB,SAASa,iBAAiB,qDAAqD,GAClGb,SAASmB,iBAAiB,SAAS,SAAUZ,OACrCG,WACGA,YAAcH,MAAMC,SACnBE,UAAUU,SAASb,MAAMC,SAC1BzK,EAAEmL,cAAcT,GAAG,cACtB1K,EAAE,8BAA8ByI,YAAY,QAC5CzI,EAAE,4BAA4BoH,YAAY,iBAO9DjH,wBAAwBG,UAAUgK,aAAe,WAE7CtK,EAAE,kBAAkBqE,MAAK,WACrBrE,EAAEW,MAAM0G,OACRrH,EAAEW,MAAMyG,YAAY,UAAUA,YAAY,eAE9CpH,EAAEE,kBAAkBmK,IAAI,IALb1J,KAMNQ,qBACDmK,UAAY,EAChBtL,EAAE,kBAAkBqE,MAAK,WACjBrE,EAAEW,MAAM+J,GAAG,aACXY,eAGU,IAAdA,WAAqBtL,EAAE,gCAAgC2C,OAAS,EAI3C,IAAd2I,WAAoBtL,EAAE,gCAAgC2C,OAAS,GACtE3C,EAAE,oBAAoBmH,OACtBnH,EAAE,oBAAoBqH,OACtBrH,EAAE,yBAAyBmH,QACN,IAAdmE,YACPtL,EAAE,yBAAyBmH,OAC3BnH,EAAE,oBAAoBmH,OACtBnH,EAAE,oBAAoBmH,SAVtBnH,EAAE,oBAAoBmH,OACtBnH,EAAE,oBAAoBmH,OACtBnH,EAAE,yBAAyBqH,QAhBpB1G,KA0BNQ,kBAGThB,wBAAwBG,UAAUa,eAAiB,kBAC/CnB,EAAEE,uBAAuBmE,MAAK,WACbrE,EAAEW,MAAMyD,KAAK,0BACnBC,MAAK,WACRpE,aAAaD,EAAEW,MAAMqC,KAAK,eAAiB,SAG5C/C,cAGXE,wBAAwBG,UAAU6B,cAAgB,eAC1CzB,KAAOC,SACP4K,WAGAvL,EAAE,iBAAiB0K,GAAG,WAAY,KAC9B9J,aAAeF,KAAKE,aACxB2K,QAAU3K,aAAasD,IAAI,gBAE3BqH,QAAUvL,EAAE,+BAA+BgD,KAAK,eAGhDwI,qBAAsB,EAC1BxL,EAAE,iBAAiBqE,MAAK,WAChBrE,EAAEW,MAAMqC,KAAK,aAAeuI,UAC5BvL,EAAEW,MAAMyG,YAAY,UAChBpH,EAAEW,MAAM8K,WAAW3D,SAAS,cAC5B0D,qBAAsB,OAIP,GAAvBA,qBACAxL,EAAEE,kBAAkBiH,OAIxBnH,EAAE,sBAAsB4H,GAAG,SAAS,WAEhClH,KAAKiD,iBAAgB,OACjB+H,WAAa1L,EAAEW,MAAMyD,KAAK,KAAKpB,KAAK,WACxChD,EAAE,iBAAiBqE,MAAK,WAChBrE,EAAEW,MAAMqC,KAAK,aAAe0I,WAC5B1L,EAAEW,MAAMyG,YAAY,UACZpH,EAAEW,MAAMmH,SAAS,WACzB9H,EAAEW,MAAMuG,SAAS,iBAOjC/G,wBAAwBG,UAAUoD,kBAAoB,eAC9ChD,KAAOC,SACPgL,UAAYjL,KAAKE,aACjB2K,QAAUI,UAAUzH,IAAI,WACxB0H,WAAaD,UAAUzH,IAAI,WAC3BZ,QAAUV,KAAKC,MAAM7C,EAAE,iBAAiBqK,YAEzB,OAAfuB,YAAuBtI,QAAQX,OAAS,UAGjC,KAFPjC,KAAK6J,gCAIU,OAAfqB,WAAqB,KACjBC,UAAYN,QAAU,eAAiBK,WAC3ClL,KAAKa,uBAAuBb,KAAKO,iBAAiBqI,KAAKuC,WAGvDvI,QAAQX,OAAS,GACjB3C,EAAEsD,SAASe,MAAK,eACR0G,OAASQ,QAAU,IAAM5K,KAAK+I,IAAM,IAAM/I,KAAKwH,MACnDzH,KAAKa,uBAAuBb,KAAKO,iBAAiBqI,KAAKyB,WAK/DtI,aAAa2F,QAAQ1H,KAAKc,gBACtBoB,KAAKyF,UAAUC,OAAOC,OAAO,GAAI7H,KAAKa,2BAG9CpB,wBAAwBG,UAAUY,eAAiB,eAE3CqK,YACC5K,KAAKM,gBAAiB,oBAEvBsK,+BAJO5K,KAGcC,aACDsD,IAAI,oDAAc,mBAEnCqH,SAGXpL,wBAAwBG,UAAUgC,cAAgB,eAC1C5B,KAAOC,KACXX,EAAEE,wBAAwB0H,GAAG,SAAS,qCAClClH,KAAKO,gBAAkBjB,EAAEW,MAAMqC,KAAK,WACpCtC,KAAKuB,kBAAmB,EACxBvB,KAAKa,qDAAyBb,KAAKa,8EAA0B,UAEHwF,IAAtDrG,KAAKa,uBAAuBb,KAAKO,kBACjCP,KAAKkD,oBAETlD,KAAKa,uBAAuBb,KAAKO,iBAAmB,GAChDjB,EAAE8L,cAAcpL,KAAK8B,iBACrBC,aAAa2F,QAAQ1H,KAAKc,gBACtBoB,KAAKyF,UAAUC,OAAOC,OAAO,GAAI7H,KAAKa,uBAAuBb,KAAKO,wBAKlFd,wBAAwBG,UAAUsD,kBAAoB,eAC9ClD,KAAOC,KACXX,EAAEiK,UAAU8B,OAAM,WACsB,OAAhCrL,KAAKa,yBACLvB,EAAE,WAAWoH,YAAY,kBACzBpH,EAAE,YAAYoH,YAAY,SAC1BpH,EAAEqE,KAAK3D,KAAKa,uBAAuBb,KAAKO,kBAAkB,SAAUyI,IAAKvB,OACrEnI,EAAE,IAAMmI,OAAOkB,KAAK,WAAW,GAAOvG,QAEtC9C,EAAE,6BAA+BmI,MAAQ,MAAMkB,KAAK,WAAW,GAAMW,OAAO9C,SAAS,0BAMrG/G,wBAAwBG,UAAUuD,iBAAmB,eACjDmF,iEACAgD,kEAAa,KACbC,oEAAe,SACXvL,KAAOC,KACPuL,WAAalM,EAAE,kCAAoCW,KAAKM,gBAAkB,qBAC1EgI,QAAUgD,aACdvL,KAAKY,iBAAiBZ,KAAKO,iBAAmB,GAGzC+H,UAGDtI,KAAKU,kBAAoBpB,EAAEkM,YAAYnB,OAAO,YAAYpI,OAC1DuJ,WAAalM,EAAEkM,YAAYnB,OAAO,YAClCrK,KAAKW,WAAWX,KAAKO,iBAAoBP,KAAKW,WAAWX,KAAKO,iBAExDP,KAAKW,WAAWX,KAAKO,iBADrBP,KAAKO,gBAAkB,IAAM+K,YALnCtL,KAAKU,kBAAoBpB,EAAEkM,YAAYvJ,aAUrCwJ,OAASzL,KAAKa,uBAAuBb,KAAKO,qBAC5CmL,iBACUrF,MAAVoF,OAAqB,KACjBE,iBAAmBF,OAAOG,WAAU,SAAUC,aACJ,GAAlCA,KAAKrJ,QAAQ,oBAGzBkJ,cAAgBD,OAAOG,WAAU,SAAUC,aACG,GAAlCA,KAAKrJ,QAAQ,mBAAyD,GAA/BqJ,KAAKrJ,QAAQ,kBAGvC,GAArBmJ,mBAA4C,GAAlBD,eAAuBD,OAAOxJ,OAAS,IACjEjC,KAAKW,WAAWX,KAAKO,iBAAmBP,KAAKO,gBAAkB,kBAGvEjB,EAAE,mBAAmBwM,KAAK,IAAM9L,KAAKU,kBAAoB,MAEzC,IAAZ6H,UACAvI,KAAKY,iBAAiBZ,KAAKO,iBAAmB,GAC9CgI,SAAU,GAEdjJ,EAAEkM,YAAY7H,MAAK,WACfrE,EAAEqE,KAAKrE,EAAEW,MAAMqC,QAAQ,SAAU0G,IAAKvB,OAClCuB,IAAc,SAARA,IAAiB,iBAAmBA,SACe3C,IAArDrG,KAAKY,iBAAiBZ,KAAKO,iBAAiByI,MAAuBV,WACnEtI,KAAKY,iBAAiBZ,KAAKO,iBAAiByI,KAAO,IAEnDT,SAAWD,WAAoF,IAAxEhJ,EAAEwJ,QAAQrB,MAAOzH,KAAKY,iBAAiBZ,KAAKO,iBAAiByI,QACpFhJ,KAAKY,iBAAiBZ,KAAKO,iBAAiByI,KAAKJ,KAAKnB,OACtDc,SAAU,EACVvI,KAAKuB,kBAAmB,SAIpCgH,SAAU,MAENwD,mBAAqBzM,EAAE,2BAC3BA,EAAEyM,oBAAoBpI,MAAK,eAEnBqI,WAAa1M,EAAEW,MAAMyD,KAAK,SAG9BpE,EAAE0M,YAAYrI,MAAK,WACfrE,EAAEW,MAAMyC,SAASiE,OACjBrH,EAAEW,MAAM+G,QAAQ,MAAML,cAItBW,mBAAqBpF,KAAKC,MAAMJ,aAAaC,QAAQhC,KAAKc,kBAC1DyG,iBAA0C,OAAvBD,mBAA8BA,mBAAmBtH,KAAKO,iBAAmB,KAChGjB,EAAE,4BAA8BU,KAAKO,gBAAkBjB,EAAEW,MAAMqC,KAAK,MAAQ,gBAAgBqE,OAC5FrH,EAAE0M,YAAYrI,MAAK,eAEX0D,GAAK/H,EAAEW,MAAMqC,KAAK,MAClB2J,OAAS3M,EAAEW,MAAMe,KAAK,MACtBkL,KAAO5M,EAAEW,MAAMqC,KAAK,UACpBI,OAASpD,EAAEW,MAAMe,KAAK,MAAMmI,MAAM,IAAK,GAAGC,KAAK,KAC/C+C,aAAe7M,EALPW,MAKgB+G,QAAQ,6BAA6BhG,KAAK,MAClEoL,gBAAkBD,aAAaE,SAASrM,KAAKO,gBAAkB,kBAC7D,mBAAqBP,KAAKO,gBAAkB,8BAAgC4L,aAAe,KAC3F,mBAAqBnM,KAAKO,gBAAkB,4BAA8B4L,aAAe,QAE/F7M,EAAE8M,iBAAiBzF,OAEf2B,UAAYiD,cAAgB7I,SAAW1C,KAAKW,WAAWX,KAAKO,mBAC2B,IAApFjB,EAAEwJ,QAAQzB,GAAIrH,KAAKY,iBAAiBZ,KAAKO,iBAAiB2L,KAAKI,kBAClEhN,EAdQW,MAcCyC,SAAS+D,OAClBc,iBAAmBjI,EAAEkI,KAAKD,kBAAkB,SAAUE,cAC3CA,QAAUwE,UAErB3E,mBAAmBtH,KAAKO,iBAAmBgH,iBAC3CjI,EAnBQW,MAmBC+G,QAAQ,MAAMP,OACvBnH,EAAE,mBAAqBU,KAAKO,gBAAkB,qBAAuB4L,aAAe,MAAM1F,OACtFzG,KAAKO,gBAAkB,IAAM2L,OAASlM,KAAKW,WAAWX,KAAKO,kBAC3DjB,EAAE8M,iBAAiB3F,QAGvB6B,UAAYiD,cAAgB7I,SAAW1C,KAAKW,WAAWX,KAAKO,kBACzDmC,SAAW1C,KAAKO,gBAAkB,IAAM+K,aAC4C,IAApFhM,EAAEwJ,QAAQzB,GAAIrH,KAAKY,iBAAiBZ,KAAKO,iBAAiB2L,KAAKI,gBAAwB,KACtFC,SAAWjN,EAAE,2BAA6B6M,aAAe,MAAM7J,KAAK,iBAAmB,QAC1D,IAA7BhD,EA7BIW,MA6BK0I,KAAK,aACdrJ,EA9BIW,MA8BK0I,KAAK,WAAW,GACzBrJ,EAAE,IAAM2M,OAAS,UAAUvF,YAAY,YACvCpH,EAAE,aAAe2M,OAAS,MAAMvJ,SAASC,SACzCrD,EAAE,IAAMiN,UAAU5D,KAAK,WAAW,IAEtCpB,iBAAmBjI,EAAEkI,KAAKD,kBAAkB,SAAUE,cAC3CA,QAAU8E,YAErBjF,mBAAmBtH,KAAKO,iBAAmBgH,iBAC3CjI,EAvCQW,MAuCC+G,QAAQ,MAAMP,OAEnBzG,KAAKO,gBAAkB,IAAM2L,OAASlM,KAAKW,WAAWX,KAAKO,kBAC3DjB,EAAE8M,iBAAiB3F,OAGvB6B,WAAaiD,cAAgB7I,SAAW1C,KAAKW,WAAWX,KAAKO,mBAC0B,IAApFjB,EAAEwJ,QAAQzB,GAAIrH,KAAKY,iBAAiBZ,KAAKO,iBAAiB2L,KAAKI,kBAClEhN,EA/CQW,MA+CCyC,SAAS+D,OAClBc,iBAAmBjI,EAAEkI,KAAKD,kBAAkB,SAAUE,cAC3CA,QAAUwE,UAErB3E,mBAAmBtH,KAAKO,iBAAmBgH,iBAC3CjI,EApDQW,MAoDC+G,QAAQ,MAAMP,OACvBnH,EAAE,mBAAqBU,KAAKO,gBAAkB,qBAAuB4L,aAAe,MAAM1F,OACtFzG,KAAKO,gBAAkB,IAAM2L,OAASlM,KAAKW,WAAWX,KAAKO,kBAC3DjB,EAAE8M,iBAAiB3F,WAI/BzG,KAAKa,uBAAyByG,mBAC9BvF,aAAa2F,QAAQ1H,KAAKc,gBAAiBoB,KAAKyF,UAAUC,OAAOC,OAAO,GAAIP,0BAExEkF,SAAU,EACVC,iBAAmBnN,EAAEW,MAAMyD,KAAK,oBAEpCpE,EAAEmN,kBAAkB9I,MAAK,iBACU,SAA3BrE,EAAEW,MAAMyM,IAAI,aACZF,SAAU,IAEP,KAGNA,QAGDlN,EAzFWW,MAyFC0G,OAFZrH,EAvFWW,MAuFCwG,UAMhBnH,EAAE,gCAAgC2C,OAAS,EAC3C3C,EAAEE,oBAAoBkH,YAAY,aAElCpH,EAAEE,oBAAoBgH,SAAS,kBAKuBH,IAAtDrG,KAAKa,uBAAuBb,KAAKO,kBACjCjB,EAAE,kCAAoCU,KAAKO,gBAAkB,MAAM8J,OAAO,YAAY1G,MAAK,eACnFkH,QAAU5K,KACV0M,IAAMrN,EAAEW,MAAMqC,KAAK,YACvBhD,EAAE,oBAAsBU,KAAKO,gBAAkB,IAAMoM,IAAM,YAAYtC,OAAO,YAAY1G,MAAK,eACvFiJ,OAAStN,EAAEW,MAAMqC,KAAK,eACtBuK,QAAUvN,EAAEuL,SAASnH,KAAK,kBACzB2G,OAAO,wBAA0BuC,OAAS,MAC1CE,IAAI,WAAW7K,OAChB8K,MAAQzN,EAAEW,MAAMyD,KAAK,WACT,IAAZmJ,QACAvN,EAAEW,MAAMwG,QAERsG,MAAMjB,KAAK,IAAMe,QAAU,KAC3BvN,EAAEW,MAAM0G,eAS5BlH,wBAAwBG,UAAUoN,6BAA+B,SAAUC,sDAI9C/K,KAAKC,MAAMJ,aAAaC,QAHtC/B,KAGmDa,uDAAqB,IAHxEb,KAIaM,iBAAmB,OACvC2M,KALOjN,KAKKY,uBALLZ,KAKiCM,iBAAiB8J,QACzDV,MAAQsD,cAAcZ,SAAS1C,OANxB1J,KASNgD,iBAAgB,GATVhD,KAUNY,uBAVMZ,KAUsBM,iBAAmB2M,KACpDnL,aAAa2F,QAXFzH,KAWea,gBAAiBoB,KAAKyF,UAAUC,OAAOC,OAAO,GAX7D5H,KAWsEY,0BAXtEZ,KAcNiD,qBAGTzD,wBAAwBG,UAAUuN,2BAA6B,eAEvDC,gBAAkB9N,EAAE,IADbW,KACwBM,gBAAkB,qBACrDjB,EAAE8N,iBAAiBzJ,MAAK,cACW,UAA3BrE,EAAEW,MAAMyM,IAAI,WAAwB,KAChCW,uBAAyB/N,EAAEW,MAC3BqN,SAAU,EACdhO,EAAEW,MAAMyD,KAAK,MAAMC,MAAK,WACU,aAA1BrE,EAAEW,MAAMyM,IAAI,aACZY,SAAU,MAGdA,QACAD,uBAAuB5G,OAEvB4G,uBAAuB1G,YAKvClH,wBAAwBG,UAAU2N,0BAA4B,eAEtDC,cAAgBlO,EAAE,IADXW,KACsBM,iBAAiBmD,KAAKlE,2BAA6B,YAAYyC,OAC5FS,OAAS,IAFFzC,KAEaM,gBAAkB,kCACtCiN,cAAgB,GAChBA,cAAgBA,eAAiB,GAAK,KAAOA,cAC7ClO,EAAEoD,QAAQ8D,SAAS,QACnBlH,EAAE,IANKW,KAMMM,gBAAkB,mBAAmBkN,KAAKD,iBAEvDlO,EAAEoD,QAAQgE,YAAY,QACtBpH,EAAE,IATKW,KASMM,gBAAkB,mBAAmBkN,KAAK,OAI/DhO,wBAAwBG,UAAUiC,oBAAsB,eAChD7B,KAAOC,KAEXX,EAAE,kCAAkC4H,GAAG,SAAS,eACxCG,GAAK/H,EAAEW,MAAMe,KAAK,MACtB1B,EAAE,IAAM+H,GAAK,aAAaU,YAAY,WAG1CzI,EAAE,sBAAsB4H,GAAG,SAAS,WAChC5H,EAAE,8BAA8ByI,YAAY,QAC5CzI,EAAE,4BAA4BoH,YAAY,WAG9CpH,EAAEE,4BAA4B0H,GAAG,SAAS,WAEjClH,KAAKsB,cAWNtB,KAAKsB,eAAgB,GAVe,OAAhCtB,KAAKa,6BAAyFwF,IAAtDrG,KAAKa,uBAAuBb,KAAKO,kBACvC,kBAA/BjB,EAAEW,MAAMqC,KAAK,gBAChBtC,KAAKa,uBAAuBb,KAAKO,iBAAmB,IAErB,kBAA/BjB,EAAEW,MAAMqC,KAAK,eAAqE,OAAhCtC,KAAKa,yBAC+B,IAAtFvB,EAAEwJ,QAAQxJ,EAAEW,MAAMe,KAAK,MAAOhB,KAAKa,uBAAuBb,KAAKO,oBAC/DP,KAAKa,uBAAuBb,KAAKO,iBAAiBqI,KAAKtJ,EAAEW,MAAM,GAAGoH,IAClEtF,aAAa2F,QAAQ1H,KAAKc,gBAAiBoB,KAAKyF,UAAUC,OAAOC,OAAO,GAAI7H,KAAKa,4BAOzFb,KAAKoB,aAAc,KAKvB9B,EAAEE,8BAA8B0H,GAAG,SAAS,eACpCwG,cAAgBpO,EAAEW,MAAMqC,KAAK,kBAC7BqL,SAAWrO,EAAEW,MAAMqC,KAAK,eACxBgF,mBAAqBpF,KAAKC,MAAMJ,aAAaC,QAAQhC,KAAKc,kBAC1DyG,iBAAmBD,mBAAmBtH,KAAKO,iBAC/CgH,iBAAmBjI,EAAEkI,KAAKD,kBAAkB,SAAUE,cAC1CA,QAAUiG,eAAiBjG,QAAUkG,YAEjDrG,mBAAmBtH,KAAKO,iBAAmBgH,iBAE3CvH,KAAKW,WAAWX,KAAKO,iBAAmB,GACxCwB,aAAa2F,QAAQ1H,KAAKc,gBAAiBoB,KAAKyF,UAAUC,OAAOC,OAAO,GAAIP,0BACxEe,MAAS/I,EAAEW,MAAMyC,SAAS,GAC9BpD,EAAE+I,OAAO3B,YAAY,YACrB1G,KAAKsB,eAAgB,KAGzBhC,EAAEE,uBAAuB0H,GAAG,SAAS,eAC7B0G,SAAW,IAAM5N,KAAKO,gBAAkB,kBAAoBjB,EAAEW,MAAMqC,KAAK,eACzEuL,YAAc,IAAMvO,EAAEW,MAAMqC,KAAK,YACrCtC,KAAKkB,kBAAoB5B,EAAEW,MAAMqC,KAAK,iBACtChD,EAAEsO,UAAUxL,SAC2B,IAAnC9C,EAAEuO,aAAalF,KAAK,YACpBrJ,EAAEuO,aAAazL,QAEdpC,KAAKsB,eACNhC,EAAEW,MAAMuG,SAAS,sBAKtB/G"}
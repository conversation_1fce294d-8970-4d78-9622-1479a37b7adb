define("local_npe/modal_change_delivery_type",["exports","jquery","core/notification","core/custom_interaction_events","core/modal","core/modal_registry","core/ajax"],(function(_exports,_jquery,_notification,_custom_interaction_events,_modal,_modal_registry,_ajax){function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _defineProperty(e,r,t){return(r=function(t){var i=function(t,r){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var i=e.call(t,r||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==typeof i?i:i+""}(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_notification=_interopRequireDefault(_notification),_custom_interaction_events=_interopRequireDefault(_custom_interaction_events),_modal=_interopRequireDefault(_modal),_modal_registry=_interopRequireDefault(_modal_registry),_ajax=_interopRequireDefault(_ajax);const SELECTORS_MODAL=".change-delivery-type-modal",SELECTORS_MODALBACKDROP=".modal-backdrop",SELECTORS_CONFIRMBUTTON='[data-action="confirm"]',SELECTORS_CANCELBUTTON='[data-action="cancel"]',SELECTORS_HIDE=".close",SERVICES_REMOVEASSIGN="local_npe_remove_assign";let registered=!1;class ModalChangeDeliveryType extends _modal.default{constructor(root){super(root)}setData(assignurl,assignid,activityid,courseid){this.assignurl=assignurl,this.assignid=assignid,this.courseid=courseid,this.activityid=activityid}registerEventListeners(){super.registerEventListeners(this),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_CONFIRMBUTTON,(()=>{this.ajaxCall()})),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_HIDE+", "+SELECTORS_CANCELBUTTON,(()=>{(0,_jquery.default)(SELECTORS_MODAL).remove(),(0,_jquery.default)(SELECTORS_MODALBACKDROP).remove(),(0,_jquery.default)("body").removeClass("modal-open")}))}ajaxCall(){_ajax.default.call([{methodname:SERVICES_REMOVEASSIGN,args:{assign:[{id:this.activityid}],teamid:null,userid:null,activityid:this.activityid,changedelivery:!0,courseid:this.courseid}}])[0].done((response=>{this.ajaxResult(response)})).fail((ex=>{this.ajaxError(ex)}))}ajaxError(ex){_notification.default.exception({message:ex})}ajaxResult(response){!0===response.success&&(window.location.href=this.assignurl)}}return _exports.default=ModalChangeDeliveryType,_defineProperty(ModalChangeDeliveryType,"TYPE","local_npe/modal_change_delivery_type"),_defineProperty(ModalChangeDeliveryType,"TEMPLATE","local_npe/assignments/modal_change_delivery_type"),registered||(_modal_registry.default.register(ModalChangeDeliveryType.TYPE,ModalChangeDeliveryType,ModalChangeDeliveryType.TEMPLATE),registered=!0),_exports.default}));

//# sourceMappingURL=modal_change_delivery_type.min.js.map
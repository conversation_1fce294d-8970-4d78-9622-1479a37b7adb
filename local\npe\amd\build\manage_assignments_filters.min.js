define("local_npe/manage_assignments_filters",["jquery","jqueryui","local_npe/datepicker","local_npe/event_listener"],(function($){var filtersArray={};let SELECTORS_ROOT=".myassignments",SELECTORS_HEADERMENU=".header-menu",SELECTORS_HEADERBAR="#nd_header_wrapper",SELECTORS_MEGAMENUMOBILE=".button-mobile-megamenu",SELECTORS_SEARCH="#search",SELECTORS_CLEANALL=".clean-filters",SELECTORS_DELETEFILTERS=".delete-filters",SELECTORS_FILTER=".assignments-filter",SELECTORS_FILTERCONTAINER=".assignments-filter-container",SELECTORS_FILTERCONTAINERITEM=".assignments-filter-container .assignments-filter-selected",SELECTORS_FILTERCLOSE="#filterButton .close",SELECTORS_OPACITYLAYER=".assignments-filter-opacity-layer",SELECTORS_CARD=".card-header h5",SELECTORS_FILTERCHECKBOX=".custom_checkbox input",SELECTORS_FILTERZONE=".filters-zone",SELECTORS_FILTERZONEITEM=".filters-zone .assignments-filter-selected",SELECTORS_REMOVEFILTER=".clean-filter-selected",SELECTORS_FILTERTITLE=".filter-title",SELECTORS_SIDEMENUITEM=".sidebar-menu a",SELECTORS_SUBCATEGORYINPUT=".cc-subcat input",SELECTORS_SUBCATEGORY=".subcat",SELECTORS_SUBCATEGORYRESTORE=".cc-subcat i";function ManageAssignmentsFilter(filter_topics,filter_categories){ManageAssignmentsFilter.prototype.constructor=ManageAssignmentsFilter,ManageAssignmentsFilter.prototype.root=null,ManageAssignmentsFilter.prototype.filter_topics=filter_topics,ManageAssignmentsFilter.prototype.filter_categories=filter_categories,this.init()}return ManageAssignmentsFilter.prototype.init=function(){var that=this;this.root=$(SELECTORS_ROOT),this.searchParams=new URLSearchParams(window.location.search),this.sectionSelected=that.defaultSection(),this.filtersArray=that.prepareFilters(),this.activitiesCounter=0,this.mainFilter=[],this.filterController=[],this.filterControllerClicks={},this.sesskeyCourseid="",this.sessionCheck(),this.categoryDropValue="",this.categoryDropValueReload="",this.subCatAdded=!1,this.subCatDelete=!1,that.subCatDeleted=!1,that.resetControlller=!1,that.toggleDropdownFilter(),that.chooseFilters(),that.checkFilter(),that.openFilters(),that.changeSection(),this.subFilterManagement(),this.filtersStorage=localStorage.getItem(this.sesskeyCourseid)??"{}",null!==this.filtersStorage&&0!==this.filtersStorage.length&&"{}"!==this.filtersStorage&&(that.filterControllerClicks=JSON.parse(this.filtersStorage)),$(".filtercategory .clean-filter-selected").click((function(){var categoryid=$(this).data("categoryid");let index=that.filter_categories.indexOf(categoryid);index>-1&&that.filter_categories.splice(index,1),$(this).parent().remove(),that.filters()})),$(".filtertopic .clean-filter-selected").click((function(){let topicid=$(this).data("topicid"),index=that.filter_topics.indexOf(topicid);index>-1&&that.filter_topics.splice(index,1),$(this).parent().remove(),that.filters()})),$(".filtertoassign .clean-filter-selected").click((function(){that.filter_toassign=0,$(this).parent().remove(),that.filters()})),window.onload=()=>{that.getFiltersFromURL()||(that.cleanAllFilters(),that.applyFiltersSaved())},that.manageActivities()},ManageAssignmentsFilter.prototype.sessionCheck=function(){let urlSesskey=$("#logout_link").attr("href"),sesskey=urlSesskey.substring(urlSesskey.lastIndexOf("=")+1),courseid=this.searchParams.get("courseid");this.sesskeyCourseid=sesskey+"-"+courseid,null===localStorage.getItem(this.sesskeyCourseid)&&localStorage.clear()},ManageAssignmentsFilter.prototype.filters=function(){var that=this;$("."+that.sectionSelected).find(".activity-item").each((function(){let topicid=$(this).data("sequenceid"),categoryid=$(this).data("categoryid"),toassign=$(this).data("toassign"),bytype=$(this).data("type"),bydelivery=$(this).data("recommendedgroupingid"),bydificulty=$(this).data("difficultylevelid"),bykeyevidence=$(this).data("keyevidenceid"),byblock=$(this).data("blockid"),bycompetence=$(this).data("competenceid"),bycriterion=$(this).data("criterionid"),bytheme=$(this).data("themeid"),bytransversekey=$(this).data("transversekeyid"),byskills=$(this).data("skillsid"),bypedagogicalpurposes=$(this).data("pedagogicalpurposesid"),byassessment=$(this).data("assessmentid"),bylearninglevel=$(this).data("learninglevelid"),bytrackingactivitiesid=$(this).data("trackingactivitiesid"),bychallengesid=$(this).data("challengesid"),byincontextid=$(this).data("incontextid"),bytypeofactivityid=$(this).data("typeofactivityid"),bypresentationresourcesid=$(this).data("presentationresourcesid"),bysubcategoryid=$(this).data("subcategoryid"),hidebytype=!1,hidebydelivery=!1,hidebydificulty=!1,hidebykeyevidence=!1,hidebycompetence=!1,hidebycriterion=!1,hidebytheme=!1,hidebytransversekey=!1,hidebyskills=!1,hidebypedagogicalpurposes=!1,hidebyassessment=!1,hidebylearninglevel=!1,hidebyblock=!1,hidebytoassign=that.filter_toassign&&!toassign,hidebytrackingactivitiesid=!1,hidebychallengesid=!1,hidebyincontextid=!1,hidetypeofactivityid=!1,hidepresentationresourcesid=!1,hidesubcategorysid=!1,hidebytopic=that.filter_topics.length>0&&-1===that.filter_topics.indexOf(topicid)||void 0!==that.filtersArray.sequenceId&&that.filtersArray.sequenceId.length>0&&-1===that.filtersArray.sequenceId.indexOf(topicid),hidebycategory=that.filter_categories.length>0&&-1===that.filter_categories.indexOf(categoryid)||void 0!==that.filtersArray.categoryId&&that.filtersArray.categoryId.length>0&&-1===that.filtersArray.categoryId.indexOf(categoryid);that.filtersArray.hasOwnProperty("activityTypeId")&&(hidebytype=that.filtersArray.activityTypeId.length>0&&-1===that.filtersArray.activityTypeId.indexOf(bytype)),that.filtersArray.hasOwnProperty("difficultyLevelId")&&(hidebydificulty=that.filtersArray.difficultyLevelId.length>0&&-1===that.filtersArray.difficultyLevelId.indexOf(bydificulty)),that.filtersArray.hasOwnProperty("recommendedGroupingId")&&(hidebydelivery=that.filtersArray.recommendedGroupingId.length>0&&-1===that.filtersArray.recommendedGroupingId.indexOf(bydelivery)),that.filtersArray.hasOwnProperty("keyEvidenceId")&&(hidebykeyevidence=that.filtersArray.keyEvidenceId.length>0&&-1===that.filtersArray.keyEvidenceId.indexOf(bykeyevidence)),that.filtersArray.hasOwnProperty("blockId")&&(hidebyblock=that.filtersArray.blockId.length>0&&-1===that.filtersArray.blockId.indexOf(byblock)),that.filtersArray.hasOwnProperty("competenceId")&&(hidebycompetence=that.filtersArray.competenceId.length>0&&-1===that.filtersArray.competenceId.indexOf(bycompetence)),that.filtersArray.hasOwnProperty("criterionId")&&(hidebycriterion=that.filtersArray.criterionId.length>0&&-1===that.filtersArray.criterionId.indexOf(bycriterion)),that.filtersArray.hasOwnProperty("themeId")&&(hidebytheme=that.filtersArray.themeId.length>0&&-1===that.filtersArray.themeId.indexOf(bytheme)),that.filtersArray.hasOwnProperty("transverseKeyId")&&(hidebytransversekey=that.filtersArray.transverseKeyId.length>0&&-1===that.filtersArray.transverseKeyId.indexOf(bytransversekey)),that.filtersArray.hasOwnProperty("skillsId")&&(hidebyskills=that.filtersArray.skillsId.length>0&&-1===that.filtersArray.skillsId.indexOf(byskills)),that.filtersArray.hasOwnProperty("pedagogicalPurposesId")&&(hidebypedagogicalpurposes=that.filtersArray.pedagogicalPurposesId.length>0&&-1===that.filtersArray.pedagogicalPurposesId.indexOf(bypedagogicalpurposes)),that.filtersArray.hasOwnProperty("assessmentId")&&(hidebyassessment=that.filtersArray.assessmentId.length>0&&-1===that.filtersArray.assessmentId.indexOf(byassessment)),that.filtersArray.hasOwnProperty("learningLevelId")&&(hidebylearninglevel=that.filtersArray.learningLevelId.length>0&&-1===that.filtersArray.learningLevelId.indexOf(bylearninglevel)),that.filtersArray.hasOwnProperty("trackingActivitiesId")&&(hidebytrackingactivitiesid=that.filtersArray.trackingActivitiesId.length>0&&-1===that.filtersArray.trackingActivitiesId.indexOf(bytrackingactivitiesid)),that.filtersArray.hasOwnProperty("challengesId")&&(hidebychallengesid=that.filtersArray.challengesId.length>0&&-1===that.filtersArray.challengesId.indexOf(bychallengesid)),that.filtersArray.hasOwnProperty("inContextId")&&(hidebyincontextid=that.filtersArray.inContextId.length>0&&-1===that.filtersArray.inContextId.indexOf(byincontextid)),that.filtersArray.hasOwnProperty("typeOfActivityId")&&(hidetypeofactivityid=that.filtersArray.typeOfActivityId.length>0&&-1===that.filtersArray.typeOfActivityId.indexOf(bytypeofactivityid)),that.filtersArray.hasOwnProperty("presentationResourcesId")&&(hidepresentationresourcesid=that.filtersArray.presentationResourcesId.length>0&&-1===that.filtersArray.presentationResourcesId.indexOf(bypresentationresourcesid)),that.filtersArray.hasOwnProperty("subcategoryId")&&(hidesubcategorysid=that.filtersArray.subcategoryId.length>0&&-1===that.filtersArray.subcategoryId.indexOf(bysubcategoryid)),hidebytopic||hidebycategory||hidebytoassign||hidebyblock||hidebycompetence||hidebycriterion||hidebytheme||hidebytransversekey||hidebytype||hidebydelivery||hidebydificulty||hidebykeyevidence||hidebyskills||hidebypedagogicalpurposes||hidebyassessment||hidebylearninglevel||hidebytrackingactivitiesid||hidebychallengesid||hidebyincontextid||hidetypeofactivityid||hidepresentationresourcesid||hidesubcategorysid?($(this).addClass("hidden"),$(this).hide()):($(this).removeClass("hidden"),$(this).show())})),$("#search").keyup()},ManageAssignmentsFilter.prototype.applyColorButton=function(data,add){var dataParent=$(data).closest(".dropdown");add?($(dataParent).addClass("selected"),$(dataParent).find(".dropbtn").addClass("bgf-c")):($(dataParent).removeClass("selected"),$(dataParent).find(".dropbtn").removeClass("bgf-c"))},ManageAssignmentsFilter.prototype.toggleDropdownFilter=function(){var openId="",that=this;$(".dropbtn i").on("click",(function(e){if($(this).parent().hasClass("bgf-c"))return $("ul[data-id ="+$(this).parent().attr("id")+"] "+SELECTORS_FILTERCHECKBOX+":checked").each((function(){var id=$(this).attr("id"),filtersFromStorage=JSON.parse(localStorage.getItem(that.sesskeyCourseid)),transitionalVars=filtersFromStorage[that.sectionSelected];transitionalVars=$.grep(transitionalVars,(function(value){return value!==id})),filtersFromStorage[that.sectionSelected]=transitionalVars,localStorage.setItem(that.sesskeyCourseid,JSON.stringify(Object.assign({},filtersFromStorage))),$(this).click(),$("[data-id ="+$(this).attr("id")+"]").parent().remove()})),$(this).parent().removeClass("bgf-c"),void e.preventDefault()})),$(".dropbtn").on("click",(function(e){$(this).toggleClass("open");var id=$(this).attr("id");openId&&openId!==$(this).attr("id")&&($("ul[data-id ="+openId+"]").hide(),$("#"+openId).removeClass("open")),openId=id,$("ul[data-id ="+id+"]").toggle(),e.stopPropagation()}))},ManageAssignmentsFilter.prototype.checkFilter=function(){var that=this;that.filtersStorage&&(that.filterControllerClicks[that.sectionSelected]=JSON.parse(that.filtersStorage)),$(SELECTORS_FILTERCHECKBOX).on("click",(function(){var id=$(this).data("id"),uniquekey=$(this).attr("id"),uniquekeySub="",filtertype=$(this).data("filtertype"),label=$(this).data("label"),filtered=!0,checked=!1,idselected="#"+$(this).attr("id")+"-label",idselectedsub="#"+$(this).attr("id")+"-sub",filterSelector=that.sectionSelected+"-"+filtertype;if(that.applyColorButton(idselected,!0),$(this).prop("checked"))that.filtersArray[filtertype].push(id),label=$(this).data("label"),that.categoryDropValueReload&&"subcategoryId"===filtertype&&(uniquekeySub=$("#"+that.categoryDropValueReload).closest("li").data("filtersuddrop")),"subcategoryId"!==filtertype&&0===$(".assignments-filter-container").find(`[data-id='${uniquekey}']`).length&&$(SELECTORS_FILTERCONTAINER).prepend('<div class="filtercategory assignments-filter-selected border-c" data-checkid="'+id+'"><div class="filter-text fc-c">'+label+'</div><div class="clean-filter-selected bg-c" data-label="'+label+'" data-category="'+id+'" data-id="'+uniquekey+'" data-idSub="'+uniquekeySub+'" data-filtertype="'+filtertype+'" data-filterselector="'+filterSelector+'"></div></div>'),$(SELECTORS_DELETEFILTERS).addClass("show"),checked=!0,that.subCatAdded||(null!==that.filterControllerClicks&&void 0===that.filterControllerClicks[that.sectionSelected]&&(that.filterControllerClicks[that.sectionSelected]=[]),null!==that.filterControllerClicks&&-1===$.inArray($(this).attr("id"),that.filterControllerClicks[that.sectionSelected])&&(that.filterControllerClicks[that.sectionSelected].push($(this).attr("id")),localStorage.setItem(that.sesskeyCourseid,JSON.stringify(Object.assign({},that.filterControllerClicks))))),that.subCatAdded=!1,$("#"+$(this).attr("id")+"-label").addClass("selected"),$(idselectedsub).prop("checked",!0);else{let removeItem=$(this).attr("id");that.subCatDelete&&(removeItem=that.categoryDropValueReload??that.categoryDropValue+"-subcategoryId-"+id+"-sub",that.subCatDelete=!1),that.filterControllerClicks[that.sectionSelected]=$.grep(that.filterControllerClicks[that.sectionSelected],(function(value){return value!==removeItem}));let key=that.filtersArray[filtertype].indexOf(id);that.filtersArray[filtertype].splice(key,1),$(SELECTORS_FILTERZONE).find(`[data-checkid='${id}']`).remove(),$(SELECTORS_FILTERCONTAINER).find(`[data-checkid='${id}']`).remove(),$(SELECTORS_FILTERCONTAINERITEM).length<1&&($(SELECTORS_DELETEFILTERS).removeClass("show"),filtered=!1,filtertype=null,that.applyColorButton(idselected,!1));var idAlt="subcategoryId"===$(this).data("filter")?that.categoryDropValueReload:$(this).attr("id"),filtersFromStorage=JSON.parse(localStorage.getItem(that.sesskeyCourseid)),transitionalVars=filtersFromStorage[that.sectionSelected];if(transitionalVars=$.grep(transitionalVars,(function(value){return value!==idAlt})),filtersFromStorage[that.sectionSelected]=transitionalVars,that.mainFilter[that.sectionSelected]="",localStorage.setItem(that.sesskeyCourseid,JSON.stringify(Object.assign({},filtersFromStorage))),void 0!==that.filterControllerClicks&&void 0!==that.filterControllerClicks[that.sectionSelected]&&void 0!==that.filterControllerClicks[that.sectionSelected][0]){var nextMainFilter=that.filterControllerClicks[that.sectionSelected][0].split("-",2).join("-");that.mainFilter[that.sectionSelected]=nextMainFilter}"categoryId"===$(this).data("filter")&&$('.subcat[data-category~="'+$(this).attr("id")+'"]').siblings("input").next().hasClass("selected")&&(that.cleanAllFilters(!0),$(".dropbtn").removeClass("bgf-c"),$(".subcat").removeClass("selected bgf-c"),$("#search").click()),$(idselected).toggleClass("selected")}""===that.filterControllerClicks[that.sectionSelected]&&(that.mainFilter[that.sectionSelected]=""),$(".subcat").show(),that.filters(),that.manageActivities(filtered,filtertype,checked)})),$(document).on("click",SELECTORS_REMOVEFILTER,(function(){if("categoryId"===$(this).data("filtertype")&&$(SELECTORS_FILTERCONTAINER).find("[data-filtertype='subcategoryId']").length>0){var restore=$(this).data("id");$(".restore[data-categoryid-restore='"+restore+"']").click()}else{var id=$(this).data("id"),idSub=$(this).data("idsub"),filtersFromStorage=JSON.parse(localStorage.getItem(that.sesskeyCourseid)),transitionalVars=filtersFromStorage[that.sectionSelected];transitionalVars=$.grep(transitionalVars,(function(value){return value!==id})),filtersFromStorage[that.sectionSelected]=transitionalVars,localStorage.setItem(that.sesskeyCourseid,JSON.stringify(Object.assign({},filtersFromStorage))),idSub?(that.subCatDelete=!0,$("#"+idSub+"-sub").click()):$("#"+id).click(),$(SELECTORS_FILTERCONTAINERITEM).length<1&&$(SELECTORS_DELETEFILTERS).removeClass("show");var filterSelector="heading-"+$(this).data("filterselector");$("#"+$(this).data("filterselector")).find(".custom_checkbox span.selected").length<1&&$("#"+filterSelector).removeClass("bgf-c")}})),$(SELECTORS_CLEANALL).on("click",(function(){that.cleanAllFilters(!0),$(".dropbtn").removeClass("bgf-c"),$(".subcat").removeClass("selected bgf-c"),$("#search").click()}))},ManageAssignmentsFilter.prototype.cleanAllFilters=function(){let cleanClicks=arguments.length>0&&void 0!==arguments[0]&&arguments[0];$(SELECTORS_SEARCH).val(""),$(SELECTORS_FILTERCHECKBOX).each((function(){$(this).prop("checked",!1);var idselected="#"+$(this).attr("id")+"-label";$(idselected).removeClass("selected")})),$(SELECTORS_FILTERCONTAINERITEM).each((function(){$(this).remove()})),$(SELECTORS_FILTERZONEITEM).each((function(){$(this).remove()})),$(SELECTORS_DELETEFILTERS).removeClass("show"),this.setToDefault(),this.manageActivities(),cleanClicks&&(this.mainFilter[this.sectionSelected]="",this.cleanStorageForCurrentSection(),this.filterControllerClicks[this.sectionSelected]=[],$(".activities-subcats").show(),$(".subcat").show(),$(".collapse-units-dropdown .topic-item").show(),$(".subcats-dropdown .list-header").removeClass("bg-d"),$("#categories-"+this.sectionSelected).find("li.bgf-c.active").removeClass("bgf-c active"))},ManageAssignmentsFilter.prototype.cleanStorageForCurrentSection=function(){(JSON.parse(localStorage.getItem(this.sesskeyCourseid))??[])[this.sectionSelected]=[],this.filterControllerClicks[this.sectionSelected]=[],localStorage.setItem(this.sesskeyCourseid,JSON.stringify(Object.assign({},this.filterControllerClicks)))},ManageAssignmentsFilter.prototype.openFilters=function(){$(SELECTORS_FILTER).on("click",(function(){$(SELECTORS_OPACITYLAYER).toggle(),$(SELECTORS_HEADERMENU).addClass("z0"),$(SELECTORS_HEADERBAR).addClass("z0"),$(SELECTORS_MEGAMENUMOBILE).addClass("z0")})),$(SELECTORS_CARD).on("click",(function(){$(this).find(".rotate").toggleClass("down"),$(this).toggleClass("bold")})),$(SELECTORS_FILTERCLOSE).on("click",(function(){$(SELECTORS_OPACITYLAYER).toggle(),$(SELECTORS_HEADERMENU).removeClass("z0"),$(SELECTORS_HEADERBAR).removeClass("z0"),$(SELECTORS_MEGAMENUMOBILE).removeClass("z0")})),$("body").click((function(event){if(("closeSubcats"===$(event.target).attr("id")||$(event.target).hasClass("results"))&&($(".subcats-dropdown i.rotate").toggleClass("down"),$(".collapse-units-dropdown").removeClass("show")),$(".collapse-units-dropdown").is(":visible")){var container=Array.from(document.querySelectorAll(".subcatsdropdown")).filter((s=>"none"!=window.getComputedStyle(s).getPropertyValue("display")));container=$(container).find(".subcats-dropdown")[0];var containerSub=document.querySelectorAll(".collapse-units-dropdown.collapse.show .list-body")[0];document.addEventListener("click",(function(event){container&&container!==event.target&&!container.contains(event.target)&&$(containerSub).is(":visible")&&($(".subcats-dropdown i.rotate").toggleClass("down"),$(".collapse-units-dropdown").removeClass("show"))}))}}))},ManageAssignmentsFilter.prototype.setToDefault=function(){$(".activity-item").each((function(){$(this).show(),$(this).removeClass("hidden").removeClass("filtered")})),$(SELECTORS_SEARCH).val(""),this.prepareFilters();var isVisible=0;$(".activity-item").each((function(){$(this).is(":visible")&&isVisible++})),0!==isVisible||$(".assignments-filter-selected").length>0?0===isVisible&&$(".assignments-filter-selected").length>0?($(".noresultssearch").hide(),$(".noresultsfilter").show(),$(".noresultsassignments").hide()):0!==isVisible&&($(".noresultsassignments").hide(),$(".noresultsfilter").hide(),$(".noresultssearch").hide()):($(".noresultssearch").hide(),$(".noresultsfilter").hide(),$(".noresultsassignments").show()),this.prepareFilters()},ManageAssignmentsFilter.prototype.prepareFilters=function(){return $(SELECTORS_FILTERTITLE).each((function(){$(this).find(".custom_checkbox input").each((function(){filtersArray[$(this).data("filtertype")]=[]}))})),filtersArray},ManageAssignmentsFilter.prototype.chooseFilters=function(){var that=this;let section;if($(".sidebar-menu").is(":hidden")){let searchParams=that.searchParams;section=searchParams.get("section")}else section=$(".icon_menu.section.active a").data("section");let visibleFilterbutton=!1;$(".modal-filter").each((function(){$(this).data("section")===section&&($(this).removeClass("hidden"),$(this).children().hasClass("dropdown")&&(visibleFilterbutton=!0))})),0==visibleFilterbutton&&$(SELECTORS_FILTER).hide(),$(".icon_menu.section").on("click",(function(){that.cleanAllFilters(!1);var newSection=$(this).find("a").data("section");$(".modal-filter").each((function(){$(this).data("section")===newSection?$(this).removeClass("hidden"):$(this).hasClass("hidden")||$(this).addClass("hidden")}))}))},ManageAssignmentsFilter.prototype.getFiltersFromURL=function(){var that=this;let urlParams=that.searchParams,section=urlParams.get("section"),sequenceId=urlParams.get("topicid"),filters=JSON.parse($("#filterbyitem").val());if(!(null!==sequenceId||filters.length>0))return!1;if(that.cleanStorageForCurrentSection(),null!==sequenceId){let filterSeq=section+"-sequenceId-"+sequenceId;that.filterControllerClicks[that.sectionSelected].push(filterSeq)}filters.length>0&&$(filters).each((function(){let filter=section+"-"+this.key+"-"+this.value;that.filterControllerClicks[that.sectionSelected].push(filter)})),localStorage.setItem(that.sesskeyCourseid,JSON.stringify(Object.assign({},that.filterControllerClicks)))},ManageAssignmentsFilter.prototype.defaultSection=function(){var section;if(!this.sectionSelected){section=this.searchParams.get("section")??"actAndRec"}return section},ManageAssignmentsFilter.prototype.changeSection=function(){var that=this;$(SELECTORS_SIDEMENUITEM).on("click",(function(){that.sectionSelected=$(this).data("section"),that.resetControlller=!1,that.filterControllerClicks=that.filterControllerClicks??"{}",void 0!==that.filterControllerClicks[that.sectionSelected]&&that.applyFiltersSaved(),that.filterControllerClicks[that.sectionSelected]=[],$.isEmptyObject(that.filtersStorage)&&localStorage.setItem(that.sesskeyCourseid,JSON.stringify(Object.assign({},that.filterControllerClicks[that.sectionSelected])))}))},ManageAssignmentsFilter.prototype.applyFiltersSaved=function(){var that=this;$(document).ready((function(){null!==that.filterControllerClicks&&($(".subcat").removeClass("selected bgf-c"),$(".dropbtn").removeClass("bgf-c"),$.each(that.filterControllerClicks[that.sectionSelected],(function(key,value){$("#"+value).prop("checked",!1).click(),$('.cc-subcat [data-filter~="'+value+'"]').prop("checked",!0).next().addClass("selected bgf-c")})))}))},ManageAssignmentsFilter.prototype.manageActivities=function(){let filtered=arguments.length>0&&void 0!==arguments[0]&&arguments[0],filterType=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,checkedUpper=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;var that=this,activities=$('.activities-list[data-section="'+this.sectionSelected+'"] .activity-item'),checked=checkedUpper;that.filterController[that.sectionSelected]=[],filtered?(that.activitiesCounter=$(activities).filter(":visible").length,activities=$(activities).filter(":visible"),that.mainFilter[that.sectionSelected]=that.mainFilter[that.sectionSelected]?that.mainFilter[that.sectionSelected]:that.sectionSelected+"-"+filterType):that.activitiesCounter=$(activities).length;const clicks=that.filterControllerClicks[that.sectionSelected];let currentFilter;if(null!=clicks){var currentFilterCat=clicks.findIndex((function(item){return-1!=item.indexOf("subcategoryId")}));currentFilter=clicks.findIndex((function(item){return-1==item.indexOf("subcategoryId")&&-1==item.indexOf("categoryId")})),-1!=currentFilterCat&&-1==currentFilter&&clicks.length>0&&(that.mainFilter[that.sectionSelected]=that.sectionSelected+"-subcategoryId")}$("#filter-counter").html("("+that.activitiesCounter+")"),!1===checked&&(that.filterController[that.sectionSelected]=[],checked=!0),$(activities).each((function(){$.each($(this).data(),(function(key,value){key="type"===key?"activitytypeid":key,void 0!==that.filterController[that.sectionSelected][key]&&filtered||(that.filterController[that.sectionSelected][key]=[]),checked&&filtered&&-1===$.inArray(value,that.filterController[that.sectionSelected][key])&&(that.filterController[that.sectionSelected][key].push(value),checked=!0,that.resetControlller=!0)}))})),checked=!1;var filterTypeDropdown=$(".modal-filter .dropdown");$(filterTypeDropdown).each((function(){var checkboxes=$(this).find("input");$(checkboxes).each((function(){$(this).parent().show(),$(this).closest("li").show()}));let filtersFromStorage=JSON.parse(localStorage.getItem(that.sesskeyCourseid)),transitionalVars=null!==filtersFromStorage?filtersFromStorage[that.sectionSelected]:"{}";$("#collapse-units-dropdown-"+that.sectionSelected+$(this).data("id")+" .topic-item").show(),$(checkboxes).each((function(){var id=$(this).data("id"),fullId=$(this).attr("id"),type=$(this).data("filter"),parent=$(this).attr("id").split("-",2).join("-"),subCatToHide=$(this).closest("li .custom_checkbox input").attr("id"),previoussuddrop=subCatToHide.includes(that.sectionSelected+"-subcategoryId")?'[data-section~="'+that.sectionSelected+'"] [data-previoussuddrop~="'+subCatToHide+'"]':'[data-section~="'+that.sectionSelected+'"] [data-filtersuddrop~="'+subCatToHide+'"]';if($(previoussuddrop).show(),filtered&&checkedUpper&&parent!==that.mainFilter[that.sectionSelected]&&-1===$.inArray(id,that.filterController[that.sectionSelected][type.toLowerCase()])&&($(this).parent().hide(),transitionalVars=$.grep(transitionalVars,(function(value){return value!==fullId})),filtersFromStorage[that.sectionSelected]=transitionalVars,$(this).closest("li").hide(),$('[data-section~="'+that.sectionSelected+'"] [data-filter~="'+subCatToHide+'"]').hide(),that.sectionSelected+"-"+type!==that.mainFilter[that.sectionSelected]&&$(previoussuddrop).hide()),filtered&&checkedUpper&&parent===that.mainFilter[that.sectionSelected]&&parent!==that.sectionSelected+"-"+filterType&&-1===$.inArray(id,that.filterController[that.sectionSelected][type.toLowerCase()])){var toDelete=$('[data-previoussuddrop~="'+subCatToHide+'"]').data("filtersuddrop")+"-sub";!0===$(this).prop("checked")&&($(this).prop("checked",!1),$("#"+fullId+"-label").removeClass("selected"),$('[data-id="'+fullId+'"]').parent().remove(),$("#"+toDelete).prop("checked",!1)),transitionalVars=$.grep(transitionalVars,(function(value){return value!==toDelete})),filtersFromStorage[that.sectionSelected]=transitionalVars,$(this).closest("li").hide(),that.sectionSelected+"-"+type!==that.mainFilter[that.sectionSelected]&&$(previoussuddrop).hide()}filtered&&!checkedUpper&&parent!==that.mainFilter[that.sectionSelected]&&-1===$.inArray(id,that.filterController[that.sectionSelected][type.toLowerCase()])&&($(this).parent().hide(),transitionalVars=$.grep(transitionalVars,(function(value){return value!==fullId})),filtersFromStorage[that.sectionSelected]=transitionalVars,$(this).closest("li").hide(),$('[data-section~="'+that.sectionSelected+'"] [data-filter~="'+subCatToHide+'"]').hide(),that.sectionSelected+"-"+type!==that.mainFilter[that.sectionSelected]&&$(previoussuddrop).hide())})),that.filterControllerClicks=filtersFromStorage,localStorage.setItem(that.sesskeyCourseid,JSON.stringify(Object.assign({},filtersFromStorage)));var visible=!1,customCheckboxes=$(this).find(".custom_checkbox");$(customCheckboxes).each((function(){return"none"!==$(this).css("display")&&(visible=!0),!0})),visible?$(this).show():$(this).hide()})),$(".assignments-filter-selected").length>0?$(SELECTORS_CLEANALL).removeClass("nofilters"):$(SELECTORS_CLEANALL).addClass("nofilters"),void 0!==that.filterControllerClicks[that.sectionSelected]&&$('.activities-list[data-section="'+that.sectionSelected+'"]').filter(":visible").each((function(){var section=this,cat=$(this).data("category");$("#subcatsdropdown-"+that.sectionSelected+"-"+cat+" .subcat").filter(":visible").each((function(){var subcat=$(this).data("subcategory"),counter=$(section).find(".activity-item").filter('[data-subcategoryid="'+subcat+'"]').not(".hidden").length,count=$(this).find("counter");0===counter?$(this).hide():(count.html("("+counter+")"),$(this).show())}))}))},ManageAssignmentsFilter.prototype.cleanStorageForSubcategories=function(subcategories){(JSON.parse(localStorage.getItem(this.sesskeyCourseid))??[])[this.sectionSelected]=[];var temp=this.filterControllerClicks[this.sectionSelected].filter((val=>!subcategories.includes(val)));this.cleanAllFilters(!0),this.filterControllerClicks[this.sectionSelected]=temp,localStorage.setItem(this.sesskeyCourseid,JSON.stringify(Object.assign({},this.filterControllerClicks))),this.applyFiltersSaved()},ManageAssignmentsFilter.prototype.checkSubcategoriesDropdown=function(){var subcatsdropdown=$("."+this.sectionSelected+" .subcatsdropdown");$(subcatsdropdown).each((function(){if("block"===$(this).css("display")){var subcatsdropdownVisible=$(this),checker=!0;$(this).find("li").each((function(){"list-item"==$(this).css("display")&&(checker=!1)})),checker?subcatsdropdownVisible.hide():subcatsdropdownVisible.show()}}))},ManageAssignmentsFilter.prototype.showSubcategoriesDropdown=function(){var filtercounter=$("."+this.sectionSelected).find(SELECTORS_SUBCATEGORYINPUT+":checked").length,parent="."+this.sectionSelected+" .subcats-dropdown .list-header";filtercounter>0?(filtercounter=filtercounter>=10?"+9":filtercounter,$(parent).addClass("bg-d"),$("."+this.sectionSelected+" .filter-active").text(filtercounter)):($(parent).removeClass("bg-d"),$("."+this.sectionSelected+" .filter-active").text("x"))},ManageAssignmentsFilter.prototype.subFilterManagement=function(){var that=this;$(".subcats-dropdown .list-header").on("click",(function(){var id=$(this).attr("id");$("#"+id+" i.rotate").toggleClass("down")})),$(".controls .results").on("click",(function(){$(".subcats-dropdown i.rotate").toggleClass("down"),$("#collapse-units-dropdown").removeClass("show")})),$(SELECTORS_SUBCATEGORYINPUT).on("click",(function(){that.subCatDeleted?that.subCatDeleted=!1:(null!==that.filterControllerClicks&&void 0===that.filterControllerClicks[that.sectionSelected]&&"subcategoryId"===$(this).data("filtertype")&&(that.filterControllerClicks[that.sectionSelected]=[]),"subcategoryId"===$(this).data("filtertype")&&null!==that.filterControllerClicks&&-1===$.inArray($(this).attr("id"),that.filterControllerClicks[that.sectionSelected])&&(that.filterControllerClicks[that.sectionSelected].push($(this)[0].id),localStorage.setItem(that.sesskeyCourseid,JSON.stringify(Object.assign({},that.filterControllerClicks))))),that.subCatAdded=!0})),$(SELECTORS_SUBCATEGORYRESTORE).on("click",(function(){var idSubCatClose=$(this).data("subfilterclose"),idSubCat=$(this).data("subfilterid"),filtersFromStorage=JSON.parse(localStorage.getItem(that.sesskeyCourseid)),transitionalVars=filtersFromStorage[that.sectionSelected];transitionalVars=$.grep(transitionalVars,(function(value){return value!==idSubCatClose&&value!==idSubCat})),filtersFromStorage[that.sectionSelected]=transitionalVars,that.mainFilter[that.sectionSelected]="",localStorage.setItem(that.sesskeyCourseid,JSON.stringify(Object.assign({},filtersFromStorage)));var label=$(this).parent()[0];$(label).removeClass("selected"),that.subCatDeleted=!0})),$(SELECTORS_SUBCATEGORY).on("click",(function(){var filterId="#"+that.sectionSelected+"-subcategoryId-"+$(this).data("subcategory"),filterCatId="#"+$(this).data("category");that.categoryDropValue=$(this).data("category-drop"),$(filterId).click(),!1===$(filterCatId).prop("checked")&&$(filterCatId).click(),that.subCatDeleted||$(this).addClass("selected bgf-c")}))},ManageAssignmentsFilter}));

//# sourceMappingURL=manage_assignments_filters.min.js.map
import $ from 'jquery';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';

const SELECTORS = {
    HEADER: '[data-region="header"]',
    NAMEGROUPTOPICS: '#namegrouptopics',
    BUTTONCOPYTOPICS: '.buttoncopytopics',
};

let registered = false;

export default class ModalChangeOtherGroups extends Modal {
    static TYPE = 'local_npe/Modal_Change_Other_Groups';
    static TEMPLATE = 'local_npe/courses/modal_change_other_groups';

    constructor(root) {
        super(root);
    }

    setData(nameGroup, havemoregroups) {
        if (havemoregroups === 1) {
            this.getRoot().find(SELECTORS.BUTTONCOPYTOPICS).hide();
        }
        this.getRoot().find(SELECTORS.NAMEGROUPTOPICS).text(nameGroup);
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {
            this.getRoot().removeClass('show');
            $('.change-other-groups-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {
            this.getRoot().find(SELECTORS.HEADER).show();
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.CANCELBUTTON, () => {
            $('.change-other-groups-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
            $('body').removeClass('modal-open');
        });
    }
}

if (!registered) {
    ModalRegistry.register(ModalChangeOtherGroups.TYPE, ModalChangeOtherGroups, ModalChangeOtherGroups.TEMPLATE);
    registered = true;
}

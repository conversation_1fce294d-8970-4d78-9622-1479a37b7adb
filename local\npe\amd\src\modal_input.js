import $ from 'jquery';
import Notification from 'core/notification';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';
import Ajax from 'core/ajax';
import EventListener from 'local_npe/event_listener';
import Str from 'core/str';
import Templates from 'core/templates';
import Events from 'local_npe/project_events';
import Services from 'local_npe/project_services';

const SELECTORS = {
    ACCEPT_BUTTON: '[data-action="accept"]',
    CANCEL_BUTTON: '[data-action="cancel"]',
    INPUT_COURSE_NAME: '[data-modal="input-value"]',
    MESSAGE_ALERT: '.message_alert',
};

let registered = false;

// TODO esta modal parece que no se usa, los eventos que la lanzan no se disparan nunca. revisar y borrar.
export default class ModalInput extends Modal {

    static TYPE = 'local_npe/modal_input';
    static TEMPLATE = 'local_npe/modal_input';

    #identifier;

    constructor(root) {
        super(root);

        if (!this.getFooter().find(SELECTORS.ACCEPT_BUTTON).length) {
            Notification.exception({message: 'No accept button found'});
        }

        if (!this.getFooter().find(SELECTORS.CANCEL_BUTTON).length) {
            Notification.exception({message: 'No cancel button found'});
        }
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.ACCEPT_BUTTON, () => {
            EventListener.shoutEvent(Events.BUTTONS_MODAL_INPUT.SUBMIT, {
                'identifier': this.#identifier,
                'inputval': $(SELECTORS.INPUT_COURSE_NAME).val()
            });
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.CANCEL_BUTTON, () => {
            EventListener.shoutEvent(Events.RELOAD.PROJECT, {
                'identifier': this.#identifier
            });
            this.hide();
            this.destroy();
        });

        this.getModal().on('focus', SELECTORS.INPUT_COURSE_NAME, () => {
            $(SELECTORS.MESSAGE_ALERT).html('');
            $(SELECTORS.ACCEPT_BUTTON).removeClass('disabled');
        });
    }

    setIntro(string, lang) {
        Str.get_strings(
            [
                {key: 'save', component: 'local_npe', lang: lang},
                {key: 'cancel', component: 'local_npe', lang: lang},
            ]
        ).done((strs) => {
            this.setBody(Templates.render('local_npe/modal_input_body', {intro: string}));
            this.setFooter(Templates.render('local_npe/modal_input_footer', {save: strs[0], cancel: strs[1]}));
        });
    }

    setId(string) {
        this.#identifier = string;
    }

    showError(dataError) {
        Templates.render('local_npe/modal_input_error_message', dataError).done((html) => {
            $('[data-region="modal_input_error_message"]').replaceWith(html);
            $(SELECTORS.ACCEPT_BUTTON).addClass('disabled');
        });
    }

    setSucces(courseid, coursename, namecreator, picture) {
        switch (this.#identifier) {
            case 0:
                // Join course.
                var arr = {name: coursename, namecreator: namecreator, picture: picture};
                this.setBody(Templates.render('local_npe/modal_input_succes_join', arr));
                this.setFooter(Templates.render('local_npe/modal_input_footer_button_continue', ''));
                Str.get_string('welcome', 'local_npe').done((welcome) => {
                    this.setTitle(welcome);
                });
                break;
            case 1:
                // Create course.
                var getTeacherKey = Ajax.call([{methodname: Services.GET_STUDENT_KEY, args: {courseid: courseid}}], true, true);
                getTeacherKey[0].done((response) => {
                    var arr = {name: coursename, studentcode: response.password};
                    this.setBody(Templates.render('local_npe/modal_input_succes_create', arr));
                    this.setFooter(Templates.render('local_npe/modal_input_footer_button_continue', ''));
                    Str.get_string('welcome', 'local_npe').done((welcome) => {
                        this.setTitle(welcome);
                    });
                });
                break;
            case 2:
                // Edit name.
                this.hide();
                this.destroy();
                break;
        }
    }
}

if (!registered) {
    ModalRegistry.register(ModalInput.TYPE, ModalInput, ModalInput.TEMPLATE);
    registered = true;
}

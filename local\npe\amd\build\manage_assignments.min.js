define("local_npe/manage_assignments",["jquery","jqueryui","local_npe/datepicker","local_npe/event_listener","local_npe/date_validator","core/str"],(function($,j<PERSON><PERSON><PERSON>,DatePicker,EventListener,DateValidator,Str){let SELECTORS_ROOT=".myassignments",SELECTORS_ADD_BUTTON=".add",SELECTORS_ACTIVITYLIST=".activities-list",SELECTORS_SEARCH="#search",SELECTORS_CLEAN=".clean",SELECTORS_HOUR=".hour-begining",SELECTORS_HOUREND=".hour-end",EVENTS_LISTCHANGE="list-changed",EVENTS_TEAMWITHCONFLICT="team-with-conflict",EVENTS_TEAMSWITHCONFLICTS="teams-with-conflicts";function ManageAssignment(){let resourcesaved=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",activitysaved=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",examsaved=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",assignmentsurl=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",urltoviewer=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",isassignmentfilter=arguments.length>5&&void 0!==arguments[5]&&arguments[5],txtSequence=arguments.length>6&&void 0!==arguments[6]?arguments[6]:"";ManageAssignment.prototype.constructor=ManageAssignment,ManageAssignment.prototype.root=null,ManageAssignment.prototype.inputdescription=null,ManageAssignment.prototype.form=null,ManageAssignment.prototype.resourcesaved=resourcesaved,ManageAssignment.prototype.activitysaved=activitysaved,ManageAssignment.prototype.examsaved=examsaved,ManageAssignment.prototype.assignmentsurl=assignmentsurl,ManageAssignment.prototype.urltoviewer=urltoviewer,ManageAssignment.prototype.isassignmentfilter=isassignmentfilter,ManageAssignment.prototype.txtSequence=txtSequence,setTemplate(txtSequence),this.init(),this.isAssignmentSaved(),this.changeView()}function session_storage_history(section){let urlParams=new URLSearchParams(window.location.search);urlParams.set("section",section);const urltogo=window.location.origin+window.location.pathname+"?"+urlParams;window.history.pushState({},"",urltogo),$(window).trigger("OnHistoryUrlStateUpdated",[urltogo])}function setTemplate(txtSequence){let section=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,noChangeText=arguments.length>2&&void 0!==arguments[2]&&arguments[2];var textoBreadcrumbs="";let urlParams=new URLSearchParams(window.location.search);if(null===section&&(section=urlParams.get("section")??"actAndRec"),$(".activities-container").hide().addClass("hidden"),$(".activities-container."+section).removeClass("hidden").show(),!noChangeText){const sections=$("a.section");let fromviewer=!1,urlfromviewer=!1,last_url_array=sessionStorage.getItem("urlhistory").split(",");if(last_url_array.length>=2){if(fromviewer=last_url_array[last_url_array.length-2].includes("viewer/index.php"),urlfromviewer=urlParams.get("fromviewer")??!1,fromviewer&&1==urlfromviewer){$("ul.npe-linkcontainer li").css("display","none"),$("ul.npe-linkcontainer li:last-child").css("content","");let a={txtSequence:txtSequence};Str.get_string("backtosequence","local_npe",a).done((function(string){$("ul.npe-linkcontainer li:last-child").text(string)})),$("ul.npe-linkcontainer li:last-child").addClass("noslash"),$("ul.npe-linkcontainer li:last-child").css("display","inline"),session_storage_history(section)}}!sections.length||fromviewer&&urlfromviewer||(textoBreadcrumbs=sections.filter("."+section).text(),$("ul.npe-linkcontainer li:last-child").text(textoBreadcrumbs),$("ul.npe-linkcontainer li:last-child").removeClass("noslash"),$("ul.npe-linkcontainer li").css("display","inline"),session_storage_history(section))}}return ManageAssignment.prototype.init=function(){this.root=$(SELECTORS_ROOT),EventListener.hearEvent(EVENTS_LISTCHANGE,function(e){e.detail.data.response.haselements?$(SELECTORS_ADD_BUTTON).prop("disabled",!1):$(SELECTORS_ADD_BUTTON).prop("disabled",!0)}.bind(this)),EventListener.hearEvent(EVENTS_TEAMWITHCONFLICT,function(){require(["local_npe/modal_team_conflict","core/modal_factory"],(function(ModalTeamConflict,ModalFactory){ModalFactory.create({type:ModalTeamConflict.TYPE},$("#success")).done((function(modal){modal.setData("single"),modal.show()}))}))}.bind(this)),EventListener.hearEvent(EVENTS_TEAMSWITHCONFLICTS,function(){require(["local_npe/modal_team_conflict","core/modal_factory"],(function(ModalTeamConflict,ModalFactory){ModalFactory.create({type:ModalTeamConflict.TYPE}).done((function(modal){modal.setData("multiple"),modal.show()}))}))}.bind(this)),$(SELECTORS_HOUR).length&&$(SELECTORS_ROOT).ready((function(){DateValidator.setFields('#userdate[name="user_date"]',SELECTORS_HOUR,SELECTORS_HOUREND,SELECTORS_ADD_BUTTON)})),this.prepareView(),this.searchInList()},ManageAssignment.prototype.prepareView=function(){$("#collapse-icon").on("click",(function(){$(this).hasClass("collapse-right")?($(".list-group-item").show(),$("#collapse-icon").removeClass("collapse-right").addClass("collapse-left"),$(".assignments-sidebar").removeClass("collapse-width"),$("#separator").removeClass("separator-collapse").addClass("separator")):($(".list-group-item").hide(),$("#collapse-icon").removeClass("collapse-left").addClass("collapse-right"),$(".assignments-sidebar").addClass("collapse-width"),$("#separator").removeClass("separator").addClass("separator-collapse"))}))},ManageAssignment.prototype.isAssignmentSaved=function(){window.performance&&window.performance.navigation.type===window.performance.navigation.TYPE_BACK_FORWARD&&(this.resourcesaved="",this.activitysaved="",this.examsaved="");var that=this;""!==this.resourcesaved&&"0"!==this.resourcesaved&&require(["local_npe/modal_success_assign","core/modal_factory"],(function(ModalSuccessAssign,ModalFactory){ModalFactory.create({type:ModalSuccessAssign.TYPE},$("#success")).done((function(modal){that.isassignmentfilter?(modal.setData("newresourceassignment",that.urltoviewer,that.isassignmentfilter,that.txtSequence),modal.show()):(modal.setData("newresourceassignment",that.assignmentsurl),modal.show())}))})),""!==this.activitysaved&&"0"!==this.resourcesaved&&require(["local_npe/modal_success_assign","core/modal_factory"],(function(ModalSuccessAssign,ModalFactory){ModalFactory.create({type:ModalSuccessAssign.TYPE},$("#success")).done((function(modal){that.isassignmentfilter?(modal.setData("newactivityassignment",that.urltoviewer,that.isassignmentfilter,that.txtSequence),modal.show()):(modal.setData("newactivityassignment",that.assignmentsurl),modal.show())}))})),""!==this.examsaved&&"0"!==this.resourcesaved&&require(["local_npe/modal_success_assign","core/modal_factory"],(function(ModalSuccessAssign,ModalFactory){ModalFactory.create({type:ModalSuccessAssign.TYPE},$("#success")).done((function(modal){that.isassignmentfilter?(modal.setData("newactivityassignment",that.urltoviewer,that.isassignmentfilter,that.txtSequence),modal.show()):(modal.setData("newactivityassignment",that.assignmentsurl),modal.show())}))}))},ManageAssignment.prototype.searchInList=function(){$(".icon-lupa").on("click",(function(){$(".icon-lupa").toggleClass("active-icon"),$(".assignments-search").toggleClass("active-input"),$(window).width()<799&&$(".npe-page-header-title").toggle(),$("#search").focus()})),$(SELECTORS_SEARCH).val()||$(SELECTORS_ACTIVITYLIST).each((function(){$(this).find(".count").html("("+$(this).find(".activity-item").length+")")})),$("#search, .clean").on("keyup click",(function(){$(SELECTORS_SEARCH).val()||$(SELECTORS_ACTIVITYLIST).each((function(){$(this).find(".count").html("("+$(this).find(".activity-item:not(.hidden)").length+")")}));var searching=$(this).val();$(SELECTORS_CLEAN).on("click",(function(){$(SELECTORS_SEARCH).val(""),$(".icon-lupa").removeClass("active-icon"),$(".assignments-search").removeClass("active-input")})),$(SELECTORS_ACTIVITYLIST).each((function(){let name=$(this).data("name"),sec=$(this).data("section"),list=$(`[data-section='${sec}'][data-name="${name}"]`),numberofelements=$(list).find(".activity-item").length;$(list).find(".activity-item").filter((function(){return"none"==$(this).css("display")})).length===numberofelements?$(list).hide():$(list).show(),$('.showmore[data-section="'+sec+'"]').is(":visible")||$('.activities-list[data-section="'+sec+'"]').filter(":visible").each((function(){var section=this,cat=$(this).data("category");$("#subcatsdropdown-"+sec+"-"+cat+" .subcat").filter(":visible").each((function(){var subcat=$(this).data("subcategory"),counter=$(section).find(".activity-item").filter('[data-subcategoryid="'+subcat+'"]').filter(":visible").length,count=$(this).find("counter");0===counter?$(this).hide():(count.html("("+counter+")"),$(this).show())}))}))}));var isVisible=0;$(".activity-item").each((function(){$(this).is(":visible")&&isVisible++})),0===isVisible&&searching.length>0&&!($(".assignments-filter-selected").length>0)?($(".noresultsassignments").hide(),$(".noresultsfilter").hide(),$(".noresultssearch").show()):0!==isVisible||searching.length>0||$(".assignments-filter-selected").length>0?0===isVisible&&!(searching.length>0)&&$(".assignments-filter-selected").length>0?($(".noresultssearch").hide(),$(".noresultsfilter").show(),$(".noresultsassignments").hide()):0===isVisible&&searching.length>0&&$(".assignments-filter-selected").length>0?($(".noresultssearch").show(),$(".noresultsfilter").hide(),$(".noresultsassignments").hide()):0!==isVisible&&($(".noresultsassignments").hide(),$(".noresultsfilter").hide(),$(".noresultssearch").hide()):($(".noresultssearch").hide(),$(".noresultsfilter").hide(),$(".noresultsassignments").show())})),$(SELECTORS_CLEAN).on("click",(function(){$(SELECTORS_ACTIVITYLIST).each((function(){$(this).find(".count").html("("+$(this).find(".activity-item:not(.hidden)").length+")");var section=this;let sec=$(this).data("section");var cat=$(this).data("category");$("#subcatsdropdown-"+sec+"-"+cat+" .subcat").each((function(){var subcat=$(this).data("subcategory"),counter=$(section).find(".activity-item").filter('[data-subcategoryid="'+subcat+'"]').length;$(this).find("counter").html("("+counter+")"),$(this).show()}))}))})),$("#search").click()},ManageAssignment.prototype.changeView=function(){$(".sidebar-menu a").on("click",(function(){let section=$(this).data("section");setTemplate(this.txtSequence,section)}))},ManageAssignment}));

//# sourceMappingURL=manage_assignments.min.js.map
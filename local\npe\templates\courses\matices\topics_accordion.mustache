<div class="cardindex matices" idcourse="{{courseid}}" codproduct="{{codproduct}}" coduser="{{coduser}}" codcentro="{{codcentro}}" namegroup="{{group}}">
    <div class="cards">
        {{# isteacher}}
            <div class="content-bottom-gear-close npe-content-bottom-gear-close matices">
                {{> local_npe/courses/matices/topics_dropdown }}
            </div>
        {{/ isteacher}}
        {{^ isteacher}}
            {{# hasteachers}}<div class="row distance-50"></div>{{/ hasteachers}}
            {{^ hasteachers}}<div class="row distance-30"></div>{{/ hasteachers}}
        {{/ isteacher}}

        <div class="accordion{{^ isteacher}} mmt{{/ isteacher}}" id="npeAccordion">
            {{# topicsblocks }}
                <div class="card-index" id="{{packerid}}" >
                    <div class="card-header card-header-block bgf-c {{^ name }}d-none{{/ name }}" id="heading{{id}}" positionblock="" data-packerid={{packerid}}>
                        <div class="text-car-header-index">
                            {{# name }} <h2>{{ name }}</h2>{{/ name }}
                        </div>
                        <div class="buttons-car-header-index">
                            <button class="item-index npe-eye-topics content-icon-eye-open toggleEyeTopics" data-idblock={{id}} data-packerid={{packerid}} id="{{id}}"><i class="icon-eye-open"></i></button>
                            <div class="item-index npe-fullscream-topics"><i class="icon-fullscream"></i></div>
                        </div>
                    </div>
                    <div id="collapse{{id}}" class="collapse show pd0" aria-labelledby="heading{{id}}" data-packerid={{packerid}}>
                        <div class="card-deck">
                            <div class="card-body sortable connected npe-grid {{ theme }}" idblock="{{id}}" data-packerid="{{packerid}}">
                                {{> local_npe/courses/matices/topics }}
                            </div>
                        </div>
                    </div>
                </div>
            {{/ topicsblocks }}
        </div>

        {{# isteacher}}
            <div class="accordion hide" id="npeAccordionAlt">
                {{# topicsalt }}
                    <div class="card-index" id="{{packerid}}" >
                        <div class="card-header bb-a" id="heading{{id}}" positionblock="" data-packerid={{packerid}}>
                            <div class="text-car-header-index">
                                {{# name }} <h2>{{ name }}</h2>{{/ name }}
                            </div>
                            <div class="buttons-car-header-index">
                                <div class="item-index npe-fullscream-topics"><i class="icon-fullscream"></i></div>
                                <button class="item-index npe-eye-topics content-icon-eye-open" data-idblock={{id}} data-packerid={{packerid}} id="{{id}}"><i class="icon-eye-open"></i></button>
                            </div>
                        </div>
                        <div id="collapse{{id}}" class="collapse show" aria-labelledby="heading{{id}}" data-packerid={{packerid}}>
                            <div class="card-deck">
                                <div class="card-body sortable connected npe-grid {{ theme }}" idblock="{{id}}" data-packerid="{{packerid}}">
                                    {{# isstandard }}
                                        {{> local_npe/courses/matices/topics/topic_standard }}
                                    {{/ isstandard }}
                                    {{^ isstandard }}
                                        {{> local_npe/courses/matices/topics/topic_special }}
                                    {{/ isstandard }}
                                </div>
                            </div>
                        </div>
                    </div>
                {{/ topicsalt }}
            </div>
        {{/ isteacher}}
    </div>
</div>

{{# js }}
    require(['local_npe/button_change_other_groups'], 
        function(ButtonChangeOtherGroups) {
            new ButtonChangeOtherGroups();
        }
    );

    $('.matices-edit')
        .on('click', function() {
            $('.card-header-block').removeClass('d-none');
        })
        .on('mouseenter', '.activeindex', function() {
            $(this).addClass('bgf-c');
        })
        .on('mouseleave', '.activeindex', function() {
            $(this).removeClass('bgf-c');
    });

    $('#npeAccordion').each(function() {
        var header = $(this).find('.card-header-block');
        // Por cada bloque de tarjetas, vemos si estas las unidades ocultas para cambiar la cabecera
        var notHidden = $(this).find('.topic_card').filter(function() {
            return !$(this).hasClass('npe_topic_black_only_teacher') && $(this).hasClass('npe_topic_white');
        });

        if (notHidden.length === 0) {
            $(header).removeClass('bgf-c').addClass('black');
        } else {
            return;
        }
    });

    var eyeControl = [];
    $(document).on('click', '.toggleEyeTopics', function() {
        var that = $(this).parent().closest('.card-index');
        var id = $(that).attr('id');
        eyeControl[id] = eyeControl[id] ?? $(this).hasClass('npe-eye-topics-close');

        $(that).find('.topic_card').each(function() {
            if ($(this).hasClass('npe_topic_black_only_teacher')) {
                return;
            }

            var topicCard = $(this);
            if(eyeControl[id] === false) {
                fullWhite(that, topicCard);
            } else {
                fullBlack(that, topicCard);
            }
        });

        eyeControl[id] = !eyeControl[id]
    });

    function fullWhite (item1, item2) {
        $(item1).find('.toggleEyeTopic').removeClass('bg-c');
        $(item2).find('.only-teacher').addClass('d-none');
        $(item2).addClass('npe_topic_white').find('.topic-col-img').removeClass('npe_topic_black').addClass('npe_topic_white');
        if($(item2).hasClass('npe_topic_white')) {
            $(item2).removeClass("npe_topic_black").addClass("npe_topic_white");
        }
    }

    function fullBlack (item1, item2) {
        $(item1).find('.toggleEyeTopic').addClass('bg-c');
        $(item2).find('.only-teacher').removeClass('d-none');
        $(item2).addClass('npe_topic_black').find('.topic-col-img').removeClass('npe_topic_white').addClass('npe_topic_black');
    }

    $(document).ready(function() {
        $('.topic-col-filter[data-href]').on('click', function(event) {
            event.stopPropagation();
            event.preventDefault();
            window.location.href = $(this).data('href');
        });

        $('.dropdown-course-index-item[data-href]').on('click', function(event) {
            event.stopPropagation();
            event.preventDefault();
            window.location.href = $(this).data('href');
        });

        $('.dropdown-item.share-url[data-url]').on('click', function(event) {
            event.stopPropagation();
            event.preventDefault();
        });

        $(document)
            .on('click', '.toggleEyeTopic', function() {
                var topicCard = $(this).closest('.topic_card');
                if ($(this).hasClass('bg-c')) {
                    $(this).removeClass('bg-c');
                    $(topicCard).find('.only-teacher').addClass('d-none');
                    $(topicCard).addClass('npe_topic_white').find('.topic-col-img').removeClass('npe_topic_black').addClass('npe_topic_white');
                } else {
                    $(this).addClass('bg-c');
                    $(this).closest('.topic_card').find('.only-teacher').removeClass('d-none');
                    $(topicCard).addClass('npe_topic_black').find('.topic-col-img').removeClass('npe_topic_white').addClass('npe_topic_black');
                }
            })
            .on('mouseenter', '.toggleEyeTopics', function() {
                if (!$(this).hasClass('npe-eye-topics-close')) {
                    $(this).addClass('bgf-d');
                }
            })
            .on('mouseleave', '.toggleEyeTopics', function() {
                if (!$(this).hasClass('npe-eye-topics-close')) {
                    $(this).removeClass('bgf-d');
                }
            });

        $(document).on('click', '.activeindex', function() {
            $('.topic_card').each(function() {
             const button = $(this).find('.toggleEyeTopic');
                if ($(this).hasClass('editmode')
                    && $(this).hasClass('npe_topic_black')) {
                    button.addClass('bg-c');
                }
            });
        });

        $('.topic_card').each(function() {
            if ($(this).hasClass('npe_topic_white')) {
                $(this).find('.only-teacher-info').addClass('d-none');
            }
        });
    });

    $('.topic-col-filter, .toggleEyeTopic')
        .on('mouseenter', function() {
            if ($(this).hasClass('bg-c')) {
                return;
            }
            $(this).addClass('bgf-c');
        });

    $('.topic-col-filter, .toggleEyeTopic')
        .on('mouseleave', function() {
            $(this).removeClass('bgf-c');
        });
{{/ js }}
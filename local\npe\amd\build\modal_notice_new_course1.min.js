define("local_npe/modal_notice_new_course1",["exports","jquery","core/custom_interaction_events","core/modal","core/modal_registry","local_npe/modal_notice_new_course2","core/modal_factory"],(function(_exports,_jquery,_custom_interaction_events,_modal,_modal_registry,_modal_notice_new_course,_modal_factory){function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _defineProperty(e,r,t){return(r=function(t){var i=function(t,r){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var i=e.call(t,r||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==typeof i?i:i+""}(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_custom_interaction_events=_interopRequireDefault(_custom_interaction_events),_modal=_interopRequireDefault(_modal),_modal_registry=_interopRequireDefault(_modal_registry),_modal_notice_new_course=_interopRequireDefault(_modal_notice_new_course),_modal_factory=_interopRequireDefault(_modal_factory);const SELECTORS_USERNAMETEACHER="#usernameteacher",SELECTORS_GOON_BUTTON='[data-action="go-on"]',SELECTORS_NOTNOW_BUTTON='[data-action="not-now"]';let registered=!1;class ModalNoticeNewCourse1 extends _modal.default{constructor(root){super(root)}setData(username){this.getRoot().find(SELECTORS_USERNAMETEACHER).text(username)}registerEventListeners(){super.registerEventListeners(this),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_GOON_BUTTON,(()=>{this.getRoot().removeClass("show"),(0,_jquery.default)("body").removeClass("modal-open"),(0,_jquery.default)(".notice-new-course-hi-modal").remove(),(0,_jquery.default)(".modal-backdrop").removeClass("show").addClass("hide"),_modal_factory.default.create({type:_modal_notice_new_course.default.TYPE}).done((modal2=>{modal2.show()}))})),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_NOTNOW_BUTTON,(()=>{this.getRoot().removeClass("show"),(0,_jquery.default)("body").removeClass("modal-open"),(0,_jquery.default)(".notice-new-course-hi-modal").remove(),(0,_jquery.default)(".modal-backdrop").removeClass("show").addClass("hide"),(0,_jquery.default)("#wizard-noticenewcourse").show(),(0,_jquery.default)("#screen-megamenu").hide()}))}}return _exports.default=ModalNoticeNewCourse1,_defineProperty(ModalNoticeNewCourse1,"TYPE","local_npe/modal_notice_new_course_1"),_defineProperty(ModalNoticeNewCourse1,"TEMPLATE","local_npe/noticenewcourse/noticenewcourse_modal01_hi"),registered||(_modal_registry.default.register(ModalNoticeNewCourse1.TYPE,ModalNoticeNewCourse1,ModalNoticeNewCourse1.TEMPLATE),registered=!0),_exports.default}));

//# sourceMappingURL=modal_notice_new_course1.min.js.map
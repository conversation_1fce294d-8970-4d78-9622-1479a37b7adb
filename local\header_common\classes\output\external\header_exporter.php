<?php

// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_header_common\output\external;

use core\external\exporter;
use renderer_base;
use local_header_common\api;
use local_npe\app;

defined('MOODLE_INTERNAL') || die();

/**
 * Class assign_group_exporter
 *
 * @package    local_header_common
 * @copyright  2022 SM
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class header_exporter extends exporter {

    protected static function define_related() {
        return [];
    }

    /**
     * Define Other Properties
     *
     * @return array
     */
    protected static function define_other_properties() {
        return [
            'img' => array(
                'type' => PARAM_RAW,
                'optional' => true,
            ),
            'user_name' => array(
                'type' => PARAM_RAW,
                'optional' => true,
            ),
            'notifications_counter' => array(
                'type' => PARAM_RAW,
                'optional' => true,
            ),
            'homeurl' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'basicdownloadurl' => [
                'type' => PARAM_RAW,
                'optional' => true
            ], 'logourl' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'logohelp' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'mylicenseurl' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'helpurl' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'contacturl' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'reportsurl' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'logouturl' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'showhelponheader' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'showcontacturl' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'showhelponusermenu' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'myprofilelink' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'mylicensesurl' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'showgoogleclassroomlink' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'showmicrosoftteamslink' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'isroldirectoractive' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'isdirector' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'ismarsupial' => [
                'type' => PARAM_BOOL,
                'optional' => true
            ],
            'mynotificationsurl' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'extralinks' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'logonotification' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'logogoogleclassroom' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'logomicrosoftteam' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'userlang' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'roldirector_url' => [
                'type' => PARAM_RAW,
                'optional' => true
            ],
            'isseneca' => [
                'type' => PARAM_BOOL,
                'optional' => true
            ]
        ];
    }

    /**
     * Get the additional values to inject while exporting.
     *
     * @param \renderer_base $output The renderer.
     * @return array Keys are the property names, values are their values.
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\user_exception
     * @throws \moodle_exception
     */

    protected function get_other_values(renderer_base $output) {
        global $CFG, $OUTPUT, $USER, $DB;

        $isdirector = 0;
        $notificationscounter = 0;
        $showcontacturl = false;

        if (isset($USER->idnumber)) {
            $user = app::get_userfactory()->get_current_user();
            $notificationscounter = !$user->get_user_data()->is_educamos_user() ?
                $this->get_count_notifications($user->get_idnumber()) : 0;
            $isdirector = $user->get_user_data()->get_phone1();
            $userdata = $user->get_user_data();
            if($userdata->is_sma_user() || $userdata->is_marsupial_user() || $userdata->is_seneca_user()){
                $showcontacturl = true;
            }
        }

        $user = $USER->id !== 0 ? $USER : 0;

        $ismarsupial = $user !== 0 ? app::get_userfactory()->get_current_user()->get_user_data()->is_marsupial_user() : false;
        $isexternal = $user !== 0 ? app::get_userfactory()->get_current_user()->get_user_data()->is_external_user() : false;
        $isseneca = $user !== 0 ? app::get_userfactory()->get_current_user()->get_user_data()->is_seneca_user() : false;
        $showcontacturl = false;
        $userdata = null;

        if ($user !== 0) {
            $lang = $DB->get_field('user', 'lang', array('id' => $user->id));
            $userdata = app::get_userfactory()->get_current_user()->get_user_data();
            if ($userdata->is_sma_user() || $userdata->is_marsupial_user() || $userdata->is_seneca_user()) {
                $showcontacturl = true;
            }
        } else {
            $lang = $CFG->lang;
        }

        $extralinks = get_config('local_header_common', 'showgoogleclassroomlink') === '1'
            || get_config('local_header_common', 'showmicrosoftteamslink') === '1';

        if ($ismarsupial) {
            $profileurl = app::get_url_helper()->get_evasm_redirect(
                get_config('local_header_common',
                    'domainheader') . '/local/profile/'
            );
        } elseif ($isexternal) {
            $profileurl = app::get_url_helper()->get_evasm_redirect(
                get_config('local_header_common',
                    'domainheader') . '/local/profile/externaluser.php'
            );
        } else {
            $profileurl = get_config('local_header_common', 'domainheader') . '/local/profile/';
        }

        $licenseurl = app::get_url_helper()->get_evasm_redirect(
            get_config('local_header_common', 
                'domainheader') . '/local/dashboard/mylicenses.php'
        );

        $values = [
            'basicdownloadurl' => get_config('local_header_common', 'basicdownloadurl'),
            'logourl' => $CFG->wwwroot . '/local/header_common/pix/logo/logo_sm.svg',
            'logohelp' => $CFG->wwwroot . '/local/header_common/pix/help.svg',
            'logonotification' => $CFG->wwwroot . '/local/header_common/pix/bell.svg',
            'mylicensesurl' => $licenseurl,
            'notifications_counter' => $notificationscounter,
            'mynotificationsurl' => get_config('local_header_common', 'domainheader') .
                '/local/notifications/mynotifications.php',
            'helpurl' => empty($user->icq) ? '' : api::get_faq_url(),
            'contacturl' => api::get_contact_url(),
            'logogoogleclassroom' => $CFG->wwwroot . '/local/header_common/pix/google_classroom.svg',
            'logomicrosoftteam' => $CFG->wwwroot . '/local/header_common/pix/microsoft_teams.svg',
            'userlang' => $this->getTranslate($lang),
            'logouturl' => new \moodle_url('/login/logout.php', array('sesskey' => sesskey())),
            'showhelponheader' => 1,
            'showcontacturl' => $showcontacturl,
            'extralinks' => $extralinks,
            'img' => $userdata ? $userdata->get_picture() : $CFG->wwwroot . '/theme/npe/pix_core/u/f1.svg',
            'user_name' => empty($user) ? '' : $user->firstname,
            'showhelponusermenu' => get_config('local_header_common', 'showhelponusermenu') == '1',
            'myprofilelink' => $profileurl,
            'showgoogleclassroomlink' => get_config('local_header_common', 'showgoogleclassroomlink') == '1',
            'showmicrosoftteamslink' => get_config('local_header_common', 'showmicrosoftteamslink') == '1',
            'isdirector' => $isdirector,
            'ismarsupial' => $ismarsupial,
            'isroldirectoractive' => get_config('local_header_common', 'roldirectoractive'),
            'reportsurl' => get_config('local_header_common', 'roldirectorurl'),
            'roldirector_url' => api::calculate_roldirector_jwt(),
            'isseneca' => $isseneca
        ];

        return $values;
        //return parent::get_other_values($output); // TODO: Change the autogenerated stub
    }

    private function getTranslate($lang) {
        $translate = array(
            'help' => get_string_manager()->get_string('help', 'local_header_common', null, $lang),
            'notifications' => get_string_manager()->get_string('notifications', 'local_header_common', null, $lang),
            'hello_start' => get_string_manager()->get_string('hello_start', 'local_header_common', null, $lang),
            'edit_profile' => get_string_manager()->get_string('edit_profile', 'local_header_common', null, $lang),
            'deleteuser' => get_string_manager()->get_string('deleteuser', 'local_header_common', null, $lang),
            'resetsettings' => get_string_manager()->get_string('resetsettings', 'local_header_common', null, $lang),
            'mylicenses' => get_string_manager()->get_string('mylicenses', 'local_header_common', null, $lang),
            'download_app' => get_string_manager()->get_string('download_app', 'local_header_common', null, $lang),
            'reports' => get_string_manager()->get_string('reports', 'local_header_common', null, $lang),
            'googleclassroom' => get_string_manager()->get_string('googleclassroom', 'local_header_common', null, $lang),
            'microsoftteams' => get_string_manager()->get_string('microsoftteams', 'local_header_common', null, $lang),
            'logout' => get_string_manager()->get_string('logout', 'local_header_common', null, $lang),
            'title_link_my_profile' => get_string_manager()->get_string('title_link_my_profile', 'local_header_common', null,
                $lang),
            'headercontact' => get_string_manager()->get_string('footercontact', 'local_header_common', null, $lang),
        );
        return $translate;
    }

    private function get_count_notifications($userguid) {
        global $CFG;

        $url_instanceevasm = trim(get_config('local_npe', 'instanceevasm_url'), '/');
        $url_evasm = $url_instanceevasm . '/lib/ajax/service-nologin.php';

        $headers = [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json',
        ];
        $fnparams = (object) [
            'userguid' => $userguid,
        ];

        $args = [
            (object) [
                'methodname' => 'local_evasm_common_send_counter_notifications',
                'args' => $fnparams,
            ]
        ];

        $respuesta = download_file_content($url_evasm, $headers, json_encode($args));

        $respuesta = json_decode($respuesta);

        if ($respuesta && $respuesta[0]->error) {
            throw new \moodle_exception($respuesta[0]->data->message);
        }

        if ($respuesta) {
            return $respuesta[0]->data->count;
        }
        return null;
    }

}

<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_npe\exporter\assignments_data;

use local_npe\access_control;
use local_npe\activity\activity;
use local_npe\app;
use local_npe\constants;
use local_npe\course;
use local_npe\DTO\activity_category_dto;
use local_npe\DTO\activity_section_dto;
use local_npe\exporter_data;
use local_npe\core\persistent\activity_subcategory;
use local_npe\filter\assignment_filter;
use local_npe\product;
use function count;
use local_npe\product\megamenu;

/**
 * Class category_data
 *
 * @package local_npe\exporter\assignments_data
 */
class category_data extends exporter_data {

    public $id;
    public $name;
    public $count;
    public $category;

    public $items = [];
    public $subcats = [];
    public $showsubcats;
    public $subcatlabel;

    public $showsubcategories;
    public $subcatname;
    public $resourcename;
    public $unitname;

    // Parámetros no exportables.
    /** @var course */
    private $course;
    /** @var product */
    private $product;
    /** @var assignment_filter */
    private $assignmentfilter;
    /** @var activity_data */
    private $mockactivitydata;
    private $sectionid;
    /** @var activity_category_dto */
    private $categorydto;

    public function __construct(course $course, product $product, assignment_filter $assignmentfilter) {
        $this->course = $course;
        $this->product = $product;
        $this->assignmentfilter = $assignmentfilter;
    }

    public function set_section_id($id) {
        $this->sectionid = $id;
    }

    /**
     * @param activity_category_dto $categorydto
     * @return void
     */
    public function set_category_dto(activity_category_dto $categorydto) {
        $this->categorydto = $categorydto;
    }

    /**
     * Establecer los parámetros a exportar.
     *
     * @throws \JsonException
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\activity_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\user_exception
     * @throws \moodle_exception
     */
    public function set_params(): void {
        if ($this->categorydto) {
            $this->id = $this->categorydto->get_id();
            $this->name = $this->categorydto->get_name();
            $this->category = $this->categorydto->get_category();
        }
        $user = app::get_userfactory()->get_current_user();
        // Actividades de la categoría dada.
        $activities = $this->id === null
            ? $this->product->get_activities_and_resources_by_section($this->sectionid, $user, $this->course)
            : $this->product->get_activities_and_resources_by_category($this->categorydto, $user, $this->sectionid, $this->course);
        $totalactividades = count($activities);
        $this->count = $totalactividades;
        $this->set_items($activities);

        $subcats = [];
        if ($this->id) {
            $sbcontrol = array_column($this->items, 'subcategoryid');
            foreach($sbcontrol as $index=>$value) {
                if(empty($value)) unset($sbcontrol[$index]);
            }
            $subcats = array_count_values($sbcontrol);
        }

        if ($subcats) {
            $this->set_subcategories($subcats);
        }

        $this->showsubcats = false;
        if ($this->subcats) {
            $this->showsubcats = true;
        }
    }

    /**
     * @param $subcats
     */
    private function set_subcategories($subcats) {

        // TODO: Eliminar cuando se hagan los cambios visuales de los filtros.
        $sections = $this->product->get_product_data()->get_repository_json();
        $arrStdClass = get_object_vars($sections);
        $sections2 = $arrStdClass['sections'];
        $allfilter = [];
        foreach ($sections2 as $item) {
            foreach ($item->sectionFilters as $filter) {
                if ($filter->filter == 'subcategoryId') {
                    $this->subcatlabel = $filter->filterLabel;
                    break;
                }
            }
        }
        
        $productid = $this->product->get_id();
        $subcategories = activity_subcategory::get_subcategories_by_product($productid);

        foreach ($subcategories as $subcategory) {
            if (array_key_exists($subcategory->name, $subcats)){
                $this->subcats[] = [
                    'subcatname' => $subcategory->name,
                    'subcatrealname' => $subcategory->realname,
                    'subcatcounter' => $subcats[$subcategory->name]
                ];
            }
        }
    }

    /**
     * @param $activities
     * @throws \JsonException
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\activity_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\user_exception
     * @throws \moodle_exception
     */
    private function set_items($activities) {
        foreach ($activities as $activity) {
            if ($this->can_show_activity($activity) === false) {
                continue;
            }
            $activitydata = $this->get_activity_data();
            $activitydata->set_activity($activity);
            $activitydata->set_params();
            $this->items[] = $activitydata;
        }
        $this->noitems = empty($this->items) ? true : false;
    }

    /**
     * Verificar si el usuario actual puede acceder a la actividad.
     *
     * @param activity $activity
     * @return bool
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\activity_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\user_exception
     * @throws \moodle_exception
     */
    private function can_show_activity(activity $activity) {
        return access_control::check_activity_access($this->course->get_id(), $activity, false);
    }

    /**
     * @return activity_data
     */
    private function get_activity_data() {
        return $this->mockactivitydata ?: new activity_data($this->course, $this->assignmentfilter);
    }

    /**
     * Se emplea para Test Unitarios.
     *
     * @param activity_data $activitydata
     */
    public function set_mock_activity_data(activity_data $activitydata) {
        $this->mockactivitydata = $activitydata;
    }
}

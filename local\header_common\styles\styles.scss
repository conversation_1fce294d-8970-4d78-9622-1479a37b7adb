header.header__wrapper.header_common {
    -webkit-box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.05);
    height: 50px;
    position: fixed;
    background-color: #fff;
    z-index: 1051;
    width: 100%;
    font-family: "Montserrat", sans-serif;
    top: 0;
    .header {
        height: 100%;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #fff;
        padding: 0px 20px;
        &.marsupial a[data-args="#"]{
            pointer-events: none;
        }
        a {
            text-decoration: none;
            color: #0070a8;
            img.icon {
                height: 36px;
                width: auto;
                -webkit-background-size: contain;
                background-size: contain;
                padding: 0;
                vertical-align: middle;
            }
        }
        .header__right-wrapper {
            display: flex;
            height: 100%;
            position: relative;
            gap: 15px;
            .header__help {
                a {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    height: 100%;
                    .tooltiptext {
                        visibility: hidden;
                        padding: 10px;
                        background: #fbfbfb;
                        -webkit-box-shadow: 0 3px 3px 0 rgba(0, 0, 0, .4);
                        box-shadow: 0 3px 3px 0rgba(0,0,0,.4);
                        border-radius: 5px;
                        font-size: 12px;
                        text-align: center;
                        font-family: Montserrat;
                        line-height: 20px;
                        color: #1a6ca0;
                        font-weight: 700;

                         /* Position the tooltip */
                         position: absolute;
                         z-index: 1;
                         top: 90%;
                         left: 35%;
                         margin-left: -60px;
                    }
                }
                a:hover{
                    .tooltiptext {
                        visibility: visible;
                    }
                }
                img {
                    width: 24px;
                }
            }
            .header__mynotifications {
                position: relative;
                a {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    height: 100%;
                    margin-right: 25px;
                    .tooltiptext {
                        visibility: hidden;
                        padding: 10px;
                        background: #fbfbfb;
                        -webkit-box-shadow: 0 3px 3px 0 rgba(0, 0, 0, .4);
                        box-shadow: 0 3px 3px 0rgba(0,0,0,.4);
                        border-radius: 5px;
                        font-size: 12px;
                        text-align: center;
                        font-family: Montserrat;
                        line-height: 20px;
                        color: #1a6ca0;
                        font-weight: 700;

                         /* Position the tooltip */
                         position: absolute;
                         z-index: 1;
                         top: 90%;
                         left: 35%;
                         margin-left: -60px;
                    }
                }
                a:hover{
                    .tooltiptext {
                        visibility: visible;
                    }
                }
                img {
                    height: 24px;
                }
            }
            .header__profile {
                display: flex;
                align-items: center;
                cursor: pointer;
                .button-profile-wrapper {
                    position: absolute;
                    width: 100%;
                    height: 100%
                }

                .header__profile__more {
                    color: #0e0e0e;
                    font-size: 16px;
                    line-height: 14px;
                    width: 35px;
                    margin: 0;
                }
                .header__profile__more:before {
                    margin-left: 10px;
                }
                .header__profile__photo {
                    display: flex;
                    height: 36px;
                    .userinitials.size-64 {
                        width: 36px;
                        height: 36px;
                        background-color: #1a6ca0;
                        color: #fff;
                    }
                }
                .header__profile__photo img {
                    height: 36px;
                    width: 36px;
                    -moz-border-radius: 50%;
                    -webkit-border-radius: 50%;
                    border-radius: 50%;
                    margin: 0;
                }
            }
            .header__user-area__wrapper {
                -webkit-box-shadow: 0 6px 6px 0 rgba(0, 0, 0, 0.2);
                -moz-box-shadow: 0 6px 6px 0 rgba(0, 0, 0, 0.2);
                box-shadow: 0 6px 6px 0 rgba(0, 0, 0, 0.2);
                -moz-border-radius: 0 0 5px 5px;
                -webkit-border-radius: 0 0 5px 5px;
                border-radius: 0 0 5px 5px;
                background-color: #fff;
                display: none;
                overflow-x: hidden;
                position: absolute;
                right: 0;
                top: 50px;
                width: 380px;
                z-index: 3;
                font-family: Montserrat, sans-serif;
                .header__user-area {
                    margin-left: 40px;
                    ul {
                        margin: 40px 0 10px;
                        font-size: 18px;
                        color: #666;
                        padding-left: 0;
                        li {
                            margin-bottom: 20px;
                            line-height: initial;
                            list-style: none;
                            a {
                                color: #666;
                                line-height: 29px;
                            }
                        }
                    }
                    .header__user-area__name {
                        font-size: 20px;
                        font-weight: 600;
                        line-height: 24px;
                        margin-bottom: 11px;
                        margin-top: 33px;
                    }
                    .header__user-area__edit {
                        display: flex;
                        color: #1a6ca0;
                        font-size: 16px;
                        font-weight: 600;
                        line-height: 22px;
                        span.thumbnail {
                            border-radius: 50%;
                            height: 20px;
                            overflow: hidden;
                            padding: 0;
                            width: 20px;
                            img,
                            .userinitials.size-64 {
                                height: 100%;
                                vertical-align: unset;
                                width: 100%;
                            }
                            .userinitials.size-64 {
                                background-color: #1a6ca0;
                                color: #fff;
                                font-size: 12px;
                            }
                        }
                        span.myprofile {
                            margin-left: 10px;
                        }
                    }
                }
                .header__user-area__close {
                    background-color: #1a6ca0;
                    height: 60px;
                    text-align: center;
                    font-size: 20px;
                    color: #fff;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding-top: 18px;
                    padding-bottom: 18px;
                    a {
                        color: #fff;
                        line-height: 24px;
                    }
                    a:before {
                        font-size: 24px;
                        height: 24px;
                        width: 27px;
                        position: relative;
                        content: url(../pix/cerrar_sesion_sma.svg);
                        font-family: "fontello";
                        font-style: normal;
                        speak: none;
                        font-variant: normal;
                        text-transform: none;
                        display: inline-block;
                        color: #fff;
                        float: left;
                        vertical-align: middle;
                    }
                    span {
                        margin-left: 12px;
                        display: inline-block;
                        vertical-align: middle;
                    }
                }
            }
        }
    }
    .icon-down-open:before {
        content: url("../pix/dropdown.svg");
        margin-right: .3em;
    }
    .header__mynotifications__counter {
        display: flex;
        align-items: center;
        justify-content: center;
        align-self: flex-start;
        padding: 2px 4px;
        border-radius: 50%;
        background-color: red;
        color: #fff;
        text-align: center;
        font-size: 14px;
        font-weight: 700;
        position: absolute;
        top: 5px;
        left: 24px;
        margin-top: 0;
        min-width: 18px;
        height: 18px;
    }
}

header.header__wrapper.header_common.educamosheader {
    height: 28px;
    .header {
        display: flex;
        margin: 0 auto;
        justify-content: space-between;
        align-items: center;
        background-color: #fff;
        min-width: 450px;
        img.icon {
            height: 17.89px;
            width: auto;
            background-size: contain;
            padding: 0;
            vertical-align: middle;
        }
        .header__right-wrapper {
            .header__user-area__wrapper {
                display: block;
                top: 28px;
                width: -moz-available;
                width: -webkit-fill-available;
                min-width: 224px;
                border-radius: 0;
                position: absolute;
                right: 0;
                z-index: 3;
                .header__user-area {
                    margin-left: 0;
                    ul {
                        margin: 0;
                        font-size: 12px;
                        li {
                            margin: 15px 16px 15px;
                            a {
                                font-size: 12px;
                            }
                        }
                        .educamosclose {
                            margin: 0 12px;
                            border-top: 1px solid;
                            float: none;
                            text-shadow: none;
                            opacity: 1;
                            color: #ccc;
                            font-weight: 400;
                            a#logout_link {
                                color: #666;
                                img {
                                    width: 15px;
                                    height: 15px;
                                    font-size: 12px;
                                }
                                span {
                                    margin: 12px 0 12px;
                                    display: inline-block;
                                    vertical-align: middle;
                                }
                            }
                        }
                    }
                }
            }
            .header__profile {
                font-size: 12px;
                background: #d4d4d4 0% 0% no-repeat padding-box;
                opacity: 1;
                min-width: 224px;
                .header__profile__more {
                    font-size: 10px;
                    padding-right: 0px;
                }
                .header__profile__more.icon-down-open:before {
                    content: '\e85c';
                }
                .header__profile__more.icon-down-open i.icon {
                    font-size: 13px;
                    font-weight: bold;
                }
            }
            span#js_menu__user_name {
                margin-left: 10px;
                margin-right: 10px;
                font-size: 12px;
            }
        }
    }
}

.footer-wrapper {
    font-family: Montserrat;
    background-color: #fff;
    //@include shadow(0, -1px, 0, 1px, transparentize(#000, 0.95));
    width: 100%;
    min-height: 68px;
    display: none;
    justify-content: center;
    #course-footer {
        margin-top: 0;
    }
    .footer {
        max-width: 960px;
        width: 100%;
        margin: 0 auto;
        ul,
        li {
            padding-left: 0;
            margin-left: 0;
        }
        &__links {
            display: flex;
            width: 100%;
            justify-content: center;
            align-items: center;
            margin: 0 auto;
            padding-bottom: 24px;
            padding-top: 24px;
            li,
            li[class^="icon-"] {
                display: inline-flex;
                align-items: center;
                font-size: 14px;
                margin: 0;
                a {
                    text-decoration: none;
                    color: #000;
                    letter-spacing: 0.1px;
                    &:hover {
                        text-decoration: none;
                        cursor: pointer;
                        color: #1A6CA0;
                    }
                }
            }
            li.icon-circle:before {
                content: "";
                font-size: 3px;
                margin-left: 8px;
                margin-right: 8px;
            }
        }
        img.icon {
            background-size: contain;
            height: auto;
            width: 134px;
            padding: 0;
        }
    }
}

#logout_link {
    text-decoration: none;
    color: #fff;
}


@media only screen and (max-width: 799px) {
    .footer-wrapper {
        .footer {
            &__links {
                flex-flow: column;
                li {
                    padding-bottom: 20px;
                }
            }
        }
    }
}
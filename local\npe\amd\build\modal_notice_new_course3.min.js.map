{"version": 3, "file": "modal_notice_new_course3.min.js", "sources": ["../src/modal_notice_new_course3.js"], "sourcesContent": ["import $ from 'jquery';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\n\nconst SELECTORS = {\n    PREVMODAL_BUTTON: '[data-action=\"prev-modal\"]',\n    FINISH: '[data-action=\"finish\"]'\n};\n\nlet registered = false;\n\nexport default class ModalNoticeNewCourse3 extends Modal {\n\n    static TYPE = 'local_npe/modal_notice_new_course_3';\n    static TEMPLATE = 'local_npe/noticenewcourse/noticenewcourse_modal03_manag';\n\n    constructor(root) {\n        super(root);\n    }\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.PREVMODAL_BUTTON, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.notice-new-course-manag-modal').removeClass('show').addClass('hide');\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n            $('.notice-new-course-info-modal').removeClass('hide').addClass('show');\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.notice-new-course-manag-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n            location.reload();\n        });\n    }\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalNoticeNewCourse3.TYPE, ModalNoticeNewCourse3, ModalNoticeNewCourse3.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_defineProperty", "r", "t", "i", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_j<PERSON>y", "_custom_interaction_events", "_modal", "_modal_registry", "SELECTORS", "registered", "ModalNoticeNewCourse3", "Modal", "constructor", "root", "super", "registerEventListeners", "this", "getModal", "on", "CustomEvents", "events", "activate", "getRoot", "removeClass", "$", "addClass", "remove", "location", "reload", "_exports", "ModalRegistry", "register", "TYPE", "TEMPLATE"], "mappings": "mNAGgD,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,gBAAAH,EAAAI,EAAAC,GAAAD,OAAAA,EAAA,SAAAC,GAAAC,IAAAA,EAAA,SAAAD,EAAAD,GAAAC,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAL,IAAAA,EAAAK,EAAAE,OAAAC,yBAAAR,EAAA,CAAA,IAAAM,EAAAN,EAAAS,KAAAJ,EAAAD,kCAAAE,EAAA,OAAAA,EAAAI,MAAAA,IAAAA,4EAAAN,EAAAO,OAAAC,QAAAP,EAAA,CAAAQ,CAAAR,EAAAC,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAQ,CAAAV,MAAAJ,EAAAe,OAAAC,eAAAhB,EAAAI,EAAAa,CAAAA,MAAAZ,EAAAa,YAAAC,EAAAA,cAAAC,EAAAA,cAAApB,EAAAI,GAAAC,EAAAL,CAAA,iFAHhDqB,QAAAtB,uBAAAsB,SACAC,2BAAAvB,uBAAAuB,4BACAC,OAAAxB,uBAAAwB,QACAC,gBAAAzB,uBAAAyB,iBAEA,MAAMC,2BACgB,6BADhBA,iBAEM,yBAGZ,IAAIC,YAAa,EAEF,MAAMC,8BAA8BC,OAAAA,QAK/CC,WAAAA,CAAYC,MACRC,MAAMD,KACV,CAEAE,sBAAAA,GACID,MAAMC,uBAAuBC,MAE7BA,KAAKC,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUb,4BAA4B,KACzEQ,KAAKM,UAAUC,YAAY,SAC3B,EAAAC,QAAAA,SAAE,QAAQD,YAAY,eACtB,EAAAC,QAACvC,SAAC,kCAAkCsC,YAAY,QAAQE,SAAS,SACjE,EAAAD,QAACvC,SAAC,mBAAmBsC,YAAY,QAAQE,SAAS,SAClD,EAAAD,QAACvC,SAAC,iCAAiCsC,YAAY,QAAQE,SAAS,OAAO,IAG3ET,KAAKC,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUb,kBAAkB,KAC/DQ,KAAKM,UAAUC,YAAY,SAC3B,EAAAC,QAAAA,SAAE,QAAQD,YAAY,eACtB,EAAAC,iBAAE,kCAAkCE,UACpC,EAAAF,QAACvC,SAAC,mBAAmBsC,YAAY,QAAQE,SAAS,QAClDE,SAASC,QAAQ,GAEzB,EAMH,OALAC,SAAA5C,QAAAyB,sBAAAxB,gBA5BoBwB,sBAAqB,OAExB,uCAAqCxB,gBAFlCwB,sBAAqB,WAGpB,2DA2BjBD,aACDqB,gBAAAA,QAAcC,SAASrB,sBAAsBsB,KAAMtB,sBAAuBA,sBAAsBuB,UAChGxB,YAAa,GAChBoB,SAAA5C,OAAA"}
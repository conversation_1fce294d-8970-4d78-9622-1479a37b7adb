#!/bin/bash
# SM Deploy script

# ENV && REGION Params
echo "$DEPLOYMENT_GROUP_NAME"

if [[ $DEPLOYMENT_GROUP_NAME = "Cron" || $DEPLOYMENT_GROUP_NAME = "Cron-Staging" || $DEPLOYMENT_GROUP_NAME = "MoodleCron" ]]; then
  DEBUG=true
else
  DEBUG=false
fi

#TODO: Obtener mediante parametro
ENTORNO=QA
if [[ $DEPLOYMENT_GROUP_NAME = "Moodle-Staging" || $DEPLOYMENT_GROUP_NAME = "Cron-Staging" ]]; then
  ENTORNO=STG
elif [[ $DEPLOYMENT_GROUP_NAME = "Moodle" || $DEPLOYMENT_GROUP_NAME = "MoodleCron" ]]; then
  ENTORNO=PRO
fi

CONFIGPATH=/webs/www/moodle/config.php

# AWS Params
JSON=$(aws ssm get-parameters-by-path --recursive --path /"$ENTORNO"/NPE --with-decryption)

DEBUGMODE=$DEBUG
INSTANCIAS=$(echo "$JSON" | jq -r --arg NAME "/$ENTORNO/NPE/Instancias" '.Parameters[] | select(.Name==$NAME) | .Value')
DBHost=$(echo "$JSON" | jq -r --arg NAME "/$ENTORNO/NPE/DBHost" '.Parameters[] | select(.Name==$NAME) | .Value')
DBUser=$(echo "$JSON" | jq -r --arg NAME "/$ENTORNO/NPE/DBUser" '.Parameters[] | select(.Name==$NAME) | .Value')
DBPass=$(echo "$JSON" | jq -r --arg NAME "/$ENTORNO/NPE/DBPass" '.Parameters[] | select(.Name==$NAME) | .Value')
DBOptions=$(echo "$JSON" | jq -r --arg NAME "/$ENTORNO/NPE/DBOptions" '.Parameters[] | select(.Name==$NAME) | .Value')
DataRoot=$(echo "$JSON" | jq -r --arg NAME "/$ENTORNO/NPE/DataRoot" '.Parameters[] | select(.Name==$NAME) | .Value')
LocalCache=$(echo "$JSON" | jq -r --arg NAME "/$ENTORNO/NPE/LocalCache" '.Parameters[] | select(.Name==$NAME) | .Value')
redisendpoint=$(echo "$JSON" | jq -r --arg NAME "/$ENTORNO/NPE/redisendpoint" '.Parameters[] | select(.Name==$NAME) | .Value')
Maxtimelimit=$(echo "$JSON" | jq -r --arg NAME "/$ENTORNO/NPE/Maxtimelimit" '.Parameters[] | select(.Name==$NAME) | .Value')

# Add PARAMS
sedParams=(
  -e "s|__DEBUGMODE__|$DEBUGMODE|g"
  -e "s|__INSTANCIAS__|$INSTANCIAS|g"
  -e "s|__DBHOST__|$DBHost|g"
  -e "s|__DBUSER__|$DBUser|g"
  -e "s|__DBPASS__|$DBPass|g"
  -e "s|__DBOPTIONS__|$DBOptions|g"
  -e "s|__DATAROOT__|$DataRoot|g"
  -e "s|__LOCALCACHE__|$LocalCache|g"
  -e "s|__REDISENDPOINT__|$redisendpoint|g"
  -e "s|__MAXTIMELIMIT__|$Maxtimelimit|g"
)

sed -i "${sedParams[@]}" "$CONFIGPATH"

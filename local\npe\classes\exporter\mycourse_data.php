<?php

namespace local_npe\exporter;

use local_npe\access_control;
use local_npe\app;
use local_npe\base\user\iuser;
use local_npe\constants;
use local_npe\core\course_core;
use local_npe\course;
use local_npe\exporter\mycourse_data\course_section_data;
use local_npe\exporter_data;
use local_npe\helper\json;
use local_npe\helper\url;
use local_npe\core\ccaa_core;
use local_npe\product;
use local_npe\traits\cache_url;
use local_npe\traits\check_course_status;
use local_npe\product\megamenu;

class mycourse_data extends exporter_data {

    use cache_url;
    use check_course_status;

    public $title;
    public $level;
    public $group;
    public $hasteachers;
    /** @var course_section_data[] */
    public $topicsblocks = [];
    public $topicsalt = [];
    public $originaltopicsblocks = [];
    public $topicsblockslistbtven = [];
    public $topicsarealistbtven = [];
    public $area_in_json;
    public $isstudent;
    public $isteacher;
    public $ccaa;
    public $username;
    public $courseid;
    public $coduser;
    public $codproduct;
    public $codcentro;
    public $persovisible;
    public $persoswitch;
    public $originalposition;
    public $isblocktopicstitle;
    public $productname;

    public bool $havemoregroups;

    public $topicsblockslabels;
    public $topicsunitthemeslabels;
    public $floatingroup;
    public $floatingrouptitle;
    public $floatingrouptab0;
    public $floatingrouptab1;
    public $itemstab0;
    public $itemstab1;
    public $tab0view;
    public $tab1view;
    public $tab0type;
    public $tab1type;
    public $idtab0;
    public $idtab1;
    public $istabletst0;
    public $istabletst1;
    public bool $showhelp;
    public bool $showicontext;

    public $logo;
    public $linkcourse;
    public $lastgroups;
    public $iseducamos;
    public $showgroupselector;
    public $groupsmanagement;

    public $hasfamilyresources;
    public $urlfamilyresources;
    public $titlefamilyresources;
    public $texthelpintroteacher;
    public $onboardinggroups;
    public $noticenewcourse;
    public $hasmultiplegroups;
    public $noticenewcourse_intime;
    public $course_hasinteractions;
    public $topicstooltipswitch;
    public $showmodalccaalinking;
    public $ccaaavailable;
    public $evasmurl;
    public $ismarsupialuser;
    public $iseducamosuser;
    public $hideeducamosccaa;
    public $helppanellink;
    public $helppanellinklabel;
    public $issmauser;
    public $hashelppanel;
    public $theme;
    public $isdefaulttheme;
    public $isnuancestheme;
    public $chatboturl;
    public string $sdanameplural;
    public string $gearoption;

    // Parámetros no exportables.
    /** @var course  */
    private $course;
    /** @var iuser */
    private $currentuser;
    /** @var course_section_data */
    private $mocksectiondata;
    private url $urlhelper;
    private json $jsonhelper;
    /** @var product  */
    private $product;
    private $productdata;

    public function __construct(course $course) {
        $this->course = $course;
    }

    /**
     * Establecer los parámetros a exportar.
     *
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\user_exception
     * @throws \local_npe\exception\product_exception
     * @throws \Exception
     */
    public function set_params(): void {
        $this->courseid = $this->course->get_id();
        $this->product = $this->course->get_product();
        $this->productdata = $this->product->get_product_data();
        $this->currentuser = app::get_userfactory()->get_current_user();
        $this->ismarsupialuser = $this->currentuser->get_user_data()->is_marsupial_user();
        $this->iseducamosuser = $this->currentuser->get_user_data()->is_educamos_user();
        $this->title = $this->course->get_course_data()->get_title();
        $this->group = $this->course->is_marsupial_course() === true && $this->course->get_marsupial_groupname() !== null
            ? $this->course->get_marsupial_groupname()
            : $this->course->get_course_data()->get_fullname();
        $this->ccaaavailable = app::get_course($this->course->get_id())->get_product()->get_ccaa();
        $hasccavariation = count(json_decode($this->ccaaavailable, true)) > 1;
        if (!$hasccavariation) {
            $this->ccaa = false;
        } else {
            $this->ccaa = $this->show_ccaa();
        }
        $this->evasmurl = app::get_config()->get_instance_evasm_url();
        $this->showmodalccaalinking = $hasccavariation && !$this->ccaa && !$this->iseducamosuser;
        if ($this->iseducamosuser) {
            $this->set_linking_modal_ccaa_for_educamos();
        }
        $this->username = $this->currentuser->get_user_data()->get_firstname();
        $this->hasteachers = $this->course->is_educamos_course() ?
            $this->course->has_educamos_teachers() : $this->course->has_teachers();
        $this->coduser = ($this->currentuser->get_user_data()->is_educamos_user() || null === $this->course->get_creator())
            ? $this->course->get_idnumber()
            : $this->course->get_creator()->get_idnumber();
        $this->codproduct = $this->course->get_codproduct();
        if ($this->course->get_creator() !== null) {
            $this->codcentro = $this->course->get_creator()->get_user_data()->get_department();
        } else {
            $this->codcentro = $this->currentuser->get_user_data()->get_department();
        }
        $this->isstudent = $this->currentuser->is_student();
        $this->isteacher = $this->currentuser->is_teacher();

        $this->set_sections();
        // JSON - floatingGroup.
        $this->set_floatingroup_items($this->isstudent);
        // Chatbot URL
        $this->chatboturl = app::get_chatbot()->get_chatbot_url();
        // Family resources.
        $this->set_family_resources();
        // Ayuda - assignments.
        $this->hashelppanel = false;
        $this->issmauser = $this->currentuser->get_user_data()->is_sma_user();
        if ($this->issmauser) {
            $helppanel = $this->productdata->get_helppanel(constants::HOME_SECTION);
            if ($helppanel) {
                $this->hashelppanel = true;
                $this->helppanellink = $helppanel->link;
                $this->helppanellinklabel = $helppanel->linkLabel;
            }
        }
        $this->showhelp = $this->product->can_show_help();
        $this->topicsblockslabels = $this->productdata->get_json_labels('txtBlocks');
        $this->topicsunitthemeslabels = $this->productdata->get_json_labels('txtAreas');
        $this->sdanameplural = $this->productdata->get_json_labels('sdaNamePlural');
        $this->gearoption = '';
        if ($this->isteacher) {
            $this->texthelpintroteacher = get_string('texthelpintroteacher', 'local_npe', [
                'sdaName' => $this->productdata->get_json_labels('sdaName'),
                'sdaNamePlural' => $this->productdata->get_json_labels('sdaNamePlural')
            ]);

            $this->topicstooltipswitch = get_string('topicstooltipswitch', 'local_npe', [
                'sdaName' => $this->productdata->get_json_labels('sdaName'),
                'sdaNamePlural' => $this->productdata->get_json_labels('sdaNamePlural')
            ]);

            $this->gearoption = $this->productdata->get_json_labels('txtCustomizationOption') ?? '';
        }
        // Composición de cabecera.
        $this->theme = $this->productdata->get_theme();
        $this->isdefaulttheme = $this->theme === 'defaultTheme';
        $this->isnuancestheme = $this->theme === 'nuancesTheme';
        $this->logo = $this->get_url_content($this->productdata->get_icon());
        $this->level = $this->productdata->get_stage();

        // Lista de grupos.
        $this->set_last_groups_list();
        $this->isblocktopicstitle = ($this->persoswitch !== '"areas"');
        $this->havemoregroups = count($this->currentuser->get_enrolled_courses_by_codproduct($this->codproduct));
        $this->hasmultiplegroups = count($this->currentuser->get_enrolled_courses_by_codproduct($this->codproduct)) > 1;
        $this->topicsblockslistbtven = json_encode($this->set_list_btven_sections(constants::PERSO_BLOCKS));
        $this->topicsarealistbtven = json_encode($this->set_list_btven_sections(constants::PERSO_AREAS));
        $this->area_in_json = $this->product->has_topic_areas();

        $this->onboardinggroups = $this->currentuser->get_onboarding_groups($this->codproduct);
        if (!$this->onboardinggroups) {
            $this->currentuser->update_onboarding_groups($this->codproduct);
        }

        // Establece si el curso cumple todas las condiciones para aviso de nuevo curso escolar
        $this->noticenewcourse_intime = $this::is_notice_new_course_intime($this->codproduct);
        // Establece si el grupo actual tiene o no interacciones para mostrar aviso de nuevo curso escolar
        $this->course_hasinteractions = $this::has_interactions_course($this->courseid);
        // Establece si el curso ya mostro la modal de aviso de nuevo curso escolar
        $this->noticenewcourse = $this->currentuser->get_noticenewcourse($this->codproduct);
        if (!$this->noticenewcourse && $this->noticenewcourse_intime) {
            // Actualiza el aviso de nuevo curso escolar noticenewcourse en BD para que no se vuelva a mostrar al usuario
            $this->currentuser->update_noticenewcourse($this->codproduct);
        }
    }

    /**
     * Comprobar si la ccaa asociada al grupo está entre las asociadas al producto
     * @return string|false
     */
    private function show_ccaa() {
        foreach (json_decode($this->ccaaavailable, true) as $key => $value) {
            $existsccaa = (bool)array_search($this->load_ccaa(), $value);
            if ($existsccaa) {
                return $this->load_ccaa();
            }
        }
        return false;
    }

    /**
     * @retur void
     */
    private function set_linking_modal_ccaa_for_educamos() {
        /** @var course_core $coursecore */
        $coursecore = app::get_instance()->get(course_core::class);
        $showeducamosccaa = $coursecore->course_format_data()->get_show_educamos_ccaa($this->courseid);
        foreach (json_decode($this->ccaaavailable, true) as $key => $value) {
            $existsccaa = (bool)array_search($this->ccaa, $value);
            if ($existsccaa) break;
        }

        if (isset($existsccaa) && !$existsccaa) {
            $this->ccaa = false;
            $this->showmodalccaalinking = false;
            $this->hideeducamosccaa = true;
        } else if (isset($existsccaa) && $existsccaa && !$showeducamosccaa) {
            $this->showmodalccaalinking = true;
            $coursecore->course_format_data()->set_show_educamos_ccaa($this->courseid, true);
        } else {
            $this->showmodalccaalinking = false;
        }
    }

    /**
     * @return void
     */
    private function set_family_resources() {
        if ($this->productdata->get_familylink()) {
            $this->hasfamilyresources = true;
            $url = $this->productdata->get_familyurl();
            $this->urlfamilyresources = $url ? $url : app::get_url_helper()->get_familyresources_url($this->courseid);
            $title = $this->productdata->get_json_labels(constants::TXT_FAMILY_LINK);
            $this->titlefamilyresources = $title;
        } else {
            $this->hasfamilyresources = false;
        }
    }

    /**
     * @return void
     */
    private function set_last_groups_list() {
        $userid = $this->currentuser->get_id();
        $this->linkcourse = $this->get_url_helper()->get_groups_url($this->codproduct);
        app::get_last_groups_manager()->add_group_visited([
            'courseid' => $this->courseid, 'userid' => $userid
        ]);
        $this->lastgroups = app::get_last_groups_manager()->get_last_groups_list_by_user($userid, $this->course);
        $this->iseducamos = $this->currentuser->get_user_data()->is_educamos_user();
        $isseneca = app::get_userfactory()->get_current_user()->get_user_data()->is_seneca_user();
        $isexternal = app::get_userfactory()->get_current_user()->get_user_data()->is_external_user();
        $isltiuser = $isexternal && !$isseneca;
        $this->showgroupselector = !($this->course->is_educamos_course() || $this->course->is_marsupial_course() || $isltiuser);
        $megamenu = megamenu::get_instance($this->course->get_codproduct());
        $section = $megamenu->get_section(
            constants::BLOCK_SECTION_MYCLASS,
            constants::AREA_GROUPS, constants::GROUPS_SECTION
        );
        $this->groupsmanagement = app::get_menu_helper()->get_item_title($section);
    }

    /**
     * @return false|string
     */
    private function load_ccaa() {
        /** @var ccaa_core $ccaacore */
        $ccaacore = app::get_instance()->get(ccaa_core::class);
        return $ccaacore->ccaa_name_by_id($this->course->get_ccaa());
    }

    /**
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\user_exception
     * @throws \moodle_exception
     */
    private function set_sections() {
        $iseducamosuser = $this->currentuser->get_user_data()->is_educamos_user();
        $idnumber = ($iseducamosuser || null === $this->course->get_creator())
            ? $this->course->get_idnumber()
            : $this->currentuser->get_user_data()->get_idnumber();
        if ($this->course->get_creator() !== null) {
            $idnumber = $this->course->get_creator()->get_user_data()->get_idnumber();
        }

        $this->product->prepare_perso($idnumber, $this->codcentro, $this->courseid);
        $this->persovisible = json_encode($this->product->get_perso_visible());
        $this->persoswitch = json_encode($this->product->get_perso_switch());

        $persovisible = json_decode($this->persovisible);
        if ($this->persoswitch === '"areas"') {
            $items = $this->course->get_topics_areas_with_perso();
            $itemsalt = $this->course->get_topics_blocks_with_perso();
        } else {
            $items = $this->course->get_topics_blocks_with_perso();
            $itemsalt = $this->course->get_topics_areas_with_perso();
        }

        $this->topicsblocks[] = null;
        $count = 0;
        foreach ($items as $item) {
            $iditem = $item->get_id();
            $this->topicsblocks[$count] = (object) [];
            $this->topicsblocks[$count]->name = $item->name ?? null;
            $this->topicsblocks[$count]->id = $iditem ?? null;
            $this->topicsblocks[$count]->packerid = $item->packerid ?? null;

            if ($this->persoswitch === '"areas"') {
                $items2 = $this->course->get_topics_by_area_with_perso($iditem, $item->packerid);
            } else {
                $items2 = $this->course->get_topics_by_block_with_perso($iditem, $item->packerid);
            }
            foreach ($items2 as $topic) {
                if (!$this->isstudent && access_control::check_topic_access($this->courseid, $topic, false) === false ||
                    $this->isstudent && (
                        $topic->get_topic_data()->get_visibilityn2() === 'onlyTeacher' ||
                        ($persovisible !== null && in_array($topic->get_packerid(), $persovisible, true))
                    )
                ) {
                    continue;
                }
                $sectiondata = $this->get_section_data();
                $sectiondata->set_topic($topic);
                $sectiondata->set_course($this->course);
                $sectiondata->hidden = ($persovisible !== null && in_array($topic->get_packerid(), $persovisible, true));
                $sectiondata->set_params();
                $this->topicsblocks[$count]->topicsbyblock[] = $sectiondata;
            }
            // Eliminar las categorías
            $hastopics = $this->topicsblocks[$count]->topicsbyblock ?? false;
            if ($hastopics === false) {
                unset($this->topicsblocks[$count]);
            }
            $count++;
        }
        $this->topicsblocks = array_values($this->topicsblocks);
        $this->originaltopicsblocks = $this->topicsblocks;
        $this->topicsalt[] = null;
        if (!$this->isstudent) {
            $countalt = 0;
            foreach ($itemsalt as $itemalt) {
                $iditemalt = $itemalt->get_id();
                $this->topicsalt[$countalt] = (object) [];
                $this->topicsalt[$countalt]->name = $itemalt->name ?? null;
                $this->topicsalt[$countalt]->id = $iditemalt ?? null;
                $this->topicsalt[$countalt]->packerid = $itemalt->packerid ?? null;

                if ($this->persoswitch === '"blocks"') {
                    $items2alt = $this->course->get_topics_by_area_with_perso($iditemalt, $itemalt->packerid);
                } else {
                    $items2alt = $this->course->get_topics_by_block_with_perso($iditemalt, $itemalt->packerid);
                }

                foreach ($items2alt as $topicalt) {
                    if (
                        !$this->isstudent && access_control::check_topic_access($this->courseid, $topicalt, false) === false
                        || $this->isstudent && ($persovisible !== null && in_array($topicalt->get_packerid(), $persovisible, true))
                    ) {
                        continue;
                    }
                    $sectiondataalt = $this->get_section_data();
                    $sectiondataalt->set_topic($topicalt);
                    $sectiondataalt->set_course($this->course);
                    $sectiondataalt->hidden = ($persovisible !== null && in_array($topicalt->get_packerid(), $persovisible, true));
                    $sectiondataalt->set_params();
                    $this->topicsalt[$countalt]->topicsbyblock[] = $sectiondataalt;
                }
                $hastopicsalt = $this->topicsalt[$countalt]->topicsbyblock ?? false;
                if ($hastopicsalt === false) {
                    unset($this->topicsalt[$countalt]);
                }
                $countalt++;
            }
            $this->topicsalt = array_values($this->topicsalt);
        }

        //Original.
        // Preparo Original.
        $position = $this->persoswitch == '"areas"' ? 'areas' : 'blocks';
        $original = $this->product->prepare_originalperso(-1, $this->codcentro, $this->courseid, $position);
        $originalpersoswitch = json_encode($original['item_switch']);
        $this->originalposition = $originalpersoswitch;
        $item_move = $original['item_move'];
        if ($originalpersoswitch === '"areas"') {
            $originalitems = $this->course->get_topics_areas_with_originalperso($item_move);
        } else {
            $originalitems = $this->course->get_topics_blocks_with_originalperso($item_move);
        }
        $count = 0;
        foreach ($originalitems as $item) {
            $iditem = $item->get_id();
            $this->originaltopicsblocks[$count] = (object) [];
            $this->originaltopicsblocks[$count]->name = $item->name ?? null;
            $this->originaltopicsblocks[$count]->id = $iditem ?? null;
            $this->originaltopicsblocks[$count]->packerid = $item->packerid ?? null;

            if ($originalpersoswitch === '"areas"') {
                $items2 = $this->course->get_topics_by_area_with_originalperso($iditem, $item->packerid, $item_move);
            } else {
                $items2 = $this->course->get_topics_by_block_with_originalperso($iditem, $item->packerid, $item_move);
            }
            foreach ($items2 as $topic) {
                if (
                    !$this->isstudent && access_control::check_topic_access($this->courseid, $topic, false) === false
                    || $this->isstudent && ($persovisible !== null && in_array($topic->get_packerid(), $persovisible, true))
                ) {
                    continue;
                }
                $sectiondata = $this->get_section_data();
                $sectiondata->set_topic($topic);
                $sectiondata->set_course($this->course);
                $sectiondata->hidden = ($persovisible !== null && in_array($topic->get_packerid(), $persovisible, true));
                $sectiondata->set_params();
                $this->originaltopicsblocks[$count]->topicsbyblock[] = $sectiondata;
            }

            // Eliminar las categorías
            $hastopics = $this->originaltopicsblocks[$count]->topicsbyblock ?? false;
            $this->productname = $this->product->get_name();
            if ($hastopics === false) {
                unset($this->originaltopicsblocks[$count]);
            }
            $count++;
            $count++;
        }
        $this->originaltopicsblocks = array_values($this->originaltopicsblocks);
    }

    /**
     * @param $switchperso
     * @return mixed
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\user_exception
     * @throws \moodle_exception
     */
    private function set_list_btven_sections($switchperso) {
        if ($switchperso === constants::PERSO_BLOCKS) {
            $items = $this->course->get_topics_blocks();
        } else {
            $items = $this->course->get_topics_areas();
        }

        $topicsblockslistbtven = [];
        $count = 0;
        foreach ($items as $item) {
            $iditem = $item->get_id();

            if ($switchperso === constants::PERSO_BLOCKS) {
                $items2 = $this->course->get_topics_by_block($iditem);
            } else {
                $items2 = $this->course->get_topics_by_area($iditem);
            }

            if (count($items2) === 0) {
                continue;
            }

            $topicsblockslistbtven[$count] = (object) [];
            $topicsblockslistbtven[$count]->packerid = $item->packerid ?? null;

            foreach ($items2 as $topic) {
                $sectiondata = $this->get_section_data();
                $sectiondata->set_topic($topic);
                $sectiondata->set_course($this->course);
                $sectiondata->set_params();
                $topicsblockslistbtven[$count]->topicsblocks[] = $sectiondata->packerid;
            }
            $count++;
        }
        return $topicsblockslistbtven;
    }

    /**
     * @return course_section_data
     */
    private function get_section_data(): course_section_data {
        return $this->mocksectiondata ?: new course_section_data();
    }

    /**
     * Se emplea para Test Unitarios.
     *
     * @param course_section_data $sectiondata
     */
    public function set_mock_group_data(course_section_data $sectiondata) {
        $this->mocksectiondata = $sectiondata;
    }

    /**
     * @return json
     */
    private function get_json_helper() {
        return $this->jsonhelper ?? $this->set_json_helper();
    }

    /**
     * Setea el json helper.
     * Costura para Test Unitarios.
     *
     * @param json|null $jsonhelper
     * @return json
     */
    private function set_json_helper(): json {
        $this->jsonhelper = new json();
        return $this->jsonhelper;
    }

    /**
     * @throws \Exception
     */
    private function set_floatingroup_items(bool $isstudent) {
        $this->floatingroup = $this->get_json_helper()->get_floating_group($this->codproduct, $isstudent);
        if ($this->floatingroup) {
            $this->floatingrouptitle = $this->floatingroup['title'];
            $this->floatingrouptab0 = $this->floatingroup['tab0'];
            $this->floatingrouptab1 = $this->floatingroup['tab1'];
            $this->itemstab0 = $this->floatingroup['itemstab0'];
            $this->itemstab1 = $this->floatingroup['itemstab1'];
            $this->showicontext = $this->floatingroup['showicontext'] ?? false;
            if ($this->floatingroup['tab0id'] === 'txtClassroom') {
                $this->istabletst0 = true;
                $this->idtab0 = 'nav-aula';
                $this->idtab1 = 'nav-area';
            } else {
                $this->istabletst1 = true;
                $this->idtab0 = 'nav-area';
                $this->idtab1 = 'nav-aula';
            }
            if ($this->floatingroup['tab0view'] === 'tablet') {
                $this->tab0type = 'tablets';
                $this->tab1type = 'lis';
            } else {
                $this->tab0type = 'lis';
                $this->tab1type = 'tablets';
            }
        }
    }

    /**
     * @return url
     */
    private function get_url_helper() {
        return $this->urlhelper ?? $this->set_url_helper();
    }

    /**
     * Setea el url helper.
     * Costura para Test Unitarios.
     *
     * @param url|null $urlhelper
     *
     * @return url
     */
    private function set_url_helper(): url{
        $this->urlhelper = new url();
        return $this->urlhelper;
    }
}

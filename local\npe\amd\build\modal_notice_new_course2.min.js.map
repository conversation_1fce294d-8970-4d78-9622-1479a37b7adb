{"version": 3, "file": "modal_notice_new_course2.min.js", "sources": ["../src/modal_notice_new_course2.js"], "sourcesContent": ["import $ from 'jquery';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\nimport ModalNoticeNewCourse3 from 'local_npe/modal_notice_new_course3';\nimport ModalFactory from 'core/modal_factory';\n\nconst SELECTORS = {\n    NEXTMODAL_BUTTON: '[data-action=\"next-modal\"]'\n};\n\nlet registered = false;\n\nexport default class ModalNoticeNewCourse2 extends Modal {\n\n    static TYPE = 'local_npe/modal_notice_new_course_2';\n    static TEMPLATE = 'local_npe/noticenewcourse/noticenewcourse_modal02_info';\n\n    constructor(root) {\n        super(root);\n    }\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.NEXTMODAL_BUTTON, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.notice-new-course-info-modal').removeClass('show').addClass('hide');\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n            if ($('.notice-new-course-manag-modal').hasClass('hide')) {\n                $('.notice-new-course-manag-modal').removeClass('hide').addClass('show');\n            } else {\n                ModalFactory.create({type: ModalNoticeNewCourse3.TYPE}).done((modal3) => {\n                    modal3.show();\n                });\n            }\n        });\n    }\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalNoticeNewCourse2.TYPE, ModalNoticeNewCourse2, ModalNoticeNewCourse2.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_defineProperty", "r", "t", "i", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_j<PERSON>y", "_custom_interaction_events", "_modal", "_modal_registry", "_modal_notice_new_course", "_modal_factory", "SELECTORS", "registered", "ModalNoticeNewCourse2", "Modal", "constructor", "root", "super", "registerEventListeners", "this", "getModal", "on", "CustomEvents", "events", "activate", "getRoot", "removeClass", "$", "addClass", "hasClass", "ModalFactory", "create", "type", "ModalNoticeNewCourse3", "TYPE", "done", "modal3", "show", "_exports", "ModalRegistry", "register", "TEMPLATE"], "mappings": "qTAK8C,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,gBAAAH,EAAAI,EAAAC,GAAAD,OAAAA,EAAA,SAAAC,GAAAC,IAAAA,EAAA,SAAAD,EAAAD,GAAAC,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAL,IAAAA,EAAAK,EAAAE,OAAAC,yBAAAR,EAAA,CAAA,IAAAM,EAAAN,EAAAS,KAAAJ,EAAAD,kCAAAE,EAAA,OAAAA,EAAAI,MAAAA,IAAAA,4EAAAN,EAAAO,OAAAC,QAAAP,EAAA,CAAAQ,CAAAR,EAAAC,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAQ,CAAAV,MAAAJ,EAAAe,OAAAC,eAAAhB,EAAAI,EAAAa,CAAAA,MAAAZ,EAAAa,YAAAC,EAAAA,cAAAC,EAAAA,cAAApB,EAAAI,GAAAC,EAAAL,CAAA,iFAL9CqB,QAAAtB,uBAAAsB,SACAC,2BAAAvB,uBAAAuB,4BACAC,OAAAxB,uBAAAwB,QACAC,gBAAAzB,uBAAAyB,iBACAC,yBAAA1B,uBAAA0B,0BACAC,eAAA3B,uBAAA2B,gBAEA,MAAMC,2BACgB,6BAGtB,IAAIC,YAAa,EAEF,MAAMC,8BAA8BC,OAAAA,QAK/CC,WAAAA,CAAYC,MACRC,MAAMD,KACV,CAEAE,sBAAAA,GACID,MAAMC,uBAAuBC,MAE7BA,KAAKC,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUb,4BAA4B,KACzEQ,KAAKM,UAAUC,YAAY,SAC3B,EAAAC,QAAAA,SAAE,QAAQD,YAAY,eACtB,EAAAC,QAACzC,SAAC,iCAAiCwC,YAAY,QAAQE,SAAS,SAChE,EAAAD,QAACzC,SAAC,mBAAmBwC,YAAY,QAAQE,SAAS,SAC9C,EAAAD,QAACzC,SAAC,kCAAkC2C,SAAS,SAC7C,EAAAF,QAACzC,SAAC,kCAAkCwC,YAAY,QAAQE,SAAS,QAEjEE,eAAY5C,QAAC6C,OAAO,CAACC,KAAMC,iCAAsBC,OAAOC,MAAMC,SAC1DA,OAAOC,MAAM,GAErB,GAER,EAMH,OALAC,SAAApD,QAAA2B,sBAAA1B,gBA1BoB0B,sBAAqB,OAExB,uCAAqC1B,gBAFlC0B,sBAAqB,WAGpB,0DAyBjBD,aACD2B,gBAAAA,QAAcC,SAAS3B,sBAAsBqB,KAAMrB,sBAAuBA,sBAAsB4B,UAChG7B,YAAa,GAChB0B,SAAApD,OAAA"}
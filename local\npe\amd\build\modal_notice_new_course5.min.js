define("local_npe/modal_notice_new_course5",["exports","jquery","core/custom_interaction_events","core/modal","core/modal_registry"],(function(_exports,_jquery,_custom_interaction_events,_modal,_modal_registry){function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _defineProperty(e,r,t){return(r=function(t){var i=function(t,r){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var i=e.call(t,r||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==typeof i?i:i+""}(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_custom_interaction_events=_interopRequireDefault(_custom_interaction_events),_modal=_interopRequireDefault(_modal),_modal_registry=_interopRequireDefault(_modal_registry);const SELECTORS_FINISH='[data-action="finish"]';let registered=!1;class ModalNoticeNewCourse5 extends _modal.default{constructor(root){super(root)}registerEventListeners(){super.registerEventListeners(this),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_FINISH,(()=>{this.getRoot().removeClass("show"),(0,_jquery.default)("body").removeClass("modal-open"),(0,_jquery.default)(".notice-new-course-info-modal").remove(),(0,_jquery.default)(".modal-backdrop").removeClass("show").addClass("hide")}))}}return _exports.default=ModalNoticeNewCourse5,_defineProperty(ModalNoticeNewCourse5,"TYPE","local_npe/modal_notice_new_course_5"),_defineProperty(ModalNoticeNewCourse5,"TEMPLATE","local_npe/noticenewcourse/noticenewcourse_modal_infobartooltip"),registered||(_modal_registry.default.register(ModalNoticeNewCourse5.TYPE,ModalNoticeNewCourse5,ModalNoticeNewCourse5.TEMPLATE),registered=!0),_exports.default}));

//# sourceMappingURL=modal_notice_new_course5.min.js.map
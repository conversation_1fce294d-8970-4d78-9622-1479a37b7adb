#!/bin/bash

if [[ $DEPLOYMENT_GROUP_NAME != "Cron" && $DEPLOYMENT_GROUP_NAME != "Cron-Staging" && $DEPLOYMENT_GROUP_NAME != "MoodleCron" ]]; then
  exit 0
fi

ENTORNO=QA
if [[ $DEPLOYMENT_GROUP_NAME = "Cron-Staging" ]]; then
  ENTORNO=STG
elif [[ $DEPLOYMENT_GROUP_NAME = "MoodleCron" ]]; then
  ENTORNO=PRO
fi

ListaInstancias=$(aws ssm get-parameters --names /"$ENTORNO"/NPE/ListaInstancias --query Parameters[0].Value)

ERROR=false
for INSTANCE in ${ListaInstancias:1:-1}; do
  echo "### UPGRADE $INSTANCE###"
  time sudo -u apache php /webs/www/moodle/admin/cli/upgrade_tenant.php --non-interactive --lang=en -i="$INSTANCE"
  if [ $? -ne 0 ]; then
    ERROR=true
  fi
done

if [ "$ERROR" = true ]; then
  exit 1
fi

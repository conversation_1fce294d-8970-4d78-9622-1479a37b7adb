{"version": 3, "file": "modal_change_delivery_type.min.js", "sources": ["../src/modal_change_delivery_type.js"], "sourcesContent": ["import $ from 'jquery';\nimport Notification from 'core/notification';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\nimport Ajax from 'core/ajax';\n\nconst SELECTORS = {\n    MODAL: '.change-delivery-type-modal',\n    MODALBACKDROP: '.modal-backdrop',\n    CONFIRMBUTTON: '[data-action=\"confirm\"]',\n    CANCELBUTTON: '[data-action=\"cancel\"]',\n    HIDE: '.close'\n};\n\nconst SERVICES = {\n    REMOVEASSIGN: 'local_npe_remove_assign'\n};\n\nlet registered = false;\n\nexport default class ModalChangeDeliveryType extends Modal {\n\n    static TYPE = 'local_npe/modal_change_delivery_type';\n    static TEMPLATE = 'local_npe/assignments/modal_change_delivery_type';\n\n    constructor(root) {\n        super(root);\n    }\n\n    setData(assignurl, assignid, activityid, courseid) {\n        this.assignurl = assignurl;\n        this.assignid = assignid;\n        this.courseid = courseid;\n        this.activityid = activityid;\n    }\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CONFIRMBUTTON, () => {\n            this.ajaxCall();\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE + ', ' + SELECTORS.CANCELBUTTON, () => {\n            $(SELECTORS.MODAL).remove();\n            $(SELECTORS.MODALBACKDROP).remove();\n            $('body').removeClass('modal-open');\n        });\n    }\n\n    ajaxCall() {\n        const promises = Ajax.call([{\n            methodname: SERVICES.REMOVEASSIGN,\n            args: {\n                assign: [{'id': this.activityid}],\n                teamid: null,\n                userid: null,\n                activityid: this.activityid,\n                changedelivery: true,\n                courseid: this.courseid\n            }\n        }]);\n\n        promises[0].done((response) => {\n            this.ajaxResult(response);\n        }).fail((ex) => {\n            this.ajaxError(ex);\n        });\n    }\n\n    ajaxError(ex) {\n        Notification.exception({message: ex});\n    }\n\n    ajaxResult(response) {\n        if (response.success === true) {\n            window.location.href = this.assignurl;\n        }\n    }\n\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalChangeDeliveryType.TYPE, ModalChangeDeliveryType, ModalChangeDeliveryType.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_defineProperty", "r", "t", "i", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_j<PERSON>y", "_notification", "_custom_interaction_events", "_modal", "_modal_registry", "_ajax", "SELECTORS", "SERVICES", "registered", "ModalChangeDeliveryType", "Modal", "constructor", "root", "super", "setData", "<PERSON>url", "assignid", "activityid", "courseid", "this", "registerEventListeners", "getModal", "on", "CustomEvents", "events", "activate", "ajaxCall", "$", "remove", "removeClass", "Ajax", "methodname", "args", "assign", "id", "teamid", "userid", "changedelivery", "done", "response", "ajaxResult", "fail", "ex", "ajaxError", "Notification", "exception", "message", "success", "window", "location", "href", "_exports", "ModalRegistry", "register", "TYPE", "TEMPLATE"], "mappings": "yQAK6B,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,gBAAAH,EAAAI,EAAAC,GAAAD,OAAAA,EAAA,SAAAC,GAAAC,IAAAA,EAAA,SAAAD,EAAAD,GAAAC,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAL,IAAAA,EAAAK,EAAAE,OAAAC,yBAAAR,EAAA,CAAA,IAAAM,EAAAN,EAAAS,KAAAJ,EAAAD,kCAAAE,EAAA,OAAAA,EAAAI,MAAAA,IAAAA,4EAAAN,EAAAO,OAAAC,QAAAP,EAAA,CAAAQ,CAAAR,EAAAC,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAQ,CAAAV,MAAAJ,EAAAe,OAAAC,eAAAhB,EAAAI,EAAAa,CAAAA,MAAAZ,EAAAa,YAAAC,EAAAA,cAAAC,EAAAA,cAAApB,EAAAI,GAAAC,EAAAL,CAAA,iFAL7BqB,QAAAtB,uBAAAsB,SACAC,cAAAvB,uBAAAuB,eACAC,2BAAAxB,uBAAAwB,4BACAC,OAAAzB,uBAAAyB,QACAC,gBAAA1B,uBAAA0B,iBACAC,MAAA3B,uBAAA2B,OAEA,MAAMC,gBACK,8BADLA,wBAEa,kBAFbA,wBAGa,0BAHbA,uBAIY,yBAJZA,eAKI,SAGJC,sBACY,0BAGlB,IAAIC,YAAa,EAEF,MAAMC,gCAAgCC,OAAAA,QAKjDC,WAAAA,CAAYC,MACRC,MAAMD,KACV,CAEAE,OAAAA,CAAQC,UAAWC,SAAUC,WAAYC,UACrCC,KAAKJ,UAAYA,UACjBI,KAAKH,SAAWA,SAChBG,KAAKD,SAAWA,SAChBC,KAAKF,WAAaA,UACtB,CAEAG,sBAAAA,GACIP,MAAMO,uBAAuBD,MAE7BA,KAAKE,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUnB,yBAAyB,KACtEa,KAAKO,UAAU,IAGnBP,KAAKE,WAAWC,GAAGC,2BAAY1C,QAAC2C,OAAOC,SAAUnB,eAAiB,KAAOA,wBAAwB,MAC7F,EAAAqB,QAAAA,SAAErB,iBAAiBsB,UACnB,EAAAD,QAAAA,SAAErB,yBAAyBsB,UAC3B,EAAAD,QAAAA,SAAE,QAAQE,YAAY,aAAa,GAE3C,CAEAH,QAAAA,GACqBI,MAAAA,QAAK1C,KAAK,CAAC,CACxB2C,WAAYxB,sBACZyB,KAAM,CACFC,OAAQ,CAAC,CAACC,GAAMf,KAAKF,aACrBkB,OAAQ,KACRC,OAAQ,KACRnB,WAAYE,KAAKF,WACjBoB,gBAAgB,EAChBnB,SAAUC,KAAKD,aAId,GAAGoB,MAAMC,WACdpB,KAAKqB,WAAWD,SAAS,IAC1BE,MAAMC,KACLvB,KAAKwB,UAAUD,GAAG,GAE1B,CAEAC,SAAAA,CAAUD,IACNE,cAAY/D,QAACgE,UAAU,CAACC,QAASJ,IACrC,CAEAF,UAAAA,CAAWD,WACkB,IAArBA,SAASQ,UACTC,OAAOC,SAASC,KAAO/B,KAAKJ,UAEpC,EAOH,OALAoC,SAAAtE,QAAA4B,wBAAA3B,gBA5DoB2B,wBAAuB,OAE1B,wCAAsC3B,gBAFnC2B,wBAAuB,WAGtB,oDA2DjBD,aACD4C,gBAAAA,QAAcC,SAAS5C,wBAAwB6C,KAAM7C,wBAAyBA,wBAAwB8C,UACtG/C,YAAa,GAChB2C,SAAAtE,OAAA"}
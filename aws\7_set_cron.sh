#!/bin/bash

if [[ $DEPLOYMENT_GROUP_NAME != "Cron" && $DEPLOYMENT_GROUP_NAME != "Cron-Staging" && $DEPLOYMENT_GROUP_NAME != "MoodleCron" ]]; then
  exit 0
fi

ENTORNO=QA
if [[ $DEPLOYMENT_GROUP_NAME = "Cron-Staging" ]]; then
  ENTORNO=STG
elif [[ $DEPLOYMENT_GROUP_NAME = "MoodleCron" ]]; then
  ENTORNO=PRO
fi

ListaInstancias=$(aws ssm get-parameters --names /"$ENTORNO"/NPE/ListaInstancias --query Parameters[0].Value)

install -d -m 02700 -o apache -g apache /var/log/moodlecron

CRONFILE=/tmp/moodlecron.cron

:>$CRONFILE

for INSTANCE in ${ListaInstancias:1:-1}; do
  echo "* * * * *  sudo -u apache php /webs/www/moodle/admin/cli/cron_tenant.php         -i=$INSTANCE >> /var/log/moodlecron/npe-$INSTANCE.log 2>> /var/log/moodlecron/npe-$INSTANCE-err.log" >> $CRONFILE
  echo "* * * * *  sudo -u apache php /webs/www/moodle/admin/cli/adhoc_task_tenant.php --instance=$INSTANCE --execute --keep-alive=59         -i=$INSTANCE >> /var/log/moodlecron/adhoc-npe-$INSTANCE.log 2>> /var/log/moodlecron/adhoc-npe-$INSTANCE-err.log" >> $CRONFILE
  echo "* * * * *  sudo -u apache php /webs/www/moodle/admin/cli/adhoc_task_tenant.php --instance=$INSTANCE --execute --keep-alive=59         -i=$INSTANCE >> /var/log/moodlecron/adhoc-npe-$INSTANCE.log 2>> /var/log/moodlecron/adhoc-npe-$INSTANCE-err.log" >> $CRONFILE
  echo "* * * * *  sudo -u apache php /webs/www/moodle/admin/cli/adhoc_task_tenant.php --instance=$INSTANCE --execute --keep-alive=59         -i=$INSTANCE >> /var/log/moodlecron/adhoc-npe-$INSTANCE.log 2>> /var/log/moodlecron/adhoc-npe-$INSTANCE-err.log" >> $CRONFILE
  echo "0 6 * * *  sudo -u apache php /webs/www/moodle/admin/cli/purge_caches_tenant.php -i=$INSTANCE" >> $CRONFILE
done

crontab $CRONFILE

rm -f $CRONFILE

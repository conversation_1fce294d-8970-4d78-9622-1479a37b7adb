{"version": 3, "file": "modal_linking_ccaa_educamos.min.js", "sources": ["../src/modal_linking_ccaa_educamos.js"], "sourcesContent": ["import $ from 'jquery';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\n\nconst SELECTORS = {\n    CONFIRM: '[data-action=\"confirm\"]',\n    CLOSE: '[data-region=\"equis\"]',\n    CCAAQUEESTION: '#caaquestion',\n    CCAAINFO: '#ccaainfo'\n};\n\nlet registered = false;\n\nexport default class ModalLinkCCAAEducamos extends Modal {\n\n    static TYPE = 'local_npe/modal_link_ccaa_educamos';\n    static TEMPLATE = 'local_npe/courses/modal_linking_ccaa_educamos';\n\n    #textccaainfo = true;\n\n    constructor(root) {\n        super(root);\n    }\n\n    setData() {}\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CCAAQUEESTION, () => {\n            this.showtext();\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CLOSE, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.success-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CONFIRM, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.success-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n        });\n    }\n\n    showtext() {\n        if (this.#textccaainfo) {\n            $(SELECTORS.CCAAINFO).show();\n            this.#textccaainfo = false;\n        } else {\n            $(SELECTORS.CCAAINFO).hide();\n            this.#textccaainfo = true;\n        }\n    }\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalLinkCCAAEducamos.TYPE, ModalLinkCCAAEducamos, ModalLinkCCAAEducamos.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_classPrivateFieldInitSpec", "t", "a", "has", "TypeError", "_checkPrivateRedeclaration", "set", "_defineProperty", "r", "i", "Symbol", "toPrimitive", "call", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_classPrivateFieldSet", "s", "_assert<PERSON>lassBrand", "n", "arguments", "length", "_j<PERSON>y", "_custom_interaction_events", "_modal", "_modal_registry", "SELECTORS", "registered", "_textccaainfo", "WeakMap", "ModalLinkCCAAEducamos", "Modal", "constructor", "root", "super", "this", "setData", "registerEventListeners", "getModal", "on", "CustomEvents", "events", "activate", "showtext", "getRoot", "removeClass", "$", "remove", "addClass", "get", "show", "hide", "_exports", "ModalRegistry", "register", "TYPE", "TEMPLATE"], "mappings": "sNAGgD,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,2BAAAH,EAAAI,EAAAC,IAAA,SAAAL,EAAAI,GAAA,GAAAA,EAAAE,IAAAN,GAAA,MAAA,IAAAO,UAAA,iEAAA,EAAAC,CAAAR,EAAAI,GAAAA,EAAAK,IAAAT,EAAAK,EAAA,CAAA,SAAAK,gBAAAV,EAAAW,EAAAP,GAAAO,OAAAA,EAAA,SAAAP,GAAAQ,IAAAA,EAAA,SAAAR,EAAAO,GAAAP,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAJ,IAAAA,EAAAI,EAAAS,OAAAC,yBAAAd,EAAA,CAAA,IAAAY,EAAAZ,EAAAe,KAAAX,EAAAO,kCAAAC,EAAA,OAAAA,EAAAL,MAAAA,IAAAA,4EAAAI,EAAAK,OAAAC,QAAAb,EAAA,CAAAc,CAAAd,EAAAQ,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAO,CAAAR,MAAAX,EAAAoB,OAAAC,eAAArB,EAAAW,EAAAW,CAAAA,MAAAlB,EAAAmB,YAAAC,EAAAA,cAAAC,EAAAA,cAAAzB,EAAAW,GAAAP,EAAAJ,CAAA,CAAA,SAAA0B,sBAAAC,EAAAtB,EAAAM,UAAAgB,EAAAlB,IAAAmB,kBAAAD,EAAAtB,GAAAM,GAAAA,CAAA,CAAA,SAAAiB,kBAAA5B,EAAAI,EAAAyB,GAAA,GAAA,mBAAA7B,EAAAA,IAAAI,EAAAJ,EAAAM,IAAAF,GAAA,OAAA0B,UAAAC,OAAA,EAAA3B,EAAAyB,EAAA,MAAA,IAAAtB,UAAA,gDAAA,iFAHhDyB,QAAAjC,uBAAAiC,SACAC,2BAAAlC,uBAAAkC,4BACAC,OAAAnC,uBAAAmC,QACAC,gBAAApC,uBAAAoC,iBAEA,MAAMC,kBACO,0BADPA,gBAEK,wBAFLA,wBAGa,eAHbA,mBAIQ,YAGd,IAAIC,YAAa,EAAM,IAAAC,kBAAAC,QAER,MAAMC,8BAA8BC,OAAAA,QAO/CC,WAAAA,CAAYC,MACRC,MAAMD,MAHVxC,2BAAA0C,KAAAP,eAAgB,EAIhB,CAEAQ,OAAAA,GAAW,CAEXC,sBAAAA,GACIH,MAAMG,uBAAuBF,MAE7BA,KAAKG,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUhB,yBAAyB,KACtES,KAAKQ,UAAU,IAGnBR,KAAKG,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUhB,iBAAiB,KAC9DS,KAAKS,UAAUC,YAAY,SAC3B,EAAAC,QAAAA,SAAE,QAAQD,YAAY,eACtB,EAAAC,iBAAE,kBAAkBC,UACpB,EAAAD,QAACtD,SAAC,mBAAmBqD,YAAY,QAAQG,SAAS,OAAO,IAG7Db,KAAKG,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUhB,mBAAmB,KAChES,KAAKS,UAAUC,YAAY,SAC3B,EAAAC,QAAAA,SAAE,QAAQD,YAAY,eACtB,EAAAC,iBAAE,kBAAkBC,UACpB,EAAAD,QAACtD,SAAC,mBAAmBqD,YAAY,QAAQG,SAAS,OAAO,GAEjE,CAEAL,QAAAA,GA9C4C,IAAA1B,EAAAtB,IA+CpCwC,MA/CoClB,EA+C/BW,eA/C+BqB,IAAA/B,kBAAAD,EAAAtB,MAgDpC,EAAAmD,QAAAA,SAAEpB,oBAAoBwB,OACtBlC,sBAAKY,cAALO,MAAqB,MAErB,EAAAW,QAAAA,SAAEpB,oBAAoByB,OACtBnC,sBAAKY,cAALO,MAAqB,GAE7B,EAMH,OALAiB,SAAA5D,QAAAsC,sBAAA9B,gBA5CoB8B,sBAAqB,OAExB,sCAAoC9B,gBAFjC8B,sBAAqB,WAGpB,iDA2CjBH,aACD0B,gBAAAA,QAAcC,SAASxB,sBAAsByB,KAAMzB,sBAAuBA,sBAAsB0B,UAChG7B,YAAa,GAChByB,SAAA5D,OAAA"}
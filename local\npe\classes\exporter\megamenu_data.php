<?php

namespace local_npe\exporter;

use local_npe\app;
use local_npe\course;
use local_npe\exporter_data;
use local_npe\product\megamenu;
use local_npe\npe_enrolment_manager;

class megamenu_data extends exporter_data {


    public $blocks;
    public $init;
    public $myclass;
    public $library;
    public $evaluation;
    public $comunication;
    public $enrolmentkey;
    public $courseid;
    public $pagetype;
    public $iseducamoscourse;
    public $alertsharecode;
    public $hasteachers;
    public $islti;

    public function __construct($courseid, $pagetype) {
        $this->courseid = $courseid;
        $this->pagetype = $pagetype;
    }

    /**
     * Establecer los parámetros a exportar.
     *
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\user_exception
     * @throws \moodle_exception
     */

    public function set_params() :void {
        $pagetype = $this->pagetype;
        $courseid = $this->courseid;
        $codproduct = app::get_course($courseid)->get_codproduct();

        $megamenudata = megamenu::get_instance($codproduct);
        $jsondata = $megamenudata->blocks($courseid, $codproduct);
        $this->init = $jsondata[1] ?? null;
        $this->myclass = $jsondata[2] ?? null;
        $this->library = $jsondata[3] ?? null;
        $this->evaluation = $jsondata[4] ?? null;
        $this->comunication = $jsondata[5] ?? null;

        $course = course::get_instance($this->courseid);
        $this->hasteachers = $course->is_educamos_course() ?
            $course->has_educamos_teachers() : $course->has_teachers();
        $this->enrolmentkey = npe_enrolment_manager::get_enrolment_key_by_courseid($courseid);
        $this->courseid = $courseid;
        $this->alertsharecode = npe_enrolment_manager::check_expired_key_by_courseid($this->courseid);
        $this->pagetype = $pagetype;
        $this->iseducamoscourse = app::get_course($courseid)->is_educamos_course();
        $isseneca = app::get_userfactory()->get_current_user()->get_user_data()->is_seneca_user();
        $isexternal = app::get_userfactory()->get_current_user()->get_user_data()->is_external_user();
        $this->islti = $isexternal && !$isseneca;

    }

}

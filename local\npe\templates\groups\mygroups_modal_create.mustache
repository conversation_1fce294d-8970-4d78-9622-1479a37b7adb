<div class="modal fade group-create-modal" id="createGroup" tabindex="-1" role="dialog" data-region="modal-container" aria-hidden="true">
    <div class="modal-dialog" role="document" data-region="modal">
        <div class="modal-content modalbox">
            <div class="modal-header" data-region="header">
                <h5 class="modal-title" data-region="title">
                    {{# str }} creategroup, local_npe {{/ str }}
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-action="hide">
                    <span aria-hidden="true"></span>
                </button>
            </div>
            <div class="modal-body" data-region="body">
                <div  class="level levelinfo previous"></div>
                <div class="modal_text previous choose_name_title">
                    {{# str}} choosename, local_npe {{/ str }}
                    <span class="red_star"></span>
                </div>

                <input id="groupname" class="form-control group-input previous" placeholder="" maxlength="30" aria-label="nombre">
                <div class="counter previous">
                    <span>{{# str}} maxcharallow, local_npe {{/ str }}</span>
                    <span class="numbers">
                        <span id="chars">0</span>/30
                    </span>
                </div>
                <div id="hideforaoneccaa">
                    <div id="title_ccaa" class="modal_text previous d-none">
                        <div>
                            {{# str}} chooseccaa, local_npe {{/ str }}
                        </div>
                        <span class="red_star"></span>
                        <div class="tooltip-help">
                            <button class="" type="button" id="helpButton" aria-label="help">
                                <div class="tooltiptexthelp mygroups">
                                    {{# str}}tooltipccaa, local_npe{{/ str }}
                                </div>
                            </button>
                        </div>
                    </div>
                    <input type="hidden" id="groupccaa">
                    <input type="text" id="groupccaa_text" class="form-control group-input previous dropdown-toggle d-none" placeholder="Escoge una" readonly>
                    <div id="dropdown_ico" class="d-none"></div>
                    <div id="groupccaa_dropdown" class="d-none">
                        <div id="groupccaa_dropdown_content">
                        </div>
                    </div>
                </div>
                <div class="result hide" data-region="result">
                    <div class="okicon"></div>
                    <div class="congrats">{{# str}} groupoktitle, local_npe {{/ str }}</div>
                    <div class="congrats_text">{{# str}} groupoktext, local_npe {{/ str }}</div>
                    <div id="levelinforesult" class="level levelinfo previous"></div>
                    <div class="title-addgroup" data-field="group"></div>
                    <div class="codeactions">
                        <div id="copytoclip" class="enrollment" data-field="code"></div>
                        <div class="actions">
                            <a id="copy" href="javascript:void(0)" data-toggle="popover" data-trigger="focus"
                               data-placement="bottom" data-content="{{# str}} copytoclipboard, local_npe {{/ str }}" aria-label="copytoclip">
                                <span class="copy" data-container="body" ></span>
                            </a>
                            <a id="download-code-button" href="javascript:void(0)" aria-label="download">
                                <span class="download"></span>
                            </a>
                        </div>
                    </div>
                    <button type="button" class="btn btn-primary" data-action="finish">
                        {{# str}} finishbutton, local_npe {{/ str }}
                    </button>
                </div>
                <div class="download-code hide" data-region="download-code">
                    <div class="title">{{# str}} downloadcode, local_npe {{/ str }}</div>
                    <div class="download_text">{{# str}} downloadtext, local_npe {{/ str }}</div>
                    <div class="download-buttons">
                        <a href="#" class="download" data-region="codestudent">{{# str}} downloadstudent, local_npe {{/ str }}</a>
                        <a href="#" class="download" data-region="codeteacher">{{# str}} downloadteacher, local_npe {{/ str }}</a>
                    </div>
                    <button type="button" class="btn btn-primary" data-action="finish">{{# str}} finishbutton, local_npe {{/ str }}</button>
                </div>
            </div>
            {{$ footer}}
                <div class="modal-footer" data-region="footer">
                    <button type="button" class="btn btn-primary" data-action="save" disabled>
                        {{# str }} creategroupbutton, local_npe {{/ str }}
                    </button>
                </div>
            {{/ footer}}
        </div>
    </div>
</div>
<div class="cards-container">
    <div class="card-item panel"
         data-assignid = "{{ assignid }}" data-order="{{ order }}" data-state="{{ state }}"
         data-topicid="{{ producttopicid }}" data-name="{{ name }}" data-date="{{ assigneddate }}"
         data-created="{{ timecreated }}">
        <div class="card-head panel-heading">
            <div class="flex">
                <span class="state-tag {{ state }}">{{ statestr }}</span>
                <div class="card-head-info">
                    {{# feedback }}
                        <span class="comment {{^ grade }}right0{{/ grade }}" data-toggle="tooltip" data-placement="bottom" title="{{# str}} commentsforsubmission, local_npe{{/ str}}"></span>
                    {{/ feedback }}
                    {{^ isresource }}
                    <span class="grade grey{{ gradetype }}"
                        {{# graded }}{{^ hashiddengrades }}
                          style="border: 2px solid {{ grade_color_border }}; background-color:{{ grade_color_fill }} "
                        {{/ hashiddengrades }}{{/ graded }}>
                    {{^ hashiddengrades }}
                        {{ grade }}
                    {{/ hashiddengrades }}
                    {{# hashiddengrades }}
                        {{# pix }} eye-close-alt, theme_npe {{/pix}}
                    {{/ hashiddengrades }}
                    </span>
                    {{/ isresource }}
                </div>
            </div>
        </div>
        <div class="card-body activity-heading extra panel-body">
            <span class="type">{{ type }}</span>
            {{#activityurl}}
                {{# isavailable }}
                    <span class="name"> <a href="{{{ activityurl }}}" class="link">{{{ name }}}</a> </span>
                {{/ isavailable }}
                {{^ isavailable }}
                    <span class="name" data-toggle="tooltip" data-placement="bottom" title="{{ notavailabletext }}">
                    <a href="" class="notavai">{{{ name }}}</a>
                </span>
                {{/ isavailable}}
            {{/activityurl}}
            {{^activityurl}}
                <span class="name">{{ name }}</span>
            {{/activityurl}}
            <span class="unit">{{ unit }}</span>
        </div>
        <div class="card-body extra panel-body">
            {{# deliverdate }}<span class="date">{{# str }}myhomework_deliverdate, local_npe{{/ str }}: <b>{{ deliverdate }}</b></span>{{/ deliverdate }}
            {{# date }}<span class="date">{{# str }}myhomework_deliverdate, local_npe{{/ str }}: <b>{{ date }}</b></span>{{/ date }}
            {{# timebegin }}<span class="date">{{# str }}myassignments_hourbegining, local_npe{{/ str }}: <b>{{ timebegin }}</b></span>{{/ timebegin }}
            {{# timeend }}<span class="date">{{# str }}myassignments_hourend, local_npe{{/ str }}: <b>{{ timeend }}</b></span>{{/ timeend }}
            <div class="collapse">
                {{# gradedate }}<span class="date">{{# str }}wasgradeddate, local_npe{{/ str }} <b>{{ gradedate }}</b></span>{{/ gradedate }}
                {{# reopendate }}<span class="date">{{# str }}wasreopendate, local_npe{{/ str }} <b>{{ reopendate }}</b></span>{{/ reopendate }}
                {{#assigneddate}}
                {{^ isstudentevidence}}
                <span class="date end">{{# str }}myhomework_assigndate, local_npe{{/ str }} <b>{{ assigneddate }}</b></span>
                {{/ isstudentevidence}}
                {{/assigneddate}}
            </div>
        </div>
        <div class="card-body panel-group panel-body collapse {{^ team }}{{^ evidence }}{{^ insequence }}is-empty{{/ insequence }}{{/ evidence }}{{/ team }}">
            {{# team }}<span class="card-property teams">{{# str }}team, local_npe{{/ str }} <b>{{ team }}</b></span>{{/ team }}
            {{# evidence }}<span class="card-property evidence">{{ txtPortfolioEvidence }}</span>{{/ evidence }}
            {{# insequence }}<span class="card-property sequence">{{# str }}myhomework_insequence, local_npe{{/ str }}</span>{{/ insequence }}
        </div>
        {{# comment }}
        <div class="card-body panel-group panel-body collapse">
            <div class="tit">{{# str }}headerlibrary, local_npe{{/ str }}</div>
            <div class="description">{{ comment }}</div>
        </div>
        {{/ comment }}
        <div class="card-foot panel-footer">
            {{# iscustom}}
                {{# teacher }}
                    <div class="card-body extra panel-body">
                        <span class="tit collapse">{{# str }}myhomework_createdby, local_npe{{/ str }}</span>
                        <img src="{{ picture }}" role="none">
                        <span class="teacher">{{ name }}</span>
                    </div>
                {{/ teacher }}
                {{# isstudentevidence}}
                    <div class="card-body extra panel-body">
                        <span class="tit collapse">{{# str }}myhomework_createdby, local_npe{{/ str }}</span>
                        <img class="user-picture" src="{{ evidenceuserpicture }}" role="none">
                        <span class="user-fullname">{{ evidenceuserfullname }}</span>
                    </div>
                {{/ isstudentevidence}}
            {{/ iscustom}}

            {{# markable }}
                <div class="card-body extra panel-body">
                    <div class="custom_checkbox">
                        <input type="checkbox" id="post-{{id}}-card" data-id="{{ id }}" data-courseid="{{ courseid }}" {{# done }}checked="checked" {{/ done }}>
                        <label for="post-{{id}}-card"></label>
                        <span>{{# str }}myhomework_markasdone, local_npe{{/ str }}</span>
                    </div>
                </div>
            {{/ markable }}
            <div class="viewmore tcard"></div>
            <div class="viewless tcard hidden"></div>
        </div>
    </div>
</div>

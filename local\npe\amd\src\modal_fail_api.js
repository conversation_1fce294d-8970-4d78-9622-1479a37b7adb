import $ from 'jquery';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';

let SELECTORS = {
    HIDE: '.close',
    FINISH: '[data-action="cancel"]',
    EXIT: '[data-action="exit"]',
};

let registered = false;

export default class ModalFailApi extends Modal {

    static TYPE = 'local_npe/modal_fail_api';
    static TEMPLATE = 'local_npe/courses/modal_fail_api';

    constructor(root) {
        super(root);
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.success-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
            window.location.reload();
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.success-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
            window.location.reload();
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.EXIT, () => {
            window.location.reload();
        });
    }
}

if (!registered) {
    ModalRegistry.register(ModalFailApi.TYPE, ModalFailApi, ModalFailApi.TEMPLATE);
    registered = true;
}

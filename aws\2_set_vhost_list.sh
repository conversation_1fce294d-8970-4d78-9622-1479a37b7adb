#!/bin/bash
# SM Set vhost script

if [[ $DEPLOYMENT_GROUP_NAME = "Cron" || $DEPLOYMENT_GROUP_NAME = "Cron-Staging" || $DEPLOYMENT_GROUP_NAME = "MoodleCron" ]]; then
  exit 0
fi

# Set params
ENTORNO=QA
if [[ $DEPLOYMENT_GROUP_NAME = "Moodle-Staging" ]]; then
  ENTORNO=STG
elif [[ $DEPLOYMENT_GROUP_NAME = "Moodle" ]]; then
  ENTORNO=PRO
fi

VHOSTFILE=/etc/httpd/conf.d/macro-vhosts.list
VHOST=__MACROVHOSTSLIST__

#Check if vhost exists
if [ ! -f "$VHOSTFILE" ]; then
    echo "$VHOSTFILE not found!"
    exit 1
fi

# Exec if Host has not been built
if grep -q $VHOST $VHOSTFILE; then
  # Get AWS params
  AWSDATA=$( (aws ssm get-parameters --names /"$ENTORNO"/NPE/MacroVHostList --query Parameters[0].Value) | sed 's/"//g')

  # Add PARAMS
  sedParams=(-e "s|$VHOST|$AWSDATA|g")
  sed -i "${sedParams[@]}" $VHOSTFILE
else
  echo "$VHOSTFILE already configured"
fi

<div id="accordion-item-{{section}}" class="accordion-item li-section">
    <div class="icon_menu section collapse-right">
        <div class="select-section-sidemenu collapse-right"></div>
        {{# label }}
            {{{ icon }}}
            {{# hasfilters }}
                <a class="section {{ section }} has-filters" 
                    href="{{href}}" 
                    target="{{target}}" 
                    data-section="{{ section }}" 
                    data-toggle="collapse" 
                    data-target="#categories-{{section}}" 
                    aria-expanded="false">{{ label }}</a>
            {{/ hasfilters }}
            {{^ hasfilters }}
                <a class="section {{ section }} nocats" 
                   href="{{href}}" 
                   target="{{target}}" 
                   data-section="{{ section }}">{{ label }}</a>
            {{/ hasfilters }}
        {{/label}}
    </div>
    {{# hasfilters }}
    <div id="categories-{{section}}" class="categories collapse border-left-c">
        <ul>
        {{# filters }}
            <li data-cat="{{ key }}" 
                data-cat-name="{{ value }}" 
                data-section="{{ section }}"
                data-sideid="{{ section }}-categoryId-{{ key }}" >
                {{ value }} <span class="count">(0)</span>
            </li>
        {{/ filters }}
        </ul>
    </div>
    {{/ hasfilters }}
</div>

{{#js}}
require(['jquery'], function($) {
    $('.section.has-filters').on('click', function(e) {
        e.preventDefault();
        
        // Comprobar si el side-menu está colapsado
        if ($('.sidebar-menu').hasClass('collapse-width') || $('.sidebar-menu').hasClass('collapse-width-no-animated')) {
            // Si está colapsado, no desplegar el acordeón
            return false;
        }
        
        var target = $(this).data('target');
        
        // Cerrar todos los demás acordeones
        $('.categories.collapse.show').not(target).collapse('hide');
        
        // Toggle acordeón actual
        $(target).collapse('toggle');
        
        if ($(this).parent().hasClass('active')){
            return false;
        }
    });

    // Colapsar todos los acordeones cuando se pulsa el botón sidebar-icon
    $('#sidebar-icon').on('click', function() {
        $('.categories.collapse.show').collapse('hide');
    });

    $('.section.nocats').on('click', function(e) {
        $('.categories.collapse.show').collapse('hide');
    });

    if (document.readyState === 'complete') {
        initAfterLoad();
    } else {
        $(window).on('load', initAfterLoad);
    }

    function initAfterLoad() {
        waitForActiveElement(function(activeElement) {
            setTimeout(function() {
                activeElement.trigger('click');
            }, 100);
        });

        $('.activities-list.list, .activities-list.tablet').each(function() {
            var count = $(this).data('count');
            var section = $(this).data('section');
            var category = $(this).data('category');
            var categoryLi = $('.categories ul li[data-section="' + section + '"][data-cat="' + category + '"]');
            if (categoryLi.length > 0) {
                categoryLi.find('.count').text('(' + count + ')');
            }
        });

        $('input.custom_checkbox:checked').each(function() {
            var dataFilter = $(this).data('filter');
            if (dataFilter && dataFilter.indexOf('categoryId') !== -1) {
                var elementId = $(this).attr('id');
                var categoryLi = $('.categories ul li[data-sideid="' + elementId + '"]');
                if (categoryLi.length > 0) {
                    categoryLi.addClass('active bgf-c');
                }
            }
        });

        $(document).on('change', 'input[data-filter]', function() {
            var dataFilter = $(this).data('filter');
            if (dataFilter && dataFilter.indexOf('categoryId') !== -1) {
                var elementId = $(this).attr('id');
                var categoryLi = $('.categories ul li[data-sideid="' + elementId + '"]');
                if (categoryLi.length > 0) {
                    if ($(this).prop('checked')) {
                        var $categoryContainer = categoryLi.closest('ul');
                        var $activeItems = $categoryContainer.find('li.active').not(categoryLi);
                        if ($activeItems.length > 0) {
                            $activeItems.removeClass('active bgf-c');
                            $activeItems.each(function() {
                                var activeItemId = $(this).data('id');
                                if (activeItemId) {
                                    $('#' + activeItemId).prop('checked', false).trigger('change');
                                }
                            });
                        }
                        categoryLi.addClass('active bgf-c');
                    } else {
                        categoryLi.removeClass('active bgf-c');
                    }
                }
            }
        });
    }

    function waitForActiveElement(callback) {
        var interval = setInterval(function() {
            var activeElement = $('.icon_menu.section.active a');
            if (activeElement.length > 0) {
                clearInterval(interval);
                callback(activeElement);
            }
        }, 100);
    }

    // Eliminar cualquier clic existente
    $('.categories ul li').off('click');
    
    // Clic manager en los elementos de categorías
    $('.categories ul li').on('click', function(e) {
        e.stopPropagation();
        e.preventDefault();
        
        // Asegurarse de que estamos trabajando con el elemento li
        var $this = $(e.currentTarget);
        
        // Obtener el contenedor de categoría (ul) del elemento actual
        var $categoryContainer = $this.closest('ul');
        
        // Verificar si ya hay algún elemento activo en esta categoría
        var hasActiveItem = $categoryContainer.find('li.active');
        
        // Si ya hay un elemento activo y no es el actual, no hacemos nada
        if ($(hasActiveItem).length > 0 && !$this.hasClass('active')) {
            $(hasActiveItem).trigger('click');
        }
        
        if ($this.hasClass('active')) {
            $this.removeClass('active');
            $this.removeClass('bgf-c');
        } else {
            $this.addClass('active');
            $this.addClass('bgf-c');
        }
        
        // Obtener el data-sideid del elemento li
        var dataId = $this.data('sideid');
        
        // Si existe el data-sideid, buscar el input correspondiente y simulamos un clic
        if (dataId) {
            var inputElement = $('#' + dataId);
            if (inputElement.length > 0) {
                inputElement.trigger('click');
            }
        }
        
        // Evitar que el evento se propague más
        return false;
    });
    
    // Mouse events para hover en categorías
    $('.icon_menu .section, .categories ul li')
        .on('mouseenter', function() {
            if (!$(this).hasClass('active')) {
                $(this).addClass('bgf-c');
            }
        })
        .on('mouseleave', function() {
            if (!$(this).hasClass('active')) {
                $(this).removeClass('bgf-c');
            }
        });
});
{{/js}}

import $ from 'jquery';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';

const SELECTORS = {
    CONFIRM: '[data-action="confirm"]',
    CLOSE: '[data-region="equis"]',
    CCAAQUEESTION: '#caaquestion',
    CCAAINFO: '#ccaainfo'
};

let registered = false;

export default class ModalLinkCCAAEducamos extends Modal {

    static TYPE = 'local_npe/modal_link_ccaa_educamos';
    static TEMPLATE = 'local_npe/courses/modal_linking_ccaa_educamos';

    #textccaainfo = true;

    constructor(root) {
        super(root);
    }

    setData() {}

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.CCAAQUEESTION, () => {
            this.showtext();
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.CLOSE, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.success-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.CONFIRM, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.success-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
        });
    }

    showtext() {
        if (this.#textccaainfo) {
            $(SELECTORS.CCAAINFO).show();
            this.#textccaainfo = false;
        } else {
            $(SELECTORS.CCAAINFO).hide();
            this.#textccaainfo = true;
        }
    }
}

if (!registered) {
    ModalRegistry.register(ModalLinkCCAAEducamos.TYPE, ModalLinkCCAAEducamos, ModalLinkCCAAEducamos.TEMPLATE);
    registered = true;
}

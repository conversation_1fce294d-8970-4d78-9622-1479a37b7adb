{"version": 3, "file": "modal_notice_new_course1.min.js", "sources": ["../src/modal_notice_new_course1.js"], "sourcesContent": ["import $ from 'jquery';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\nimport ModalNoticeNewCourse2 from 'local_npe/modal_notice_new_course2';\nimport ModalFactory from 'core/modal_factory';\n\nconst SELECTORS = {\n    USERNAMETEACHER: '#usernameteacher',\n    GOON_BUTTON: '[data-action=\"go-on\"]',\n    NOTNOW_BUTTON: '[data-action=\"not-now\"]'\n};\n\nlet registered = false;\n\nexport default class ModalNoticeNewCourse1 extends Modal {\n\n    static TYPE = 'local_npe/modal_notice_new_course_1';\n    static TEMPLATE = 'local_npe/noticenewcourse/noticenewcourse_modal01_hi';\n\n    constructor(root) {\n        super(root);\n    }\n\n    setData(username) {\n        this.getRoot().find(SELECTORS.USERNAMETEACHER).text(username);\n    }\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.GOON_BUTTON, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.notice-new-course-hi-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n            ModalFactory.create({type: ModalNoticeNewCourse2.TYPE}).done((modal2) => {\n                modal2.show();\n            });\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.NOTNOW_BUTTON, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.notice-new-course-hi-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n            $('#wizard-noticenewcourse').show();\n            $('#screen-megamenu').hide();\n        });\n    }\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalNoticeNewCourse1.TYPE, ModalNoticeNewCourse1, ModalNoticeNewCourse1.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_defineProperty", "r", "t", "i", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_j<PERSON>y", "_custom_interaction_events", "_modal", "_modal_registry", "_modal_notice_new_course", "_modal_factory", "SELECTORS", "registered", "ModalNoticeNewCourse1", "Modal", "constructor", "root", "super", "setData", "username", "this", "getRoot", "find", "text", "registerEventListeners", "getModal", "on", "CustomEvents", "events", "activate", "removeClass", "$", "remove", "addClass", "ModalFactory", "create", "type", "ModalNoticeNewCourse2", "TYPE", "done", "modal2", "show", "hide", "_exports", "ModalRegistry", "register", "TEMPLATE"], "mappings": "qTAK8C,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,gBAAAH,EAAAI,EAAAC,GAAAD,OAAAA,EAAA,SAAAC,GAAAC,IAAAA,EAAA,SAAAD,EAAAD,GAAAC,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAL,IAAAA,EAAAK,EAAAE,OAAAC,yBAAAR,EAAA,CAAA,IAAAM,EAAAN,EAAAS,KAAAJ,EAAAD,kCAAAE,EAAA,OAAAA,EAAAI,MAAAA,IAAAA,4EAAAN,EAAAO,OAAAC,QAAAP,EAAA,CAAAQ,CAAAR,EAAAC,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAQ,CAAAV,MAAAJ,EAAAe,OAAAC,eAAAhB,EAAAI,EAAAa,CAAAA,MAAAZ,EAAAa,YAAAC,EAAAA,cAAAC,EAAAA,cAAApB,EAAAI,GAAAC,EAAAL,CAAA,iFAL9CqB,QAAAtB,uBAAAsB,SACAC,2BAAAvB,uBAAAuB,4BACAC,OAAAxB,uBAAAwB,QACAC,gBAAAzB,uBAAAyB,iBACAC,yBAAA1B,uBAAA0B,0BACAC,eAAA3B,uBAAA2B,gBAEA,MAAMC,0BACe,mBADfA,sBAEW,wBAFXA,wBAGa,0BAGnB,IAAIC,YAAa,EAEF,MAAMC,8BAA8BC,OAAAA,QAK/CC,WAAAA,CAAYC,MACRC,MAAMD,KACV,CAEAE,OAAAA,CAAQC,UACJC,KAAKC,UAAUC,KAAKX,2BAA2BY,KAAKJ,SACxD,CAEAK,sBAAAA,GACIP,MAAMO,uBAAuBJ,MAE7BA,KAAKK,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUlB,uBAAuB,KACpES,KAAKC,UAAUS,YAAY,SAC3B,EAAAC,QAAAA,SAAE,QAAQD,YAAY,eACtB,EAAAC,iBAAE,+BAA+BC,UACjC,EAAAD,QAAC7C,SAAC,mBAAmB4C,YAAY,QAAQG,SAAS,QAClDC,eAAYhD,QAACiD,OAAO,CAACC,KAAMC,iCAAsBC,OAAOC,MAAMC,SAC1DA,OAAOC,MAAM,GACf,IAGNrB,KAAKK,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUlB,yBAAyB,KACtES,KAAKC,UAAUS,YAAY,SAC3B,EAAAC,QAAAA,SAAE,QAAQD,YAAY,eACtB,EAAAC,iBAAE,+BAA+BC,UACjC,EAAAD,QAAC7C,SAAC,mBAAmB4C,YAAY,QAAQG,SAAS,SAClD,EAAAF,iBAAE,2BAA2BU,QAC7B,EAAAV,iBAAE,oBAAoBW,MAAM,GAEpC,EAMH,OALAC,SAAAzD,QAAA2B,sBAAA1B,gBAnCoB0B,sBAAqB,OAExB,uCAAqC1B,gBAFlC0B,sBAAqB,WAGpB,wDAkCjBD,aACDgC,gBAAAA,QAAcC,SAAShC,sBAAsByB,KAAMzB,sBAAuBA,sBAAsBiC,UAChGlC,YAAa,GAChB+B,SAAAzD,OAAA"}
<div class="activities-container ac4filters {{ section }} hidden" data-section="{{ section }}" >
    {{# showhelp }}
        {{< local_npe/commons/info_help }}
            {{$ headerhelp }}
                {{ headerhelp }}
            {{/ headerhelp }}
            {{$ buttonhelppanel }}
                {{# hashelppanel }}
                    {{# isteacher }}
                        {{# issmauser }}
                            <a id="linkhelppanel" class="btn npe-button-primary"
                               href="{{helppanellink}}" target="_blank">{{helppanellinklabel}}</a>
                        {{/issmauser}}
                    {{/isteacher}}
                {{/hashelppanel}}
            {{/buttonhelppanel}}
            {{$ texthelp }}
                {{{ texthelp }}}
            {{/ texthelp }}
            {{$ section }}
                {{ section }}
            {{/ section }}
        {{/ local_npe/commons/info_help }}
    {{/ showhelp }}
    {{# categories }}
        {{#count}}
            {{^ noitems }}
                <div class="activities-list {{ idView }}" data-section="{{ section }}" data-name="{{ name }}"
                     data-category="{{ category }}" data-count="{{count}}">
                    <div class="activities-type">
                        {{ name }}<span class="count">({{ count }})</span>
                    </div>
                    {{# showsubcats }}
                        <div class="accordion md-accordion subcatsdropdown activities-subcats" id="subcatsdropdown-{{ section }}-{{ category }}" >
                            <ul class="itemslist">
                                {{# subcats }}
                                <li class="topic-item cc-subcat" data-filtersuddrop="{{ section }}-{{ category }}-subcategoryId-{{ subcatname }}">
                                    <input type="checkbox" id="{{ section }}-{{ category }}-subcategoryId-{{ subcatname }}-sub" data-filtertype="subcategoryId" data-id="{{ subcatname }}" 
                                    data-filter="{{ section }}-subcategoryId-{{ subcatname }}" data-parent-header="heading-units-dropdown-{{ section }}-{{ category }}"
                                    data-label="{{ value }}">
                                    <label class="subcat" for="{{ section }}-{{ category }}-subcategoryId-{{ subcatname }}-sub" data-subcategory="{{ subcatname }}"
                                    data-category="{{ section }}-categoryId-{{ category }}" data-category-drop="{{ section }}-{{ category }}"
                                    data-filter="{{ section }}-subcategoryId-{{ subcatname }}">
                                        <span>{{ subcatrealname }}</span> <counter>({{ subcatcounter }})</counter>
                                        <i data-subfilterclose="{{ section }}-subcategoryId-{{ subcatname }}"
                                        data-subfilterid="{{ section }}-{{ category }}-subcategoryId-{{ subcatname }}-sub"></i>
                                    </label>
                                </li>
                                {{/ subcats }}
                            </ul>
                        </div>
                    {{/ showsubcats }}

                    <div class="activities-header npe-table npe-table-header npe-table-x5-activities {{^showsubcategoriesname}}notsubcat{{/showsubcategoriesname}}">
                        <div class="td-1 npe-table-label header-name">
                            <div class="title" title="{{resourcetitle}}">
                                {{resourcetitle}}
                            </div>
                        </div>
                        <div class="td-2 npe-table-label header-evidence"></div>
                        <div class="td-3 npe-table-label header-unit">
                            <div class="title" title="{{unittitle}}">
                                {{unittitle}}
                            </div>
                        </div>
                        <div class="td-4 npe-table-label">
                            {{# showsubcategoriesname }} 
                                <div class="header-subcategory title" title="{{subcattitle}}">
                                    {{subcattitle}}
                                </div>
                            {{/ showsubcategoriesname }}
                        </div>
                        <div class="td-5 npe-table-label header-unit"></div>
                    </div>
                    <div class="{{ idView }}-view">
                        {{# items }}
                            <div class="activity-item npe-table npe-table-x5-activities {{^showsubcategoriesname}}notsubcat{{/showsubcategoriesname}}"
                                 data-sequenceid="{{ topicid }}"
                                 data-categoryid="{{ categoryid }}"
                                 data-blockid="{{ blockid }}"
                                 data-toassign="{{ toassign }}"
                                 data-sequence="{{ insequence }}"
                                 data-recommendedgroupingid="{{ deliverymapped }}"
                                 data-difficultylevelid="{{ dificultylevel }}"
                                 data-keyevidenceid="{{# keyevidence }}true{{/ keyevidence }}{{^ keyevidence }}false{{/ keyevidence }}"
                                 data-type="{{ type }}"
                                 data-competenceid="{{ competenceid }}"
                                 data-criterionid="{{ criterionid }}"
                                 data-themeid="{{ themeid }}"
                                 data-transversekeyid="{{ transversekeyid }}"
                                 data-skillsid="{{ skillsid }}"
                                 data-pedagogicalpurposesid="{{ pedagogicalpurposesid }}"
                                 data-assessmentid="{{ assessmentid }}"
                                 data-learninglevelid="{{ learninglevelid }}"
                                 data-trackingactivitiesid="{{ trackingactivitiesid }}"
                                 data-challengesid="{{ challengesid }}"
                                 data-incontextid="{{ incontextid }}"
                                 data-typeofactivityid="{{ typeofactivityid }}"
                                 data-presentationresourcesid="{{ presentationresourcesid }}"
                                 data-subcategoryid="{{ subcategoryid }}"
                            >

                                <div class="activity-type npe-table-label mt10 {{type}}" data-toggle="tooltip" data-placement="bottom" title="{{activitytypename}}"></div>
                                <div class="td-1 npe-table-label activity-info">
                                    {{# activityurl }}
                                        <div class="activity-name {{^ isassigned}}{{^ insequence}}{{^ authorinfo }}pt10{{/ authorinfo }}{{/ insequence}}{{/ isassigned}}"
                                             data-name="{{ name }}">
                                            <a href="{{{ activityurl }}}">{{{ name }}}</a>
                                        </div>
                                    {{/ activityurl }}
                                    {{^ activityurl }}
                                        <div class="activity-name {{^ isassigned}}{{^ insequence}}{{^ authorinfo }}pt10{{/ authorinfo }}{{/ insequence}}{{/ isassigned}}"
                                             data-name="{{ name }}">
                                            {{ name }}
                                        </div>
                                    {{/ activityurl }}
                                </div>

                                <div class="td-2 npe-table-label">
                                    {{# keyevidence}}
                                        <div class="activity-keyevidence mt10" data-toggle="tooltip" data-placement="bottom" title="{{ txtPortfolioEvidence }}"></div>
                                    {{/ keyevidence}}
                                </div>

                                {{# sdanumber }}
                                    <div class="td-3 npe-table-label activity-unit">{{ sdanumber }} - {{ unit }}</div>
                                {{/ sdanumber }}
                                {{^ sdanumber }}
                                    <div class="td-3 npe-table-label activity-unit">{{ unit }}</div>
                                {{/ sdanumber }}

                                <div class="td-4 npe-table-label">
                                    {{# showsubcategoriesname }}
                                        <div class="activity-subcategory">{{ subcategoryname }}</div>
                                    {{/ showsubcategoriesname }}
                                </div>

                                 <div class="td-5 npe-table-label activity-dropdown">
                                    <div class="npe-dropdown-container">
                                        {{# isteacher}}
                                            {{> local_npe/assignments/assignments_dropdown }}
                                        {{/ isteacher}}
                                    </div>
                                </div>

                            </div>
                        {{/ items }}
                    </div>
                </div>
            {{/ noitems }}
        {{/count}}
    {{/ categories}}
</div>



define("local_npe/modal_join_group",["exports","jquery","core/notification","core/custom_interaction_events","core/modal","core/modal_registry","core/ajax","core/str"],(function(_exports,_jquery,_notification,_custom_interaction_events,_modal,_modal_registry,_ajax,_str){function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _classPrivateFieldInitSpec(e,t,a){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,a)}function _defineProperty(e,r,t){return(r=function(t){var i=function(t,r){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var i=e.call(t,r||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==typeof i?i:i+""}(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function _classPrivateFieldGet(s,a){return s.get(_assertClassBrand(s,a))}function _classPrivateFieldSet(s,a,r){return s.set(_assertClassBrand(s,a),r),r}function _assertClassBrand(e,t,n){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_notification=_interopRequireDefault(_notification),_custom_interaction_events=_interopRequireDefault(_custom_interaction_events),_modal=_interopRequireDefault(_modal),_modal_registry=_interopRequireDefault(_modal_registry),_ajax=_interopRequireDefault(_ajax);const SELECTORS_MODALBOX=".modalbox",SELECTORS_JOIN_BUTTON='[data-action="JOIN"]',SELECTORS_SAVE_BUTTON='[data-action="save"]',SELECTORS_INPUT="#groupcode",SELECTORS_CONTAINER='[data-region="modal-container"]',SELECTORS_RESULT='[data-region="result"]',SELECTORS_ERROR=".error",SELECTORS_HIDE=".close",SELECTORS_PREVIOUS=".previous",SELECTORS_CUSTOM=".custom-text",SELECTORS_CONGRATSTITLE=".congrats",SELECTORS_CONGRATSTEXT=".congrats_text",SELECTORS_FINISH='[data-action="finish"]',SELECTORS_LEVELINFO=".levelinfo",SERVICES_JOINGROUP="local_npe_join_group";let registered=!1;var _courseid=new WeakMap,_level=new WeakMap,_name=new WeakMap,_newcourse=new WeakMap,_courseurl=new WeakMap;class ModalJoinGroup extends _modal.default{constructor(root){super(root),_classPrivateFieldInitSpec(this,_courseid,void 0),_classPrivateFieldInitSpec(this,_level,void 0),_classPrivateFieldInitSpec(this,_name,void 0),_classPrivateFieldInitSpec(this,_newcourse,!1),_classPrivateFieldInitSpec(this,_courseurl,"")}setData(level,name,courseid){_classPrivateFieldSet(_level,this,level),_classPrivateFieldSet(_name,this,name),_classPrivateFieldSet(_courseid,this,courseid),(0,_str.get_strings)([{key:"joincoursestudenttext",component:"local_npe",param:name},{key:"joincourseteachertext",component:"local_npe",param:name}]).done((strings=>{1==(0,_jquery.default)(SELECTORS_JOIN_BUTTON).data("student")?this.getRoot().find(SELECTORS_CUSTOM).html(strings[0]):this.getRoot().find(SELECTORS_CUSTOM).html(strings[1])})),this.getRoot().find(SELECTORS_LEVELINFO).html(level+". "+name)}registerEventListeners(){super.registerEventListeners(this),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_JOIN_BUTTON,(()=>{this.getRoot().find(SELECTORS_LEVELINFO).html(_classPrivateFieldGet(_level,this)+". "+_classPrivateFieldGet(_name,this))})),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_HIDE,(()=>{this.getRoot().find(SELECTORS_CONTAINER).show(),this.getTitle().show(),this.getRoot().find(SELECTORS_RESULT).hide(),this.getRoot().find(SELECTORS_ERROR).hide(),this.getRoot().find(SELECTORS_INPUT).removeClass("error"),this.getRoot().removeClass("show"),this.getRoot().find(SELECTORS_INPUT).val(""),this.getRoot().removeClass("show")})),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_SAVE_BUTTON,(()=>{this.ajaxCall()})),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_FINISH,(()=>{this.getRoot().removeClass("show"),(0,_jquery.default)(".group-join-modal").remove(),(0,_jquery.default)(".modal-backdrop").removeClass("show").addClass("hide"),_classPrivateFieldGet(_newcourse,this)?window.location.replace(_classPrivateFieldGet(_courseurl,this)):location.reload()})),this.eventMaxChar()}ajaxResult(response){if(void 0!==response.course){let name=response.course.name;1==(0,_jquery.default)(SELECTORS_JOIN_BUTTON).data("student")&&(_classPrivateFieldSet(_newcourse,this,response.course.id),_classPrivateFieldSet(_courseurl,this,response.course.url)),(0,_str.get_strings)([{key:"joincourseoktitle",component:"local_npe",param:name},{key:"joincourseokteachertext",component:"local_npe",param:name},{key:"joincourseokstudenttext",component:"local_npe",param:name}]).done((strings=>{(0,_jquery.default)(SELECTORS_HIDE).attr("data-action","finish"),this.getRoot().find(SELECTORS_PREVIOUS).hide(),this.getRoot().find(SELECTORS_CONTAINER).hide(),this.getTitle().hide(),this.getRoot().find(SELECTORS_INPUT).removeClass("error"),this.getRoot().find(SELECTORS_ERROR).hide(),this.getRoot().find(SELECTORS_MODALBOX).animate({height:"340px"},200),0==(0,_jquery.default)(SELECTORS_JOIN_BUTTON).data("student")?(this.getRoot().find(SELECTORS_CONGRATSTITLE).html(strings[0]),this.getRoot().find(SELECTORS_CONGRATSTEXT).html(strings[1])):(this.getRoot().find(SELECTORS_CONGRATSTITLE).html(strings[0]),this.getRoot().find(SELECTORS_CONGRATSTEXT).html(strings[2])),this.getRoot().find(SELECTORS_RESULT).show(),this.getFooter().hide()}))}else this.getRoot().find(SELECTORS_INPUT).addClass("error"),this.getRoot().find(SELECTORS_ERROR).html(response.error.errordes),this.getRoot().find(SELECTORS_ERROR).show(),this.getRoot().find(SELECTORS_INPUT).show()}ajaxError(ex){_notification.default.exception({message:ex})}ajaxCall(){_ajax.default.call([{methodname:SERVICES_JOINGROUP,args:{enrolmentkey:this.getRoot().find(SELECTORS_INPUT).val(),courseid:_classPrivateFieldGet(_courseid,this)}}])[0].done((response=>{this.ajaxResult(response)})).fail((ex=>{this.ajaxError(ex)}))}eventMaxChar(){this.getRoot().find(SELECTORS_INPUT).on("keyup",(event=>{let text=(0,_jquery.default)(event.currentTarget).val();if(text.length>0){const format=/[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~·]/;(0,_jquery.default)(SELECTORS_SAVE_BUTTON).prop("disabled",format.test(text))}else(0,_jquery.default)(SELECTORS_SAVE_BUTTON).prop("disabled",!0)}))}}return _exports.default=ModalJoinGroup,_defineProperty(ModalJoinGroup,"TYPE","local_npe/modal_join_group"),_defineProperty(ModalJoinGroup,"TEMPLATE","local_npe/groups/mygroups_modal_join_group"),registered||(_modal_registry.default.register(ModalJoinGroup.TYPE,ModalJoinGroup,ModalJoinGroup.TEMPLATE),registered=!0),_exports.default}));

//# sourceMappingURL=modal_join_group.min.js.map
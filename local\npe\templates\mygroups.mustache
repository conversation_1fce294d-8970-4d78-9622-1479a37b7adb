{{^ iseducamos}}
    {{#isteacher}}
        {{#noticenewcourse_intime}}
            {{> local_npe/modal_notice_new_course_shown }}
            {{^ noticenewcourse}}
                {{> local_npe/modal_notice_new_course }}
            {{/noticenewcourse}}
        {{/noticenewcourse_intime}}
    {{/isteacher}}
{{/iseducamos}}
<div class="top-bar">
    {{^ iseducamos }}
        {{^ isexternal }}
            {{> local_npe/commons/breadcrumbs }}
        {{/ isexternal }}
        {{# showhelp }}
            {{< local_npe/commons/info_help }}
                {{$ headerhelp }}
                    {{# str}} headerhelpgroupsmanagement, local_npe {{/str}}
                {{/ headerhelp }}
                {{$ buttonhelppanel }}
                    {{# hashelppanel }}
                        {{# isteacher }}
                            {{# issmauser }}
                                <a id="linkhelppanel" class="btn npe-button-primary"
                                href="{{helppanellink}}" target="_blank">{{helppanellinklabel}}</a>
                            {{/issmauser}}
                        {{/isteacher}}
                    {{/hashelppanel}}
                {{/buttonhelppanel}}
                {{$ texthelp }}
                    {{# str}} texthelpgroupsmanagement, local_npe, {{limitgroup}} {{/str}}
                {{/ texthelp }}
            {{/ local_npe/commons/info_help }}
        {{/ showhelp }}
    {{/ iseducamos }}
</div>
<div id="npe-helpdock">
</div>
<div class="main_container npe-margin-auto-center">
    <div class="row logo svglogo">
        {{{ logo }}}
    </div>
    <div class="row center product">
        <span class="fullw">{{ title }}</span>
    </div>
    {{# hasgroups }}
        <div class="row center level">
            <span class="fullw">{{#str}} levelgroups, local_npe,{{stage}} {{/str}}</span>
            {{# isurlshared }}
                <span class="fullw select-group-shared">{{#str}} mygroups_selectgroupshared, local_npe {{/str}}</span>
            {{/ isurlshared }}
        </div>
        <div class="center">
            <div class="center cards npe-grid-flex">
                {{> local_npe/groups/mygroups_list }}
            </div>
        </div>
    {{/ hasgroups }}
    {{^ hasgroups }}
        {{> local_npe/groups/mygroups_nogroups }}
    {{/ hasgroups }}
    {{^ isurlshared }}
        {{> local_npe/groups/mygroups_buttons }}
    {{/isurlshared}}
</div>
<!-- Menu Flotante de Herramientas -->
{{^ iseducamos }}
    {{> local_npe/commons/floating_tools_menu }}
{{/ iseducamos }}
<!-- Menu Flotante de Chatbox -->
{{> local_npe/commons/floating_chatbot_menu }}
{{# js }}
    var noticenewcourse = "{{ noticenewcourse }}";
    var noticenewcourse_intime = "{{ noticenewcourse_intime }}";
    var isteacher = "{{ isteacher }}";

    require(['local_npe/buttons_share_code', 'local_npe/button_delete_group', 'local_npe/button_dropout_group', 'local_npe/button_rename_group', 'local_npe/button_duplicate_group',
    'local_npe/modal_notice_new_course1', 'core/modal_factory', 'local_npe/prepare_ui'],
        function(ButtonsShareCode, ButtonDeleteGroup, ButtonDropoutGroup, ButtonRenameGroup, ButtonDuplicateGroup, ModalNoticeNewCourse1, ModalFactory, PrepareUI) {
            new ButtonsShareCode(null, null);
            new ButtonDeleteGroup();
            new ButtonDropoutGroup();
            new ButtonRenameGroup();
            new ButtonDuplicateGroup();
            new PrepareUI('.card_counter','.group_card');
            if ((isteacher == true) && (noticenewcourse_intime == true)) {
                if (noticenewcourse == false) {
                    ModalFactory.create({type: ModalNoticeNewCourse1.TYPE}).done(function (modal) {
                        modal.setData("{{ username }}");
                        modal.show();
                    });
                }
            }
        }
    );

{{/ js }}
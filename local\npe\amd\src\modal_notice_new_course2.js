import $ from 'jquery';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';
import ModalNoticeNewCourse3 from 'local_npe/modal_notice_new_course3';
import ModalFactory from 'core/modal_factory';

const SELECTORS = {
    NEXTMODAL_BUTTON: '[data-action="next-modal"]'
};

let registered = false;

export default class ModalNoticeNewCourse2 extends Modal {

    static TYPE = 'local_npe/modal_notice_new_course_2';
    static TEMPLATE = 'local_npe/noticenewcourse/noticenewcourse_modal02_info';

    constructor(root) {
        super(root);
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.NEXTMODAL_BUTTON, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.notice-new-course-info-modal').removeClass('show').addClass('hide');
            $('.modal-backdrop').removeClass('show').addClass('hide');
            if ($('.notice-new-course-manag-modal').hasClass('hide')) {
                $('.notice-new-course-manag-modal').removeClass('hide').addClass('show');
            } else {
                ModalFactory.create({type: ModalNoticeNewCourse3.TYPE}).done((modal3) => {
                    modal3.show();
                });
            }
        });
    }
}

if (!registered) {
    ModalRegistry.register(ModalNoticeNewCourse2.TYPE, ModalNoticeNewCourse2, ModalNoticeNewCourse2.TEMPLATE);
    registered = true;
}

import $ from 'jquery';
import Notification from 'core/notification';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';
import Ajax from 'core/ajax';
import { get_strings as getStrings } from 'core/str';

const SELECTORS = {
    MODALBOX: '.modalbox',
    JOIN_BUTTON: '[data-action="JOIN"]',
    SAVE_BUTTON: '[data-action="save"]',
    INPUT: '#groupcode',
    CONTAINER: '[data-region="modal-container"]',
    RESULT: '[data-region="result"]',
    ERROR: '.error',
    HIDE: '.close',
    PREVIOUS: '.previous',
    CUSTOM: '.custom-text',
    CONGRATSTITLE: '.congrats',
    CONGRATSTEXT: '.congrats_text',
    FINISH: '[data-action="finish"]',
    LEVELINFO: '.levelinfo',
};

const SERVICES = {
    JOINGROUP: 'local_npe_join_group'
};

let registered = false;

export default class ModalJoinGroup extends Modal {

    static TYPE = 'local_npe/modal_join_group';
    static TEMPLATE = 'local_npe/groups/mygroups_modal_join_group';

    #courseid;
    #level;
    #name;
    #newcourse = false;
    #courseurl = '';

    constructor(root) {
        super(root);
    }

    setData(level, name, courseid) {
        this.#level = level;
        this.#name = name;
        this.#courseid = courseid;

        getStrings(
            [
                {key: 'joincoursestudenttext', component: 'local_npe', param: name},
                {key: 'joincourseteachertext', component: 'local_npe', param: name}
            ]
        ).done((strings) => {
            if ($(SELECTORS.JOIN_BUTTON).data('student') == true) {
                this.getRoot().find(SELECTORS.CUSTOM).html(strings[0]);
            } else {
                this.getRoot().find(SELECTORS.CUSTOM).html(strings[1]);
            }
        });

        this.getRoot().find(SELECTORS.LEVELINFO).html(level + '. ' + name);
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.JOIN_BUTTON, () => {
            this.getRoot().find(SELECTORS.LEVELINFO).html(this.#level + '. ' + this.#name);
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {
            this.getRoot().find(SELECTORS.CONTAINER).show();
            this.getTitle().show();
            this.getRoot().find(SELECTORS.RESULT).hide();
            this.getRoot().find(SELECTORS.ERROR).hide();
            this.getRoot().find(SELECTORS.INPUT).removeClass('error');
            this.getRoot().removeClass('show');
            this.getRoot().find(SELECTORS.INPUT).val('');
            this.getRoot().removeClass('show');
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.SAVE_BUTTON, () => {
            this.ajaxCall();
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {
            this.getRoot().removeClass('show');
            $('.group-join-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
            if (this.#newcourse) {
                window.location.replace(this.#courseurl);
            } else {
                location.reload();
            }
        });

        this.eventMaxChar();
    }

    ajaxResult(response) {
        if (response.course !== undefined) {
            let name = response.course.name;
            if ($(SELECTORS.JOIN_BUTTON).data('student') == true) {
                this.#newcourse = response.course.id;
                this.#courseurl = response.course.url;
            }

            getStrings([
                {key: 'joincourseoktitle', component: 'local_npe', param: name},
                {key: 'joincourseokteachertext', component: 'local_npe', param: name},
                {key: 'joincourseokstudenttext', component: 'local_npe', param: name},
            ]).done((strings) => {
                $(SELECTORS.HIDE).attr('data-action', 'finish');
                this.getRoot().find(SELECTORS.PREVIOUS).hide();
                this.getRoot().find(SELECTORS.CONTAINER).hide();
                this.getTitle().hide();
                this.getRoot().find(SELECTORS.INPUT).removeClass('error');
                this.getRoot().find(SELECTORS.ERROR).hide();
                this.getRoot().find(SELECTORS.MODALBOX).animate({height: '340px'}, 200);
                if ($(SELECTORS.JOIN_BUTTON).data('student') == false) {
                    this.getRoot().find(SELECTORS.CONGRATSTITLE).html(strings[0]);
                    this.getRoot().find(SELECTORS.CONGRATSTEXT).html(strings[1]);
                } else {
                    this.getRoot().find(SELECTORS.CONGRATSTITLE).html(strings[0]);
                    this.getRoot().find(SELECTORS.CONGRATSTEXT).html(strings[2]);
                }
                this.getRoot().find(SELECTORS.RESULT).show();
                this.getFooter().hide();
            });
        } else {
            this.getRoot().find(SELECTORS.INPUT).addClass('error');
            this.getRoot().find(SELECTORS.ERROR).html(response.error.errordes);
            this.getRoot().find(SELECTORS.ERROR).show();
            this.getRoot().find(SELECTORS.INPUT).show();
        }
    }

    ajaxError(ex) {
        Notification.exception({message: ex});
    }

    ajaxCall() {
        const promises = Ajax.call([{
            methodname: SERVICES.JOINGROUP,
            args: {enrolmentkey: this.getRoot().find(SELECTORS.INPUT).val(), courseid: this.#courseid}
        }]);

        promises[0].done((response) => {
            this.ajaxResult(response);
        }).fail((ex) => {
            this.ajaxError(ex);
        });
    }

    eventMaxChar() {
        this.getRoot().find(SELECTORS.INPUT).on('keyup', (event) => {
            let text = $(event.currentTarget).val();
            let length = text.length;
            if (length > 0) {
                const format = /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~·]/;
                $(SELECTORS.SAVE_BUTTON).prop('disabled', format.test(text));
            } else {
                $(SELECTORS.SAVE_BUTTON).prop('disabled', true);
            }
        });
    }
}

if (!registered) {
    ModalRegistry.register(ModalJoinGroup.TYPE, ModalJoinGroup, ModalJoinGroup.TEMPLATE);
    registered = true;
}

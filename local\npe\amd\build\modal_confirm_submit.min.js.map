{"version": 3, "file": "modal_confirm_submit.min.js", "sources": ["../src/modal_confirm_submit.js"], "sourcesContent": ["import $ from 'jquery';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\nimport EventListener from 'local_npe/event_listener';\n\nconst SELECTORS = {\n    HIDE: '.close',\n    CONFIRM: '[data-action=\"submit-assignment\"]',\n    CANCEL: '[data-action=\"hide\"]',\n};\n\nconst EVENTS = {\n    CONFIRMSUBMIT: 'npe:confirm-assignment-submit'\n};\n\nlet registered = false;\n\nexport default class ModalConfirmSubmit extends Modal {\n\n    static TYPE = 'local_npe/modal_confirm_submit';\n    static TEMPLATE = 'local_npe/commons/modal_confirm_submit';\n\n    constructor(root) {\n        super(root);\n    }\n\n    setData(type) {\n        if (type === 'single') {\n            this.getBody().find('.confirmation-warning-single').removeClass('hidden');\n        } else if (type === 'group') {\n            this.getBody().find('.confirmation-warning-group').removeClass('hidden');\n        }\n    }\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.success-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.success-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CONFIRM, () => {\n            EventListener.shoutEvent(EVENTS.CONFIRMSUBMIT, {name: 'confirmassignmentsubmit'});\n        });\n    }\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalConfirmSubmit.TYPE, ModalConfirmSubmit, ModalConfirmSubmit.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_defineProperty", "r", "t", "i", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_j<PERSON>y", "_custom_interaction_events", "_modal", "_modal_registry", "_event_listener", "SELECTORS", "HIDE", "CONFIRM", "CANCEL", "EVENTS", "registered", "ModalConfirmSubmit", "Modal", "constructor", "root", "super", "setData", "type", "this", "getBody", "find", "removeClass", "registerEventListeners", "getModal", "on", "CustomEvents", "events", "activate", "FINISH", "getRoot", "$", "remove", "addClass", "EventListener", "shoutEvent", "name", "_exports", "ModalRegistry", "register", "TYPE", "TEMPLATE"], "mappings": "0PAIqD,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,gBAAAH,EAAAI,EAAAC,GAAAD,OAAAA,EAAA,SAAAC,GAAAC,IAAAA,EAAA,SAAAD,EAAAD,GAAAC,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAL,IAAAA,EAAAK,EAAAE,OAAAC,yBAAAR,EAAA,CAAA,IAAAM,EAAAN,EAAAS,KAAAJ,EAAAD,kCAAAE,EAAA,OAAAA,EAAAI,MAAAA,IAAAA,4EAAAN,EAAAO,OAAAC,QAAAP,EAAA,CAAAQ,CAAAR,EAAAC,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAQ,CAAAV,MAAAJ,EAAAe,OAAAC,eAAAhB,EAAAI,EAAAa,CAAAA,MAAAZ,EAAAa,YAAAC,EAAAA,cAAAC,EAAAA,cAAApB,EAAAI,GAAAC,EAAAL,CAAA,iFAJrDqB,QAAAtB,uBAAAsB,SACAC,2BAAAvB,uBAAAuB,4BACAC,OAAAxB,uBAAAwB,QACAC,gBAAAzB,uBAAAyB,iBACAC,gBAAA1B,uBAAA0B,iBAEA,MAAMC,UAAY,CACdC,KAAM,SACNC,QAAS,oCACTC,OAAQ,wBAGNC,qBACa,gCAGnB,IAAIC,YAAa,EAEF,MAAMC,2BAA2BC,OAAAA,QAK5CC,WAAAA,CAAYC,MACRC,MAAMD,KACV,CAEAE,OAAAA,CAAQC,MACS,WAATA,KACAC,KAAKC,UAAUC,KAAK,gCAAgCC,YAAY,UAChD,UAATJ,MACPC,KAAKC,UAAUC,KAAK,+BAA+BC,YAAY,SAEvE,CAEAC,sBAAAA,GACIP,MAAMO,uBAAuBJ,MAE7BA,KAAKK,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUtB,UAAUuB,QAAQ,KAC/DV,KAAKW,UAAUR,YAAY,SAC3B,EAAAS,QAAAA,SAAE,QAAQT,YAAY,eACtB,EAAAS,iBAAE,kBAAkBC,UACpB,EAAAD,QAACjD,SAAC,mBAAmBwC,YAAY,QAAQW,SAAS,OAAO,IAG7Dd,KAAKK,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUtB,UAAUC,MAAM,KAC7DY,KAAKW,UAAUR,YAAY,SAC3B,EAAAS,QAAAA,SAAE,QAAQT,YAAY,eACtB,EAAAS,iBAAE,kBAAkBC,UACpB,EAAAD,QAACjD,SAAC,mBAAmBwC,YAAY,QAAQW,SAAS,OAAO,IAG7Dd,KAAKK,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUtB,UAAUE,SAAS,KAChE0B,gBAAAA,QAAcC,WAAWzB,qBAAsB,CAAC0B,KAAM,2BAA2B,GAEzF,EAMH,OALAC,SAAAvD,QAAA8B,mBAAA7B,gBAtCoB6B,mBAAkB,OAErB,kCAAgC7B,gBAF7B6B,mBAAkB,WAGjB,0CAqCjBD,aACD2B,gBAAAA,QAAcC,SAAS3B,mBAAmB4B,KAAM5B,mBAAoBA,mBAAmB6B,UACvF9B,YAAa,GAChB0B,SAAAvD,OAAA"}
define("local_npe/modal_input",["exports","jquery","core/notification","core/custom_interaction_events","core/modal","core/modal_registry","core/ajax","local_npe/event_listener","core/str","core/templates","local_npe/project_events","local_npe/project_services"],(function(_exports,_jquery,_notification,_custom_interaction_events,_modal,_modal_registry,_ajax,_event_listener,_str,_templates,_project_events,_project_services){function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _classPrivateFieldInitSpec(e,t,a){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,a)}function _defineProperty(e,r,t){return(r=function(t){var i=function(t,r){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var i=e.call(t,r||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==typeof i?i:i+""}(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function _classPrivateFieldGet(s,a){return s.get(_assertClassBrand(s,a))}function _assertClassBrand(e,t,n){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_notification=_interopRequireDefault(_notification),_custom_interaction_events=_interopRequireDefault(_custom_interaction_events),_modal=_interopRequireDefault(_modal),_modal_registry=_interopRequireDefault(_modal_registry),_ajax=_interopRequireDefault(_ajax),_event_listener=_interopRequireDefault(_event_listener),_str=_interopRequireDefault(_str),_templates=_interopRequireDefault(_templates),_project_events=_interopRequireDefault(_project_events),_project_services=_interopRequireDefault(_project_services);const SELECTORS_ACCEPT_BUTTON='[data-action="accept"]',SELECTORS_CANCEL_BUTTON='[data-action="cancel"]',SELECTORS_INPUT_COURSE_NAME='[data-modal="input-value"]',SELECTORS_MESSAGE_ALERT=".message_alert";let registered=!1;var _identifier=new WeakMap;class ModalInput extends _modal.default{constructor(root){super(root),_classPrivateFieldInitSpec(this,_identifier,void 0),this.getFooter().find(SELECTORS_ACCEPT_BUTTON).length||_notification.default.exception({message:"No accept button found"}),this.getFooter().find(SELECTORS_CANCEL_BUTTON).length||_notification.default.exception({message:"No cancel button found"})}registerEventListeners(){super.registerEventListeners(this),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_ACCEPT_BUTTON,(()=>{_event_listener.default.shoutEvent(_project_events.default.BUTTONS_MODAL_INPUT.SUBMIT,{identifier:_classPrivateFieldGet(_identifier,this),inputval:(0,_jquery.default)(SELECTORS_INPUT_COURSE_NAME).val()})})),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_CANCEL_BUTTON,(()=>{_event_listener.default.shoutEvent(_project_events.default.RELOAD.PROJECT,{identifier:_classPrivateFieldGet(_identifier,this)}),this.hide(),this.destroy()})),this.getModal().on("focus",SELECTORS_INPUT_COURSE_NAME,(()=>{(0,_jquery.default)(SELECTORS_MESSAGE_ALERT).html(""),(0,_jquery.default)(SELECTORS_ACCEPT_BUTTON).removeClass("disabled")}))}setIntro(string,lang){_str.default.get_strings([{key:"save",component:"local_npe",lang:lang},{key:"cancel",component:"local_npe",lang:lang}]).done((strs=>{this.setBody(_templates.default.render("local_npe/modal_input_body",{intro:string})),this.setFooter(_templates.default.render("local_npe/modal_input_footer",{save:strs[0],cancel:strs[1]}))}))}setId(string){var s,a,r;a=this,r=string,(s=_identifier).set(_assertClassBrand(s,a),r)}showError(dataError){_templates.default.render("local_npe/modal_input_error_message",dataError).done((html=>{(0,_jquery.default)('[data-region="modal_input_error_message"]').replaceWith(html),(0,_jquery.default)(SELECTORS_ACCEPT_BUTTON).addClass("disabled")}))}setSucces(courseid,coursename,namecreator,picture){switch(_classPrivateFieldGet(_identifier,this)){case 0:var arr={name:coursename,namecreator:namecreator,picture:picture};this.setBody(_templates.default.render("local_npe/modal_input_succes_join",arr)),this.setFooter(_templates.default.render("local_npe/modal_input_footer_button_continue","")),_str.default.get_string("welcome","local_npe").done((welcome=>{this.setTitle(welcome)}));break;case 1:_ajax.default.call([{methodname:_project_services.default.GET_STUDENT_KEY,args:{courseid:courseid}}],!0,!0)[0].done((response=>{var arr={name:coursename,studentcode:response.password};this.setBody(_templates.default.render("local_npe/modal_input_succes_create",arr)),this.setFooter(_templates.default.render("local_npe/modal_input_footer_button_continue","")),_str.default.get_string("welcome","local_npe").done((welcome=>{this.setTitle(welcome)}))}));break;case 2:this.hide(),this.destroy()}}}return _exports.default=ModalInput,_defineProperty(ModalInput,"TYPE","local_npe/modal_input"),_defineProperty(ModalInput,"TEMPLATE","local_npe/modal_input"),registered||(_modal_registry.default.register(ModalInput.TYPE,ModalInput,ModalInput.TEMPLATE),registered=!0),_exports.default}));

//# sourceMappingURL=modal_input.min.js.map
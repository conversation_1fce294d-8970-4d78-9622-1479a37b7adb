import $ from 'jquery';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';
import EventListener from 'local_npe/event_listener';

const SELECTORS = {
    HIDE: '.close',
    CONFIRM: '[data-action="submit-assignment"]',
    CANCEL: '[data-action="hide"]',
};

const EVENTS = {
    CONFIRMSUBMIT: 'npe:confirm-assignment-submit'
};

let registered = false;

export default class ModalConfirmRemove extends Modal {

    static TYPE = 'local_npe/modal_confirm_remove';
    static TEMPLATE = 'local_npe/commons/modal_confirm_remove';

    constructor(root) {
        super(root);
    }

    setData(type) {
        if (type === 'group') {
            this.getBody().find('.confirmation-warning-group').removeClass('hidden');
        }
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.success-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.success-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.CONFIRM, () => {
            EventListener.shoutEvent(EVENTS.CONFIRMSUBMIT, {name: 'confirmassignmentsubmit'});
        });
    }
}

if (!registered) {
    ModalRegistry.register(ModalConfirmRemove.TYPE, ModalConfirmRemove, ModalConfirmRemove.TEMPLATE);
    registered = true;
}

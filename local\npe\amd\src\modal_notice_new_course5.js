import $ from 'jquery';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';

const SELECTORS = {
    FINISH: '[data-action="finish"]'
};

let registered = false;

export default class ModalNoticeNewCourse5 extends Modal {

    static TYPE = 'local_npe/modal_notice_new_course_5';
    static TEMPLATE = 'local_npe/noticenewcourse/noticenewcourse_modal_infobartooltip';

    constructor(root) {
        super(root);
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.notice-new-course-info-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
        });
    }
}

if (!registered) {
    ModalRegistry.register(ModalNoticeNewCourse5.TYPE, ModalNoticeNewCourse5, ModalNoticeNewCourse5.TEMPLATE);
    registered = true;
}

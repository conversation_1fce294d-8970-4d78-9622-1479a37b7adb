{"name": "moodle/moodle", "license": "GPL-3.0-or-later", "description": "Nuevo Proyecto Editorial", "type": "project", "require-dev": {"roave/security-advisories": "dev-latest", "symfony/var-dumper": "^5.2", "phpunit/phpunit": "9.5.*", "mikey179/vfsstream": "1.6.*", "behat/mink": "^1.10.0", "friends-of-behat/mink-extension": "^2.7.2", "behat/mink-goutte-driver": "^2.0", "symfony/process": "^4.4 || ^5.0 || ^6.0", "behat/behat": "3.13.*", "oleg-andreyev/mink-phpwebdriver": "1.2.*", "symfony/yaml": "*"}, "autoload-dev": {"psr-0": {"Moodle\\BehatExtension": "lib/behat/extension/"}}, "minimum-stability": "dev", "prefer-stable": true, "require": {"php": ">=8.1", "ext-iconv": "*", "ext-mbstring": "*", "ext-curl": "*", "ext-openssl": "*", "ext-ctype": "*", "ext-zip": "*", "ext-zlib": "*", "ext-gd": "*", "ext-simplexml": "*", "ext-spl": "*", "ext-pcre": "*", "ext-dom": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-intl": "*", "ext-json": "*", "ext-hash": "*", "ext-fileinfo": "*", "php-di/php-di": "^6.3"}, "suggest": {"ext-mysqli": "Needed when Moodle uses MySQL or MariaDB database.", "ext-pgsql": "Needed when Mo<PERSON><PERSON> uses PostgreSQL database.", "ext-sqlsrv": "Needed when <PERSON><PERSON><PERSON> uses MS SQL Server database.", "ext-oci8": "Needed when Mo<PERSON><PERSON> uses Oracle database.", "ext-tokenizer": "Enabling Tokenizer PHP extension is recommended, it improves Moodle Networking functionality.", "ext-soap": "Enabling SOAP PHP extension is useful for web services and some plugins.", "ext-sodium": "Enabling Sodium PHP extension is recommended, it is used by Moodle encryption API.", "ext-exif": "Enabling Exif PHP extension is recommended, it is used by Moodle to parse image meta data."}, "autoload": {"psr-4": {"local_npe\\customscripts\\": "local/npe/customscripts", "local_npe\\db\\": "local/npe/db"}}, "config": {"platform": {"php": "8.1"}}}
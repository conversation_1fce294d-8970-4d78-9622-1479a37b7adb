define("local_npe/modal_confirm_submit",["exports","jquery","core/custom_interaction_events","core/modal","core/modal_registry","local_npe/event_listener"],(function(_exports,_jquery,_custom_interaction_events,_modal,_modal_registry,_event_listener){function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _defineProperty(e,r,t){return(r=function(t){var i=function(t,r){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var i=e.call(t,r||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==typeof i?i:i+""}(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_custom_interaction_events=_interopRequireDefault(_custom_interaction_events),_modal=_interopRequireDefault(_modal),_modal_registry=_interopRequireDefault(_modal_registry),_event_listener=_interopRequireDefault(_event_listener);const SELECTORS={HIDE:".close",CONFIRM:'[data-action="submit-assignment"]',CANCEL:'[data-action="hide"]'},EVENTS_CONFIRMSUBMIT="npe:confirm-assignment-submit";let registered=!1;class ModalConfirmSubmit extends _modal.default{constructor(root){super(root)}setData(type){"single"===type?this.getBody().find(".confirmation-warning-single").removeClass("hidden"):"group"===type&&this.getBody().find(".confirmation-warning-group").removeClass("hidden")}registerEventListeners(){super.registerEventListeners(this),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS.FINISH,(()=>{this.getRoot().removeClass("show"),(0,_jquery.default)("body").removeClass("modal-open"),(0,_jquery.default)(".success-modal").remove(),(0,_jquery.default)(".modal-backdrop").removeClass("show").addClass("hide")})),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS.HIDE,(()=>{this.getRoot().removeClass("show"),(0,_jquery.default)("body").removeClass("modal-open"),(0,_jquery.default)(".success-modal").remove(),(0,_jquery.default)(".modal-backdrop").removeClass("show").addClass("hide")})),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS.CONFIRM,(()=>{_event_listener.default.shoutEvent(EVENTS_CONFIRMSUBMIT,{name:"confirmassignmentsubmit"})}))}}return _exports.default=ModalConfirmSubmit,_defineProperty(ModalConfirmSubmit,"TYPE","local_npe/modal_confirm_submit"),_defineProperty(ModalConfirmSubmit,"TEMPLATE","local_npe/commons/modal_confirm_submit"),registered||(_modal_registry.default.register(ModalConfirmSubmit.TYPE,ModalConfirmSubmit,ModalConfirmSubmit.TEMPLATE),registered=!0),_exports.default}));

//# sourceMappingURL=modal_confirm_submit.min.js.map
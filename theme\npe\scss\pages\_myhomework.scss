#page-local-npe-courses-homework {
    .myhomework {
        padding-right: 0;

        .myhomework-topbar {
            width: 100%;
            height: 40px;
            line-height: 40px;
            vertical-align: middle;
            background-color: $npe_dark-grey;
            color: $npe-white;
            padding-left: 40px;
            box-shadow: 0 3px 5px #0000001a;
            border-right: 1px solid $npe_dark-grey;
            z-index: 10;

            &.ini {
                margin: 20px 0 0 0;
                font-family: $ossemibold;
                font-size: 16px;
                color: $npe-white;
                padding-left: 30px;
                background-image: url([[pix:theme|arrow-white]]);
                background-repeat: no-repeat;
                background-size: 17px 17px;
                background-position-x: 15px;
                background-position-y: 12px;
                position: relative;

                .first {
                    margin-left: 10px;
                    padding-right: 20px;
                    border-right: 1px solid white;
                }
            }
        }

        .myhomework-sidebar {
            background-color: $npe_dark-grey;
            position: relative;
            min-height: 600px;

            .list-group {
                margin-top: 20px;
                width: 232px;

                .list-group-item {
                    &.list-group-item-action {
                        width: auto;
                        height: 40px;
                        font-family: $oslight;
                        color: $npe-white;
                        background-color: transparent;
                        font-size: 16px;
                        vertical-align: middle;
                        margin-left: 20px;
                        padding: 1px 0 0 40px;
                        margin-top: 10px;
                        margin-bottom: 10px;
                        border-bottom: 0;
                        opacity: .6;

                        &.active {
                            font-family: $ossemibold;
                            opacity: 1;
                        }

                        &.participants {
                            background-image: url([[pix:theme|participants]]);
                            background-repeat: no-repeat;
                            background-size: 24px auto;
                            background-position-x: 0;
                            background-position-y: 0;
                        }

                        &.teams {
                            background-image: url([[pix:theme|teams]]);
                            background-repeat: no-repeat;
                            background-size: 24px auto;
                            background-position-x: 0;
                            background-position-y: 0;
                        }

                        &.assigns {
                            background-image: url([[pix:theme|assign]]);
                            background-repeat: no-repeat;
                            background-size: 24px auto;
                            background-position-x: 0;
                            background-position-y: 0;
                        }

                        &.stats {
                            background-image: url([[pix:theme|activity]]);
                            background-repeat: no-repeat;
                            background-size: 24px auto;
                            background-position-x: 0;
                            background-position-y: 0;
                        }
                    }

                    &.actions {
                        font-family: $ossemibold;
                        font-size: 14px;
                        margin-right: 40px;
                        padding-left: 0;
                        text-decoration: underline;
                        opacity: 1;
                    }
                }

                .separator {
                    border-top: 1px solid $npe-grey-alt;
                    width: 192px;
                    opacity: .1;
                }
            }

            .share {
                position: absolute;
                bottom: 0;
            }
        }

        .myhomework-content {
            width: 100%;
        }

        .myhomework-participants {
            background-color: $npe_white;

            .title {
                position: relative;
                font-family: $ossemibold;
                font-size: 18px;
                color: $npe_black;
                text-transform: uppercase;
                margin-bottom: 10px;
                padding-left: 60px;
                margin-left: 40px;
                margin-right: 10px;

                &:before {
                    position: absolute;
                    left: 0;
                    content: " ";
                    width: 40px;
                    height: 40px;
                    background-color: $npe-dark-grey;
                    background-image: url([[pix:theme|participants]]);
                    background-repeat: no-repeat;
                    background-size: 25px auto;
                    background-position: 8px 7px;
                    border-radius: 10px;
                }
            }

            .subtitle-one {
                display: block;
                font-family: $oslight;
                font-size: 18px;
                color: $npe-dark-grey;
                margin-left: 100px;
            }

            .subtitle-two {
                display: block;
                font-family: $oslight;
                font-size: 18px;
                color: $npe-dark-grey;
                margin-left: 100px;
            }

            .combos {
                display: flex;
                padding: 0 40px 30px 40px;
                align-content: center;

                #unitsdropdown {
                    display: unset;
                    margin-top: unset;
                }

                .myhomework-search {
                    display: inline-block;
                    width: 570px;
                    height: 40px;
                    top: 20px;
                    left: 40px;

                    input::placeholder {
                        font-style: italic;
                    }

                    input {
                        font-family: $osregular;
                        font-size: 14px;
                        font-style: normal;
                        color: $npe-dark-grey;
                        height: 40px;
                        border: 2px solid $npe-dark-grey;
                        border-radius: 25px;
                        background-image: url([[pix:theme|search]]);
                        background-repeat: no-repeat;
                        background-size: 25px 25px;
                        background-position-x: 5px;
                        background-position-y: 6px;
                        padding-left: 50px;

                        &:focus {
                            box-shadow: 0 5px 10px $npe-black-33;
                            border: none;
                        }

                        &:focus::placeholder {
                            color: transparent;
                        }
                    }

                    span {
                        position: relative;
                        display: block;
                        left: 540px;
                        bottom: 30px;
                        width: 20px;
                        height: 20px;
                        border-radius: 50px;
                        background: $npe_grey-mid url([[pix:theme|close]]) no-repeat;
                        background-size: 10px 10px;
                        background-position-x: 4.5px;
                        background-position-y: 5px;
                        cursor: pointer;

                        &[data-opaque="true"] {
                            opacity: 0.5;
                        }
                    }
                }

                @media only screen and (max-width: 1120px) {
                    .myhomework-search {
                        width: 500px;
                        span {
                            left: 470px;
                        }
                    }
                }
                @media only screen and (max-width: 1024px) {
                    .myhomework-search {
                        width: 470px;
                        span {
                            left: 440px;
                        }
                    }
                }
                @media only screen and (max-width: 799px) {
                    .myhomework-search {
                        width: 100%;
                        margin-bottom: 10px;
                        span {
                            left: calc(100% - 30px);
                        }
                    }
                }

                .myhomework-units {
                    display: inline-block;
                    font-family: $ossemibold;
                    font-size: 14px;
                    width: 200px;
                    height: 40px;
                    position: absolute;
                    top: 20px;
                    right: 40px;
                    border: 2px solid #666;
                    border-radius: 25px;
                    color: #000;

                    label {
                        padding: 10px;
                    }

                    .actions-chevron-down {
                        position: absolute;
                        right: 5px;
                        top: 6px;
                        width: 25px;
                        height: 25px;
                        background-image: url([[pix:theme|chevron-down]]);
                        background-repeat: no-repeat;
                        background-size: 25px 25px;
                        cursor: pointer;

                        &._up {
                            transform: rotate(180deg);
                        }
                    }
                }
            }

            @media only screen and (max-width: 1024px) {
                .combos {
                    padding: 0 20px 30px 20px;
                }
            }
        }

        .myhomework-teacher,
        .myhomework-students {
            &.no-assignments {
                text-align: center;
                padding-top: 40px;
                .description {
                    font-size: 14px;
                    font-family: $osregular;
                    color: $npe-dark-grey;
                }
            }
            .reduce {
                @media (min-width: 576px) {
                    .orders {
                        position: relative;
                        top: -60px;
                    }
                }
                @media (max-width: 1024px) {
                    .orders {
                        visibility: hidden;
                    }
                }
                @media (min-width: 1200px) {
                    width: 80%;
                    &.teacher {
                        width: 100%;
                    }
                    margin: 0 auto;
                    .orders {
                        position: relative;
                        display: inline-block;
                        width: 200px;
                        float: right;
                        top: -35px;
                    }
                    .myhomework-menu-order {
                        clear: both;
                    }
                }
            }

            font-family: $ossemibold;
            font-size: 18px;
            color: $npe_black;
            margin-top: 10px;
            padding-left: 40px;
            padding-right: 20px;
            padding-top: 20px;
            background-color: $npe-bg-color;
            min-height: 300px;
            position: relative;
            border-top: 4px solid $npe-grey-alt;
            margin-bottom: 120px;

            span {
                font-family: $osregular;
                color: $npe_dark-grey;
            }

            .type-grid {
                width: 40px;
                height: 40px;
                border-radius: 50px;
                position: absolute;
                right: 20px;
                background-color: $npe-white;

                &.tgrid {
                    background-image: url([[pix:theme|grid]]);
                    background-repeat: no-repeat;
                    background-size: 22px auto;
                    background-position-x: 7px;
                    background-position-y: 7px;
                    right: 70px;
                }

                &.tlist {
                    background-image: url([[pix:theme|list]]);
                    background-repeat: no-repeat;
                    background-size: 22px auto;
                    background-position-x: 7px;
                    background-position-y: 7px;
                }

                &.selected {
                    border: 2px solid $npe-grey-mid;
                }

                &.unselected {
                    opacity: .5;
                    cursor: pointer;
                }
            }

            @media only screen and (max-width: 1024px) {
                .type-grid {
                    display: none;
                }
            }

            .list {
                margin-top: 20px;
                padding-top: 20px;

                .min-100 {
                    min-width: 100px;
                    min-height: 60px;
                }

                .myhomework-menu-order {
                    display: flex;
                    justify-content: flex-end;
                    margin-right: 20px;

                    button {
                        outline: none;
                        border: none;
                        background: none;
                        font-family: $ossemibold;
                        font-size: 14px;

                        &:after {
                            all: unset;
                            content: "";
                            display: inline-block;
                            background-image: url([[pix:theme|chevron-down]]);
                            cursor: pointer;
                            height: 25px;
                            width: 25px;
                            position: relative;
                            left: 10px;
                            top: 10px;
                        }

                        &:active,
                        &:focus {
                            color: $npe-dark-grey;
                            font-family: $ossemibold;
                        }
                    }

                    .selector {
                        border-radius: 10px;
                        z-index: 1030;
                        background-color: $npe-bg-color;
                        color: $npe-dark-grey;
                        font-family: $ossemibold;
                        font-size: 14px;

                        .selected {
                            font-family: $ossemibold;
                        }
                    }
                }

                .filters {
                    display: inline-block;
                    position: relative;
                    top: -30px;

                    .filter {
                        font-family: $ossemibold;
                        font-size: 14px;
                        color: $npe-dark-grey;
                        border: 2px solid $npe-grey-mid;
                        margin-right: 10px;
                        padding: 5px 10px;
                        border-radius: 20px;
                        cursor: pointer;

                        span {
                            font-family: $oslight;
                        }

                        &.selected {
                            background-color: $npe-dark-grey;
                            color: $npe-white;

                            span {
                                color: $npe-white !important;
                            }
                        }
                    }
                }

                .card-item {
                    font-size: 14px;
                    &[data-match-content="false"] {
                        display: none!important;
                    }

                    .state-tag {
                        font-size: 12px;
                        font-style: italic;
                        border: 2px solid;
                        padding: 5px 10px;
                        border-radius: 5px;
                        min-width: 90px;
                        text-align: center;
                        color: #000;

                        &.pending {
                            border-color: #e88f15;
                            background-color: #e88f150d;
                        }

                        &.reopen {
                            border-color: #e88f15;
                            background-color: #e88f150d;
                        }

                        &.done {
                            border-color: #003277;
                            background-color: #0032770d;
                        }

                        &.graded {
                            border-color: #00860f;
                            background-color: #00860f0d;
                        }
                    }

                }

                .card-columns {
                    position: relative;
                    margin-top: 20px;

                    [data-region="loading-icon-container"] {
                        position: absolute;
                        left: 50%;
                        top: 50%;
                        transform: translate(-50%, -50%);
                    }

                    @media (min-width: 576px) {
                        .cards-container {
                            width: 100%;
                        }
                    }
                    @media (min-width: 960px) {
                        .cards-container {
                            width: 50%;
                        }
                    }
                    @media (min-width: 1280px) {
                        .cards-container {
                            width: 33.3%;
                        }
                    }
                    @media (min-width: 1920px) {
                        .cards-container {
                            width: 25%;
                        }
                    }

                    .cards-container,
                    .card-item {
                        display: none;
                    }

                    &.show {
                        display: flex;
                        .cards-container,
                        .card-item {
                            display: block;
                        }

                        .cards-container {
                            &.hidden {
                                display: none;
                            }
                        }

                        [data-region="loading-icon-container"] {
                            display: none;
                        }
                    }

                    .card-item {
                        position: relative;
                        font-family: $osregular;
                        font-size: 14px;
                        border-radius: 20px;
                        border: 1px solid $npe-grey-alt;
                        background: #fff;
                        margin-bottom: 30px;
                        break-inside: avoid-column;
                        height: auto;
                        transition: height 0.2s ease-in-out;
                        width: 100%;

                        @-moz-document url-prefix() {
                            display: inline-block;
                        }
                        .card-head-info {
                            margin-left: auto;
                            display: flex;
                            > * {
                                display: inline-block;
                                &:not(:first-child) {
                                    margin-left: 10px;
                                }
                            }
                        }
                        .panel-heading {
                            img {
                                position: relative;
                                top: -2px;
                                left: 4px;
                            }

                            color: #000;
                            overflow: inherit;
                            line-height: normal;
                            margin: 20px 20px 20px;
                            &.teacher {
                                margin: 20px 20px 0px;
                            }
                            position: relative;
                            height: 30px;

                            .comment {
                                height: 30px;
                                width: 30px;
                                display: inline-block;
                                right: 58px;
                                background-image: url([[pix:theme|comment]]);
                                background-repeat: no-repeat;
                                background-size: 20px auto;
                                background-position: 50%;
                                background-color: $npe_grey-alt;
                                border-radius: 5px;

                                &.right0 {
                                    right: 0;
                                }

                                &:hover {
                                    opacity: 1;
                                }
                            }

                            .grade {
                                right: 0;
                                font-family: $ossemibold;
                                font-size: 14px;
                                text-align: center;
                                padding: 5px;
                                display: inline-block;
                                width: auto;
                                min-width: 30px;
                                height: 30px;
                                border-radius: 4px;
                                border: 2px solid;

                                &.green {
                                    @include green();
                                }

                                &.green_yellow {
                                    @include green_yellow();
                                }

                                &.orange {
                                    @include orange();
                                }

                                &.red {
                                    @include red();
                                }
                            }

                            .flex {
                                display: flex;
                                justify-content: flex-end;
                                gap: 10px;
                            }
                        }

                        .panel-body {
                            &.activity-heading {
                                span:not(:last-child) {
                                    display: block;
                                    margin-bottom: 5px;
                                }

                                .type {
                                    font-style: italic;
                                }

                                .name {
                                    font-size: 16px;
                                    color: $npe-black;
                                    font-family: "Work Sans Semibold";
                                    word-break: break-word;

                                    .notavai {
                                        cursor: not-allowed;
                                        pointer-events: none;
                                    }

                                    .link {
                                        color: $npe-black;
                                    }
                                }
                            }

                            .date {
                                display: list-item;
                                list-style-type: disc;
                                list-style-position: inside;
                                margin-bottom: 10px;
                            }

                            .description {
                                font-size: 14px;
                            }

                            .separator {
                                width: 100%;
                                height: 2px;
                                border-top: 2px solid $npe-grey-alt;
                                position: absolute;
                                left: 0;
                            }

                            .user-fullname {
                                margin: 0;
                            }

                            .card-properties {
                                margin: 0;
                                padding: 0;

                                li:not(:last-child) {
                                    margin-bottom: 10px;
                                }

                                li {
                                    font-family: $osregular;
                                    padding: 0;
                                    list-style-type: none;
                                    display: flex;
                                    align-items: center;
                                }
                            }

                            .user-picture {
                                width: 20px;
                                margin-right: 10px;
                            }

                            .teams {
                                &:before {
                                    background-image: url([[pix:theme|users]]);
                                }
                            }

                            .evidence {
                                &:before {
                                    background-image: url([[pix:theme|star_blue]]);
                                }
                            }

                            .sequence {
                                &:before {
                                    background-image: url([[pix:theme|insequence]]);
                                }
                            }

                            .tit {
                                font-family: "Work Sans SemiBold";
                                margin-bottom: 10px;
                            }

                            &.extra {
                                position: relative;
                                margin-bottom: 20px;
                            }

                            .pendingstring {
                                color: $npe-red-pending;
                            }

                            .deliverdate {
                                span {
                                    font-weight: bold;
                                    color: $npe-black;
                                    display: inline-block;
                                }
                            }

                            .totalstring {
                                span {
                                    font-weight: bold;
                                    color: $npe-black;
                                }
                            }
                        }

                        .panel-footer {
                            .tit {
                                font-family: $ossemibold;
                                display: block;
                                margin-bottom: 5px;
                            }

                            img {
                                display: inline-block;
                                width: 20px;
                                height: 20px;
                                border-radius: 50px;
                                position: relative;
                                margin-right: 6px;
                            }

                            .teacher {
                                position: relative;
                            }

                            .viewmore {
                                background-image: url([[pix:theme|chevron-down]]);
                                background-repeat: no-repeat;
                                background-position: 50%;
                                border-bottom-left-radius: 20px;
                                border-bottom-right-radius: 20px;
                                margin-top: 20px;
                                cursor: pointer;
                                height: 26px;

                                &:hover {
                                    background-color: $npe-grey-alt;
                                }
                            }

                            .viewless {
                                background-image: url([[pix:theme|chevron-up]]);
                                background-repeat: no-repeat;
                                background-position: 50%;
                                border-bottom-left-radius: 20px;
                                border-bottom-right-radius: 20px;
                                margin-top: 20px;
                                cursor: pointer;
                                height: 26px;
                                background-color: $npe-black;
                            }
                        }
                    }
                }

                .card-list {
                    margin-top: 20px;

                    &.show {
                        display: block;
                    }

                    .list-panel-heading {
                        font-family: $ossemibold;
                        font-size: 12px;
                        display: flex;
                        margin-bottom: 10px;
                        padding: 0 10px 0 20px;
                    }

                    .list-panel-heading,
                    .card-item .panel-heading {
                        .block {
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            padding: 5px;
                            font-size: 14px;
                            color: #666;
                        }

                        .block-30 {
                            width: 30%;
                        }

                        .block-20 {
                            width: 20%;
                        }

                        .block-15 {
                            width: 15%;
                        }

                        .block-10 {
                            width: 10%;
                        }

                        .block-options {
                            width: 50px;
                            align-items: end;
                            padding: 0;
                        }
                    }

                    .card-item {
                        font-family: $osregular;
                        border-radius: 20px;
                        border: 1px solid $npe-grey-alt;
                        background: #fff;
                        position: relative;
                        margin-bottom: 10px;
                        min-height: 60px;
                        transition: height 0.2s ease-in-out;

                        .panel-heading {
                            display: flex;
                            align-items: center;
                            color: #000;
                            overflow: inherit;
                            line-height: normal;
                            padding: 0 10px 0 20px;
                            position: relative;
                            img {
                                position: relative;
                                left: 4px;
                                width: 22px;
                                height: 22px;
                            }
                            .block {
                                .pendingstring {
                                    font-size: 14px;
                                    display: inline-block;
                                    color: $npe-red-pending;
                                    margin-bottom: 10px;
                                }

                                .deliverdate {
                                    font-size: 14px;
                                    margin-top: 20px;
                                    display: inline-block;
                                    span {
                                        font-weight: bold;
                                        color: $npe-black;
                                    }
                                }

                                .totalstring {
                                    font-size: 14px;
                                    margin-top: 10px;
                                    margin-bottom: 0;
                                    display: inline-block;
                                    span {
                                        font-weight: bold;
                                        color: $npe-black;
                                    }
                                }

                                .type {
                                    display: flex;
                                    width: 100%;
                                    font-size: 12px;
                                    font-style: italic;
                                    align-items: center;
                                    gap: 5px;

                                    .evidence {
                                        width: 20px;
                                        height: 20px;
                                        background-image: url([[pix:theme|star_blue]]);
                                        background-size: 20px 20px;
                                        display: inline-block;
                                        margin-right: 5px;
                                    }

                                    .sequence {
                                        width: 20px;
                                        height: 20px;
                                        background-image: url([[pix:theme|insequence]]);
                                        background-repeat: no-repeat;
                                        background-size: 20px auto;
                                        display: inline-block;
                                        margin-right: 5px;
                                    }

                                    .typename {
                                        display: inline-block;
                                        position: relative;
                                    }
                                }

                                .name {
                                    display: block;
                                    width: 100%;
                                    color: $npe-black;
                                    font-family: "Work Sans Semibold";
                                    word-break: break-word;
                                    .link{
                                        color: $npe-black;
                                    }
                                }

                                .state {
                                    font-size: 14px;
                                }

                                .unit {
                                    display: block;
                                    font-size: 14px;
                                }

                                .date {
                                    font-size: 14px;

                                    &.end {
                                        margin-bottom: 20px;
                                    }
                                }

                                .viewmore {
                                    background-image: url([[pix:theme|chevron-down]]);
                                    background-color: $npe-grey-alt;
                                    background-repeat: no-repeat;
                                    background-position: 50%;
                                    border-radius: 50px;
                                    cursor: pointer;
                                    height: 30px;
                                    width: 30px;
                                    transition: transform .3s ease-in-out;

                                    &:active {
                                        transform: rotate(180deg);
                                    }
                                }

                                .viewless {
                                    background-image: url([[pix:theme|chevron-up]]);
                                    background-color: $npe-black;
                                    background-repeat: no-repeat;
                                    background-position: 50%;
                                    border-radius: 50px;
                                    cursor: pointer;
                                    height: 30px;
                                    width: 30px;
                                    transition: transform .3s ease-in-out;

                                    &:active {
                                        transform: rotate(-180deg);
                                    }
                                }

                                .grade {
                                    font-family: $ossemibold;
                                    font-size: 20px;
                                    width: 70px;
                                    height: 50px;
                                    border: 2px solid $npe-grey-alt;
                                    display: block;
                                    border-radius: 10px;
                                    text-align: center;
                                    padding-top: 10px;

                                    &.green {
                                        @include green();
                                    }

                                    &.green_yellow {
                                        @include green_yellow();
                                    }

                                    &.orange {
                                        @include orange();
                                    }

                                    &.red {
                                        @include red();
                                    }
                                }

                            }

                            .block4 {
                                display: inline-block;
                                width: 20%;

                                .pendingstring {
                                    font-size: 14px;
                                    display: inline-block;
                                    color: $npe-red-pending;
                                    margin-bottom: 10px;
                                }

                                .deliverdate {
                                    font-size: 14px;
                                    margin-top: 20px;
                                    display: inline-block;
                                    span {
                                        font-weight: bold;
                                        color: $npe-black;
                                    }
                                }

                                .totalstring {
                                    font-size: 14px;
                                    margin-top: 10px;
                                    margin-bottom: 0;
                                    display: inline-block;
                                    span {
                                        font-weight: bold;
                                        color: $npe-black;
                                    }
                                }
                            }
                            .options {
                                top: 25px!important;
                            }

                            .block5 {
                                display: inline-block;
                                width: 15%;

                                .state-tag {
                                    @include statetag();
                                    width: fit-content;
                                }

                                &.opacity-30 {
                                    opacity: .3;
                                    pointer-events: none;
                                }
                            }
                        }

                        .separator-list {
                            width: 100%;
                            height: 2px;
                            border-top: 2px solid $npe-grey-alt;
                            margin-bottom: 20px;

                        }

                        .panel-body {
                            > span {
                                margin-bottom: 10px;
                            }

                            .description {
                                font-size: 14px;
                                &:not(:last-child) {
                                    margin-bottom: 20px;
                                }
                            }

                            .teams {
                                &:before {
                                    background-image: url([[pix:theme|users]]);
                                }
                            }

                            .evidence {
                                &:before {
                                    background-image: url([[pix:theme|star_blue]]);
                                }
                            }

                            .sequence {
                                &:before {
                                    background-image: url([[pix:theme|insequence]]);
                                }
                            }

                            .user-info {
                                margin-bottom: 10px;
                                img {
                                    display: inline-block;
                                    width: 20px;
                                    height: 20px;
                                    border-radius: 50px;
                                    position: relative;
                                    left: 3px;
                                }

                                .teacher {
                                    font-size: 14px;
                                    position: relative;
                                    left: 4px;
                                }
                            }

                            .date {
                                display: list-item;
                                list-style-type: disc;
                                list-style-position: inside;
                                font-size: 14px;

                                &.end {
                                    margin-left: 5px;
                                }
                            }

                            .tit {
                                font-family: "Work Sans SemiBold";
                                margin-bottom: 10px;
                                display: block;
                            }

                            .description {
                                font-family: $osregular;
                                font-size: 14px;
                                margin-top: 5px;
                                display: block;
                            }

                            &.extra {
                                position: relative;
                                padding-left: 20px;
                                margin-bottom: 20px;
                                word-break: break-word;

                                .type {
                                    margin-top: 40px;
                                }

                                .description {
                                    display: block;
                                    margin-bottom: 20px;
                                }
                            }
                        }

                        .panel-footer {
                            img {
                                display: inline-block;
                                width: 20px;
                                height: 20px;
                                border-radius: 50px;
                                position: relative;
                                left: 20px;
                                margin-right: 6px;
                            }

                            .teacher {
                                font-size: 14px;
                                position: relative;
                                left: 15px;
                            }

                        }
                    }
                }

                .card-body {
                    //transition: all 0.4s;
                    padding-top: 0;
                    padding-bottom: 0;
                    min-height: inherit;

                    &.footer {
                        span {
                            display: -webkit-box;
                            &.deliverdate {
                                margin-bottom: 10px;
                            }
                        }
                    }

                    &.panel-group {
                        border-top: 2px solid $npe-grey-alt;
                        padding: 20px;
                    }
                }

                .panel-body {
                    &:empty {
                        display: none!important;
                    }
                    &.is-empty {
                        display: none!important;
                    }

                    .card-property {
                        display: block;
                        margin-bottom: 10px;
                        padding-left: 30px;
                        position: relative;
                        &:before {
                            position: absolute;
                            left: 0;
                            content: " ";
                            width: 20px;
                            height: 20px;
                            background-repeat: no-repeat;
                            background-size: 20px auto;
                            top: 50%;
                            transform: translateY(-50%);
                        }
                    }

                    .card-property:last-child {
                        margin-bottom: 0!important;
                    }

                }

                .collapsed .card-head span::before {
                    transform: rotate(-180deg);
                }

                .card-grid {
                    &.single-card {
                        display: inline-flex;

                        .lastaccess {
                            font-family: $osregular;
                            font-size: 12px;
                            color: $npe_dark-grey;
                            display: block;
                            text-align: center;
                        }
                    }

                    &.list-card {
                        .card_list {
                            height: 60px;
                            position: relative;
                            display: block;
                            border: 1px solid $npe-grey-alt;
                            border-radius: 20px;
                            margin-right: 20px;
                            margin-left: 10px;
                            margin-bottom: 10px;

                            img {
                                width: 40px;
                                height: 40px;
                                border-radius: 50px;
                                margin-top: 10px;
                                margin-bottom: 10px;
                                margin-left: 10px;
                                display: inline-flex;
                            }

                            h5 {
                                display: inline-flex;
                                font-family: $ossemibold;
                                font-size: 14px;
                                color: $npe_black;
                                position: absolute;
                                top: 15px;
                                left: 60px;
                            }

                            .lastaccess {
                                font-family: $osregular;
                                font-size: 12px;
                                position: absolute;
                                left: 60px;
                                top: 30px;
                                color: $npe-grey-alt;
                            }

                            .divider {
                                content: "";
                                border-left: 1px solid $npe_grey-alt;
                                position: absolute;
                                top: 0;
                                right: 60px;
                                height: 58px;
                            }
                        }
                    }
                }
            }

            .custom_checkbox {
                display: flex;
                align-items: center;
                position: relative;
                left: initial;
                top: initial;
                label {
                    margin: 0;
                    &:before {
                        margin-right: 10px;
                    }
                }
            }
        }

        .myhomework-students {
            .list {
                .card-columns {
                    column-width: 13em;
                    column-gap: 20px;
                    column-count: 4;

                    .card-item .panel-body {
                        .card-properties {
                            padding-top: 20px;
                        }
                    }

                    @media (min-width: 576px) {
                        column-count: 1;
                    }
                    @media (min-width: 960px) {
                        column-count: 2;
                    }
                    @media (min-width: 1280px) {
                        column-count: 3;
                    }
                    @media (min-width: 1920px) {
                        column-count: 4;
                    }
                }
            }
        }
        .myhomework-teacher {
            .list {
                .card-columns {
                    flex-wrap: wrap;
                    column-gap: 20px;

                    &:after {
                        content: "";
                        flex: auto;
                        margin-right: 20px;
                    }

                    .cards-container {
                        box-sizing: border-box;
                    }

                    @media (min-width: $media-extra-small) {
                        .cards-container {
                            width: 100%;
                        }
                    }
                    @media (min-width: $media-small) {
                        .cards-container {
                            width: 100%;
                        }
                    }
                    @media (min-width: $media-medium) {
                        .cards-container {
                            width: calc(50% - 20px);
                        }
                    }
                    @media (min-width: $media-large) {
                        .cards-container {
                            width: calc(33.3% - 20px);
                        }
                    }
                    @media (min-width: $media-extra-large) {
                        .cards-container {
                            width: calc(25% - 40px);
                        }
                    }
                    @media (min-width: $media-full) {
                        .cards-container {
                            width: calc(20% - 40px);
                        }
                    }
                }

                @media (min-width: $media-extra-small) {
                    .card-columns {
                        margin-right: 20px;
                        .card-item {
                            margin-bottom: 20px;
                        }
                    }
                }

                @media (min-width: $media-small) {
                    .card-columns {
                        margin-right: 0;
                    }
                }

                @media (min-width: $media-extra-large) {
                    .card-columns {
                        column-gap: 40px;
                        margin-right: -20px;
                        &:after {
                            margin-right: 40px;
                        }
                        .card-item {
                            margin-bottom: 40px;
                        }
                    }
                }
            }
        }
        .mygroup {
            background-color: $npe-dark-grey-bg;
            color: #fff;
            text-align: center;
            width: 232px;
            height: 140px;
            padding: 10px;
            font-size: 14px;
            font-family: $osregular;

            &.h100 {
                height: 100px;
            }

            .text {
                margin: 12px 0;
            }

            .code {
                font-family: $ossemibold;
                font-size: 18px;
            }

            .buttons {
                width: 220px;
                height: 50px;
                display: flex;
                justify-content: space-evenly;
                margin: 10px 0 0;

                .copy-button {
                    width: 40px;
                    height: 40px;
                    background-color: $npe-grey-alt;
                    border-radius: 5px;
                    background-image: url([[pix:theme|copy_white]]);
                    background-repeat: no-repeat;
                    background-size: 25px 25px;
                    background-position-x: 8px;
                    background-position-y: 8px;
                    cursor: pointer;
                }

                .download-button {
                    width: 40px;
                    height: 40px;
                    background-color: $npe-dark-grey;
                    border-radius: 5px;
                    background-image: url([[pix:theme|download_white]]);
                    background-repeat: no-repeat;
                    background-size: 25px 25px;
                    background-position-x: 8px;
                    background-position-y: 8px;
                    cursor: pointer;
                }

                .display-button {
                    width: 40px;
                    height: 40px;
                    background-color: $npe-dark-grey;
                    border-radius: 5px;
                    background-image: url([[pix:theme|full-screen_white]]);
                    background-repeat: no-repeat;
                    background-size: 25px 25px;
                    background-position-x: 8px;
                    background-position-y: 8px;
                    cursor: pointer;
                }
            }
        }

        .myhomework-teams-content {
            display: none;
            width: 100%;

            .myhomework-teams {
                background-color: $npe_white;
                padding-top: 20px;

                .title {
                    position: relative;
                    font-family: $ossemibold;
                    font-size: 18px;
                    color: $npe_black;
                    text-transform: uppercase;
                    margin-bottom: 10px;
                    padding-left: 60px;
                    margin-left: 40px;
                    margin-right: 10px;

                    &:before {
                        position: absolute;
                        left: 0;
                        content: " ";
                        width: 40px;
                        height: 40px;
                        background-color: $npe-dark-grey;
                        background-image: url([[pix:theme|teams]]);
                        background-repeat: no-repeat;
                        background-size: 25px auto;
                        background-position: 8px 7px;
                        border-radius: 50px;
                    }
                }

                .subtitle-one {
                    display: block;
                    font-family: $oslight;
                    font-size: 18px;
                    color: $npe-dark-grey;
                    margin-left: 100px;
                }

                .subtitle-two {
                    display: block;
                    font-family: $oslight;
                    font-size: 18px;
                    color: $npe-dark-grey;
                    margin-left: 100px;
                }

                .card {
                    width: 228px;
                    height: 186px;
                    display: inline-block;
                    -webkit-box-shadow: 0 3px 6px rgb(0 0 0 / 16%);
                    box-shadow: 0 3px 6px rgb(0 0 0 / 16%);
                    border-radius: 20px;
                    margin-right: 20px;
                    margin-bottom: 24px;

                    .icon {
                        height: 100px;
                        width: 100px;
                        border-radius: 50px;
                        margin: 10px auto;
                        display: block;

                        &.custom {
                            font-family: $ossemibold;
                            font-size: 30px;
                            content: " ";
                            background-repeat: no-repeat;
                            background-size: 25px auto;
                            background-position: 8px 7px;
                            color: $npe-white;
                            text-align: center;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            text-transform: uppercase;
                        }

                        img {
                            width: 35px;
                        }
                    }

                    h5 {
                        font-family: $ossemibold;
                        text-align: center;

                        &.general {
                            text-transform: uppercase;
                        }
                    }

                    .participants {
                        font-family: $osregular;
                        font-size: 12px;
                        color: $npe_dark-grey;
                        display: block;
                        text-align: center;
                        pointer-events: none;
                    }
                }
            }

            .myhomework-teams-lists {
                background-color: $npe-bg-color;
                padding: 20px;
                padding-left: 40px;
                margin-left: -40px;

                &.student {
                    margin-left: 0;

                    .myhomework-team {
                        display: inline-block;
                    }
                }

                .noteams {
                    display: flex;
                    flex-direction: column;
                    margin-top: 40px;
                }

                .noteamsyet {
                    font-family: $ossemibold;
                    font-size: 18px;
                    color: $npe_black;
                    display: block;
                    text-align: center;
                    margin-bottom: 20px;
                }

                .noteamsyetdesc {
                    font-family: $oslight;
                    font-size: 14px;
                    color: $npe_dark-grey;
                    display: block;
                    text-align: center;
                    width: 30%;
                    margin: 0 auto;
                }
            }

            .team-header {
                width: 96%;
                margin: 20px;
                display: flex;
                font-size: 12px;
                color: $npe-dark-grey;
                font-family: $osregular;

                .name {
                    width: 52%;
                    padding-left: 100px;
                }

                .participants {
                    width: 15%;
                }
            }

            .myhomework-team {
                .list-item {
                    font-family: $ossemibold;
                    width: 96%;
                    height: 60px;
                    border: 1px solid $npe-grey-alt;
                    border-radius: 20px;
                    margin: 20px;
                    margin-left: 40px;
                    font-size: 14px;
                    font-weight: bold;
                    display: flex;
                    align-items: center;

                    &.main-team {
                        .name {
                            text-transform: uppercase;
                            display: inline-block;
                        }

                        .participants {
                            pointer-events: none;
                        }
                    }

                    .team-name {
                        width: 50%;
                        display: flex;
                        flex-direction: column;
                        padding-left: 40px;

                        a.name {
                            color: $npe-dark-grey;
                            width: fit-content;
                            text-decoration: none;
                        }

                        .team-label {
                            float: left;
                            font-family: $ossemibold;
                            font-size: 12px;
                            text-align: left;
                            border: 2px solid $npe-green;
                            border-radius: 5px;
                            padding: 0 5px;
                            width: fit-content;
                            margin-top: 4px;
                        }
                    }

                    .participants {
                        width: 15%;
                        pointer-events: none;
                    }

                    .nactivities {
                        width: 25%;
                    }

                    .icon {
                        position: relative;
                        left: 20px;
                        content: " ";
                        width: 40px;
                        height: 40px;
                        background-repeat: no-repeat;
                        background-size: 25px auto;
                        background-position: 8px 7px;
                        border-radius: 50px;
                        color: $npe-white;
                        text-align: center;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        text-transform: uppercase;

                        img {
                            width: 30px;
                            height: 30px;
                            position: absolute;
                            left: 6px;
                            top: 5px;
                        }
                    }
                }
            }

            .myhomework-menu-order {
                display: flex;
                justify-content: flex-end;
                margin-right: 20px;

                button {
                    outline: none;
                    border: none;
                    background: none;
                    font-size: 12px;
                    font-family: $ossemibold;

                    &:active, &:focus {
                        color: $npe-dark-grey;
                        font-family: $ossemibold;
                    }
                }

                .selector {
                    border-radius: 10px;
                    z-index: 1030;
                    background-color: $npe-bg-color;
                    color: $npe-dark-grey;
                    font-family: $osregular;
                    font-size: 12px;

                    .selected {
                        font-family: $ossemibold;
                    }
                }
            }

            .myhomework-teams-list {
                .dropdown-menu.info {
                    width: 220px;
                    height: 150px;

                    .team_info_dropdown_title {
                        font-size: 14px;
                        color: $npe-dark-grey;
                        margin-bottom: 20px;
                        font-family: $oslight;
                    }

                    .team_info_dropdown_desc {
                        font-size: 14px;
                        color: $npe-dark-grey;
                        margin-bottom: 20px;
                        font-family: $oslight;
                    }
                }

                .buttons {
                    justify-content: flex-end;
                    display: flex;

                    .info_drop {
                        position: relative;
                        margin: 0 auto;
                        border-left: 2px solid $npe-grey-alt;
                        padding: 20px;
                        width: 60px;
                        height: 60px;

                        .info_drop_button {
                            height: 20px;
                            width: 20px;
                        }
                    }

                    .dropdown {
                        width: 60px;
                        height: 60px;
                        border-left: 2px solid $npe-grey-alt;
                        margin: 0 auto;
                        position: relative;
                    }
                }
            }
        }

    }

    .tooltip {
        position: relative;
        left: -100px !important;
        top: 5px !important;
    }
}
define("local_npe/modal_linking_ccaa_marsupial",["exports","jquery","core/custom_interaction_events","core/modal","core/modal_registry","core/ajax","local_npe/modal_linking_success_api","local_npe/modal_linking_fail_api","core/modal_factory"],(function(_exports,_jquery,_custom_interaction_events,_modal,_modal_registry,_ajax,_modal_linking_success_api,_modal_linking_fail_api,_modal_factory){function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _classPrivateFieldInitSpec(e,t,a){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,a)}function _defineProperty(e,r,t){return(r=function(t){var i=function(t,r){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var i=e.call(t,r||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==typeof i?i:i+""}(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function _classPrivateFieldSet(s,a,r){return s.set(_assertClassBrand(s,a),r),r}function _assertClassBrand(e,t,n){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_custom_interaction_events=_interopRequireDefault(_custom_interaction_events),_modal=_interopRequireDefault(_modal),_modal_registry=_interopRequireDefault(_modal_registry),_ajax=_interopRequireDefault(_ajax),_modal_linking_success_api=_interopRequireDefault(_modal_linking_success_api),_modal_linking_fail_api=_interopRequireDefault(_modal_linking_fail_api),_modal_factory=_interopRequireDefault(_modal_factory);const SELECTORS_MODAL=".linking-ccaa-modal",SELECTORS_MODALBACKDROP=".modal-backdrop",SELECTORS_CCAA_TITLE="#title_ccaa",SELECTORS_SELECT_CCAA_TEXT="#linkccaa_text",SELECTORS_SELECT_CCAA="#linkccaa",SELECTORS_CHOOSE_CCAA_DROPDOWN="#linkccaa_dropdown",SELECTORS_CHOOSE_CCAA_DROPDOWN_CONTENT="#linkccaa_dropdown_content",SELECTORS_CHOOSE_CCAA_OPTION=".dropdown_ccaa_item",SELECTORS_CHOOSE_CCAA_ICO="#dropdown_ico",SELECTORS_CONFIRM='[data-action="confirm"]',SELECTORS_CCAAQUESTION="#caaquestion",SELECTORS_CCAAINFO="#ccaainfo",SERVICES_LINKGROUPCCAA="local_npe_link_group_ccaa";let registered=!1;var _textccaainfo=new WeakMap;class ModalLinkCCAAMarsupial extends _modal.default{constructor(root){super(root),_classPrivateFieldInitSpec(this,_textccaainfo,!0)}setData(ccaaavailable){if(Object.keys(ccaaavailable).length>0){let first=!0;for(const ccaaKey in ccaaavailable){if(first)first=!1,this.getRoot().find(SELECTORS_SELECT_CCAA_TEXT).val(ccaaavailable[ccaaKey].name),this.getRoot().find(SELECTORS_SELECT_CCAA).val(ccaaavailable[ccaaKey].id);else{var sep_opt=document.createElement("div");sep_opt.classList.add("separator"),this.getRoot().find(SELECTORS_CHOOSE_CCAA_DROPDOWN_CONTENT).append(sep_opt)}var newOpt=document.createElement("div"),text=document.createTextNode(ccaaavailable[ccaaKey].name);newOpt.appendChild(text),newOpt.setAttribute("data-value",ccaaavailable[ccaaKey].id),newOpt.classList.add("dropdown_ccaa_item"),this.getRoot().find(SELECTORS_CHOOSE_CCAA_DROPDOWN_CONTENT).append(newOpt)}this.getRoot().find(SELECTORS_SELECT_CCAA_TEXT).removeClass("d-none"),this.getRoot().find(SELECTORS_CCAA_TITLE).removeClass("d-none"),this.getRoot().find(SELECTORS_CHOOSE_CCAA_ICO).removeClass("d-none")}}registerEventListeners(){super.registerEventListeners(this),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_SELECT_CCAA_TEXT,(()=>{(0,_jquery.default)(SELECTORS_CHOOSE_CCAA_DROPDOWN).hasClass("d-none")?(0,_jquery.default)(SELECTORS_CHOOSE_CCAA_ICO).css({transform:"rotate(270deg) translateX(7px)"}):(0,_jquery.default)(SELECTORS_CHOOSE_CCAA_ICO).css({transform:"rotate(90deg)"}),(0,_jquery.default)(SELECTORS_CHOOSE_CCAA_DROPDOWN).toggleClass("d-none")})),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_CHOOSE_CCAA_OPTION,(e=>{this.getRoot().find(SELECTORS_SELECT_CCAA_TEXT).val(e.target.innerHTML),(0,_jquery.default)(SELECTORS_CHOOSE_CCAA_OPTION).removeClass("selected"),e.target.classList.add("selected"),this.getRoot().find(SELECTORS_SELECT_CCAA).val(e.target.getAttribute("data-value")),(0,_jquery.default)(SELECTORS_CHOOSE_CCAA_DROPDOWN).addClass("d-none"),(0,_jquery.default)(SELECTORS_CHOOSE_CCAA_ICO).css({transform:"rotate(90deg)"})})),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_CONFIRM,(()=>{this.getRoot().removeClass("show"),(0,_jquery.default)("body").removeClass("modal-open"),(0,_jquery.default)(SELECTORS_MODAL).removeClass("show").addClass("hide"),(0,_jquery.default)(SELECTORS_MODALBACKDROP).removeClass("show").addClass("hide");let searchParams=new URLSearchParams(window.location.search),courseid=searchParams.get("id")?searchParams.get("id"):null,ccaaid=this.getRoot().find(SELECTORS_SELECT_CCAA).val()?this.getRoot().find(SELECTORS_SELECT_CCAA).val():null;this.ajaxCall(courseid,ccaaid)})),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_CCAAQUESTION,(()=>{this.showtext()})),(0,_jquery.default)(window).on("keydown",(event=>{"Escape"===event.key&&((0,_jquery.default)(SELECTORS_MODALBACKDROP).removeClass("hide").addClass("show").css("z-index","1051"),(0,_jquery.default)(SELECTORS_MODAL).css("z-index","1052"))}))}showtext(){var s,a;a=this,(s=_textccaainfo).get(_assertClassBrand(s,a))?((0,_jquery.default)(SELECTORS_CCAAINFO).show(),_classPrivateFieldSet(_textccaainfo,this,!1)):((0,_jquery.default)(SELECTORS_CCAAINFO).hide(),_classPrivateFieldSet(_textccaainfo,this,!0))}ajaxCall(courseid,ccaaid){_ajax.default.call([{methodname:SERVICES_LINKGROUPCCAA,args:{courseid:courseid,ccaaid:ccaaid}}])[0].done((response=>{this.ajaxResult(response)})).fail((ex=>{this.ajaxError(ex)}))}ajaxResult(){_modal_factory.default.create({type:_modal_linking_success_api.default.TYPE}).done((modal=>{modal.show()}))}ajaxError(ex){_modal_factory.default.create({type:_modal_linking_fail_api.default.TYPE}).done((modal=>{modal.show()})),window.console.log(ex)}}return _exports.default=ModalLinkCCAAMarsupial,_defineProperty(ModalLinkCCAAMarsupial,"TYPE","local_npe/modal_link_ccaa_marsupial"),_defineProperty(ModalLinkCCAAMarsupial,"TEMPLATE","local_npe/courses/modal_linking_ccaa_marsupial"),registered||(_modal_registry.default.register(ModalLinkCCAAMarsupial.TYPE,ModalLinkCCAAMarsupial,ModalLinkCCAAMarsupial.TEMPLATE),registered=!0),_exports.default}));

//# sourceMappingURL=modal_linking_ccaa_marsupial.min.js.map
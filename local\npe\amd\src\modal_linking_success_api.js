import $ from 'jquery';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';

const SELECTORS = {
    HIDE: '.close',
    CONFIRM: '[data-action="confirm"]',
};

let registered = false;

export default class ModalLinkingCCAASuccessApi extends Modal {

    static TYPE = 'local_npe/modal_linking_ccaa_success_api';
    static TEMPLATE = 'local_npe/courses/modal_linking_ccaa_success';

    constructor(root) {
        super(root);
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.success-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
            window.location.reload();
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.CONFIRM, () => {
            window.location.reload();
        });
    }
}

if (!registered) {
    ModalRegistry.register(ModalLinkingCCAASuccessApi.TYPE, ModalLinkingCCAASuccessApi, ModalLinkingCCAASuccessApi.TEMPLATE);
    registered = true;
}

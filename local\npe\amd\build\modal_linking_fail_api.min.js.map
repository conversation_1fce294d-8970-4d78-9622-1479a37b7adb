{"version": 3, "file": "modal_linking_fail_api.min.js", "sources": ["../src/modal_linking_fail_api.js"], "sourcesContent": ["import $ from 'jquery';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\n\nconst SELECTORS = {\n    FINISH: '[data-action=\"cancel\"]',\n    EXIT: '[data-action=\"confirm\"]',\n};\n\nlet registered = false;\n\nexport default class ModalLinkingCCAAFailApi extends Modal {\n\n    static TYPE = 'local_npe/modal_linking_ccaa_fail_api';\n    static TEMPLATE = 'local_npe/courses/modal_linking_ccaa_fail';\n\n    constructor(root) {\n        super(root);\n    }\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.success-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n            window.location.reload();\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.EXIT, () => {\n            window.location.reload();\n        });\n    }\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalLinkingCCAAFailApi.TYPE, ModalLinkingCCAAFailApi, ModalLinkingCCAAFailApi.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_defineProperty", "r", "t", "i", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_j<PERSON>y", "_custom_interaction_events", "_modal", "_modal_registry", "SELECTORS", "registered", "ModalLinkingCCAAFailApi", "Modal", "constructor", "root", "super", "registerEventListeners", "this", "getModal", "on", "CustomEvents", "events", "activate", "getRoot", "removeClass", "$", "remove", "addClass", "window", "location", "reload", "_exports", "ModalRegistry", "register", "TYPE", "TEMPLATE"], "mappings": "iNAGgD,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,gBAAAH,EAAAI,EAAAC,GAAAD,OAAAA,EAAA,SAAAC,GAAAC,IAAAA,EAAA,SAAAD,EAAAD,GAAAC,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAL,IAAAA,EAAAK,EAAAE,OAAAC,yBAAAR,EAAA,CAAA,IAAAM,EAAAN,EAAAS,KAAAJ,EAAAD,kCAAAE,EAAA,OAAAA,EAAAI,MAAAA,IAAAA,4EAAAN,EAAAO,OAAAC,QAAAP,EAAA,CAAAQ,CAAAR,EAAAC,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAQ,CAAAV,MAAAJ,EAAAe,OAAAC,eAAAhB,EAAAI,EAAAa,CAAAA,MAAAZ,EAAAa,YAAAC,EAAAA,cAAAC,EAAAA,cAAApB,EAAAI,GAAAC,EAAAL,CAAA,iFAHhDqB,QAAAtB,uBAAAsB,SACAC,2BAAAvB,uBAAAuB,4BACAC,OAAAxB,uBAAAwB,QACAC,gBAAAzB,uBAAAyB,iBAEA,MAAMC,iBACM,yBADNA,eAEI,0BAGV,IAAIC,YAAa,EAEF,MAAMC,gCAAgCC,OAAAA,QAKjDC,WAAAA,CAAYC,MACRC,MAAMD,KACV,CAEAE,sBAAAA,GACID,MAAMC,uBAAuBC,MAE7BA,KAAKC,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUb,kBAAkB,KAC/DQ,KAAKM,UAAUC,YAAY,SAC3B,EAAAC,QAAAA,SAAE,QAAQD,YAAY,eACtB,EAAAC,iBAAE,kBAAkBC,UACpB,EAAAD,QAACvC,SAAC,mBAAmBsC,YAAY,QAAQG,SAAS,QAClDC,OAAOC,SAASC,QAAQ,IAG5Bb,KAAKC,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUb,gBAAgB,KAC7DmB,OAAOC,SAASC,QAAQ,GAEhC,EAMH,OALAC,SAAA7C,QAAAyB,wBAAAxB,gBAxBoBwB,wBAAuB,OAE1B,yCAAuCxB,gBAFpCwB,wBAAuB,WAGtB,6CAuBjBD,aACDsB,gBAAAA,QAAcC,SAAStB,wBAAwBuB,KAAMvB,wBAAyBA,wBAAwBwB,UACtGzB,YAAa,GAChBqB,SAAA7C,OAAA"}
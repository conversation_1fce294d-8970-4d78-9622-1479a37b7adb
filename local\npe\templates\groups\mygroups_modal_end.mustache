<div class="modal fade show group-create-modal" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">
                    {{# str}} creategroup, local_npe {{/ str }}
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="level">{{ level }}. {{ name }}</div>
                <div class="modal_text">{{# str}} choosename, local_npe {{/ str }}</div>
                <input id="groupname" type="email" class="form-control group-input" placeholder="" maxlength="30" aria-label="nombre">
                <div class="counter">
                    <span>{{# str}} maxcharallow, local_npe {{/ str }}</span>
                    <span class="numbers"><span id="chars">0</span>/30</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary">Crear</button>
            </div>
        </div>
    </div>
</div>
{{#js}}
    require(['jquery'], function($) {
        $("#groupname").on("input", function() {
            $("#chars").text(this.value.length);
        });
    });
{{/js}}

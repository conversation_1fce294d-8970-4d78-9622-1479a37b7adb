{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template nperepository_teacher/view_page

    Example context (json):
    {
        "isempty": false,
        "hasingroupresources": true,
        "ingroupresources": [{"id":1, "type":1, "title":"presentacion", "count":1}],
        "hasinotherresources": true,
        "inotherresources": [{"id":2, "type":1, "title":"otra presentacion", "count":1}],
        "groupname": "Producto Modelo Revuela 5º Primaria Grupo de profe",
        "assignurl": "https://npe.test/local/npe/repository/teacher/assign.php?id=2",
    }

    Data attributes required for JS:
    * data-search
    * data-toggle="dropdown"
    * data-column="title"
    * data-toggle="delete"
    * data-region="replaceextrainfo"
    * data-toggle="extrainfo"
    * data-id="2"
    * data-region="extrainfo"
}}
<div class="npe-teacher-resources background-basic">
    <header class="npe-header-teacher-resources">
        <div>
            {{> local_npe/commons/breadcrumbs }}
            {{< local_npe/commons/info_help }}
                {{$ headerhelp }}
                    {{# str }} headerhelpview, nperepository_teacher {{/ str }}
                {{/ headerhelp }}
                {{$ buttonhelppanel }}
                    {{# hashelppanel }}
                        {{# isteacher }}
                            {{# issmauser }}
                                <a id="linkhelppanel" class="btn npe-button-primary"
                                   href="{{helppanellink}}" target="_blank">{{helppanellinklabel}}</a>
                            {{/issmauser}}
                        {{/isteacher}}
                    {{/hashelppanel}}
                {{/buttonhelppanel}}
                {{$ texthelp }}
                    {{# str }} texthelpview, nperepository_teacher {{/ str }}
                {{/ texthelp }}
            {{/ local_npe/commons/info_help }}
            <div id="npe-helpdock"></div>
        </div>
        <div class="npe-margin-auto-center  npe-title-subtitle-container">
            <h1 class="npe-title text-center">{{#str}} viewpageheading, local_npe {{/str}}</h1>
            <h3 class="npe-subtitle text-center">{{#str}} viewpagesubheading, nperepository_teacher {{/str}}</h3>
        </div>
    </header>

    <div class="npe-page-form-container npe-margin-auto-center npe-search-filter-add-bar">
        <div class="npe-search-filter">
            <div class="npe-search">
                <input data-search type="search" placeholder="{{#str}} what, local_npe {{/str}}" aria-label="Search">
                <span class="clean disabled"></span>
            </div>

            <div class="reposotory-filter {{# isempty }}disabled{{/ isempty }}" data-toggle="modal" data-target="#filterButton">
                {{#str}} filter, local_npe {{/str}}
            </div>
            <!-- The Modal -->
            <div class="modal" id="filterButton" data-backdrop="false" data-filter>
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" aria-label="Close" data-dismiss="modal"></button>
                        </div>
                        <!-- Modal body -->
                        <div class="modal-body">
                            {{> nperepository_teacher/repository_filters }}
                        </div>
                        <!-- Modal footer -->
                        <div class="modal-footer">
                            <button type="button" class="clean-filters" data-dismiss="filter">{{# str }}quit-filters, local_npe{{/ str }}</button>
                            <button type="button" class="count-filters close" data-dismiss="modal">
                                {{# str }}showfilterresults, local_npe{{/ str }}
                                <span id="filter-counter-repo"></span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="assignments-filter-opacity-layer hidden"></div>

        </div>
        <a href="{{ newurl }}" type="button" class="btn btn-npe-create">
            <span class="npe-mas">+</span>
            {{#str}}viewpagebuttonnew, nperepository_teacher {{/str}}
        </a>
    </div>
    <div class="background-border-ab repository-content">
        <div class="npe-margin-auto-center npe-page-form-container" data-region="resources">
            {{> nperepository_teacher/resources }}
        </div>
    </div>
</div>


{{#js}}
    require(["nperepository_teacher/view_page", "nperepository_teacher/prepare_ui_lite"], function (view_page, prepareuiLite) {
    view_page.init();
    prepareuiLite.init();
    });
{{/js}}

<?php

namespace local_npe\auth;

use local_npe\app;
use local_npe\course;
use local_npe\exception\course_exception;
use local_npe\exception\login_exception;
use local_npe\exception\user_exception;
use local_npe\external\user_license;
use local_npe\npe_enrolment_manager;
use local_npe\product;
use \local_npe\general_team_manager;
use local_npe\traits\access_token;
use local_npe\traits\check_license;
use local_npe\traits\complete_login;
use Throwable;
use \local_npe\base\user\iuser;
use local_npe\core\user_core;
use \local_npe\educamos\manage_users;
use local_npe\external\lib\wsci;

defined('MOODLE_INTERNAL') || die();

class external_auth {
    use access_token;
    use check_license;
    use complete_login;

    /**
     * @param string $idnumber
     * @param string $codproduct
     * @param string|null $params
     * @return array
     */
    public function process_request(string $idnumber, string $codproduct, ?string $params) {
        $response = [];
        try {
            $this->process($idnumber, $codproduct, $params);
            $response['TOKEN'] = $this->get_login_token();
            $response['GUID'] = $idnumber;
        } catch (Throwable $e) {
            $exception = get_exception_info($e);
            unset($exception->a);
            $exception->backtrace = format_backtrace($exception->backtrace, true);
            if (!debugging('', DEBUG_DEVELOPER)) {
                unset($exception->debuginfo, $exception->backtrace);
            }
            $response['error'] = true;
            $response['exception'] = $exception;
        }

        return $response;
    }

    /**
     * @param string $idnumber
     * @param string $rawcodproduct
     * @param string|null $params
     * @return void
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     * @throws course_exception
     * @throws user_exception
     */
    private function process(string $idnumber, string $rawcodproduct, ?string $params) {
        // Crear/Actualiza el usuario.
        $user = $this->check_user($idnumber);

        // Eliminar las letras P,A,B.
        $cleancodproduct = product::clean_codproduct($rawcodproduct);

        // Nombre del grupo.
        $groupname = $this->get_groupname($params);

        // Crear producto y curso si no existe.
        $courseid = $this->check_product($user, $cleancodproduct, $groupname);

        // Generar el token que se enviará en la url de acceso.
        // Se pasa el $rawcodproduct en lugar del $codproduct para poder comprobar licencias en el login.
        $this->generate_login_token($user, $rawcodproduct, '#', $courseid);
    }

    /**
     * Nombre del grupo external.
     *
     * @param string|null $params
     * @return string
     */
    private function get_groupname(?string $params) {
        if($params === null) {
            return null;
        }

        $externalpar = base64_decode($params);
        $exd = json_decode($externalpar, false);
        $groupname = null;
        if ('' !== $exd->IdCurso && !empty($exd->IdCurso) && (int) $exd->IdCurso > 1) {
            $groupname = $exd->IdCurso;
            if (!empty($exd->NombreCurso)) {
                $groupname .= '_' . $exd->NombreCurso;
            }
        }
        return $groupname;
    }

    /**
     * @param $idnumber
     * @return iuser
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\user_exception
     * @throws \moodle_exception
     */
    private function check_user($idnumber) {
        $user = app::get_userfactory()->get_user_by_idnumber($idnumber);
        if ($user === null) {
            $user = app::get_userfactory()->create_user_ci($idnumber);
            /** @var wsci $wsci */
            $wsci = app::get_instance()->get(wsci::class);
            $userdata = $wsci->get_user($idnumber);
            $jsondecoded = $userdata->status == 200 ? json_decode($userdata->results, false) : null;

            $userstd = new \stdClass();
            $userstd->id = $user->get_user_data()->get_id();

            if (array_key_exists('SistemasExternos', json_decode($userdata->results, true))) {
                foreach ($jsondecoded->SistemasExternos as $external) {
                    $system = $external->Sistema ?? null;
                    $identificador = $external->Identificador ?? null;
                    if (($system === 'LTI' || $system === 'SEN') && $identificador) {
                        $userstd->profile_field_sistemaexterno = $system;
                        break;
                    }
                }        
            }

            app::get_userfactory()->save_extra_data($userstd);
        } else {
            try {
                /** @var manage_users $manage_users */
                $manage_users = app::get_instance()->get(manage_users::class);
                $manage_users->update_user_moodle_with_ci_data($user);
            } catch (Throwable $e) {
                $code = $e->getCode();
            }
        }
        return $user;
    }

    /**
     * Devolver la fecha de expiración de la licencia para el producto dado.
     *
     * @param iuser $user
     * @param string $codproduct
     * @return int|mixed|null
     * @throws \dml_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws user_exception
     */
    private function get_license_expiration(iuser $user, string $codproduct) {
        /** @var user_license $userlicense */
        $userlicense = app::get_instance()->get(user_license::class);
        $licenses = $userlicense->get_licenses($user->get_idnumber(), true);
        if (isset($licenses[$codproduct]) === false) {
            throw new user_exception('El usuario no tiene licencia para el producto dado: ' . $codproduct);
        }
        return $licenses[$codproduct]->get_fecha_fin();
    }

    /**
     * Chequear si tiene que crear curso y matricular usuario.
     *
     * Profe:
     *  - Si tiene curso no hay nada que hacer.
     *  - Si no tiene curso se crea y matricula.
     *
     * Alumno:
     *  - Si está en curso de profe no hacer nada.
     *  - Si está en niños perdidos y NO hay curso de profe, no hacer nada.
     *
     *  - Si está en niños perdidos y hay curso de profe. Desmatricular y matricular.
     *  - Si no está en ningún curso:
     *      - Si existe curso de profe matricularlo.
     *      - Si no, matricular en niños perdidos.
     *
     * @param iuser $user
     * @param string $codproduct
     * @param string|null $groupname
     * @return int
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     * @throws course_exception
     * @throws user_exception
     */
    private function check_product(iuser $user, string $codproduct, ?string $groupname) {
        // Nombre por defecto del grupo si es null o vacío.
        if (is_null($groupname) || empty(trim($groupname))) {
            $groupname = get_string('welcomegroup', 'local_npe');
        }

        // Setear el id del usuario actual para que se use el usuario dado y no el current user real.
        app::get_userfactory()->set_current_user_id($user->get_id());
        $courses = $this->get_external_courses($user, $codproduct, $groupname);

        $currentcourse = current($courses) ?: null;

        // Producto
        $product = product::get_instance($codproduct);
        // Comunidad Autónoma para curso de profesor.
        $ccaa = ($product->has_ccaa()) ? $user->get_user_data()->get_institution() : null;

        // Si está matriculado en el curso correcto, no hay nada que hacer.
        if($this->is_enrolled_in_correct_course($user, $currentcourse, $codproduct, $ccaa, $groupname)) {
            return $currentcourse->get_id();
        }
        if ($user->is_teacher()) {
            $courseid = $this->manage_teacher_course($codproduct, $ccaa, $groupname);
        } else if ($user->is_student()) {
            $courseid = $this->manage_student_course($currentcourse, $user, $codproduct, $ccaa, $groupname);
        } else {
            throw new user_exception('El usuario no es ni profesor ni alumno.');
        }

        $user->unset_enrolled_courses();

        return $courseid;
    }

    /**
     * @param string $codproduct
     * @param $ccaa
     * @param string $groupname
     * @return int
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     * @throws course_exception
     * @throws user_exception
     */
    private function manage_teacher_course(string $codproduct, $ccaa, string $groupname) {
        // Crear curso del profe.
        $courseid = course::create_external_teacher_course('External - Grupo de profesor', $codproduct, $ccaa, $groupname);
        // Generar enrolment key.
        npe_enrolment_manager::generate_enrolment_key($courseid);
        // Creación del Equipo General.
        $teammanager = general_team_manager::get_instance($courseid);
        $teammanager->create_team('Equipo General');
        return $courseid;
    }

    /**
     * @param course|null $currentcourse
     * @param iuser $user
     * @param string $codproduct
     * @param $ccaa
     * @param string|null $groupname
     * @return int
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws course_exception
     * @throws user_exception
     */
    private function manage_student_course(?course $currentcourse, iuser $user, string $codproduct, $ccaa, ?string $groupname) {
        // Comprobar si existe curso de profe para el grupo del alumno.
        $courseid = ($groupname !== null) ? course::get_external_student_courseid($codproduct, $ccaa) : null;
        // Si no hay curso de profe, obtener el courseid de niños perdidos.
        if ($courseid === null) {
            $courseid = $this->get_student_courseid($user, $codproduct);
        }
        if ($currentcourse !== null) {
            $currentcourse->unenrol_user($user->get_id());
        }
        // Matricular alumno.
        course::get_instance($courseid)->enrol_student($user->get_id());
        return $courseid;
    }

    /**
     * @param iuser $user
     * @param string $codproduct
     * @param string|null $groupname
     * @return course[]
     */
    private function get_external_courses(iuser $user, string $codproduct, ?string $groupname) {
        $enrrolledcourses = $user->get_enrolled_courses_by_codproduct($codproduct);
        return array_filter($enrrolledcourses, static function($course) use ($groupname) {
            return $course->is_external_course() &&
                ($groupname === null || $course->get_external_groupname() === $groupname || $course->is_student_course() === true);
        });
    }

    /**
     * Verifica si el usuario está matriculado en el curso correcto.
     *  - Si es profe y tiene curso no hay nada que hacer.
     *  - Si es alumno y está matriculado en un curso de profe no hay nada que hacer.
     *  - Si es alumno y está en niños perdidos y NO hay curso de profe, no hay nada que hacer.
     *
     * @param iuser $user
     * @param course|null $currentcourse
     * @param string $codproduct
     * @param $ccaa
     * @param string|null $groupname
     * @return bool
     * @throws \dml_exception
     */
    private function is_enrolled_in_correct_course(iuser $user, ?course $currentcourse, string $codproduct, $ccaa, ?string $groupname) {
        return  ($currentcourse !== null && $user->is_teacher()) ||
                ($currentcourse !== null && $user->is_student() &&
                ($currentcourse->is_student_course() === false ||
                (
                    $currentcourse->is_student_course() === true &&
                    $groupname &&
                    course::has_external_teacher_course($codproduct, $ccaa, $groupname) === false &&
                    $this->check_student_course($currentcourse, $user)
                )));
    }

    /**
     * Devuelve el id del curso de niños perdidos.
     *
     * @param iuser $user
     * @param string $codproduct
     * @return int
     * @throws \dml_exception
     * @throws course_exception
     */
    private function get_student_courseid(iuser $user, string $codproduct) {
        // Comunidad Autónoma para curso de alumno.
        $ccaa = $user->get_user_data()->get_institution();
        if (course::has_external_student_course($codproduct, $ccaa) === false) {
            // Crear curso de niños perdidos.
            $courseid = course::create_external_student_course( 'External - Grupo de alumnos', $codproduct, $ccaa);
        } else {
            $courseid = course::get_external_student_courseid($codproduct, $ccaa);
            if($courseid === null) {
                throw new course_exception('No se puede obtener el id del curso de alumnos de External.');
            }
        }
        return $courseid;
    }

    /**
     * Verificar si el alumno está en el curso de niños perdidos de su ccaa.
     *
     * @param course $course
     * @param iuser $student
     * @return bool
     */
    private function check_student_course(course $course, iuser $student) {
        if ($course->is_student_course() === true) {
            $courseccaa = $course->get_ccaa();
            if ($courseccaa != $student->get_user_data()->get_institution()) {
                return false;
            }
        }
        return true;
    }

    /**
     * @param string $token
     * @return void
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \moodle_exception
     * @throws course_exception
     * @throws login_exception
     * @throws user_exception
     */
    public function login(string $token) {
        $accessdto = $this->get_access_dto($token);
        $userid = $accessdto->get_userid();
        $rawcodproduct = $accessdto->get_codproduct();
        $cleancodproduct = product::clean_codproduct($rawcodproduct);
        $user = app::get_userfactory()->get_user($accessdto->get_userid());
        // Verificar licencia.
        $licenseexpiration = $this->get_license_expiration($user, $cleancodproduct);
        // Crear/Actualizar licencia.
        $this->check_license($user, $cleancodproduct, $licenseexpiration);
        $this->delete_access_record($token);
        $this->complete_login($userid);
        $redirecturl = $this->get_redirect_url($token);
        redirect($redirecturl);
    }

    /**
     * @param $token
     * @return string
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws course_exception
     * @throws login_exception
     * @throws user_exception
     * @throws \moodle_exception
     */
    public function get_redirect_url($token) {
        $accessdto = $this->get_access_dto($token);
        $user = app::get_userfactory()->get_user($accessdto->get_userid());
        $courseid = $accessdto->get_courseid();

        if ($courseid === null) {
            throw new course_exception('No se puede generar la URL de acceso. El usuario no está matriculado en ningún curso.');
        }

        if ($user->get_user_data()->is_seneca_user()) {
            $codproduct = $accessdto->get_codproduct();
            $realcodproduct = ($codproduct[2] === 'P' || $codproduct[2] === 'A') ? substr($codproduct, 0, 2) . substr($codproduct, 3) : $codproduct;
            $courses = $user->get_enrolled_courses_by_codproduct($realcodproduct);
            if ($user->is_teacher()){
                if (count($courses) > 1) {
                    $url = app::get_url_helper()->get_groups_url($realcodproduct);
                } else {
                    $url = app::get_url_helper()->get_course_url($courseid);
                }
            }
            else {
                $courseid = current($courses)->get_id();
                $url = app::get_url_helper()->get_course_url($courseid);
            }
            
        } else {
            $url = app::get_url_helper()->get_course_url($courseid);
        }
        app::get_url_helper()->set_home_url($url);

        return $url;
    }
}
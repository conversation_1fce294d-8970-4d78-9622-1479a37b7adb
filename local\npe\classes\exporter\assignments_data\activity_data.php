<?php

namespace local_npe\exporter\assignments_data;

use local_npe\app;
use local_npe\activity\activity;
use local_npe\constants;
use local_npe\core\activity_core;
use local_npe\core\persistent\activity_subcategory;
use local_npe\course;
use local_npe\exporter_data;
use local_npe\filter\assignment_filter;
use local_npe\helper\url;
use local_npe\helper\json;

class activity_data extends exporter_data {

    public $id;
    public $clasification;
    public $type;
    public $activityorder;
    public $name;
    public $delivery;
    public $deliverymapped;
    public $dificultylevel;
    public $unit;
    public $isassigned;
    public $isassignedtoteam;
    public $insequence;
    public $assignurl;
    public $editassignurl;
    public $viewdeliveriesnurl;
    public $activityurl;
    public $authorinfo = [];
    public $switchtogroupdelivery;
    public $hasdeliveries;
    public $topicid;
    public $categoryid;
    public $blockid;
    public $toassign;
    public $activitytypename;
    public $visibility;
    public $keyevidence;
    public $isresource;
    public $shareurl;
    public $urltoviewer;
    public $sdanumber;
    public $editrepositoryresourceurl;
    public $competenceid;
    public $criterionid;
    public $themeid;
    public $transversekeyid;
    public $skillsid;
    public $pedagogicalpurposesid;
    public $assessmentid;
    public $learninglevelid;
    public $trackingactivitiesid;
    public $challengesid;
    public $incontextid;
    public $typeofactivityid;
    public $presentationresourcesid;
    public $subcategoryid;
    public $txtPortfolioEvidence;
    public $subcategoryname;

    // Parámetros no exportables.
    /** @var course  */
    private $course;
    /** @var activity */
    private $activity;
    /** @var assignment_filter */
    private $assignmentfilter;
    /** @var url */
    private url $urlhelper;

    public function __construct(course $course, assignment_filter $assignmentfilter) {
        $this->course = $course;
        $this->assignmentfilter = $assignmentfilter;
    }

    public function set_activity(activity $activity) {
        $this->activity = $activity;
    }

    /**
     * Establecer los parámetros a exportar.
     *
     * @throws \JsonException
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \local_npe\exception\course_exception
     * @throws \local_npe\exception\npe_exception
     * @throws \local_npe\exception\product_exception
     * @throws \local_npe\exception\stdclass_transform_exception
     * @throws \local_npe\exception\user_exception
     * @throws \moodle_exception
     */
    public function set_params() :void {
        $assigndto = $this->activity->get_assigned_data($this->course->get_id());
        $deliverytype = $this->activity->get_delivery_type($this->course->get_id());
        $fromviewer = optional_param('fromviewer', false, PARAM_BOOL);

        $this->id = $this->activity->get_id();
        $this->clasification = $this->activity->get_activity_data()->get_clasification();
        $this->name = $this->activity->get_activity_data()->get_name();
        $this->type = $this->activity->get_activity_data()->get_type_id();
        $this->activityorder = $this->activity->get_activity_data()->get_order();
        $this->delivery = $deliverytype;
        $this->deliverymapped = self::map_delivery_type($deliverytype);
        $this->dificultylevel = !empty($this->activity->get_activity_data()->get_difficultylevel()) ?
            strtolower($this->activity->get_activity_data()->get_difficultylevel()) : null;
        $this->unit = $this->activity->get_activity_data()->get_product_topic_name();
        $this->isassigned = $this->activity->has_assigned($this->course->get_id());
        if ($this->isassigned
            && $this->activity->has_group_delivery($this->course->get_id())) {
            $this->isassignedtoteam = true;
        } else {
            $this->isassignedtoteam = false;
        }

        $productdata = $this->course->get_product()->get_product_data();
        $txtPortfolioEvidenceLabel = $productdata->get_json_labels('txtPortfolioEvidence');
        $this->txtPortfolioEvidence = $txtPortfolioEvidenceLabel ? $txtPortfolioEvidenceLabel : get_string('porfoliokeyevidence', 'local_npe');

        $this->editassignurl = $this->get_url_helper()->get_assignment_edit_url($this->course->get_id(), $this->activity->get_id());
        $this->viewdeliveriesnurl = $this->get_url_helper()->get_followup_url($this->course->get_id(), $this->activity->get_id());
        $this->insequence = $this->activity->get_activity_data()->in_sequence();
        $this->hasdeliveries = $assigndto !== null && (bool) $assigndto->get_total_deliveries();
        $this->switchtogroupdelivery = $this->activity->can_switch_to_group_delivery($this->course->get_id());
        if($this->switchtogroupdelivery){
            $this->delivery = $this->activity->get_default_delivery();
        }
        $this->topicid = $this->activity->get_activity_data()->get_product_topic_packerid();
        $this->categoryid = $this->activity->get_activity_data()->get_activity_category_category();
        $this->blockid = $this->activity->get_activity_data()->get_product_topic_block_packerid();
        $this->toassign = $this->activity->get_activity_data()->is_assignable();
        $this->activityurl = $this->activity->get_url($this->course, $fromviewer);
        $topicinfo = app::get_product($this->course->get_product()->get_codproduct())->get_topic_by_packerid($this->topicid);
        $this->sdanumber = $topicinfo->get_sdanumber();
        $user = app::get_userfactory()->get_current_user();
        $this->isstudent = $user->is_student();
        $this->activitytypename = $this->activity->get_activity_data()->get_type_label(); // Nombre "real" del tipo de actividad.
        $this->visibility = $this->activity->get_activity_data()->get_visibility_label();
        $this->keyevidence = (bool) $this->activity->get_activity_data()->get_key_evidence();
        $this->isresource = $this->activity->is_resource();

        $this->set_assign_activity_url();

        if ($this->activity->get_activity_data()->is_custom()) {
            $authorid = \nperepository_teacher\persistent\repository_uses::get_resource_by_activity_id(
                $this->activity->get_id(),
                $this->course->get_id()
            )->userid;
            if (!$authorid) {
                if ($user->get_user_data()->is_educamos_user()) {
                    $authorid = \nperepository_teacher\persistent\repository_uses::get_resource_by_activity_id(
                        $this->activity->get_id(),
                        $this->course->get_id()
                    )->userid;
                } else {
                    $authorid = $this->isstudent ? $this->course->get_creator()->get_id() : $user->get_id();
                }
            }

            $author = app::get_userfactory()->get_user($authorid);
            $this->authorinfo =
                ['name' => $author->get_user_data()->get_fullname(), 'picture' => $user->get_user_data()->get_picture()];
            $this->editrepositoryresourceurl = $this->get_url_helper()
                ->get_edit_repo_resource_url($this->course->get_id(), $this->activity->get_id());
        }

        $this->shareurl = $this->get_url_helper()->get_activity_url_to_share(
            $this->course->get_codproduct(),
            $this->activity->get_activity_data()->get_idnumber()
        );

        $this->urltoviewer = app::get_url_helper()->get_viewer_url(
            $this->course->get_course_data()->get_id(),
            $this->activity->get_activity_data()->get_product_topic_id(),
            base64_encode(json_encode(['idN5' => $this->activity->get_idnumber()]))
        );

        $this->competenceid = $this->activity->get_activity_data()->get_competence();
        $this->criterionid = $this->activity->get_activity_data()->get_criterion();
        $this->themeid = $this->activity->get_activity_data()->get_theme();
        $this->transversekeyid = $this->activity->get_activity_data()->get_transversekey();
        $this->skillsid = $this->activity->get_activity_data()->get_skill();
        $this->pedagogicalpurposesid = $this->activity->get_activity_data()->get_pedagogical_purposes();
        $this->assessmentid = $this->activity->get_activity_data()->get_assessment_id();
        $this->learninglevelid = $this->activity->get_activity_data()->get_learning_level_id();
        $this->trackingactivitiesid = $this->activity->get_activity_data()->get_tracking_activities_id();
        $this->challengesid = $this->activity->get_activity_data()->get_challenges_id();
        $this->incontextid = $this->activity->get_activity_data()->get_in_context_id();
        $this->typeofactivityid = $this->activity->get_activity_data()->get_type_of_activity_id();
        $this->presentationresourcesid = $this->activity->get_activity_data()->get_presentation_resources_id();
        $this->subcategoryid = $this->activity->get_activity_data()->get_activity_subcategory();
        $subcatid = $this->activity->get_activity_data()->get_subcategory_id();
        $this->subcategoryname = $subcatid ? activity_subcategory::get_subcategory_name_by_id($subcatid) : '';  
    }


    /**
     * Activity Assignment URL
     * @throws \coding_exception
     * @throws \moodle_exception
     */
    private function set_assign_activity_url() {
        if (isset($_GET['topicid'])) {
            $this->assignurl = $this->get_url_helper()
                ->get_assignment_activity_url($this->course->get_id(), $this->activity->get_id(),  $this->topicid);
        }
        else {
            if ($this->activity->is_resource()) {
                $this->assignurl = $this->get_url_helper()
                    ->get_assignment_resource_url($this->course->get_id(), $this->activity->get_id());
            }
            if ($this->activity->is_activity()) {

                $this->assignurl = $this->get_url_helper()
                    ->get_assignment_activity_url($this->course->get_id(), $this->activity->get_id());
            }
            if ($this->activity->is_exam()) {
                $this->assignurl = $this->get_url_helper()
                    ->get_assignment_exam_url($this->course->get_id(), $this->activity->get_id());
            }
        }
    }

    /**
     * @return url
     */
    private function get_url_helper() {
        return $this->urlhelper ?? $this->set_url_helper();
    }

    /**
     * Setea el url helper.
     * Costura para Test Unitarios.
     *
     * @param url|null $urlhelper
     * @return url
     */
    private function set_url_helper(): url{
        $this->urlhelper = new url();
        return $this->urlhelper;
    }

    /**
     * @param $deliverytype
     * @return string
     */
    private function map_delivery_type($deliverytype) {
        $map = [
            'single'    => 'individual',
            'group'          => 'team',
            'grandgroup'      => 'bigGroup',
        ];
        return $map[$deliverytype] ?? $deliverytype;
    }
}

{{# sections }}
    <div class="modal-filter hidden" data-section="{{ section }}">
    {{# filters }}
        {{# hasoptions }}
        <div class="dropdown filter-title {{filterName}}" id="{{ filterId }}">
            <button class="card-header dropbtn" type="button" id="heading-{{ filterId }}">
                {{ filterLabel }}
                <i></i>
            </button>
            <!-- Filters -->
            <ul class="dropdown-content itemslist" data-id="heading-{{ filterId }}">
                {{# options }}
                    <li>
                        <div class="custom_checkbox dropdown-item">
                            <input type="checkbox" id="{{ filterId }}-{{ key }}" data-filtertype="{{ filterName }}" data-id="{{ key }}" data-filter="{{ filterName }}"
                                    data-label="{{ value }}">
                            <label for="{{ filterId }}-{{ key }}">
                            </label>
                            <span id="{{ filterId }}-{{ key }}-label">{{ value }}</span>
                        </div>
                    </li>
                {{/ options }}
            </ul>
        </div>
        {{/ hasoptions }}
    {{/ filters }}
    <button type="button" class="clean-filters" data-dismiss="filter">{{# str }}quit-filters, local_npe{{/ str }}</button>
        <!-- Filter zone -->
        <div class="filters-zone"></div>
    </div>
{{/ sections }}
{{#js}}
    require(['jquery'], function ($) {
        // Extend title event
        $('.custom_checkbox span').on('click', function() {
            $(this).closest('.custom_checkbox').children()[0].click()
        })
        // Simple color:hover for subcat
        $('.subcat').hover(function() {
            $(this).not('.selected').toggleClass('bgf-c');
        })

        // Close the dropdown if the user clicks outside of it
        window.onclick = function(event) {
            $('.dropbtn').removeClass('open');
            if (!event.target.matches('.dropbtn') && !event.target.closest('.itemslist')) {
                var dropdowns = document.getElementsByClassName("dropdown-content");
                var i;
                for (i = 0; i < dropdowns.length; i++) {
                    var openDropdown = dropdowns[i];
                    if (window.getComputedStyle(openDropdown).display !== "none") {
                        openDropdown.style.display = "none";
                    }
                }
            }
        }

        // JS para el funcionamiento del dropdown en movil con el scroll de los tag en overflow
        var newHScroll = 0;
        $("#filterButton").on("scroll", function (e) {
            var horizontal = e.currentTarget.scrollLeft;
            if (horizontal !== newHScroll) {
               $('.dropbtn').removeClass('open');
               var dropdowns = document.getElementsByClassName("dropdown-content");
                var i;
                for (i = 0; i < dropdowns.length; i++) {
                    var openDropdown = dropdowns[i];
                    if (window.getComputedStyle(openDropdown).display !== "none") {
                        openDropdown.style.display = "none";
                    }
                }
            }
            newHScroll = horizontal;
        });

        (function () {
            // detect touch
            if ("ontouchstart" in document.documentElement) {
                document.documentElement.className += " touch-device";
            }

            const scroller = document.querySelector("#filterButton");
            const dropDown = document.querySelectorAll(".dropdown-content");
            scroller.addEventListener("scroll", checkScroll);

            function checkScroll() {
                document.activeElement.blur();
                scroller.classList.add("isScrolling");
                for (let i = 0; i < dropDown.length; i++) {
                    dropDown[i].style.transform = "translateX(-" + scroller.scrollLeft + "px)";
                }
                scroller.classList.remove("isScrolling");
            }
        })();
    });
{{/js}}

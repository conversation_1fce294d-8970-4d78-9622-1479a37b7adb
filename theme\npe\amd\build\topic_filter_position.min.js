define("theme_npe/topic_filter_position",["jquery"],($=>{const adjustTopicFilterPosition=()=>{$(".maticesTheme .topic-col-filter").each(((_,element)=>{const $filter=$(element),$topicColImg=$filter.closest(".topic_card").find(".topic-col-img");if($topicColImg.length>0){const rightPosition=20;let topPosition=$topicColImg.outerHeight()-$filter.outerHeight()-20;topPosition<0&&(topPosition=20),$filter.css({right:`${rightPosition}px`,top:`${topPosition}px`,position:"absolute"})}}))};return{init:()=>{let resizeTimeout;$(document).ready((()=>{adjustTopicFilterPosition()})),$(window).on("resize",(()=>{clearTimeout(resizeTimeout),resizeTimeout=setTimeout(adjustTopicFilterPosition,50)})),$(document).on("DOMNodeInserted",".topic_card",(()=>{setTimeout(adjustTopicFilterPosition,100)})),$(document).on("load",".topic_card img",(()=>{setTimeout(adjustTopicFilterPosition,50)})),$(window).on("orientationchange",(()=>{setTimeout(adjustTopicFilterPosition,300)}))},adjustPosition:adjustTopicFilterPosition}}));

//# sourceMappingURL=topic_filter_position.min.js.map
define("theme_npe/topic_filter_position",["jquery"],(function($){function adjustTopicFilterPosition(){$(".maticesTheme .topic-col-filter").each((function(){var $filter=$(this),$topicColImg=$filter.closest(".topic_card").find(".topic-col-img");if($topicColImg.length>0){var topPosition=$topicColImg.outerHeight()-$filter.outerHeight()-20;topPosition<0&&(topPosition=20),$filter.css({right:"20px",top:topPosition+"px",position:"absolute"})}}))}return{init:function(){var resizeTimeout;$(document).ready((function(){adjustTopicFilterPosition()})),$(window).on("resize",(function(){clearTimeout(resizeTimeout),resizeTimeout=setTimeout(adjustTopicFilterPosition,150)})),$(document).on("DOMNodeInserted",".topic_card",(function(){setTimeout(adjustTopicFilterPosition,100)})),$(document).on("load",".topic_card img",(function(){setTimeout(adjustTopicFilterPosition,50)})),$(window).on("orientationchange",(function(){setTimeout(adjustTopicFilterPosition,300)}))},adjustPosition:adjustTopicFilterPosition}}));

//# sourceMappingURL=topic_filter_position.min.js.map
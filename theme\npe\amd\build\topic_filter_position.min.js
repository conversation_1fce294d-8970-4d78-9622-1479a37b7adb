define("theme_npe/topic_filter_position",["jquery"],(function($){function adjustTopicFilterPosition(){window.innerWidth<=799?$(".maticesTheme .topic-col-filter").each((function(){var $filter=$(this),$container=$filter.closest(".wrapper_topic");if($container.length>0){var proportionalRight=20/720*$container.outerWidth(),proportionalTop=280/420*$container.outerHeight();$filter.css({right:proportionalRight+"px",top:proportionalTop+"px"})}})):$(".maticesTheme .topic-col-filter").css({right:"",top:""})}return{init:function(){var resizeTimeout;adjustTopicFilterPosition(),$(window).on("resize",(function(){clearTimeout(resizeTimeout),resizeTimeout=setTimeout(adjustTopicFilterPosition,150)})),$(document).on("DOMNodeInserted",".topic_card",(function(){setTimeout(adjustTopicFilterPosition,100)}))},adjustPosition:adjustTopicFilterPosition}}));

//# sourceMappingURL=topic_filter_position.min.js.map
/* TE-22507 - Solo aplicable en las vistas necesarias */
.isactivityheader {

    // _menu ---------------
    header.header__wrapper.header_common {
        &:not(.educamosheader) {
            position: relative !important;

            .header {
                padding: 10px 20px !important;

                .header__mynotifications__counter {
                    top: 0;
                }
            }
        }

        &.educamosheader {
            min-height: 28px !important;
            position: relative !important;
        }
    }

    #page {
        padding-top: 0px;
    }

    #screen-megamenu {
        position: relative;
        top: 0;
    }

    // _breadcrumbs ---------------
    .backbutton,
    .backlinkbutton {
        width: 32px;
        height: 32px;
        background-position-x: 8px;
        background-position-y: 7px;
    }

    .npe-breadcrumb {
        margin-left: 20px;
        margin-top: 16px;
        margin-bottom: 16px;
        width: calc(75%);

        .npe-linkcontainer {
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            white-space: initial;
            @media (min-width: 1690px) {
                display: -webkit-box;
            }
        }

        @media (max-width: 1690px) {
            width: calc(70%);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            .npe-linkcontainer {
                min-width: 0;
                max-height: 30px;
            }
        }

        @media (max-width: 1380px) {
            width: calc(60%);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            .npe-linkcontainer {
                min-width: 0;
                max-height: 30px;
            }
        }

        @media (max-width: 1080px) {
            width: calc(50%);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            .npe-linkcontainer {
                min-width: 0;
                max-height: 30px;
            }
        }

        @media (max-width: 850px) {
            width: calc(40%);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            .npe-linkcontainer {
                min-width: 0;
                max-height: 30px;
            }
        }

        .npe-backcontainer {
            padding-right: 10px;
        }
    }

    // _minimenu ---------------
    .dotsminimenu {
        margin: 0;
    }

    // _assignments ---------------
    .assignments {
        &.edition {
            .assignments-add {
                margin: 0;
            }
        }
    }

    // _forum ---------------
    .myassignments {
        .content-activities {
            border-top: 0;
        }
    }

    // _myassignments ---------------
    .myassignments {
        #appViewer {
            border: 1px solid $basic-color-b;
            height: calc(100vh);
            width: calc(100% - 40px);
            margin: 20px;
            border-radius: 20px;
        }

        &.picto {
            #appViewer {
                height: calc(80vh)!important;
            }
        }

        .myassignments-content {
            position: sticky;
            top: -10px;

            .myassignments-header {
                $p: &;
                border-bottom: 1px solid $basic-color-b;
                height: 126px;
                position: sticky;
                top: 0;

                &:has(.picto-header) {
                    height: 86px;
                }

                .picto-header {
                    h3 {
                        font-size: 18px;
                        font-family: $ossemibold;
                        color: $npe-black;
                        text-transform: uppercase;
                        width: fit-content;
                        max-width: 88%;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }

                    .header-activities {
                        position: relative;

                        .activity-unit {
                            margin-left: 0;
                        }

                        .npe-grade-comment-info {
                            display: flex;
                            justify-content: right;
                            padding-right: 0;

                            .state-tag {
                                height: 30px;
                                position: relative;
                                right: 150px;
                                top: 3px;

                                &.hide {
                                    display: none;
                                }
                            }
                        }
                    }
                }
                .activity-keyevidence {
                    &:after {
                        content: "";
                        display: inline-block;
                        width: 40px;
                        height: 30px;
                        position: absolute;
                        background-image: url([[pix:theme|star_blue]]);
                        background-size: 24px;
                        background-repeat: no-repeat;
                        background-position-x: 15px;

                        @media (max-width: 799px) {
                            display: none!important;
                        }
                    }
                }

                @media (max-width: 799px) {
                    height: 96px;
                }

                &.fullscreen {
                    height: 52px;

                    .breadcrumbs,
                    .activity-unit {
                        display: none !important;
                    }

                    .header-activities {
                        background: transparent;
                        margin-top: 0;

                        .npe-assignments-intro {
                            margin-top: 13px;
                        }

                        .activity-header-icons {
                            margin-top: 40px;
                        }

                        .teacher {
                            .activity-header-icons {
                                margin-top: 20px;
                            }
                        }
                    }
                }
            }
        }
    }

    // npe ---------------
    .content-course {
        .container-fluid {
            padding: 0;
            margin-left: 0;
        }
    }
}

// CSS Original

.header-activities {
    background-color: $npe-white;
    padding: 0;
    margin-bottom: -20px;
    margin-left: 0px;
    margin-top: -10px;
    height: 60px;
    margin-bottom: 10px;

    label {
        font-size: 16px;
        color: $npe-dark-grey;
        display: block;
    }

    .activity-type {
        font-family: $oslight;
        font-style: italic;
    }

    .titleandicons {
        display: flex;
        padding-left: 10px;

        h3 {
            font-size: 18px;
            font-family: $ossemibold;
            color: $npe-black;
            text-transform: uppercase;
            width: fit-content;
            max-width: 88%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            @media (max-width: 799px) {
                padding-right: 0!important;
                padding-left: 20px !important;
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                white-space: initial;
                font-size: 14px!important;
            }
        }

        .activity-evidence-icon {
            &:after {
                content: "";
                display: inline-block;
                width: 40px;
                height: 30px;
                position: absolute;
                background-image: url([[pix:theme|star_blue]]);
                background-size: 24px;
                background-repeat: no-repeat;
                background-position-x: 0;
            }
        }

        img {
            width: 24px;
            position: relative;
            left: 55px;
            border-radius: 20px;
        }
    }

    .activity-unit {
        font-family: $osregular;
        margin-bottom: 20px;
        margin-left: 10px;
        width: fit-content;

        @media (max-width: 799px) {
            display: none;
        }
    }

    .teacher-preview {
        width: 50%;
        display: inline-block;
    }

    .header-generalforum {
        display: flex;
        align-items: flex-start;

        .icon-forum {
            background-image: url([[pix:theme|message-text]]);
            background-repeat: no-repeat;
            background-position-x: 7px;
            background-position-y: 7px;
            background-color: $npe-grey-alt;
            width: 40px;
            height: 40px;
            display: inline-block;
            border-radius: 10px;
        }

        .info {
            margin-left: 20px;
        }

        .generalforum.activity-unit {
            font-family: $oslight;
            font-size: 18px;
            margin-bottom: 0.2rem;
        }
    }

    .activity-visor {
        display: inline-block;
        width: 30px;
        height: 30px;
        position: relative;
        background-image: url([[pix:theme|insequence]]);
        background-repeat: no-repeat;
        background-position-x: 4px;
        background-position-y: 5px;
        background-size: 22px;
        border-radius: 5px;
        margin-left: 10px;
        background-color: $npe-grey-alt;
    }

    .activity-portfolio {
        display: inline-block;
        width: 30px;
        height: 30px;
        position: relative;
        left: 10px;
        background-image: url([[pix:theme|star]]);
        background-repeat: no-repeat;
        background-position-x: 5px;
        background-position-y: 4px;
        background-color: $npe-grey-alt;
        border-radius: 10px;
        top: 11px;
        background-size: 20px;
    }

    .teambox {
        display: inline-block;
        vertical-align: sub;
        padding-bottom: 20px;

        .activity-delivery-type {
            height: 35px;
            background-repeat: no-repeat;
            background-size: 22px auto;
            padding: 4px 0 0 30px;
            font-family: $osregular;
            font-size: 14px;
            margin-left: 10px;
            color: $npe-dark-grey;

            &.group {
                background-image: url([[pix:theme|users]]);
            }
        }

        .info_drop {
            position: relative;

            .info_drop_button {
                width: 20px;
                height: 20px;
            }

            .dropdown-menu {
                padding: 0;
            }
        }

        .teamname {
            color: $npe-black;
            font-family: $ossemibold;
            font-size: 14px;
        }

        .members-menu {
            ul {
                list-style: none;
                padding: 20px;
                margin: 0;

                li:last-child {
                    margin-bottom: 0;
                }

                li {
                    margin-bottom: 10px;
                    display: flex;
                    flex-direction: inherit;

                    span {
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }

                    .icon {
                        width: 30px;
                        height: 30px;
                    }

                    .student {
                        display: flex;
                        flex-direction: column;
                        max-width: 80%;

                        .name {
                            font-family: $ossemibold;
                            font-size: 14px;
                        }

                        .role {
                            font-family: $osregular;
                            font-size: 12px;
                        }
                    }

                }
            }
        }
    }

    .state-tag {
        font-size: 12px;
        font-style: italic;
        border: 1px solid;
        padding: 3px 10px;
        border-radius: 5px;
        display: inline-block;
        margin-left: 10px;
        position: relative;
        top: 3px;

        &.pending {
            color: #454545;
            border-color: #e88f15;
            background-color: #e88f150d;
        }

        &.reopen {
            color: #e88f15;
            border-color: #e88f15;
            background-color: #e88f150d;
        }

        &.done {
            color: #003277;
            border-color: #003277;
            background-color: #0032770d;
        }

        &.graded {
            color: #00860f;
            border-color: #00860f;
            background-color: #00860f0d;
        }
    }

    .grade {
        font-family: $ossemibold;
        font-size: 14px;
        color: $npe-black;
        display: flex;
        align-items: center;
        width: 137px;
        height: 36px;
        border: 2px solid $npe-grey-alt;
        border-radius: 30px;
        position: absolute;

        @media only screen and (max-width: 799px) {
            top: 0px;
        }

        .text {
            padding-left: 10px;

            &.hidegrade {
                width: min-content;
                text-align: center;
            }
        }

        .num {
            position: absolute;
            right: 0;
            font-size: 14px;
            height: 36px;
            width: 36px;
            border: 2px solid;
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            @include grey;

            img {
                position: relative;
                width: 25px;
                height: 25px;
                left: 4px;
            }
        }

    }

    .comments {
        display: flex;
        justify-content: flex-end;
        align-items: end;
        margin-bottom: 20px;
        float: right;
        padding-top: 10px;

        @media (max-width: 799px) {
            display: none;
        }

        a {
            font-size: 14px;
            color: $npe-dark-grey;
            font-family: $ossemibold;
    
            .links {
                padding-left: 5px;
            }
        }
    }

    .custom_checkbox {
        font-family: $ossemibold;
        font-size: 14px;
        justify-content: flex-end;
        align-items: end;
        float: right;
        position: inherit;
        padding-bottom: 25px;
        top: 5px;

        input {
            padding: 0;
            height: initial;
            width: initial;
            display: none;
            cursor: pointer;

            &:checked+label::after {
                content: '';
                display: block;
                position: absolute;
                top: 5px;
                left: 7px;
                width: 8px;
                height: 12px;
                border: solid #00a559;
                border-width: 0 2px 2px 0;
                transform: rotate(45deg);
            }

            &:checked+label::before {
                content: '';
                background-color: #86ff86;
                border-color: #00a559;
            }
        }

        label {
            position: relative;
            cursor: pointer;
            color: #fff;
            font-size: 20px;
            display: inline;

            &:before {
                content: '';
                background-color: transparent;
                border: 2px solid #ddd;
                padding: 9px;
                border-radius: 5px;
                display: inline-block;
                position: relative;
                vertical-align: middle;
                cursor: pointer;
                margin-right: 4px;
                transition: background-color 0.3s ease-in-out;
            }
        }

        span {
            margin-bottom: 10px;
        }
    }

    .activity-header-group {
        font-size: 0;
        display: inline-block;
        margin-left: -10px;

        &.teacher {
            width: 100%;
        }

        &.teacher-noicon {
            width: 50%;
            margin-top: -24px;

            .activityinsequence {
                top: 12px;
            }
        }

        >* {
            display: inline-flex;
            vertical-align: middle;
            justify-content: center;
        }

        .activityinsequence {
            display: flex !important;
            justify-content: space-between;
            position: absolute;
            right: 0;
            top: -45px;

            .activity-header-icons {
                margin-top: 3px;
                position: relative;

                @media (max-width: 799px) {
                    top: 0;
                }


                #dotsheaderassignments {
                    @supports (-moz-appearance:none) {
                        top: -18px;
                    }
                    &:hover {
                        .tooltiptext {
                            visibility: initial;
                        }
                    }

                    @media (max-width: 799px) {
                        top: 0px;
                        left: 10px;

                        .tooltiptext {
                            display: none!important;
                        }
                    }
                }

                .top {
                    display: inline-flex;
                    gap: 24px;
                    margin-bottom: 15px;
                    width: 100%;
                    justify-content: end;
                    min-height: 48px;

                    .grade-box {
                        display: block;
                        width: 135px;

                        @media (max-width: 799px) {
                            display: none;
                        }
                    }

                    #proyectarScreen {
                        height: 36px;
                        width: 36px;

                        @media (max-width: 799px) {
                            display: none;
                        }
                    }

                    .npe-grade-comment-info {
                        position: absolute;
                        right: 40px;

                        @media (max-width: 799px) {
                            display: none;
                        }
                    }

                    .assignments-add {
                        display: inline-block;
                        width: 36px;
                        height: 36px;
                        border-radius: 50px;
                        position: relative;
                        background-image: url([[pix:theme|add_white]]);
                        background-repeat: no-repeat;
                        background-size: 16px 16px;
                        background-position-x: 12px;
                        background-position-y: 12px;
                        cursor: pointer;
                        margin-left: 8px;

                        .hide {
                            display: none;
                        }

                        .dots {
                            height: 36px;
                            width: 36px;
                            background-size: 16px 16px;
                        }

                        .dropdown-item {
                            word-break: unset;
                            white-space: nowrap;
                            border-bottom: 1px solid #ededed;
                        }

                        .dropdown-item:not(:last-child) {
                            border-bottom: 1px solid #ededed;
                        }

                        .dropdown-menu .dropdown-item:last-child {
                            border-bottom: 0 !important;
                        }
                    }
                }

                .low {
                    display: flex;
                    gap: 16px;
                    height: 22px;
                    justify-content: flex-end;
                    margin-top: 10px;

                    @media (max-width: 799px) {
                        display: none;
                    }
                }
            }
        }

        .topevidence {
            top: -11px !important;
        }

        .settop {
            top: -30px;
        }

        .unsettop {
            top: unset;

            .state-tag {
                position: relative !important;
                top: -10px !important;
            }
        }



        .activity-evidence-icon {
            width: 30px;
            height: 30px;
            background-color: $basic-color-b;
            border-radius: 5px;
            margin-left: 10px;

            &:hover {
                .tooltiptextcreatedby {
                    visibility: visible;
                }
            }

            .tooltiptextcreatedby {
                word-break: break-word;
                max-width: 160px;
                min-width: 115px;
                visibility: hidden;
                position: absolute;
                z-index: 2000;
                margin-top: 40px;
                margin-left: 10px;
                padding: 10px 12px;
                font-family: $ossemibold;
                font-size: 14px;
                text-align: center;
                color: $npe-black;
                font-weight: bolder;
                border-radius: 12px;
                background-color: $npe-white;
                line-height: 1.2;
                -webkit-box-shadow: 0 5px 10px rgb(0 0 0 / 20%);
                box-shadow: 0 5px 10px rgb(0 0 0 / 20%);
            }
        }

        .activity-evidence-user-picture {
            width: 24px;
        }

        .activity-evidence-user-name {
            font-size: 14px;
            margin-left: 5px;
        }

        .state-tag {
            &.hide {
                display: none;
            }
        }
    }

}

.infoacticy {
    color: $npe-black;
    font-size: 18px;
    font-family: $ossemibold;
    margin-right: 10px;
}

.hidebutton {
    margin-bottom: 40px;

    a {
        color: $npe-dark-grey;
        font-size: 14px;
        text-decoration: underline;
        font-style: italic;

        &:hover {
            color: $npe-dark-grey;
        }

        &:active {
            background-color: $npe-bg-color;
        }
    }
}

h5.infoacticy {
    font-family: $ossemibold;
    float: left;
    line-height: 15px;
    margin-right: 12px;
}

.container-hide {
    .headingtext {
        display: block;
    }

    .date {
        margin: 20px 0;

        span {
            color: $npe-black;
            font-family: $ossemibold;
        }
    }

    .instructions {
        border: 2px solid #666;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;

        .icon {
            width: 24px;
            height: 24px;
        }

        √ span {
            font-family: $osregular;
        }

        .name-tag {
            padding-bottom: 15px;
        }
    }
}
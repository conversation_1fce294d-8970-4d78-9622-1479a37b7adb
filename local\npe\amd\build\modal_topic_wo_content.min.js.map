{"version": 3, "file": "modal_topic_wo_content.min.js", "sources": ["../src/modal_topic_wo_content.js"], "sourcesContent": ["import $ from 'jquery';\nimport Notification from 'core/notification';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\nimport Str from 'core/str';\n\nconst SELECTORS = {\n    MODALBOX: '.modal_text',\n    HIDE: '.close',\n    CONFIRM: '[data-action=\"confirm\"]',\n};\n\nlet registered = false;\n\nexport default class ModalTopicWoContent extends Modal {\n\n    static TYPE = 'local_npe/modal_topic_wo_content';\n    static TEMPLATE = 'local_npe/courses/modal_topic_wo_content';\n\n    constructor(root) {\n        super(root);\n    }\n\n    setData(text) {\n        if (text === 'single') {\n            Str.get_string('conflict_message', 'local_npe').done((string) => {\n                this.getRoot().find(SELECTORS.MODALBOX).html(string);\n            }).fail(Notification.exception);\n        } else if (text === 'multiple') {\n            Str.get_string('conflict_several_teams_message', 'local_npe').done((string) => {\n                this.getRoot().find(SELECTORS.MODALBOX).html(string);\n            }).fail(Notification.exception);\n        }\n    }\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CONFIRM, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.modal-backdrop').remove();\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.modal-backdrop').remove();\n        });\n    }\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalTopicWoContent.TYPE, ModalTopicWoContent, ModalTopicWoContent.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_defineProperty", "r", "t", "i", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_j<PERSON>y", "_notification", "_custom_interaction_events", "_modal", "_modal_registry", "_str", "SELECTORS", "registered", "ModalTopicWoContent", "Modal", "constructor", "root", "super", "setData", "text", "Str", "get_string", "done", "string", "this", "getRoot", "find", "html", "fail", "Notification", "exception", "registerEventListeners", "getModal", "on", "CustomEvents", "events", "activate", "removeClass", "$", "remove", "_exports", "ModalRegistry", "register", "TYPE", "TEMPLATE"], "mappings": "mQAK2B,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,gBAAAH,EAAAI,EAAAC,GAAAD,OAAAA,EAAA,SAAAC,GAAAC,IAAAA,EAAA,SAAAD,EAAAD,GAAAC,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAL,IAAAA,EAAAK,EAAAE,OAAAC,yBAAAR,EAAA,CAAA,IAAAM,EAAAN,EAAAS,KAAAJ,EAAAD,kCAAAE,EAAA,OAAAA,EAAAI,MAAAA,IAAAA,4EAAAN,EAAAO,OAAAC,QAAAP,EAAA,CAAAQ,CAAAR,EAAAC,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAQ,CAAAV,MAAAJ,EAAAe,OAAAC,eAAAhB,EAAAI,EAAAa,CAAAA,MAAAZ,EAAAa,YAAAC,EAAAA,cAAAC,EAAAA,cAAApB,EAAAI,GAAAC,EAAAL,CAAA,iFAL3BqB,QAAAtB,uBAAAsB,SACAC,cAAAvB,uBAAAuB,eACAC,2BAAAxB,uBAAAwB,4BACAC,OAAAzB,uBAAAyB,QACAC,gBAAA1B,uBAAA0B,iBACAC,KAAA3B,uBAAA2B,MAEA,MAAMC,mBACQ,cADRA,eAEI,SAFJA,kBAGO,0BAGb,IAAIC,YAAa,EAEF,MAAMC,4BAA4BC,OAAAA,QAK7CC,WAAAA,CAAYC,MACRC,MAAMD,KACV,CAEAE,OAAAA,CAAQC,MACS,WAATA,KACAC,KAAGlC,QAACmC,WAAW,mBAAoB,aAAaC,MAAMC,SAClDC,KAAKC,UAAUC,KAAKf,oBAAoBgB,KAAKJ,OAAO,IACrDK,KAAKC,cAAY3C,QAAC4C,WACL,aAATX,MACPC,KAAGlC,QAACmC,WAAW,iCAAkC,aAAaC,MAAMC,SAChEC,KAAKC,UAAUC,KAAKf,oBAAoBgB,KAAKJ,OAAO,IACrDK,KAAKC,cAAY3C,QAAC4C,UAE7B,CAEAC,sBAAAA,GACId,MAAMc,uBAAuBP,MAE7BA,KAAKQ,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUzB,mBAAmB,KAChEa,KAAKC,UAAUY,YAAY,SAC3B,EAAAC,QAAAA,SAAE,QAAQD,YAAY,eACtB,EAAAC,iBAAE,mBAAmBC,QAAQ,IAGjCf,KAAKQ,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUzB,gBAAgB,KAC7Da,KAAKC,UAAUY,YAAY,SAC3B,EAAAC,QAAAA,SAAE,QAAQD,YAAY,eACtB,EAAAC,iBAAE,mBAAmBC,QAAQ,GAErC,EAMH,OALAC,SAAAtD,QAAA2B,oBAAA1B,gBApCoB0B,oBAAmB,OAEtB,oCAAkC1B,gBAF/B0B,oBAAmB,WAGlB,4CAmCjBD,aACD6B,gBAAAA,QAAcC,SAAS7B,oBAAoB8B,KAAM9B,oBAAqBA,oBAAoB+B,UAC1FhC,YAAa,GAChB4B,SAAAtD,OAAA"}
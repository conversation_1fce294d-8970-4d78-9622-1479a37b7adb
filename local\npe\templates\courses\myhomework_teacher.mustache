{{^ haveassignments }}
    <div class="myhomework-teacher no-assignments">
     <p>{{# str }}myhomework_noitems, local_npe{{/ str }}</p>
        <p class="description">

        </p>
    </div>
{{/ haveassignments }}
{{# haveassignments }}
    <div class="combos">
        <!-- Buscador -->
        <div class="myhomework-search">
            <input id="search" class="form-control" placeholder="{{ myhomework_teamsearch }}" maxlength="30" aria-label="buscar">
            <span class="clean"></span>
        </div>
        <!-- Unidades WIP -->
        {{> local_npe/commons/units_dropdown }}
    </div>
    <div class="myhomework-teacher">
        <div class="reduce teacher">
            <div class="list">
                <div class="container-fluid">
                    <div class="col-xs-12 filters">
                                <span class="filter selected" data-filter="all">
                                    {{# str }}myhomework_filter_all, local_npe{{/ str }}<span> ({{ total }})</span>
                                </span>
                        {{# states }}
                            <span class="filter" data-filter="{{ alias }}">
                                        {{ name }}<span> ({{ count }})</span>
                                    </span>
                        {{/ states }}
                    </div>
                    <div class="orders">
                        <span class="type-grid tgrid {{ tgrid }}"></span>
                        <span class="type-grid tlist {{ tlist }}"></span>
                    </div>
                    <div class="myhomework-menu-order">
                        <div class="dropdown mr-1">
                            <button type="button" class="btn btn-secondary dropdown-toggle order-filter" id="dropdownMenuOffset" data-toggle="dropdown" aria-haspopup="true"
                                    aria-expanded="false" data-offset="10, 10">
                                {{# str }} myclass_orderby, local_npe {{/ str }}
                            </button>
                            <div class="dropdown-menu selector" aria-labelledby="dropdownMenuOffset">
                                <h6 class="dropdown-header">{{# str }} myclass_orderbydots, local_npe {{/ str }}</h6>
                                <a class="dropdown-item alpha-up selected" href="#!">{{# str }} myclass_alphaup, local_npe {{/ str }}</a>
                                <a class="dropdown-item alpha-down" href="#!">{{# str }} myclass_alphadown, local_npe {{/ str }}</a>
                                <a class="dropdown-item newest" href="#!">{{# str }} myclass_newest, local_npe {{/ str }}</a>
                                <a class="dropdown-item oldest" href="#!">{{# str }} myclass_oldest, local_npe {{/ str }}</a>
                            </div>
                        </div>
                    </div>
                    {{> local_npe/commons/noresults_filter }}
                    <div class="card-columns">
                        <span data-region="loading-icon-container">{{> core/loading }}</span>
                        {{# assignments }}
                            {{> local_npe/courses/homework_card }}
                        {{/ assignments }}
                    </div>
                    <div class="card-list">
                        <div class="list-panel-heading">
                            <div class="block block-30">
                                <span class="type">{{# str }}resourcename, local_npe{{/ str }}</span>
                            </div>
                            <div class="block block-20">
                                <span class="unit">{{# str }}unit, local_npe{{/ str }}</span>
                            </div>
                            <div class="block block-15">
                                <span class="date">{{# str }}myhomework_deliverdate, local_npe{{/ str }}</span>
                            </div>
                            <div class="block block-30">
                                <span class="comment">{{# str }}liststate, local_npe{{/ str }}</span>
                            </div>
                            <div class="block block-15">
                            </div>
                        </div>
                        {{# assignments }}
                            {{> local_npe/courses/homework_list }}
                        {{/ assignments }}
                    </div>
                </div>
            </div>
        </div>
    </div>
{{/ haveassignments }}

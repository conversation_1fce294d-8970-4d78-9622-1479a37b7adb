<?php

namespace local_npe;

use local_npe\activity\activity;
use local_npe\core\activity_core;
use local_npe\helper\json;
use local_npe\helper\url;

defined('MOODLE_INTERNAL') || die();

class access_control {

    /**
     * Chequeo común al inicio de cualquier página.
     *
     * @return void
     * @throws \moodle_exception
     */
    public static function check() {
        self::check_home_redirect();
        // @todo Agregar el resto de validaciones para que no se accedan a páginas no deseadas
        //      (como las de Moodle que no deben ser accesibles).
    }

    /**
     * Redirecciona a la página de inicio si es necesario.
     *
     * @return void
     * @throws \moodle_exception
     */
    public static function check_home_redirect() {
        $gohome = isset($_SERVER['REQUEST_URI']) && ($_SERVER['REQUEST_URI'] === '/' || $_SERVER['REQUEST_URI'] === '/my/');
        if ($gohome === true && $homeurl = app::get_url_helper()->get_home_url()) {
            redirect($homeurl);
        }
    }

    /**
     * @return void
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws exception\course_exception
     * @throws exception\stdclass_transform_exception
     * @throws exception\user_exception
     */
    public static function only_sma_user() {
        $userdto = self::get_current_user()->get_user_data();
        if ($userdto->is_educamos_user() || $userdto->is_marsupial_user() || $userdto->is_external_user()) {
            $homeurl = app::get_url_helper()->get_home_url() ?? '/';
            redirect($homeurl);
        }
    }

    /**
     * @return void
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws exception\course_exception
     * @throws exception\stdclass_transform_exception
     * @throws exception\user_exception
     */
    public static function only_sma_and_sen_teacher() {
        $isteacher = self::get_current_user()->is_teacher();
        $userdto = self::get_current_user()->get_user_data();
        $access = ($isteacher && ($userdto->is_seneca_user() || $userdto->is_sma_user()));
        if ($access === false) {
            $homeurl = app::get_url_helper()->get_home_url() ?? '/';
            redirect($homeurl);
        }
    }

    /**
     * @param null $courseid
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws exception\course_exception
     * @throws exception\stdclass_transform_exception
     * @throws exception\user_exception
     */
    public static function only_teacher($courseid = null) {
        if (self::get_current_user($courseid)->is_teacher() === false) {
            $url = $courseid !== null ? app::get_url_helper()->get_course_url($courseid) : '/';
            redirect($url);
        }
    }

    /**
     * @param null $courseid
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws exception\course_exception
     * @throws exception\stdclass_transform_exception
     * @throws exception\user_exception
     */
    public static function only_student($courseid = null) {
        if (self::get_current_user($courseid)->is_student() === false) {
            $url = $courseid !== null ? app::get_url_helper()->get_course_url($courseid) : '/';
            redirect($url);
        }
    }

    /**
     * Verificar si el usuario logado puede acceder a la actividad.
     *
     * @param int $courseid
     * @param int $activityid
     * @param bool $redirect
     * @return bool
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws exception\activity_exception
     * @throws exception\course_exception
     * @throws exception\product_exception
     * @throws exception\stdclass_transform_exception
     * @throws exception\user_exception
     * @throws exception\product_exception
     */
    public static function check_activity_access_by_activityid(int $courseid, int $activityid, bool $redirect = true) {
        $activity = course::get_instance($courseid)->get_activity($activityid);
        if (!$activity) {
            redirect(app::get_url_helper()->get_course_url($courseid));
        }

        return self::check_activity_access($courseid, $activity, $redirect);
    }

    /**
     * Verificar si el usuario logado puede acceder a la actividad.
     *
     * @param int $courseid
     * @param int $cmid
     * @param bool $redirect
     * @return bool
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws exception\activity_exception
     * @throws exception\course_exception
     * @throws exception\product_exception
     * @throws exception\stdclass_transform_exception
     * @throws exception\user_exception
     * @throws exception\product_exception
     */
    public static function check_activity_access_by_cmid(int $courseid, int $cmid, bool $redirect = true) {
        $activity = course::get_instance($courseid)->get_activity_by_cmid($cmid);
        return self::check_activity_access($courseid, $activity, $redirect);
    }

    /**
     * Verificar si el usuario logado puede acceder a la actividad.
     *
     * @param int $courseid
     * @param string $idnumber
     * @param bool $redirect
     * @return bool
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws exception\activity_exception
     * @throws exception\course_exception
     * @throws exception\product_exception
     * @throws exception\stdclass_transform_exception
     * @throws exception\user_exception
     */
    public static function check_activity_access_by_idnumber(int $courseid, string $idnumber, bool $redirect = true) {
        $activity = course::get_instance($courseid)->get_activity_by_idnumber($idnumber);
        return self::check_activity_access($courseid, $activity, $redirect);
    }

    /**
     * Verificar si el usuario logado puede acceder a la actividad.
     *
     * @param int|null $courseid
     * @param activity $activity
     * @param bool $redirect
     * @return bool
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws exception\course_exception
     * @throws exception\product_exception
     * @throws exception\stdclass_transform_exception
     * @throws exception\user_exception
     */
    public static function check_activity_access (?int $courseid, activity $activity, bool $redirect = true) {
        $currentuser = self::get_current_user($courseid);
        $course = app::get_course($courseid);

        // Si la actividad no tiene url no se puede permitir el acceso.
        // Esto puede pasar con las actividades de Moodle que aún no se han generado y por tanto no hay url a donde redirigir.
        if (!$activity->get_url($course)) {
            return false;
        }

        if ($currentuser->is_teacher() || is_siteadmin()) {
            return true;
        }

        /** @var url $urlhelper */
        $urlhelper = app::get_instance()->get(url::class);
        $access = $activity->can_student_access($courseid, $currentuser->get_id());
        if ($access === false && $redirect === true) {
            $url = $urlhelper->get_homework_url($courseid);
            redirect($url);
        }
        return $access;
    }

    /**
     * @param int $courseid
     * @param string $activityidnumber
     * @return bool
     * @throws \dml_exception
     */
    public static function check_activity_access_by_ccaa(int $courseid, string $activityidnumber): bool {
        global $DB;
        $codproduct = app::get_course($courseid)->get_codproduct();
        $productid = app::get_product($codproduct)->get_id();
        $course_ccaa = app::get_course($courseid)->get_ccaa();

        $records = $DB->get_records('npe_activity_ccaa',
            [
                'activityidnumber' => $activityidnumber,
                'productid' => $productid
            ]
        );

        // Si no hay restriccion, te dejo abrir el picto
        if (!$records) {
            return true;
        }

        // Si hay restricciones, comprobamos la comunidad
        foreach ($records as $record) {
            if ((int) $record->ccaaid === $course_ccaa) {
                return true;
            }
        }

        // En caso de haber registro y no coincidencia de comunidad...
        return false;
    }

    /**
     * Verifica si el estudiante puede acceder a la actividad.
     *
     * @param int $courseid
     * @param product_topic $topic
     * @param bool $redirect
     * @return bool
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws exception\course_exception
     * @throws exception\npe_exception
     * @throws exception\product_exception
     * @throws exception\stdclass_transform_exception
     * @throws exception\user_exception
     */
    public static function check_topic_access(int $courseid, product_topic $topic, bool $redirect = true) {
        $course = course::get_instance($courseid);
        $iseducamosuser = app::get_userfactory()->get_current_user()->get_user_data()->is_educamos_user();

        if (!$iseducamosuser && null === $course->get_creator()) { // Curso sin profe.
            return true;
        }

        if (!self::get_current_user()->is_student()) {
            return true;
        }

        $idnumber = ($iseducamosuser || null === $course->get_creator())
            ? $course->get_idnumber()
            : $course->get_creator()->get_user_data()->get_idnumber();

        $codcentro = ($iseducamosuser || null === $course->get_creator())
            ? app::get_userfactory()->get_current_user()->get_user_data()->get_department()
            : $course->get_creator()->get_user_data()->get_department();

        $course->get_product()->prepare_perso($idnumber, $codcentro, $courseid);
        $persovisible = $course->get_product()->get_perso_visible();

        $access = null === $persovisible || !in_array($topic->get_packerid(), $persovisible, true);
        if ($access === false && $redirect === true) {
            /** @var url $urlhelper */
            $urlhelper = \local_npe\app::get_instance()->get(url::class);
            $url = $urlhelper->get_course_url($courseid);
            redirect($url);
        }
        return $access;
    }

    /**
     * @param int|null $courseid
     * @return base\user\iuser
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws exception\course_exception
     * @throws exception\stdclass_transform_exception
     * @throws exception\user_exception
     */
    private static function get_current_user(?int $courseid = null) {
        /** @var user_factory $userfactory */
        $userfactory = app::get_instance()->get(user_factory::class);
        return $courseid === null ? $userfactory->get_current_user() : $userfactory->get_current_user_enroled($courseid);
    }

    /**
     * Verifica si puede acceder al producto.
     * En caso contrario se muestra al usuario la página evasm.
     *
     * @param string $codproduct
     * @param bool $redirect
     * @return bool
     * @throws \coding_exception
     * @throws \dml_exception
     * @throws \moodle_exception
     * @throws exception\course_exception
     * @throws exception\npe_exception
     * @throws exception\product_exception
     * @throws exception\stdclass_transform_exception
     * @throws exception\user_exception
     */
    public static function check_product_access(string $codproduct, bool $redirect = true) {
        $access = false;
        if (product::product_exist($codproduct)) {
            $access = self::get_current_user()->has_license($codproduct);
        }
        if ($access === false) {
            switch ($redirect) {
                case true:
                    $homeurl = app::get_url_helper()->get_home_url() ?? '/';
                    redirect($homeurl);
                    break;
                case false:
                    redirect(app::get_config()->get_instance_evasm_url());
                    break;
            }

        }
        return $access;
    }

    /**
     * @param $courseid
     * @param $userid
     * @return boolean
     */
    public static function check_activity_visibility($courseid, $activityid){
        $access = false;
        $activity = course::get_instance($courseid)->get_activity($activityid);
        if (!$activity) {
            redirect(app::get_url_helper()->get_course_url($courseid));
        }
        $currentuser = self::get_current_user($courseid);
        $currentuserVisibility = $currentuser->is_teacher() ? 'teacherVisibility': 'studentVisibility';
        if ($activity->get_activity_data()->get_visibility() === activity_core::ALL_VISIBILITY ||
            $activity->get_activity_data()->get_visibility() === $currentuserVisibility) {
            return true;
        }
        return $access;
    }
}

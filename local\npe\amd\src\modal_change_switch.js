import $ from 'jquery';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';
import EventListener from 'local_npe/event_listener';

const SELECTORS = {
    HIDE: '.close',
    FINISH: '[data-action="cancel"]',
    EXIT: '[data-action="exit"]'

};
const EVENTS = {
    CONFIRMSUBMIT: 'npe:confirm-change-switch'
};

let registered = false;

export default class ModalChangeSwitch extends Modal {

    static TYPE = 'local_npe/modal_change_switch';
    static TEMPLATE = 'local_npe/courses/modal_change_switch';

    constructor(root) {
        super(root);
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {
            $('body').removeClass('modal-open');
            $('.success-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
            EventListener.shoutEvent(EVENTS.CONFIRMSUBMIT, {change: '0'});
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {
            $('body').removeClass('modal-open');
            $('.success-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
            EventListener.shoutEvent(EVENTS.CONFIRMSUBMIT, {change: '0'});
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.EXIT, () => {
            $('body').removeClass('modal-open');
            $('.success-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
            EventListener.shoutEvent(EVENTS.CONFIRMSUBMIT, {change: '1'});
        });
    }
}

if (!registered) {
    ModalRegistry.register(ModalChangeSwitch.TYPE, ModalChangeSwitch, ModalChangeSwitch.TEMPLATE);
    registered = true;
}

{"version": 3, "file": "modal_change_switch.min.js", "sources": ["../src/modal_change_switch.js"], "sourcesContent": ["import $ from 'jquery';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\nimport EventListener from 'local_npe/event_listener';\n\nconst SELECTORS = {\n    HIDE: '.close',\n    FINISH: '[data-action=\"cancel\"]',\n    EXIT: '[data-action=\"exit\"]'\n\n};\nconst EVENTS = {\n    CONFIRMSUBMIT: 'npe:confirm-change-switch'\n};\n\nlet registered = false;\n\nexport default class ModalChangeSwitch extends Modal {\n\n    static TYPE = 'local_npe/modal_change_switch';\n    static TEMPLATE = 'local_npe/courses/modal_change_switch';\n\n    constructor(root) {\n        super(root);\n    }\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {\n            $('body').removeClass('modal-open');\n            $('.success-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n            EventListener.shoutEvent(EVENTS.CONFIRMSUBMIT, {change: '0'});\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {\n            $('body').removeClass('modal-open');\n            $('.success-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n            EventListener.shoutEvent(EVENTS.CONFIRMSUBMIT, {change: '0'});\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.EXIT, () => {\n            $('body').removeClass('modal-open');\n            $('.success-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n            EventListener.shoutEvent(EVENTS.CONFIRMSUBMIT, {change: '1'});\n        });\n    }\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalChangeSwitch.TYPE, ModalChangeSwitch, ModalChangeSwitch.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_defineProperty", "r", "t", "i", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_j<PERSON>y", "_custom_interaction_events", "_modal", "_modal_registry", "_event_listener", "SELECTORS", "EVENTS", "registered", "ModalChangeSwitch", "Modal", "constructor", "root", "super", "registerEventListeners", "this", "getModal", "on", "CustomEvents", "events", "activate", "$", "removeClass", "remove", "addClass", "EventListener", "shoutEvent", "change", "_exports", "ModalRegistry", "register", "TYPE", "TEMPLATE"], "mappings": "yPAIqD,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,gBAAAH,EAAAI,EAAAC,GAAAD,OAAAA,EAAA,SAAAC,GAAAC,IAAAA,EAAA,SAAAD,EAAAD,GAAAC,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAL,IAAAA,EAAAK,EAAAE,OAAAC,yBAAAR,EAAA,CAAA,IAAAM,EAAAN,EAAAS,KAAAJ,EAAAD,kCAAAE,EAAA,OAAAA,EAAAI,MAAAA,IAAAA,4EAAAN,EAAAO,OAAAC,QAAAP,EAAA,CAAAQ,CAAAR,EAAAC,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAQ,CAAAV,MAAAJ,EAAAe,OAAAC,eAAAhB,EAAAI,EAAAa,CAAAA,MAAAZ,EAAAa,YAAAC,EAAAA,cAAAC,EAAAA,cAAApB,EAAAI,GAAAC,EAAAL,CAAA,iFAJrDqB,QAAAtB,uBAAAsB,SACAC,2BAAAvB,uBAAAuB,4BACAC,OAAAxB,uBAAAwB,QACAC,gBAAAzB,uBAAAyB,iBACAC,gBAAA1B,uBAAA0B,iBAEA,MAAMC,eACI,SADJA,iBAEM,yBAFNA,eAGI,uBAGJC,qBACa,4BAGnB,IAAIC,YAAa,EAEF,MAAMC,0BAA0BC,OAAAA,QAK3CC,WAAAA,CAAYC,MACRC,MAAMD,KACV,CAEAE,sBAAAA,GACID,MAAMC,uBAAuBC,MAE7BA,KAAKC,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUd,kBAAkB,MAC/D,EAAAe,QAAAA,SAAE,QAAQC,YAAY,eACtB,EAAAD,iBAAE,kBAAkBE,UACpB,EAAAF,QAACvC,SAAC,mBAAmBwC,YAAY,QAAQE,SAAS,QAClDC,gBAAAA,QAAcC,WAAWnB,qBAAsB,CAACoB,OAAQ,KAAK,IAGjEZ,KAAKC,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUd,gBAAgB,MAC7D,EAAAe,QAAAA,SAAE,QAAQC,YAAY,eACtB,EAAAD,iBAAE,kBAAkBE,UACpB,EAAAF,QAACvC,SAAC,mBAAmBwC,YAAY,QAAQE,SAAS,QAClDC,gBAAAA,QAAcC,WAAWnB,qBAAsB,CAACoB,OAAQ,KAAK,IAGjEZ,KAAKC,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUd,gBAAgB,MAC7D,EAAAe,QAAAA,SAAE,QAAQC,YAAY,eACtB,EAAAD,iBAAE,kBAAkBE,UACpB,EAAAF,QAACvC,SAAC,mBAAmBwC,YAAY,QAAQE,SAAS,QAClDC,gBAAAA,QAAcC,WAAWnB,qBAAsB,CAACoB,OAAQ,KAAK,GAErE,EAMH,OALAC,SAAA9C,QAAA2B,kBAAA1B,gBAjCoB0B,kBAAiB,OAEpB,iCAA+B1B,gBAF5B0B,kBAAiB,WAGhB,yCAgCjBD,aACDqB,gBAAAA,QAAcC,SAASrB,kBAAkBsB,KAAMtB,kBAAmBA,kBAAkBuB,UACpFxB,YAAa,GAChBoB,SAAA9C,OAAA"}
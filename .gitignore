# This file specifies intentionally untracked files that all Moodle git
# repositories should ignore. It is recommended not to modify this file in your
# local clone. Instead, use .git/info/exclude and add new records there as
# needed.
#
# Example: if you deploy a contributed plugin mod/foobar into your site, put
# the following line into .git/info/exclude file in your Moodle clone:
# /mod/foobar/
#
# See gitignore(5) man page for more details
#

# Swap files (vim)
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]
# Temporary files including undo
*~
#
/config.php
/tags
/TAGS
/cscope.*
/.patches/
/.idea/
.phpstorm.*
!/.phpstorm.meta.php/
/.phpstorm.meta.php/*
!/.phpstorm.meta.php/di.php
/nbproject/
CVS
.DS_Store
/.settings/
/.project
/.buildpath
/.cache
.phpunit.result.cache
phpunit.xml
# Composer support. Do not ignore composer.json, or composer.lock. These should be shipped by us.
composer.phar
/vendor/
/behat.yml
*/yui/build/*/*-coverage.js
/lib/yuilib/*/build/*/*-coverage.js
# lib/yuilib/version/module/module-coverage.js
/lib/yuilib/*/*/*-coverage.js
atlassian-ide-plugin.xml
/node_modules/
/.vscode/
moodle-plugin-ci.phar
.eslintignore
.stylelintignore
/jsdoc
/admin/tool/componentlibrary/docs
/admin/tool/componentlibrary/hugo/site/data/my-index.json
.hugo_build.lock
phpcs.xml
jsconfig.json
UPGRADING-CURRENT.md
.sass-cache
*.orig
!.php_cs
#Ignoramos todo
*
!.gitignore
#Añado excepción composer
!composer.json
!composer.lock
!config_local.php
#############################################
# NPE
############################################
!admin/
!admin/tool/
!admin/tool/deletecourses/
!admin/tool/deletecourses/**
!admin/tool/heartbeat/
!admin/tool/heartbeat/**
!admin/cli
!admin/cli/*_tenant.php
!admin/cli/sm_add_redis_store.php
!config/
!config/**
config/config.yml
config/db.yml
config/ES_PRO.yml
config/MX_PRO.yml
!aws
!aws/**/**
!course
!course/
!course/lib.php
!course/format
!course/format/npe
!course/format/npe/
!course/format/npe/**/**
!local
!local/npe
!local/npe/**/**
!local/header_common/
!local/header_common/**/**
!local/externalconnect/
!local/externalconnect/**/**
!local/common
!local/common/**/**
!/report
!report/benchmark
!report/benchmark/**
!theme
!theme/npe
!theme/npe/**/**
!mod/
!mod/forum
!mod/forum/classes
!mod/forum/classes/local
!mod/forum/classes/local/exporters/
!mod/forum/classes/local/exporters/author.php
!mod/assign/
!mod/assign/feedback/
!mod/assign/feedback/editpdf/
!mod/assign/feedback/editpdf/classes/
!mod/assign/feedback/editpdf/classes/event/
!mod/assign/feedback/editpdf/classes/event/observer.php
!mod/assign/lib.php
!mod/assign/classes
!mod/assign/classes/dates.php
!/lib/
!lib/externallib.php
!lib/moodlelib.php
!auth/
!auth/oauth2/
!auth/oauth2/classes/
!auth/oauth2/classes/auth.php

!/favicon.ico
!/apple-touch-icon.png
!/apple-touch-icon-precomposed.png

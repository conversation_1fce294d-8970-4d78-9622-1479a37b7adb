// Variables NPE
$main-width: 1024px;
$margint20: 20;

// Fonts
$osregular: 'Work Sans Regular';
$ossemibold: 'Work Sans SemiBold';
$oslight: 'Work Sans Light';
$oslightitalic: 'Work Sans Light Italic';
$ossmcuadrado: 'SM Cuadrado Regular';
$npe-light: 300;
$npe-semibold: bold;

// Colors
$npe-bg-color: #fafafa;
$npe-dark-grey-bg: #555;
$npe-dark-grey-sep: #eee;
$npe-dark-grey: #666;
$npe-dark-supergrey: #444;
$npe-grey-alt: #ededed;
$npe-grey-alt-border: #e0e0e0;
$npe-grey-mid: #707070;
$npe-grey-light: #cccccc;
$npe-grey-light-alt: #767676;
$npe-grey-light-filters: #F2F2F2;
$npe-blue-active: #0058FF;
$npe-black: #000;
$npe-black-50: rgba(0, 0, 0, .5);
$npe-black-33: #00000033;
$npe-black-10: #00000010;
$npe-white: #fff;
$npe-white-01: #ffffff01;
$npe-white-10: #ffffff10;
$npe-green: #156906;
$npe-green-selected: #00A559;
$npe-green-principal: #1c8281;
$npe-green-bg: #F4FFFA;
$npe-light-green: #86FF86;
$npe-red: #B23232;
$npe-red-delay: #FF6767;
$npe-red-error: #CC0C1E;
$npe-pink-bg: #FFF2F2;
$npe-black-shadow-light: #0000001A;
$npe-background-color-classnotebook: #006E73;
$npe-red-pending: #CC0C1E;
$npe-black-shadow: #00000033;
$npe-blue-lic: #0054C6;
$npe-lic-premium: #D17900;
$npe-light-orange: #FDEAC2;
$npe-light-turquoise: #C2EAEA;
$npe-green-minimenu: #1c8281;
$npe-light-cyan: #F0F8FF;
$npe-light-blue: #C9E5FD;
$npe-backdrop: rgba(0, 0, 0, 0.4);
$npe-bg-close: #323333;

// General variable structure
//
// Variable format should follow the `$component-modifier-state-property` order.

$navbar-height-daa: 80px;
$navbar-sub-height: 40px;

// Options
//
// Quickly modify global styling by enabling or disabling optional features.

$enable-flex: false;
$enable-rounded: true;
$enable-shadows: false;
$enable-gradients: false;
$enable-transitions: false;
$enable-hover-media-query: false;
$enable-grid-classes: true;
$enable-print-styles: true;

// Spacing
//
// Control the default styling of most Bootstrap elements by modifying these
// variables. Mostly focused on spacing.
// You can add more entries to the $spacers map, should you need more variation.

$spacer: 0; // ALH: cambiado
$spacer-x: $spacer;
$spacer-y: $spacer;
$spacers: (
        0: (
                x: 0,
                y: 0
        ),
        1: (
                x: $spacer-x,
                y: $spacer-y
        ),
        2: (
                x: ($spacer-x * 1.5),
                y: ($spacer-y * 1.5)
        ),
        3: (
                x: ($spacer-x * 3),
                y: ($spacer-y * 3)
        )
);
$border-width: 1px;

// Body
//
// Settings for the `<body>` element.

$body-bg: $backg-generic; // ALH: modificado por DAA
$body-color: $gray-dark;

// Links
//
// Style anchor elements.

$link-color: $brand-primary;
$link-decoration: none;
$link-hover-color: darken($link-color, 15%);
$link-hover-decoration: underline;
// Grid columns
//
// Set the number of columns and specify the width of the gutters.

$grid-columns: 12;
$grid-gutter-width: 30px;

$fontdaa-generic: 'Open Sans', sans-serif;
$fontdaa-opensans: 'Open Sans', sans-serif;
$fontdaa-museosans: 'Museo Sans', sans-serif;

$font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
$font-family-serif: Georgia, "Times New Roman", Times, serif;
$font-family-monospace: Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
$font-family-base: $font-family-sans-serif;

$font-size-root: 16px;

$font-size-base: 1rem;
$font-size-lg: 1.25rem;
$font-size-sm: .875rem;
$font-size-xs: .75rem;

$line-height-base: 1.5;

$font-size-h1: 40px;
$font-size-h2: 36px;
$font-size-h3: 20px;
$font-size-h4: 1.5rem;
$font-size-h5: 16px;
$font-size-h6: 1rem;

$display1-size: 6rem;
$display2-size: 5.5rem;
$display3-size: 4.5rem;
$display4-size: 3.5rem;

$display1-weight: 300;
$display2-weight: 300;
$display3-weight: 300;
$display4-weight: 300;

$headings-margin-bottom: ($spacer / 2);
$headings-font-family: inherit;
$headings-font-weight: 500;
$headings-line-height: 1.1;
$headings-color: inherit;

$lead-font-size: 1.25rem;
$lead-font-weight: 300;

$small-font-size: 80%;

$text-muted: $gray-light;

$abbr-border-color: $gray-light;

$blockquote-small-color: $gray-light;
$blockquote-font-size: ($font-size-base * 1.25);
$blockquote-border-color: $gray-lighter;
$blockquote-border-width: .25rem;

$hr-border-color: rgba(0, 0, 0, .1);
$hr-border-width: $border-width;

$mark-padding: .2em;

$dt-font-weight: 700;

$kbd-box-shadow: inset 0 -.1rem 0 rgba(0, 0, 0, .25);
$nested-kbd-font-weight: bold;

$list-inline-padding: 5px;

// Components
//
// Define common padding and border radius sizes and more.

$line-height-lg: (4 / 3);
$line-height-sm: 1.5;

$border-radius: 5px;
$border-radius-lg: 10px;
$border-radius-sm: .2rem;
$border-radius-rounded: 50%;

$component-active-color: #fff;
$component-active-bg: $brand-primary;

$caret-width: .3em;
$caret-width-lg: $caret-width;

// Tables
//
// Customizes the `.table` component with basic values, each used across all table variations.

$table-cell-padding: .75rem;
$table-sm-cell-padding: .3rem;

$table-bg: transparent;
$table-bg-accent: rgba(0, 0, 0, .05);
$table-bg-hover: rgba(0, 0, 0, .075);
$table-bg-active: $table-bg-hover;

$table-border-width: $border-width;
$table-border-color: $gray-lighter;

// Buttons
//
// For each of Bootstrap's buttons, define text, background and border color.

$btn-padding-x: 1rem;
$btn-padding-y: .5rem;
$btn-line-height: 1.25;
$btn-font-weight: normal;
$btn-box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
$btn-active-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);

$btn-primary-color: #fff;
$btn-primary-bg: $brand-primary;
$btn-primary-border: $btn-primary-bg;

$btn-secondary-color: $gray-dark;
$btn-secondary-bg: #fff;
$btn-secondary-border: #ccc;

$btn-info-color: #fff;
$btn-info-bg: $brand-info;
$btn-info-border: $btn-info-bg;

$btn-success-color: #fff;
$btn-success-bg: $brand-success;
$btn-success-border: $btn-success-bg;

$btn-warning-color: #fff;
$btn-warning-bg: $brand-warning;
$btn-warning-border: $btn-warning-bg;

$btn-danger-color: #fff;
$btn-danger-bg: $brand-danger;
$btn-danger-border: $btn-danger-bg;

$btn-link-disabled-color: $gray-light;

$btn-padding-x-sm: .5rem;
$btn-padding-y-sm: .25rem;

$btn-padding-x-lg: 1.5rem;
$btn-padding-y-lg: .75rem;

$btn-block-spacing-y: .5rem;
$btn-toolbar-margin: .5rem;

// Allows for customizing button radius independently from global border radius
$btn-border-radius: $border-radius;
$btn-border-radius-lg: $border-radius-lg;
$btn-border-radius-sm: $border-radius-sm;

// Forms

$input-padding-x: .75rem;
$input-padding-y: .5rem;
$input-line-height: 1.25;
$input-bg: #fff;
$input-bg-disabled: $gray-lighter;
$input-color: $gray;
$input-border-color: rgba(0, 0, 0, .15);
$input-btn-border-width: $border-width; // For form controls and buttons
$input-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
$input-border-radius: $border-radius;
$input-border-radius-lg: $border-radius-lg;
$input-border-radius-sm: $border-radius-sm;
$input-bg-focus: $input-bg;
$input-border-focus: #66afe9;
$input-box-shadow-focus: rgba(102, 175, 233, .6);
$input-color-focus: $input-color;
$input-color-placeholder: #999;
$input-padding-x-sm: .5rem;
$input-padding-y-sm: .25rem;
$input-padding-x-lg: 1.5rem;
$input-padding-y-lg: .75rem;
$input-height: (($font-size-base * $line-height-base) + ($input-padding-y * 2));
$input-height-lg: (($font-size-lg * $line-height-lg) + ($input-padding-y-lg * 2));
$input-height-sm: (($font-size-sm * $line-height-sm) + ($input-padding-y-sm * 2));

$form-group-margin-bottom: $spacer-y;

$input-group-addon-bg: $gray-lighter;
$input-group-addon-border-color: $input-border-color;

$cursor-disabled: not-allowed;
$custom-control-gutter: 1.5rem;
$custom-control-spacer-x: 1rem;
$custom-control-spacer-y: .25rem;
$custom-control-indicator-size: 1rem;
$custom-control-indicator-bg: #ddd;
$custom-control-indicator-bg-size: 50% 50%;
$custom-control-indicator-box-shadow: inset 0 .25rem .25rem rgba(0, 0, 0, .1);
$custom-control-disabled-cursor: $cursor-disabled;
$custom-control-disabled-indicator-bg: #eee;
$custom-control-disabled-description-color: #767676;
$custom-control-checked-indicator-color: #fff;
$custom-control-checked-indicator-bg: #0074d9;
$custom-control-checked-indicator-box-shadow: none;
$custom-control-focus-indicator-box-shadow: 0 0 0 .075rem #fff, 0 0 0 .2rem #0074d9;
$custom-control-active-indicator-color: #fff;
$custom-control-active-indicator-bg: #84c6ff;
$custom-control-active-indicator-box-shadow: none;
$custom-checkbox-radius: $border-radius;
$custom-checkbox-checked-icon: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E");
$custom-checkbox-indeterminate-bg: #0074d9;
$custom-checkbox-indeterminate-icon: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='%23fff' d='M0 2h4'/%3E%3C/svg%3E");
$custom-checkbox-indeterminate-box-shadow: none;
$custom-radio-radius: 50%;
$custom-radio-checked-icon: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23fff'/%3E%3C/svg%3E");
$custom-select-padding-x: .75rem;
$custom-select-padding-y: .375rem;
// Extra padding to account for the presence of the background-image based indicator
$custom-select-indicator-padding: 1rem;
$custom-select-color: $input-color;
$custom-select-disabled-color: $gray-light;
$custom-select-bg: #fff;
$custom-select-disabled-bg: $gray-lighter;
$custom-select-bg-size: 8px 10px; // In pixels because image dimensions
$custom-select-indicator: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23333' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E");
$custom-select-border-width: $input-btn-border-width;
$custom-select-border-color: $input-border-color;
$custom-select-border-radius: $border-radius;
$custom-select-focus-border-color: #51a7e8;
$custom-select-focus-box-shadow: inset 0 1px 2px rgba(0, 0, 0, .075), 0 0 5px rgba(81, 167, 232, .5);
$custom-select-sm-padding-y: .2rem;
$custom-select-sm-font-size: 75%;
$custom-file-height: 2.5rem;
$custom-file-width: 14rem;
$custom-file-focus-box-shadow: 0 0 0 .075rem #fff, 0 0 0 .2rem #0074d9;
$custom-file-padding-x: .5rem;
$custom-file-padding-y: 1rem;
$custom-file-line-height: 1.5;
$custom-file-color: #555;
$custom-file-bg: #fff;
$custom-file-border-width: $border-width;
$custom-file-border-color: #ddd;
$custom-file-border-radius: $border-radius;
$custom-file-box-shadow: inset 0 .2rem .4rem rgba(0, 0, 0, .05);
$custom-file-button-color: $custom-file-color;
$custom-file-button-bg: #eee;
$custom-file-text: (
        placeholder: (
                en: "Choose file..."
        ),
        button-label: (
                en: "Browse"
        )
);

// Form validation icons
$form-icon-success: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%235cb85c' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3E%3C/svg%3E");
$form-icon-warning: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23f0ad4e' d='M4.4 5.324h-.8v-2.46h.8zm0 1.42h-.8V5.89h.8zM3.76.63L.04 7.075c-.115.2.016.425.26.426h7.397c.242 0 .372-.226.258-.426C6.726 4.924 5.47 2.79 4.253.63c-.113-.174-.39-.174-.494 0z'/%3E%3C/svg%3E");
$form-icon-danger: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23d9534f' viewBox='-2 -2 7 7'%3E%3Cpath stroke='%23d9534f' d='M0 0l3 3m0-3L0 3'/%3E%3Ccircle r='.5'/%3E%3Ccircle cx='3' r='.5'/%3E%3Ccircle cy='3' r='.5'/%3E%3Ccircle cx='3' cy='3' r='.5'/%3E%3C/svg%3E");

// Dropdowns
//
// Dropdown menu container and contents.

$dropdown-min-width: 160px;
$dropdown-padding-y: 5px;
$dropdown-margin-top: 2px;
$dropdown-bg: #fff;
$dropdown-border-color: rgba(0, 0, 0, .15);
$dropdown-border-width: $border-width;
$dropdown-divider-bg: #e5e5e5;
$dropdown-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
$dropdown-link-color: $gray-dark;
$dropdown-link-hover-color: darken($gray-dark, 5%);
$dropdown-link-hover-bg: $backg-generic;
$dropdown-link-active-color: $component-active-color;
$dropdown-link-active-bg: $component-active-bg;
$dropdown-link-disabled-color: $gray-light;
$dropdown-item-padding-x: 20px;
$dropdown-header-color: $gray-light;

// Z-index master list
//
// Warning: Avoid customizing these values. They're used for a bird's eye view
// of components dependent on the z-axis and are designed to all work together.

$zindex-dropdown-backdrop: 990;
$zindex-navbar: 1000;
$zindex-dropdown: 1000;
$zindex-popover: 1060;
$zindex-tooltip: 1070;
$zindex-navbar-fixed: 1030;
$zindex-navbar-sticky: 1030;
$zindex-modal-bg: 1040;
$zindex-modal: 1050;

// Navbar

$navbar-border-radius: $border-radius;
$navbar-padding-x: $spacer;
$navbar-padding-y: ($spacer / 2);
$navbar-brand-padding-y: .25rem;
$navbar-dark-color: rgba(255, 255, 255, .5);
$navbar-dark-hover-color: rgba(255, 255, 255, .75);
$navbar-dark-active-color: rgba(255, 255, 255, 1);
$navbar-dark-disabled-color: rgba(255, 255, 255, .25);
$navbar-light-color: rgba(0, 0, 0, .3);
$navbar-light-hover-color: rgba(0, 0, 0, .6);
$navbar-light-active-color: rgba(0, 0, 0, .8);
$navbar-light-disabled-color: rgba(0, 0, 0, .15);

// Navs

$nav-item-margin: .2rem;
$nav-item-inline-spacer: 1rem;
$nav-link-padding: .5em 1em;
$nav-link-hover-bg: $gray-lighter;
$nav-disabled-link-color: $gray-light;
$nav-disabled-link-hover-color: $gray-light;
$nav-disabled-link-hover-bg: transparent;
$nav-tabs-border-color: #ddd;
$nav-tabs-border-width: $border-width;
$nav-tabs-border-radius: $border-radius;
$nav-tabs-link-hover-border-color: $gray-lighter;
$nav-tabs-active-link-hover-color: $gray;
$nav-tabs-active-link-hover-bg: $body-bg;
$nav-tabs-active-link-hover-border-color: #ddd;
$nav-tabs-justified-link-border-color: #ddd;
$nav-tabs-justified-active-link-border-color: $body-bg;
$nav-pills-border-radius: $border-radius;
$nav-pills-active-link-color: $component-active-color;
$nav-pills-active-link-bg: $component-active-bg;

// Pagination

$pagination-padding-x: .75rem;
$pagination-padding-y: .5rem;
$pagination-padding-x-sm: .75rem;
$pagination-padding-y-sm: .275rem;
$pagination-padding-x-lg: 1.5rem;
$pagination-padding-y-lg: .75rem;
$pagination-color: $link-color;
$pagination-bg: #fff;
$pagination-border-width: $border-width;
$pagination-border-color: #ddd;
$pagination-hover-color: $link-hover-color;
$pagination-hover-bg: $gray-lighter;
$pagination-hover-border: #ddd;
$pagination-active-color: #fff;
$pagination-active-bg: $brand-primary;
$pagination-active-border: $brand-primary;
$pagination-disabled-color: $gray-light;
$pagination-disabled-bg: #fff;
$pagination-disabled-border: #ddd;

// Jumbotron

$jumbotron-padding: 2rem;
$jumbotron-bg: $gray-lighter;

// Form states and alerts
//
// Define colors for form feedback states and, by default, alerts.

$state-success-text: #3c763d;
$state-success-bg: #dff0d8;
$state-success-border: darken($state-success-bg, 5%);
$state-info-text: #31708f;
$state-info-bg: #d9edf7;
$state-info-border: darken($state-info-bg, 7%);
$state-warning-text: #8a6d3b;
$state-warning-bg: #fcf8e3;
$mark-bg: $state-warning-bg;
$state-warning-border: darken($state-warning-bg, 5%);
$state-danger-text: #a94442;
$state-danger-bg: #f2dede;
$state-danger-border: darken($state-danger-bg, 5%);

// Cards
$card-spacer-x: 0; // ALH: modificado para DAA valor original 1.25rem
$card-spacer-y: .75rem;
$card-border-width: 0; // ALH: modificado para DAA valor original 1px
$card-border-radius: 0; // ALH: modificado para DAA valor original $border-radius
$card-border-color: rgba(0, 0, 0, .125);
$card-border-radius-inner: $card-border-radius;
$card-cap-bg: $backg-generic;
$card-bg: #fff;
$card-link-hover-color: #fff;
$card-img-overlay-padding: 1.25rem;
$card-deck-margin: .625rem;
$card-columns-sm-up-column-gap: 1.25rem;

// Tooltips

$tooltip-max-width: 200px;
$tooltip-color: #fff;
$tooltip-bg: #000;
$tooltip-opacity: .9;
$tooltip-padding-y: 3px;
$tooltip-padding-x: 8px;
$tooltip-margin: 3px;
$tooltip-arrow-width: 5px;
$tooltip-arrow-color: $tooltip-bg;

// Popovers

$popover-inner-padding: 1px;
$popover-bg: #fff;
$popover-max-width: 276px;
$popover-border-width: $border-width;
$popover-border-color: rgba(0, 0, 0, .2);
$popover-box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
$popover-title-bg: darken($popover-bg, 3%);
$popover-title-padding-x: 14px;
$popover-title-padding-y: 8px;
$popover-content-padding-x: 14px;
$popover-content-padding-y: 9px;
$popover-arrow-width: 10px;
$popover-arrow-color: $popover-bg;
$popover-arrow-outer-width: ($popover-arrow-width + 1px);
$popover-arrow-outer-color: fade-in($popover-border-color, .05);

// Tags

$tag-default-bg: $gray-light;
$tag-primary-bg: $brand-primary;
$tag-success-bg: $brand-success;
$tag-info-bg: $brand-info;
$tag-warning-bg: $brand-warning;
$tag-danger-bg: $brand-danger;
$tag-color: #fff;
$tag-link-hover-color: #fff;
$tag-font-size: 75%;
$tag-font-weight: bold;
$tag-padding-x: .4em;
$tag-padding-y: .25em;
$tag-pill-padding-x: .6em;
// Use a higher than normal value to ensure completely rounded edges when
// customizing padding or font-size on labels.
$tag-pill-border-radius: 10rem;

// Modals

// Padding applied to the modal body
$modal-inner-padding: 15px;
$modal-dialog-margin: 10px;
$modal-dialog-sm-up-margin-y: 30px;
$modal-title-padding: 15px;
$modal-title-line-height: $line-height-base;
$modal-content-bg: #fff;
$modal-content-border-color: rgba(0, 0, 0, .2);
$modal-content-border-width: $border-width;
$modal-content-xs-box-shadow: 0 3px 9px rgba(0, 0, 0, .5);
$modal-content-sm-up-box-shadow: 0 5px 15px rgba(0, 0, 0, .5);
$modal-backdrop-bg: #000;
$modal-backdrop-opacity: .5;
$modal-header-border-color: #e5e5e5;
$modal-footer-border-color: $modal-header-border-color;
$modal-header-border-width: $modal-content-border-width;
$modal-footer-border-width: $modal-header-border-width;
$modal-lg: 900px;
$modal-md: 600px;
$modal-sm: 300px;

// Alerts
//
// Define alert colors, border radius, and padding.

$alert-padding: 1rem;
$alert-border-radius: $border-radius;
$alert-link-font-weight: bold;
$alert-border-width: $border-width;
$alert-success-bg: $state-success-bg;
$alert-success-text: $state-success-text;
$alert-success-border: $state-success-border;
$alert-info-bg: $state-info-bg;
$alert-info-text: $state-info-text;
$alert-info-border: $state-info-border;
$alert-warning-bg: $state-warning-bg;
$alert-warning-text: $state-warning-text;
$alert-warning-border: $state-warning-border;
$alert-danger-bg: $state-danger-bg;
$alert-danger-text: $state-danger-text;
$alert-danger-border: $state-danger-border;

// Progress bars

$progress-bg: #eee;
$progress-bar-color: #0074d9;
$progress-border-radius: $border-radius;
$progress-box-shadow: inset 0 .1rem .1rem rgba(0, 0, 0, .1);
$progress-bar-success-bg: $brand-success;
$progress-bar-warning-bg: $brand-warning;
$progress-bar-danger-bg: $brand-danger;
$progress-bar-info-bg: $brand-info;

// List group

$list-group-bg: #fff;
$list-group-border-color: #ddd;
$list-group-border-width: $border-width;
$list-group-border-radius: $border-radius;
$list-group-hover-bg: $backg-generic;
$list-group-active-color: $component-active-color;
$list-group-active-bg: $component-active-bg;
$list-group-active-border: $list-group-active-bg;
$list-group-active-text-color: lighten($list-group-active-bg, 40%);
$list-group-disabled-color: $gray-light;
$list-group-disabled-bg: $gray-lighter;
$list-group-disabled-text-color: $list-group-disabled-color;
$list-group-link-color: #555;
$list-group-link-hover-color: $list-group-link-color;
$list-group-link-heading-color: #333;
$list-group-item-padding-x: 1.25rem;
$list-group-item-padding-y: .75rem;
$list-group-item-heading-margin-bottom: 5px;

// Image thumbnails

$thumbnail-padding: .25rem;
$thumbnail-bg: $body-bg;
$thumbnail-border-width: $border-width;
$thumbnail-border-color: #ddd;
$thumbnail-border-radius: $border-radius;
$thumbnail-box-shadow: 0 1px 2px rgba(0, 0, 0, .075);

// Figures

$figure-caption-font-size: 90%;

// Breadcrumbs

$breadcrumb-padding-y: .75rem;
$breadcrumb-padding-x: 1rem;
$breadcrumb-item-padding: .5rem;
$breadcrumb-bg: $gray-lighter;
$breadcrumb-divider-color: $gray-light;
$breadcrumb-active-color: $gray-light;
$breadcrumb-divider: "/";

// Media objects

$media-margin-top: 15px;
$media-heading-margin-bottom: 5px;
$media-alignment-padding-x: 10px;

// Carousel

$carousel-text-shadow: 0 1px 2px rgba(0, 0, 0, .6);
$carousel-control-color: #fff;
$carousel-control-width: 15%;
$carousel-control-sm-up-size: 30px;
$carousel-control-opacity: .5;
$carousel-control-font-size: 20px;
$carousel-indicators-width: 60%;
$carousel-indicator-size: 10px;
$carousel-indicator-active-size: 12px;
$carousel-indicator-active-bg: #fff;
$carousel-indicator-border-color: #fff;
$carousel-caption-width: 70%;
$carousel-caption-sm-up-width: 60%;
$carousel-caption-color: #fff;
$carousel-icon-width: 20px;

// Close

$close-font-weight: bold;
$close-color: #000;
$close-text-shadow: 0 1px 0 #fff;

// Code

$code-font-size: 90%;
$code-padding-x: .4rem;
$code-padding-y: .2rem;
$code-color: #bd4147;
$code-bg: #f7f7f9;

$kbd-color: #fff;
$kbd-bg: #333;

$pre-bg: #f7f7f9;
$pre-color: $gray-dark;
$pre-border-color: #ccc;
$pre-scrollable-max-height: 340px;

$generic-width: 960px;
$generic-width-940: 940px;


$fontdaa-size-xs: 14px;
$fontdaa-size-sm: 18px;
$fontdaa-size-md: 24px;
$fontdaa-size-lg: 36px;
$fontdaa-size-middle: 30px;

// Mods
$pikoona: #5290b2;
$cms: #fd7500;
$analogic: #fdf3be;
$collaborative: #c6b1ff;
$resourcevideo: #f59494;
$resourceresource: #7ba8ff;
$mindmapboard: #ffc6b0;
$mindmapbrainstorming: #7ab100;
$mindmapmindmap: #e573c5;
$gallery: #d757a5;
$glossary: #99dccb;
$scorm: #ff6f00;
$url: #ff9cd8;
$audio: #74d36c;
$imagen: #c6b1ff;
$forum: #a7ce04;
$assign: #23b58e;
$quiz: #fe740f;
$rubric: #5dc172;
$portfolio: #2c9fa3;
$visor: #aa4481;
$desafios: #29c2e6;

// Commons
$fixed-header-y: 0;

// Media Query Breakpoints
$media-full: 1441px;
$media-extra-large: 1367px;
$media-large: 1281px;
$media-medium: 1025px;
$media-small: 800px;
$media-extra-small: 0;

define("local_npe/modal_linking_ccaa_educamos",["exports","jquery","core/custom_interaction_events","core/modal","core/modal_registry"],(function(_exports,_jquery,_custom_interaction_events,_modal,_modal_registry){function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _classPrivateFieldInitSpec(e,t,a){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,a)}function _defineProperty(e,r,t){return(r=function(t){var i=function(t,r){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var i=e.call(t,r||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==typeof i?i:i+""}(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function _classPrivateFieldSet(s,a,r){return s.set(_assertClassBrand(s,a),r),r}function _assertClassBrand(e,t,n){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_custom_interaction_events=_interopRequireDefault(_custom_interaction_events),_modal=_interopRequireDefault(_modal),_modal_registry=_interopRequireDefault(_modal_registry);const SELECTORS_CONFIRM='[data-action="confirm"]',SELECTORS_CLOSE='[data-region="equis"]',SELECTORS_CCAAQUEESTION="#caaquestion",SELECTORS_CCAAINFO="#ccaainfo";let registered=!1;var _textccaainfo=new WeakMap;class ModalLinkCCAAEducamos extends _modal.default{constructor(root){super(root),_classPrivateFieldInitSpec(this,_textccaainfo,!0)}setData(){}registerEventListeners(){super.registerEventListeners(this),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_CCAAQUEESTION,(()=>{this.showtext()})),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_CLOSE,(()=>{this.getRoot().removeClass("show"),(0,_jquery.default)("body").removeClass("modal-open"),(0,_jquery.default)(".success-modal").remove(),(0,_jquery.default)(".modal-backdrop").removeClass("show").addClass("hide")})),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_CONFIRM,(()=>{this.getRoot().removeClass("show"),(0,_jquery.default)("body").removeClass("modal-open"),(0,_jquery.default)(".success-modal").remove(),(0,_jquery.default)(".modal-backdrop").removeClass("show").addClass("hide")}))}showtext(){var s,a;a=this,(s=_textccaainfo).get(_assertClassBrand(s,a))?((0,_jquery.default)(SELECTORS_CCAAINFO).show(),_classPrivateFieldSet(_textccaainfo,this,!1)):((0,_jquery.default)(SELECTORS_CCAAINFO).hide(),_classPrivateFieldSet(_textccaainfo,this,!0))}}return _exports.default=ModalLinkCCAAEducamos,_defineProperty(ModalLinkCCAAEducamos,"TYPE","local_npe/modal_link_ccaa_educamos"),_defineProperty(ModalLinkCCAAEducamos,"TEMPLATE","local_npe/courses/modal_linking_ccaa_educamos"),registered||(_modal_registry.default.register(ModalLinkCCAAEducamos.TYPE,ModalLinkCCAAEducamos,ModalLinkCCAAEducamos.TEMPLATE),registered=!0),_exports.default}));

//# sourceMappingURL=modal_linking_ccaa_educamos.min.js.map
{"version": 3, "file": "modal_notice_new_course5.min.js", "sources": ["../src/modal_notice_new_course5.js"], "sourcesContent": ["import $ from 'jquery';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\n\nconst SELECTORS = {\n    FINISH: '[data-action=\"finish\"]'\n};\n\nlet registered = false;\n\nexport default class ModalNoticeNewCourse5 extends Modal {\n\n    static TYPE = 'local_npe/modal_notice_new_course_5';\n    static TEMPLATE = 'local_npe/noticenewcourse/noticenewcourse_modal_infobartooltip';\n\n    constructor(root) {\n        super(root);\n    }\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $('.notice-new-course-info-modal').remove();\n            $('.modal-backdrop').removeClass('show').addClass('hide');\n        });\n    }\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalNoticeNewCourse5.TYPE, ModalNoticeNewCourse5, ModalNoticeNewCourse5.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_defineProperty", "r", "t", "i", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_j<PERSON>y", "_custom_interaction_events", "_modal", "_modal_registry", "SELECTORS", "registered", "ModalNoticeNewCourse5", "Modal", "constructor", "root", "super", "registerEventListeners", "this", "getModal", "on", "CustomEvents", "events", "activate", "getRoot", "removeClass", "$", "remove", "addClass", "_exports", "ModalRegistry", "register", "TYPE", "TEMPLATE"], "mappings": "mNAGgD,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,gBAAAH,EAAAI,EAAAC,GAAAD,OAAAA,EAAA,SAAAC,GAAAC,IAAAA,EAAA,SAAAD,EAAAD,GAAAC,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAL,IAAAA,EAAAK,EAAAE,OAAAC,yBAAAR,EAAA,CAAA,IAAAM,EAAAN,EAAAS,KAAAJ,EAAAD,kCAAAE,EAAA,OAAAA,EAAAI,MAAAA,IAAAA,4EAAAN,EAAAO,OAAAC,QAAAP,EAAA,CAAAQ,CAAAR,EAAAC,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAQ,CAAAV,MAAAJ,EAAAe,OAAAC,eAAAhB,EAAAI,EAAAa,CAAAA,MAAAZ,EAAAa,YAAAC,EAAAA,cAAAC,EAAAA,cAAApB,EAAAI,GAAAC,EAAAL,CAAA,iFAHhDqB,QAAAtB,uBAAAsB,SACAC,2BAAAvB,uBAAAuB,4BACAC,OAAAxB,uBAAAwB,QACAC,gBAAAzB,uBAAAyB,iBAEA,MAAMC,iBACM,yBAGZ,IAAIC,YAAa,EAEF,MAAMC,8BAA8BC,OAAAA,QAK/CC,WAAAA,CAAYC,MACRC,MAAMD,KACV,CAEAE,sBAAAA,GACID,MAAMC,uBAAuBC,MAE7BA,KAAKC,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAUb,kBAAkB,KAC/DQ,KAAKM,UAAUC,YAAY,SAC3B,EAAAC,QAAAA,SAAE,QAAQD,YAAY,eACtB,EAAAC,iBAAE,iCAAiCC,UACnC,EAAAD,QAACvC,SAAC,mBAAmBsC,YAAY,QAAQG,SAAS,OAAO,GAEjE,EAMH,OALAC,SAAA1C,QAAAyB,sBAAAxB,gBAnBoBwB,sBAAqB,OAExB,uCAAqCxB,gBAFlCwB,sBAAqB,WAGpB,kEAkBjBD,aACDmB,gBAAAA,QAAcC,SAASnB,sBAAsBoB,KAAMpB,sBAAuBA,sBAAsBqB,UAChGtB,YAAa,GAChBkB,SAAA1C,OAAA"}
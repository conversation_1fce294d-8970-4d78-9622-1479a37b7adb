import $ from 'jquery';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';

const SELECTORS = {
    PREVMODAL_BUTTON: '[data-action="prev-modal"]',
    FINISH: '[data-action="finish"]'
};

let registered = false;

export default class ModalNoticeNewCourse3 extends Modal {

    static TYPE = 'local_npe/modal_notice_new_course_3';
    static TEMPLATE = 'local_npe/noticenewcourse/noticenewcourse_modal03_manag';

    constructor(root) {
        super(root);
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.PREVMODAL_BUTTON, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.notice-new-course-manag-modal').removeClass('show').addClass('hide');
            $('.modal-backdrop').removeClass('show').addClass('hide');
            $('.notice-new-course-info-modal').removeClass('hide').addClass('show');
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.FINISH, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.notice-new-course-manag-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
            location.reload();
        });
    }
}

if (!registered) {
    ModalRegistry.register(ModalNoticeNewCourse3.TYPE, ModalNoticeNewCourse3, ModalNoticeNewCourse3.TEMPLATE);
    registered = true;
}

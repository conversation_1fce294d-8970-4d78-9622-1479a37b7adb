/* eslint-disable complexity */
define(['jquery', 'jqueryui', 'local_npe/datepicker', 'local_npe/event_listener'],
    function ($) {

        var filtersArray = {};
        let SELECTORS = {
            ROOT: '.myassignments',
            HEADERMENU: '.header-menu',
            HEADERBAR: '#nd_header_wrapper',
            MEGAMENUMOBILE: '.button-mobile-megamenu',
            SEARCH: '#search',
            CLEAN: '.clean',
            CLEANALL: '.clean-filters',
            DELETEFILTERS: '.delete-filters',
            FILTER: '.assignments-filter',
            FILTERCONTAINER: '.assignments-filter-container',
            FILTERCONTAINERITEM: '.assignments-filter-container .assignments-filter-selected',
            FILTERCLOSE: '#filterButton .close',
            OPACITYLAYER: '.assignments-filter-opacity-layer',
            CARD: '.card-header h5',
            FILTERCHECKBOX: '.custom_checkbox input',
            FILTERZONE: '.filters-zone',
            FILTERZONEITEM: '.filters-zone .assignments-filter-selected',
            REMOVEFILTER: '.clean-filter-selected',
            ACTIVITYLIST: '.activities-list',
            SHOWMOREITEMS: '.showmoreitems',
            SHOWLESSITEMS: '.showlessitems',
            FILTERTITLE: '.filter-title',
            ACTIVITIESCONTAINER: '.activities-container.ac4filters',
            SIDEMENUITEM: '.sidebar-menu a',
            SUBCATEGORYINPUT: '.cc-subcat input',
            SUBCATEGORY: '.subcat',
            SUBCATEGORYRESTORE: '.cc-subcat i'
        };

        /**
         * @param {String} filter_topics
         * @param {String} filter_categories
         */
        function ManageAssignmentsFilter(filter_topics, filter_categories) {
            ManageAssignmentsFilter.prototype.constructor = ManageAssignmentsFilter;
            ManageAssignmentsFilter.prototype.root = null;
            ManageAssignmentsFilter.prototype.filter_topics = filter_topics;
            ManageAssignmentsFilter.prototype.filter_categories = filter_categories;

            this.init();
        }

        ManageAssignmentsFilter.prototype.init = function () {
            var that = this;
            this.root = $(SELECTORS.ROOT);
            this.searchParams = new URLSearchParams(window.location.search);
            this.sectionSelected = that.defaultSection();
            this.filtersArray = that.prepareFilters();
            this.activitiesCounter = 0;
            this.mainFilter = [];
            this.filterController = [];
            this.filterControllerClicks = {};
            this.sesskeyCourseid = '';
            let urlSesskey = $('#logout_link').attr('href');
            if (urlSesskey){
                this.sessionCheck(urlSesskey);

            }
            this.categoryDropValue = '';
            this.categoryDropValueReload = '';
            this.subCatAdded = false;
            this.subCatDelete = false;
            that.subCatDeleted = false;

            that.resetControlller = false;
            that.toggleDropdownFilter();
            that.chooseFilters();
            that.checkFilter();
            that.openFilters();
            that.changeSection();
            this.subFilterManagement();

            this.filtersStorage = localStorage.getItem(this.sesskeyCourseid) ?? '{}';

            if (this.filtersStorage !== null && this.filtersStorage.length !== 0 && this.filtersStorage !== '{}') {
                that.filterControllerClicks = JSON.parse(this.filtersStorage);
            }

            // Eliminar filtro de Categoría.
            $('.filtercategory .clean-filter-selected').click(function () {
                var categoryid = $(this).data('categoryid');
                let index = that.filter_categories.indexOf(categoryid);
                if (index > -1) {
                    that.filter_categories.splice(index, 1);
                }
                $(this).parent().remove();
                that.filters();
            });

            // Eliminar filtro de Unidad/Tema.
            $('.filtertopic .clean-filter-selected').click(function () {
                let topicid = $(this).data('topicid');
                let index = that.filter_topics.indexOf(topicid);
                if (index > -1) {
                    that.filter_topics.splice(index, 1);
                }
                $(this).parent().remove();
                that.filters();
            });

            // Eliminar filtro de actividades para asignar.
            $('.filtertoassign .clean-filter-selected').click(function () {
                that.filter_toassign = 0;
                $(this).parent().remove();
                that.filters();
            });

            window.onload = () => {
                if (!that.getFiltersFromURL()) {
                    that.cleanAllFilters();
                    that.applyFiltersSaved();
                }
            };

            that.manageActivities();
        };
        ManageAssignmentsFilter.prototype.sessionCheck = function (urlSesskey) {
            let sesskey = urlSesskey.substring(urlSesskey.lastIndexOf('=') + 1);
            let courseid = this.searchParams.get('courseid');
            this.sesskeyCourseid = sesskey + '-' + courseid;
            // Si el usuario esta en una nueva sesion o es un usuario diferente, borramos los datos anteriores
            if (null === localStorage.getItem(this.sesskeyCourseid)) {
                localStorage.clear();
            }
        };

        ManageAssignmentsFilter.prototype.filters = function () {
            var that = this;

            // Modificación: Limitar el alcance del filtro a la sección actual
            $('.' + that.sectionSelected).find('.activity-item').each(function () {
                let topicid = $(this).data('sequenceid');
                let categoryid = $(this).data('categoryid');
                let toassign = $(this).data('toassign');

                // Extracción de la información.
                let bytype = $(this).data('type');
                let bydelivery = $(this).data('recommendedgroupingid');
                let bydificulty = $(this).data('difficultylevelid');
                let bykeyevidence = $(this).data('keyevidenceid');
                let byblock = $(this).data('blockid');
                let bycompetence = $(this).data('competenceid');
                let bycriterion = $(this).data('criterionid');
                let bytheme = $(this).data('themeid');
                let bytransversekey = $(this).data('transversekeyid');
                let byskills = $(this).data('skillsid');
                let bypedagogicalpurposes = $(this).data('pedagogicalpurposesid');
                let byassessment = $(this).data('assessmentid');
                let bylearninglevel = $(this).data('learninglevelid');
                let bytrackingactivitiesid = $(this).data('trackingactivitiesid');
                let bychallengesid = $(this).data('challengesid');
                let byincontextid = $(this).data('incontextid');
                let bytypeofactivityid = $(this).data('typeofactivityid');
                let bypresentationresourcesid = $(this).data('presentationresourcesid');
                let bysubcategoryid = $(this).data('subcategoryid');


                // Control.
                let hidebytype = false;
                let hidebydelivery = false;
                let hidebysequence = false; // Añadir la extracción para hacerlo funcionar.
                let hidebydificulty = false;
                let hidebykeyevidence = false;
                let hidebycompetence = false;
                let hidebycriterion = false;
                let hidebytheme = false;
                let hidebytransversekey = false;
                let hidebyskills = false;
                let hidebypedagogicalpurposes = false;
                let hidebyassessment = false;
                let hidebylearninglevel = false;
                let hidebyblock = false;
                let hidebytoassign = (that.filter_toassign && !toassign);
                let hidebytrackingactivitiesid = false;
                let hidebychallengesid = false;
                let hidebyincontextid = false;
                let hidetypeofactivityid = false;
                let hidepresentationresourcesid = false;
                let hidesubcategorysid = false;

                let hidebytopic = ((that.filter_topics.length > 0 && that.filter_topics.indexOf(topicid) === -1) ||
                    (that.filtersArray['sequenceId'] !== undefined
                        && that.filtersArray['sequenceId'].length > 0
                        && that.filtersArray['sequenceId'].indexOf(topicid) === -1));
                let hidebycategory = (that.filter_categories.length > 0 && that.filter_categories.indexOf(categoryid) === -1)
                    || (that.filtersArray['categoryId'] !== undefined
                        && that.filtersArray['categoryId'].length > 0
                        && that.filtersArray['categoryId'].indexOf(categoryid) === -1);

                if (that.filtersArray.hasOwnProperty('activityTypeId')) {
                    hidebytype = (that.filtersArray['activityTypeId'].length > 0
                        && that.filtersArray['activityTypeId'].indexOf(bytype) === -1);
                }

                if (that.filtersArray.hasOwnProperty('difficultyLevelId')) {
                    hidebydificulty = (that.filtersArray['difficultyLevelId'].length > 0
                        && that.filtersArray['difficultyLevelId'].indexOf(bydificulty) === -1);
                }

                if (that.filtersArray.hasOwnProperty('recommendedGroupingId')) {
                    hidebydelivery = (that.filtersArray['recommendedGroupingId'].length > 0
                        && that.filtersArray['recommendedGroupingId'].indexOf(bydelivery) === -1);
                }

                if (that.filtersArray.hasOwnProperty('keyEvidenceId')) {
                    hidebykeyevidence = (that.filtersArray['keyEvidenceId'].length > 0
                        && that.filtersArray['keyEvidenceId'].indexOf(bykeyevidence) === -1);
                }

                if (that.filtersArray.hasOwnProperty('blockId')) {
                    hidebyblock = (that.filtersArray['blockId'].length > 0
                        && that.filtersArray['blockId'].indexOf(byblock) === -1);
                }

                if (that.filtersArray.hasOwnProperty('competenceId')) {
                    hidebycompetence = (that.filtersArray['competenceId'].length > 0
                        && that.filtersArray['competenceId'].indexOf(bycompetence) === -1);
                }

                if (that.filtersArray.hasOwnProperty('criterionId')) {
                    hidebycriterion = (that.filtersArray['criterionId'].length > 0
                        && that.filtersArray['criterionId'].indexOf(bycriterion) === -1);
                }

                if (that.filtersArray.hasOwnProperty('themeId')) {
                    hidebytheme = (that.filtersArray['themeId'].length > 0
                        && that.filtersArray['themeId'].indexOf(bytheme) === -1);
                }

                if (that.filtersArray.hasOwnProperty('transverseKeyId')) {
                    hidebytransversekey = (that.filtersArray['transverseKeyId'].length > 0
                        && that.filtersArray['transverseKeyId'].indexOf(bytransversekey) === -1);
                }

                if (that.filtersArray.hasOwnProperty('skillsId')) {
                    hidebyskills = (that.filtersArray['skillsId'].length > 0
                        && that.filtersArray['skillsId'].indexOf(byskills) === -1);
                }

                if (that.filtersArray.hasOwnProperty('pedagogicalPurposesId')) {
                    hidebypedagogicalpurposes = (that.filtersArray['pedagogicalPurposesId'].length > 0
                        && that.filtersArray['pedagogicalPurposesId'].indexOf(bypedagogicalpurposes) === -1);
                }

                if (that.filtersArray.hasOwnProperty('assessmentId')) {
                    hidebyassessment = (that.filtersArray['assessmentId'].length > 0
                        && that.filtersArray['assessmentId'].indexOf(byassessment) === -1);
                }

                if (that.filtersArray.hasOwnProperty('learningLevelId')) {
                    hidebylearninglevel = (that.filtersArray['learningLevelId'].length > 0
                        && that.filtersArray['learningLevelId'].indexOf(bylearninglevel) === -1);
                }

                if (that.filtersArray.hasOwnProperty('trackingActivitiesId')) {
                    hidebytrackingactivitiesid = (that.filtersArray['trackingActivitiesId'].length > 0
                        && that.filtersArray['trackingActivitiesId'].indexOf(bytrackingactivitiesid) === -1);
                }

                if (that.filtersArray.hasOwnProperty('challengesId')) {
                    hidebychallengesid = (that.filtersArray['challengesId'].length > 0
                        && that.filtersArray['challengesId'].indexOf(bychallengesid) === -1);
                }

                if (that.filtersArray.hasOwnProperty('inContextId')) {
                    hidebyincontextid = (that.filtersArray['inContextId'].length > 0
                        && that.filtersArray['inContextId'].indexOf(byincontextid) === -1);
                }

                if (that.filtersArray.hasOwnProperty('typeOfActivityId')) {
                    hidetypeofactivityid = (that.filtersArray['typeOfActivityId'].length > 0
                        && that.filtersArray['typeOfActivityId'].indexOf(bytypeofactivityid) === -1);
                }

                if (that.filtersArray.hasOwnProperty('presentationResourcesId')) {
                    hidepresentationresourcesid = (that.filtersArray['presentationResourcesId'].length > 0
                        && that.filtersArray['presentationResourcesId'].indexOf(bypresentationresourcesid) === -1);
                }

                if (that.filtersArray.hasOwnProperty('subcategoryId')) {
                    hidesubcategorysid = (that.filtersArray['subcategoryId'].length > 0
                        && that.filtersArray['subcategoryId'].indexOf(bysubcategoryid) === -1);
                }

                if (hidebytopic || hidebycategory || hidebytoassign || hidebyblock || hidebycompetence || hidebycriterion ||
                    hidebytheme || hidebytransversekey || hidebytype || hidebydelivery || hidebysequence || hidebydificulty ||
                    hidebykeyevidence || hidebyskills || hidebypedagogicalpurposes || hidebyassessment || hidebylearninglevel ||
                    hidebytrackingactivitiesid || hidebychallengesid || hidebyincontextid || hidetypeofactivityid ||
                    hidepresentationresourcesid || hidesubcategorysid) {
                    $(this).addClass('hidden');
                    $(this).hide();
                } else {
                    $(this).removeClass('hidden');
                    $(this).show();
                }
            });
            $('#search').keyup();
        };

        ManageAssignmentsFilter.prototype.applyColorButton = function (data, add) {
            var dataParent = $(data).closest('.dropdown');
            if (add) {
                $(dataParent).addClass('selected');
                $(dataParent).find('.dropbtn').addClass('bgf-c');
            } else {
                $(dataParent).removeClass('selected');
                $(dataParent).find('.dropbtn').removeClass('bgf-c');
            }
        };

        ManageAssignmentsFilter.prototype.toggleDropdownFilter = function () {
            var openId = '';
            var that = this;
            $('.dropbtn i').on('click', function (e) {
                if ($(this).parent().hasClass('bgf-c')) {
                    $("ul[data-id =" + $(this).parent().attr('id') + "] " + SELECTORS.FILTERCHECKBOX + ':checked')
                        .each(function () {
                            // Eliminamos los filtros para esa categoria.
                            var id = $(this).attr('id');
                            var filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid));
                            var transitionalVars = filtersFromStorage[that.sectionSelected];
                            transitionalVars = $.grep(transitionalVars, function (value) {
                                return value !== id;
                            });
                            filtersFromStorage[that.sectionSelected] = transitionalVars;
                            localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, filtersFromStorage)));
                            $(this).click();
                            $("[data-id =" + $(this).attr('id') + "]").parent().remove();
                        });
                    $(this).parent().removeClass('bgf-c');
                    e.preventDefault();
                    return; // Do nothing
                }
            });

            $('.dropbtn').on('click', function (e) {
                $(this).toggleClass('open');
                var id = $(this).attr('id');

                if (openId && openId !== $(this).attr('id')) {
                    $("ul[data-id =" + openId + "]").hide();
                    $('#' + openId).removeClass('open');
                }
                openId = id;
                $("ul[data-id =" + id + "]").toggle();
                e.stopPropagation();
            });
        };

        ManageAssignmentsFilter.prototype.checkFilter = function () {
            var that = this;

            if (that.filtersStorage) {
                that.filterControllerClicks[that.sectionSelected] = JSON.parse(that.filtersStorage);
            }

            $(SELECTORS.FILTERCHECKBOX).on('click', function () {
                var id = $(this).data('id');
                var uniquekey = $(this).attr('id');
                var uniquekeySub = '';
                var filtertype = $(this).data('filtertype');
                var label = $(this).data('label');
                var filtered = true;
                var checked = false;
                var idselected = '#' + $(this).attr('id') + '-label';
                var idselectedsub = '#' + $(this).attr('id') + '-sub';
                var filterSelector = that.sectionSelected + '-' + filtertype;

                that.applyColorButton(idselected, true);

                if ($(this).prop('checked')) {
                    that.filtersArray[filtertype].push(id);
                    label = $(this).data('label');

                    if (that.categoryDropValueReload && filtertype === 'subcategoryId') {
                        uniquekeySub = $('#' + that.categoryDropValueReload).closest('li').data('filtersuddrop');
                    }

                    // No añadimos el tab de subcategorias y tampoco si ya existe
                    if (filtertype !== 'subcategoryId' &&
                        $('.assignments-filter-container').find(`[data-id='${uniquekey}']`).length === 0
                    ) {
                        $(SELECTORS.FILTERCONTAINER).prepend(
                            '<div class="filtercategory assignments-filter-selected border-c" data-checkid="' + id + '">' +
                            '<div class="filter-text fc-c">' + label + '</div>' +
                            '<div class="clean-filter-selected bg-c" data-label="' + label + '" data-category="' + id + '" ' +
                            'data-id="' + uniquekey + '" ' + 'data-idSub="' + uniquekeySub + '" ' + 'data-filtertype="' +
                            filtertype + '" ' + 'data-filterselector="' + filterSelector + '"></div>' + '</div>'
                        );
                    }

                    $(SELECTORS.DELETEFILTERS).addClass('show');
                    checked = true;

                    // Rellenamos los clicks
                    // Solo aplicamos en este punto el tag si no es una subacategoria
                    if (!that.subCatAdded) {
                        if (that.filterControllerClicks !== null
                            && that.filterControllerClicks[that.sectionSelected] === undefined) {
                            that.filterControllerClicks[that.sectionSelected] = [];
                        }
                        if (that.filterControllerClicks !== null &&
                            $.inArray($(this).attr('id'), that.filterControllerClicks[that.sectionSelected]) === -1) {
                            that.filterControllerClicks[that.sectionSelected].push($(this).attr('id'));
                            localStorage.setItem(that.sesskeyCourseid,
                                JSON.stringify(Object.assign({}, that.filterControllerClicks)));
                        }
                    }
                    // Resetamos la variable con control de subfiltro para que siga el flujo normal
                    that.subCatAdded = false;

                    $('#' + $(this).attr('id') + '-label').addClass('selected');
                    $(idselectedsub).prop('checked', true);
                } else {
                    let removeItem = $(this).attr('id');
                    if (that.subCatDelete) {
                        removeItem = that.categoryDropValueReload ?? that.categoryDropValue + '-subcategoryId-' + id + '-sub';
                        that.subCatDelete = false;
                    }
                    that.filterControllerClicks[that.sectionSelected] = $.grep(
                        that.filterControllerClicks[that.sectionSelected]
                        , function (value) {
                            return value !== removeItem;
                        });
                    let key = that.filtersArray[filtertype].indexOf(id);
                    that.filtersArray[filtertype].splice(key, 1);
                    $(SELECTORS.FILTERZONE).find(`[data-checkid='${id}']`).remove();
                    $(SELECTORS.FILTERCONTAINER).find(`[data-checkid='${id}']`).remove();

                    if ($(SELECTORS.FILTERCONTAINERITEM).length < 1) {
                        $(SELECTORS.DELETEFILTERS).removeClass('show');
                        filtered = false;
                        filtertype = null;
                        that.applyColorButton(idselected, false);
                    }

                    // Guardamos los nuevos datos en el storage
                    var idAlt = $(this).data('filter') === 'subcategoryId' ? that.categoryDropValueReload : $(this).attr('id');

                    var filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid));
                    var transitionalVars = filtersFromStorage[that.sectionSelected];
                    transitionalVars = $.grep(transitionalVars, function (value) {
                        return value !== idAlt;
                    });
                    filtersFromStorage[that.sectionSelected] = transitionalVars;

                    that.mainFilter[that.sectionSelected] = '';
                    localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, filtersFromStorage)));

                    // Cambiamos el mainFilter en caso de que sea necesario
                    if (that.filterControllerClicks !== undefined
                        && that.filterControllerClicks[that.sectionSelected] !== undefined
                        && that.filterControllerClicks[that.sectionSelected][0] !== undefined) {
                        var nextMainFilter = that.filterControllerClicks[that.sectionSelected][0].split('-', 2).join('-');
                        that.mainFilter[that.sectionSelected] = nextMainFilter;
                    }
                    if ($(this).data('filter') === 'categoryId'
                        && $('.subcat[data-category~="' + $(this).attr('id') +'"]').siblings('input').next().hasClass('selected')) {
                            that.cleanAllFilters(true);
                            $('.dropbtn').removeClass('bgf-c');
                            $('.subcat').removeClass('selected bgf-c');
                            $('#search').click();
                    }

                    $(idselected).toggleClass('selected');
                }

                if (that.filterControllerClicks[that.sectionSelected] === '') {
                    that.mainFilter[that.sectionSelected] = '';
                }

                $('.subcat').show();
                that.filters();
                that.manageActivities(filtered, filtertype, checked);
            });

            $(document).on('click', SELECTORS.REMOVEFILTER, function () {
                if ($(this).data('filtertype') === 'categoryId'
                    && $(SELECTORS.FILTERCONTAINER).find(`[data-filtertype='subcategoryId']`).length > 0) {
                    var restore = $(this).data('id');
                    $(".restore[data-categoryid-restore='" + restore + "']").click();
                    return;
                }


                // Al eliminar un filtro, recuperamos datos de Storage y los modificamos con la nueva selección.
                var id = $(this).data('id');
                var idSub = $(this).data('idsub');
                var filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid));
                var transitionalVars = filtersFromStorage[that.sectionSelected];
                transitionalVars = $.grep(transitionalVars, function (value) {
                    return value !== id;
                });
                filtersFromStorage[that.sectionSelected] = transitionalVars;
                localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, filtersFromStorage)));

                if (idSub) {
                    that.subCatDelete = true;
                    // Desmarcamos los checks
                    $('#' + idSub + '-sub').click();
                } else {
                    // Desmarcamos los checks
                    $('#' + id).click();
                }

                if ($(SELECTORS.FILTERCONTAINERITEM).length < 1) {
                    $(SELECTORS.DELETEFILTERS).removeClass('show');
                }

                // Comprobamos si ese tipo de filtro está vacio
                var filterSelector = 'heading-' + $(this).data('filterselector');
                var filtercounter = $('#' + $(this).data('filterselector')).find('.custom_checkbox span.selected').length;
                if (filtercounter < 1) {
                    $('#' + filterSelector).removeClass('bgf-c');
                }
            });

            $(SELECTORS.CLEANALL).on('click', function () {
                that.cleanAllFilters(true);
                $('.dropbtn').removeClass('bgf-c');
                $('.subcat').removeClass('selected bgf-c');
                $('#search').click();
            });
        };

        ManageAssignmentsFilter.prototype.cleanAllFilters = function (cleanClicks = false) {
            var that = this;
            $(SELECTORS.SEARCH).val('');
            $(SELECTORS.FILTERCHECKBOX).each(function () {
                $(this).prop('checked', false);
                var idselected = '#' + $(this).attr('id') + '-label';
                $(idselected).removeClass('selected');
            });

            $(SELECTORS.FILTERCONTAINERITEM).each(function () {
                $(this).remove();
            });

            $(SELECTORS.FILTERZONEITEM).each(function () {
                $(this).remove();
            });

            $(SELECTORS.DELETEFILTERS).removeClass('show');
            that.setToDefault();
            that.manageActivities();

            if (cleanClicks) {
                that.mainFilter[that.sectionSelected] = '';
                // Al eliminar todos los filtros, recuperamos datos de Storage y los vaciamos.
                that.cleanStorageForCurrentSection();
                that.filterControllerClicks[that.sectionSelected] = [];
                $('.activities-subcats').show();
                $('.subcat').show();
                $('.collapse-units-dropdown .topic-item').show();
                $('.subcats-dropdown .list-header').removeClass('bg-d');
                $('#categories-' + that.sectionSelected).find('li.bgf-c.active').removeClass('bgf-c active');
            }
        };

        ManageAssignmentsFilter.prototype.cleanStorageForCurrentSection = function () {
            var that = this;
            var filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid)) ?? [];
            filtersFromStorage[that.sectionSelected] = [];
            that.filterControllerClicks[that.sectionSelected] = [];
            localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, that.filterControllerClicks)));
        };

        ManageAssignmentsFilter.prototype.openFilters = function () {
            $(SELECTORS.FILTER).on('click', function () {
                $(SELECTORS.OPACITYLAYER).toggle();
                $(SELECTORS.HEADERMENU).addClass('z0');
                $(SELECTORS.HEADERBAR).addClass('z0');
                $(SELECTORS.MEGAMENUMOBILE).addClass('z0');
            });
            $(SELECTORS.CARD).on('click', function () {
                $(this).find('.rotate').toggleClass("down");
                $(this).toggleClass('bold');
            });
            $(SELECTORS.FILTERCLOSE).on('click', function () {
                $(SELECTORS.OPACITYLAYER).toggle();
                $(SELECTORS.HEADERMENU).removeClass('z0');
                $(SELECTORS.HEADERBAR).removeClass('z0');
                $(SELECTORS.MEGAMENUMOBILE).removeClass('z0');
            });
            $('body').click(function (event) {
                if ($(event.target).attr('id') === 'closeSubcats' || $(event.target).hasClass('results')) {
                    $('.subcats-dropdown i.rotate').toggleClass('down');
                    $('.collapse-units-dropdown').removeClass('show');
                }

                if ($('.collapse-units-dropdown').is(':visible')) {
                    var container = Array.from(document.querySelectorAll('.subcatsdropdown')).filter(s =>
                        window.getComputedStyle(s).getPropertyValue('display') != 'none');
                    container = $(container).find('.subcats-dropdown')[0];

                    var containerSub = document.querySelectorAll('.collapse-units-dropdown.collapse.show .list-body')[0];
                    document.addEventListener('click', function (event) {
                        if (container
                            && container !== event.target
                            && !container.contains(event.target)
                            && $(containerSub).is(':visible')) {
                            $('.subcats-dropdown i.rotate').toggleClass('down');
                            $('.collapse-units-dropdown').removeClass('show');
                        }
                    });
                }
            });
        };

        ManageAssignmentsFilter.prototype.setToDefault = function () {
            var that = this;
            $('.activity-item').each(function () {
                $(this).show();
                $(this).removeClass('hidden').removeClass('filtered');
            });
            $(SELECTORS.SEARCH).val('');
            that.prepareFilters();
            var isVisible = 0;
            $('.activity-item').each(function () {
                if ($(this).is(":visible")) {
                    isVisible++;
                }
            });
            if (isVisible === 0 && !($('.assignments-filter-selected').length > 0)) {
                $('.noresultssearch').hide();
                $('.noresultsfilter').hide();
                $('.noresultsassignments').show();
            } else if (isVisible === 0 && ($('.assignments-filter-selected').length > 0)) {
                $('.noresultssearch').hide();
                $('.noresultsfilter').show();
                $('.noresultsassignments').hide();
            } else if (isVisible !== 0) {
                $('.noresultsassignments').hide();
                $('.noresultsfilter').hide();
                $('.noresultssearch').hide();
            }
            that.prepareFilters();
        };

        ManageAssignmentsFilter.prototype.prepareFilters = function () {
            $(SELECTORS.FILTERTITLE).each(function () {
                var filter = $(this).find('.custom_checkbox input');
                filter.each(function () {
                    filtersArray[$(this).data('filtertype')] = [];
                });
            });
            return filtersArray;
        };

        ManageAssignmentsFilter.prototype.chooseFilters = function () {
            var that = this;
            let section;

            // Initial filters.
            if ($('.sidebar-menu').is(":hidden")) {
                let searchParams = that.searchParams;
                section = searchParams.get('section');
            } else {
                section = $('.icon_menu.section.active a').data('section');
            }

            let visibleFilterbutton = false;
            $('.modal-filter').each(function () {
                if ($(this).data('section') === section) {
                    $(this).removeClass('hidden');
                    if ($(this).children().hasClass('dropdown')) {
                        visibleFilterbutton = true;
                    }
                }
            });
            if (visibleFilterbutton == false) {
                $(SELECTORS.FILTER).hide();
            }

            // Change filters when section has changed.
            $('.icon_menu.section').on('click', function () {
                // Removed filters applied.
                that.cleanAllFilters(false);
                var newSection = $(this).find('a').data('section');
                $('.modal-filter').each(function () {
                    if ($(this).data('section') === newSection) {
                        $(this).removeClass('hidden');
                    } else if (!$(this).hasClass('hidden')) {
                        $(this).addClass('hidden');
                    }
                });
            });

        };

        ManageAssignmentsFilter.prototype.getFiltersFromURL = function () {
            var that = this;
            let urlParams = that.searchParams;
            let section = urlParams.get('section');
            let sequenceId = urlParams.get('topicid');
            let filters = JSON.parse($('#filterbyitem').val());

            if (sequenceId !== null || filters.length > 0) {
                that.cleanStorageForCurrentSection();
            } else {
                return false;
            }
            if (sequenceId !== null) {
                let filterSeq = section + '-sequenceId-' + sequenceId;
                that.filterControllerClicks[that.sectionSelected].push(filterSeq);
            }

            if (filters.length > 0) {
                $(filters).each(function () {
                    let filter = section + '-' + this.key + '-' + this.value;
                    that.filterControllerClicks[that.sectionSelected].push(filter);
                });
            }

            // Save filters to storage
            localStorage.setItem(that.sesskeyCourseid,
                JSON.stringify(Object.assign({}, that.filterControllerClicks)));
        };

        ManageAssignmentsFilter.prototype.defaultSection = function () {
            var that = this;
            var section;
            if (!this.sectionSelected) {
                let urlParams = that.searchParams;
                section = urlParams.get('section') ?? 'actAndRec';
            }
            return section;
        };

        ManageAssignmentsFilter.prototype.changeSection = function () {
            var that = this;
            $(SELECTORS.SIDEMENUITEM).on('click', function () {
                that.sectionSelected = $(this).data('section');
                that.resetControlller = false;
                that.filterControllerClicks = that.filterControllerClicks ?? '{}';

                if (that.filterControllerClicks[that.sectionSelected] !== undefined) {
                    that.applyFiltersSaved();
                }
                that.filterControllerClicks[that.sectionSelected] = [];
                if ($.isEmptyObject(that.filtersStorage)) {
                    localStorage.setItem(that.sesskeyCourseid,
                        JSON.stringify(Object.assign({}, that.filterControllerClicks[that.sectionSelected])));
                }
            });
        };

        ManageAssignmentsFilter.prototype.applyFiltersSaved = function () {
            var that = this;
            $(document).ready(function () {
                if (that.filterControllerClicks !== null) {
                    $('.subcat').removeClass('selected bgf-c');
                    $('.dropbtn').removeClass('bgf-c');
                    $.each(that.filterControllerClicks[that.sectionSelected], function (key, value) {
                        $('#' + value).prop('checked', false).click();
                        // Con id, buscamos el data-filter y lo pulsamos para aplicar las clases en la subcategoría
                        $('.cc-subcat [data-filter~="' + value + '"]').prop('checked', true).next().addClass('selected bgf-c');
                    });
                }
            });
        };

        ManageAssignmentsFilter.prototype.manageActivities = function (
            filtered = false,
            filterType = null,
            checkedUpper = null) {
            var that = this;
            var activities = $('.activities-list[data-section="' + this.sectionSelected + '"] .activity-item');
            var checked = checkedUpper;
            that.filterController[that.sectionSelected] = [];

            // Calculamos las actividades visibles;
            if (!filtered) {
                that.activitiesCounter = $(activities).length;
            } else {
                that.activitiesCounter = $(activities).filter(":visible").length;
                activities = $(activities).filter(":visible");
                that.mainFilter[that.sectionSelected] = !that.mainFilter[that.sectionSelected]
                    ? that.sectionSelected + '-' + filterType
                    : that.mainFilter[that.sectionSelected];
            }

            // Si el filtro primario es category y el secundario subcategory, hacemos el cambio.
            const clicks = that.filterControllerClicks[that.sectionSelected];
            let currentFilter;
            if (clicks != undefined) {
                var currentFilterCat = clicks.findIndex(function (item) {
                    return (item.indexOf('subcategoryId') != -1);
                });

                currentFilter = clicks.findIndex(function (item) {
                    return (item.indexOf('subcategoryId') == -1 && item.indexOf('categoryId') == -1);
                });

                if (currentFilterCat != -1 && currentFilter == -1 && clicks.length > 0) {
                    that.mainFilter[that.sectionSelected] = that.sectionSelected + '-subcategoryId';
                }
            }
            $('#filter-counter').html('(' + that.activitiesCounter + ')');

            if (checked === false) {
                that.filterController[that.sectionSelected] = [];
                checked = true;
            }
            $(activities).each(function () {
                $.each($(this).data(), function (key, value) {
                    key = key === 'type' ? 'activitytypeid' : key;
                    if (that.filterController[that.sectionSelected][key] === undefined || !filtered) {
                        that.filterController[that.sectionSelected][key] = [];
                    }
                    if (checked && filtered && $.inArray(value, that.filterController[that.sectionSelected][key]) === -1) {
                        that.filterController[that.sectionSelected][key].push(value);
                        checked = true;
                        that.resetControlller = true;
                    }
                });
            });
            checked = false;
            // Construimos el objeto de controls de filtros
            var filterTypeDropdown = $('.modal-filter .dropdown');
            $(filterTypeDropdown).each(function () {
                var dropdown = this;
                var checkboxes = $(this).find('input');

                // Primero hago todas visibles
                $(checkboxes).each(function () {
                    $(this).parent().show();
                    $(this).closest('li').show();
                });

                // Ahora compruebo cual tengo que ocultar
                let filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid));
                let transitionalVars = filtersFromStorage !== null ? filtersFromStorage[that.sectionSelected] : '{}';
                $('#collapse-units-dropdown-' + that.sectionSelected + $(this).data('id') + ' .topic-item').show();
                $(checkboxes).each(function () {
                    var check = this;
                    var id = $(this).data('id'); // Value
                    var fullId = $(this).attr('id');
                    var type = $(this).data('filter'); // Key
                    var parent = $(this).attr('id').split('-', 2).join('-');
                    var subCatToHide = $(check).closest('li .custom_checkbox input').attr('id');
                    var previoussuddrop = subCatToHide.includes(that.sectionSelected + '-subcategoryId')
                        ? '[data-section~="' + that.sectionSelected + '"] [data-previoussuddrop~="' + subCatToHide + '"]'
                        : '[data-section~="' + that.sectionSelected + '"] [data-filtersuddrop~="' + subCatToHide + '"]';

                    $(previoussuddrop).show();

                    if (filtered && checkedUpper && parent !== that.mainFilter[that.sectionSelected]
                        && $.inArray(id, that.filterController[that.sectionSelected][type.toLowerCase()]) === -1) {
                        $(check).parent().hide();
                        transitionalVars = $.grep(transitionalVars, function (value) {
                            return value !== fullId;
                        });
                        filtersFromStorage[that.sectionSelected] = transitionalVars;
                        $(check).closest('li').hide();
                        $('[data-section~="' + that.sectionSelected + '"] [data-filter~="' + subCatToHide + '"]').hide();
                        if (that.sectionSelected + '-' + type !== that.mainFilter[that.sectionSelected]) {
                            $(previoussuddrop).hide();
                        }
                    }
                    if (filtered && checkedUpper && parent === that.mainFilter[that.sectionSelected]
                        && parent !== that.sectionSelected + '-' + filterType
                        && $.inArray(id, that.filterController[that.sectionSelected][type.toLowerCase()]) === -1) {
                        var toDelete = $('[data-previoussuddrop~="' + subCatToHide + '"]').data('filtersuddrop') + '-sub';
                        if ($(check).prop('checked') === true) {
                            $(check).prop('checked', false);
                            $('#' + fullId + '-label').removeClass('selected');
                            $('[data-id="' + fullId + '"]').parent().remove();
                            $('#' + toDelete).prop('checked', false);
                        }
                        transitionalVars = $.grep(transitionalVars, function (value) {
                            return value !== toDelete;
                        });
                        filtersFromStorage[that.sectionSelected] = transitionalVars;
                        $(check).closest('li').hide();

                        if (that.sectionSelected + '-' + type !== that.mainFilter[that.sectionSelected]) {
                            $(previoussuddrop).hide();
                        }
                    }
                    if (filtered && !checkedUpper && parent !== that.mainFilter[that.sectionSelected]
                        && $.inArray(id, that.filterController[that.sectionSelected][type.toLowerCase()]) === -1) {
                        $(check).parent().hide();
                        transitionalVars = $.grep(transitionalVars, function (value) {
                            return value !== fullId;
                        });
                        filtersFromStorage[that.sectionSelected] = transitionalVars;
                        $(check).closest('li').hide();
                        $('[data-section~="' + that.sectionSelected + '"] [data-filter~="' + subCatToHide + '"]').hide();
                        if (that.sectionSelected + '-' + type !== that.mainFilter[that.sectionSelected]) {
                            $(previoussuddrop).hide();
                        }
                    }
                });
                that.filterControllerClicks = filtersFromStorage;
                localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, filtersFromStorage)));

                var visible = false;
                var customCheckboxes = $(this).find('.custom_checkbox');

                $(customCheckboxes).each(function () {
                    if ($(this).css('display') !== 'none') {
                        visible = true;
                    }
                    return true;
                });

                if (!visible) {
                    $(dropdown).hide();
                } else {
                    $(dropdown).show();
                }
            });

            if ($('.assignments-filter-selected').length > 0) {
                $(SELECTORS.CLEANALL).removeClass('nofilters');
            } else {
                $(SELECTORS.CLEANALL).addClass('nofilters');
            }

            // Control de las subcategorias a la hora de mostrarla en las diferentes secciones.
            // Solo haremos esta comprobacion en caso de que haya algun filtro aplicado.
            if (that.filterControllerClicks[that.sectionSelected] !== undefined) {
                $('.activities-list[data-section="' + that.sectionSelected + '"]').filter(':visible').each(function () {
                    var section = this;
                    var cat = $(this).data('category');
                    $('#subcatsdropdown-' + that.sectionSelected + '-' + cat + ' .subcat').filter(':visible').each(function () {
                        var subcat = $(this).data('subcategory');
                        var counter = $(section).find('.activity-item')
                            .filter('[data-subcategoryid="' + subcat + '"]')
                            .not('.hidden').length;
                        var count = $(this).find('counter');
                        if (counter === 0) {
                            $(this).hide();
                        } else {
                            count.html('(' + counter + ')');
                            $(this).show();
                        }
                    });
                });
            }
        };

        // Gestión de subfilters

        ManageAssignmentsFilter.prototype.cleanStorageForSubcategories = function (subcategories) {
            var that = this;

            // Se quitan de los clicks actuales, los filtros relacionados con las subcategorias.
            var filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid)) ?? [];
            filtersFromStorage[that.sectionSelected] = [];
            var temp = that.filterControllerClicks[that.sectionSelected].filter(
                val => !subcategories.includes(val)
            );
            // Se hace limpieza total de los filtros
            that.cleanAllFilters(true);
            that.filterControllerClicks[that.sectionSelected] = temp;
            localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, that.filterControllerClicks)));

            // Se aplican los filtros no relacionados con las subcategorias.
            that.applyFiltersSaved();
        };

        ManageAssignmentsFilter.prototype.checkSubcategoriesDropdown = function () {
            var that = this;
            var subcatsdropdown = $('.' + that.sectionSelected + ' .subcatsdropdown');
            $(subcatsdropdown).each(function () {
                if ($(this).css('display') === 'block') {
                    var subcatsdropdownVisible = $(this);
                    var checker = true;
                    $(this).find('li').each(function () {
                        if ($(this).css('display') == 'list-item') {
                            checker = false;
                        }
                    });
                    if (checker) {
                        subcatsdropdownVisible.hide();
                    } else {
                        subcatsdropdownVisible.show();
                    }
                }
            });
        };
        ManageAssignmentsFilter.prototype.showSubcategoriesDropdown = function () {
            var that = this;
            var filtercounter = $('.' + that.sectionSelected).find(SELECTORS.SUBCATEGORYINPUT + ':checked').length;
            var parent = '.' + that.sectionSelected + ' .subcats-dropdown .list-header';
            if (filtercounter > 0) {
                filtercounter = filtercounter >= 10 ? '+9' : filtercounter;
                $(parent).addClass('bg-d');
                $('.' + that.sectionSelected + ' .filter-active').text(filtercounter);
            } else {
                $(parent).removeClass('bg-d');
                $('.' + that.sectionSelected + ' .filter-active').text('x');
            }
        };

        ManageAssignmentsFilter.prototype.subFilterManagement = function () {
            var that = this;

            $('.subcats-dropdown .list-header').on('click', function () {
                var id = $(this).attr('id');
                $('#' + id + ' i.rotate').toggleClass('down');
            });

            $('.controls .results').on('click', function () {
                $('.subcats-dropdown i.rotate').toggleClass('down');
                $('#collapse-units-dropdown').removeClass('show');
            });

            $(SELECTORS.SUBCATEGORYINPUT).on('click', function () {
                //Rellenamos los clicks
                if (!that.subCatDeleted) {
                    if (that.filterControllerClicks !== null && that.filterControllerClicks[that.sectionSelected] === undefined
                        && $(this).data('filtertype') === 'subcategoryId') {
                        that.filterControllerClicks[that.sectionSelected] = [];
                    }
                    if ($(this).data('filtertype') === 'subcategoryId' && that.filterControllerClicks !== null &&
                        $.inArray($(this).attr('id'), that.filterControllerClicks[that.sectionSelected]) === -1) {
                        that.filterControllerClicks[that.sectionSelected].push($(this)[0].id);
                        localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, that.filterControllerClicks)));
                    }
                } else {
                    that.subCatDeleted = false;
                }

                // Controlamos que no se añadan los tags de subcategoria mediante esta variable.
                that.subCatAdded = true;

                //that.showSubcategoriesDropdown(parent);
            });

            $(SELECTORS.SUBCATEGORYRESTORE).on('click', function () {
                var idSubCatClose = $(this).data('subfilterclose');
                var idSubCat = $(this).data('subfilterid');
                var filtersFromStorage = JSON.parse(localStorage.getItem(that.sesskeyCourseid));
                var transitionalVars = filtersFromStorage[that.sectionSelected];
                transitionalVars = $.grep(transitionalVars, function (value) {
                    return (value !== idSubCatClose && value !== idSubCat);
                });
                filtersFromStorage[that.sectionSelected] = transitionalVars;

                that.mainFilter[that.sectionSelected] = '';
                localStorage.setItem(that.sesskeyCourseid, JSON.stringify(Object.assign({}, filtersFromStorage)));
                var label = ($(this).parent()[0]);
                $(label).removeClass('selected');
                that.subCatDeleted = true;
            });

            $(SELECTORS.SUBCATEGORY).on('click', function () {
                var filterId = '#' + that.sectionSelected + '-subcategoryId-' + $(this).data('subcategory');
                var filterCatId = '#' + $(this).data('category');
                that.categoryDropValue = $(this).data('category-drop');
                $(filterId).click();
                if ($(filterCatId).prop('checked') === false) {
                    $(filterCatId).click();
                }
                if (!that.subCatDeleted) {
                    $(this).addClass('selected bgf-c');
                }
            });
        };

        return ManageAssignmentsFilter;
    });



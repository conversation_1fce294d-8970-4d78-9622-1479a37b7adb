{"version": 3, "file": "modal_linking_ccaa.min.js", "sources": ["../src/modal_linking_ccaa.js"], "sourcesContent": ["import $ from 'jquery';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalRegistry from 'core/modal_registry';\nimport <PERSON> from 'core/ajax';\nimport ModalLinkingCCAASuccessApi from 'local_npe/modal_linking_success_api';\nimport ModalLinkingCCAA<PERSON>ailApi from 'local_npe/modal_linking_fail_api';\nimport ModalFactory from 'core/modal_factory';\n\nconst SELECTORS = {\n    MODAL: '.linking-ccaa-modal',\n    MODALBACKDROP: '.modal-backdrop',\n    CCAA_TITLE: '#title_ccaa',\n    SELECT_CCAA_TEXT: '#linkccaa_text',\n    SELECT_CCAA: '#linkccaa',\n    CHOOSE_CCAA_DROPDOWN: '#linkccaa_dropdown',\n    CHOOSE_CCAA_DROPDOWN_CONTENT: '#linkccaa_dropdown_content',\n    CHOOSE_CCAA_OPTION: '.dropdown_ccaa_item',\n    CHOOSE_CCAA_ICO: '#dropdown_ico',\n    CONFIRM: '[data-action=\"confirm\"]',\n    CHOOSECCAABUTTON: '[data-action=\"chooseccaa\"]',\n    EXITBUTTON: '[data-action=\"exitgroup\"]',\n    MODALBOX: '.modal-body',\n    TITLE_FIRST: '#title-first',\n    TITLE_SECOND: '#title-second',\n    BUTTON_FIRST: '#modal-footer-first',\n    BUTTON_SECOND: '#modal-footer-second',\n    CLOSE: '[data-region=\"equis\"]',\n    CCAAQUESTION: '#caaquestion',\n    CCAAINFO: '#ccaainfo'\n};\n\nconst SERVICES = {\n    LINKGROUPCCAA: 'local_npe_link_group_ccaa'\n};\n\nlet registered = false;\n\nexport default class ModalLinkCCAA extends Modal {\n\n    static TYPE = 'local_npe/modal_link_ccaa';\n    static TEMPLATE = 'local_npe/courses/modal_linking_ccaa';\n\n    #hasmultiplegroups;\n    #linkcourse;\n    #evasmurl;\n    #flagccaa = true;\n    #textccaainfo = true;\n\n    constructor(root) {\n        super(root);\n    }\n\n    setData(ccaaavailable, hasmultiplegroups, linkcourse, evasmurl) {\n        this.#hasmultiplegroups = hasmultiplegroups;\n        this.#linkcourse = linkcourse;\n        this.#evasmurl = evasmurl;\n\n        $(SELECTORS.TITLE_SECOND).hide();\n\n        if (Object.keys(ccaaavailable).length > 0) {\n            let first = true;\n            for (const ccaaKey in ccaaavailable) {\n                if (first) {\n                    first = false;\n                    this.getRoot().find(SELECTORS.SELECT_CCAA_TEXT).val(ccaaavailable[ccaaKey]['name']);\n                    this.getRoot().find(SELECTORS.SELECT_CCAA).val(ccaaavailable[ccaaKey]['id']);\n                } else {\n                    var sep_opt = document.createElement('div');\n                    sep_opt.classList.add('separator');\n                    this.getRoot().find(SELECTORS.CHOOSE_CCAA_DROPDOWN_CONTENT).append(sep_opt);\n                }\n                let newOpt = document.createElement('div');\n                var text = document.createTextNode(ccaaavailable[ccaaKey]['name']);\n                newOpt.appendChild(text);\n                newOpt.setAttribute('data-value', ccaaavailable[ccaaKey]['id']);\n                newOpt.classList.add('dropdown_ccaa_item');\n                this.getRoot().find(SELECTORS.CHOOSE_CCAA_DROPDOWN_CONTENT).append(newOpt);\n            }\n            this.getRoot().find(SELECTORS.SELECT_CCAA_TEXT).removeClass('d-none');\n            this.getRoot().find(SELECTORS.CCAA_TITLE).removeClass('d-none');\n            this.getRoot().find(SELECTORS.CHOOSE_CCAA_ICO).removeClass('d-none');\n        }\n    }\n\n    registerEventListeners() {\n        super.registerEventListeners(this);\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.SELECT_CCAA_TEXT, () => {\n            if ($(SELECTORS.CHOOSE_CCAA_DROPDOWN).hasClass('d-none')) {\n                $(SELECTORS.CHOOSE_CCAA_ICO).css({'transform': 'rotate(270deg) translateX(7px)'});\n            } else {\n                $(SELECTORS.CHOOSE_CCAA_ICO).css({'transform': 'rotate(90deg)'});\n            }\n            $(SELECTORS.CHOOSE_CCAA_DROPDOWN).toggleClass('d-none');\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CHOOSE_CCAA_OPTION, (e) => {\n            this.getRoot().find(SELECTORS.SELECT_CCAA_TEXT).val(e.target.innerHTML);\n            $(SELECTORS.CHOOSE_CCAA_OPTION).removeClass('selected');\n            e.target.classList.add('selected');\n            this.getRoot().find(SELECTORS.SELECT_CCAA).val(e.target.getAttribute('data-value'));\n            $(SELECTORS.CHOOSE_CCAA_DROPDOWN).addClass('d-none');\n            $(SELECTORS.CHOOSE_CCAA_ICO).css({'transform': 'rotate(90deg)'});\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CONFIRM, () => {\n            this.getRoot().removeClass('show');\n            $('body').removeClass('modal-open');\n            $(SELECTORS.MODAL).removeClass('show').addClass('hide');\n            $(SELECTORS.MODALBACKDROP).removeClass('show').addClass('hide');\n            let searchParams = new URLSearchParams(window.location.search);\n            let courseid = searchParams.get('id') ? searchParams.get('id') : null;\n            let ccaaid = this.getRoot().find(SELECTORS.SELECT_CCAA).val() ? this.getRoot().find(SELECTORS.SELECT_CCAA).val() : null;\n            this.ajaxCall(courseid, ccaaid);\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.EXITBUTTON, () => {\n            if (this.#hasmultiplegroups) {\n                window.location.href = this.#linkcourse;\n            } else {\n                window.location.href = this.#evasmurl;\n            }\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CLOSE, (e) => {\n            this.setstate();\n            e.preventDefault();\n            this.show();\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CHOOSECCAABUTTON, () => {\n            this.setstate();\n            this.#textccaainfo = false;\n            this.showtext();\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CCAAQUESTION, () => {\n            this.showtext();\n        });\n\n        $(window).on('keydown', (event) => {\n            if (event.key === 'Escape') {\n                $(SELECTORS.MODALBACKDROP).removeClass('hide').addClass('show').css('z-index', '1051');\n                $(SELECTORS.MODAL).css('z-index', '1052');\n            }\n        });\n    }\n\n    showtext() {\n        if (this.#textccaainfo) {\n            $(SELECTORS.CCAAINFO).show();\n            this.#textccaainfo = false;\n        } else {\n            $(SELECTORS.CCAAINFO).hide();\n            this.#textccaainfo = true;\n        }\n    }\n\n    ajaxCall(courseid, ccaaid) {\n        const promises = Ajax.call([{\n            methodname: SERVICES.LINKGROUPCCAA,\n            args: {\n                courseid: courseid,\n                ccaaid: ccaaid\n            }\n        }]);\n\n        promises[0].done((response) => {\n            this.ajaxResult(response);\n        }).fail((ex) => {\n            this.ajaxError(ex);\n        });\n    }\n\n    ajaxResult() {\n        ModalFactory.create({type: ModalLinkingCCAASuccessApi.TYPE}).done((modal) => {\n            modal.show();\n        });\n    }\n\n    ajaxError(ex) {\n        ModalFactory.create({type: ModalLinkingCCAAFailApi.TYPE}).done((modal) => {\n            modal.show();\n        });\n        window.console.log(ex);\n    }\n\n    setstate() {\n        if (this.#flagccaa) {\n            $(SELECTORS.CLOSE).hide();\n            $(SELECTORS.MODALBOX).hide();\n            $(SELECTORS.TITLE_SECOND).show();\n            $(SELECTORS.TITLE_FIRST).hide();\n            $(SELECTORS.BUTTON_FIRST).hide();\n            $(SELECTORS.BUTTON_SECOND).show();\n            this.#flagccaa = false;\n        } else {\n            $(SELECTORS.MODALBOX).show();\n            $(SELECTORS.CLOSE).show();\n            $(SELECTORS.TITLE_SECOND).hide();\n            $(SELECTORS.TITLE_FIRST).show();\n            $(SELECTORS.BUTTON_FIRST).show();\n            $(SELECTORS.BUTTON_SECOND).hide();\n            this.#flagccaa = true;\n        }\n    }\n}\n\nif (!registered) {\n    ModalRegistry.register(ModalLinkCCAA.TYPE, ModalLinkCCAA, ModalLinkCCAA.TEMPLATE);\n    registered = true;\n}\n"], "names": ["_interopRequireDefault", "e", "__esModule", "default", "_classPrivateFieldInitSpec", "t", "a", "has", "TypeError", "_checkPrivateRedeclaration", "set", "_defineProperty", "r", "i", "Symbol", "toPrimitive", "call", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_classPrivateFieldGet", "s", "get", "_assert<PERSON>lassBrand", "_classPrivateFieldSet", "n", "arguments", "length", "_j<PERSON>y", "_custom_interaction_events", "_modal", "_modal_registry", "_ajax", "_modal_linking_success_api", "_modal_linking_fail_api", "_modal_factory", "SELECTORS", "SERVICES", "registered", "_hasmultiplegroups", "WeakMap", "_linkcourse", "_evasmurl", "_flagccaa", "_textccaainfo", "ModalLinkCCAA", "Modal", "constructor", "root", "super", "this", "setData", "ccaaavailable", "hasmultiplegroups", "linkcourse", "evasmurl", "$", "hide", "keys", "first", "ccaaKey", "getRoot", "find", "val", "sep_opt", "document", "createElement", "classList", "add", "append", "newOpt", "text", "createTextNode", "append<PERSON><PERSON><PERSON>", "setAttribute", "removeClass", "registerEventListeners", "getModal", "on", "CustomEvents", "events", "activate", "hasClass", "css", "transform", "toggleClass", "target", "innerHTML", "getAttribute", "addClass", "searchParams", "URLSearchParams", "window", "location", "search", "courseid", "ccaaid", "ajaxCall", "href", "setstate", "preventDefault", "show", "showtext", "event", "key", "Ajax", "methodname", "args", "done", "response", "ajaxResult", "fail", "ex", "ajaxError", "ModalFactory", "create", "type", "ModalLinkingCCAASuccessApi", "TYPE", "modal", "ModalLinkingCCAAFailApi", "console", "log", "_exports", "ModalRegistry", "register", "TEMPLATE"], "mappings": "+XAO8C,SAAAA,uBAAAC,GAAAA,OAAAA,GAAAA,EAAAC,WAAAD,EAAAE,CAAAA,QAAAF,EAAA,CAAA,SAAAG,2BAAAH,EAAAI,EAAAC,IAAA,SAAAL,EAAAI,GAAA,GAAAA,EAAAE,IAAAN,GAAA,MAAA,IAAAO,UAAA,iEAAA,EAAAC,CAAAR,EAAAI,GAAAA,EAAAK,IAAAT,EAAAK,EAAA,CAAA,SAAAK,gBAAAV,EAAAW,EAAAP,GAAAO,OAAAA,EAAA,SAAAP,GAAAQ,IAAAA,EAAA,SAAAR,EAAAO,GAAAP,GAAAA,iBAAAA,IAAAA,EAAA,OAAAA,EAAAJ,IAAAA,EAAAI,EAAAS,OAAAC,yBAAAd,EAAA,CAAA,IAAAY,EAAAZ,EAAAe,KAAAX,EAAAO,kCAAAC,EAAA,OAAAA,EAAAL,MAAAA,IAAAA,4EAAAI,EAAAK,OAAAC,QAAAb,EAAA,CAAAc,CAAAd,EAAAQ,UAAAA,MAAAA,iBAAAA,EAAAA,EAAAA,EAAA,EAAA,CAAAO,CAAAR,MAAAX,EAAAoB,OAAAC,eAAArB,EAAAW,EAAAW,CAAAA,MAAAlB,EAAAmB,YAAAC,EAAAA,cAAAC,EAAAA,cAAAzB,EAAAW,GAAAP,EAAAJ,CAAA,CAAA,SAAA0B,sBAAAC,EAAAtB,GAAAsB,OAAAA,EAAAC,IAAAC,kBAAAF,EAAAtB,GAAA,CAAA,SAAAyB,sBAAAH,EAAAtB,EAAAM,UAAAgB,EAAAlB,IAAAoB,kBAAAF,EAAAtB,GAAAM,GAAAA,CAAA,CAAA,SAAAkB,kBAAA7B,EAAAI,EAAA2B,GAAA,GAAA,mBAAA/B,EAAAA,IAAAI,EAAAJ,EAAAM,IAAAF,GAAA,OAAA4B,UAAAC,OAAA,EAAA7B,EAAA2B,EAAA,MAAA,IAAAxB,UAAA,gDAAA,iFAP9C2B,QAAAnC,uBAAAmC,SACAC,2BAAApC,uBAAAoC,4BACAC,OAAArC,uBAAAqC,QACAC,gBAAAtC,uBAAAsC,iBACAC,MAAAvC,uBAAAuC,OACAC,2BAAAxC,uBAAAwC,4BACAC,wBAAAzC,uBAAAyC,yBACAC,eAAA1C,uBAAA0C,gBAEA,MAAMC,gBACK,sBADLA,wBAEa,kBAFbA,qBAGU,cAHVA,2BAIgB,iBAJhBA,sBAKW,YALXA,+BAMoB,qBANpBA,uCAO4B,6BAP5BA,6BAQkB,sBARlBA,0BASe,gBATfA,kBAUO,0BAVPA,2BAWgB,6BAXhBA,qBAYU,4BAZVA,mBAaQ,cAbRA,sBAcW,eAdXA,uBAeY,gBAfZA,uBAgBY,sBAhBZA,wBAiBa,uBAjBbA,gBAkBK,wBAlBLA,uBAmBY,eAnBZA,mBAoBQ,YAGRC,uBACa,4BAGnB,IAAIC,YAAa,EAAM,IAAAC,uBAAAC,QAAAC,gBAAAD,QAAAE,cAAAF,QAAAG,cAAAH,QAAAI,kBAAAJ,QAER,MAAMK,sBAAsBC,OAAAA,QAWvCC,WAAAA,CAAYC,MACRC,MAAMD,MAPVnD,gCAAA0C,wBAAkB,GAClB1C,gCAAA4C,iBAAW,GACX5C,gCAAA6C,eAAS,GACT7C,2BAAAqD,KAAAP,WAAY,GACZ9C,2BAAAqD,KAAAN,eAAgB,EAIhB,CAEAO,OAAAA,CAAQC,cAAeC,kBAAmBC,WAAYC,UAOlD,GANA/B,sBAAKe,mBAALW,KAA0BG,mBAC1B7B,sBAAKiB,YAALS,KAAmBI,YACnB9B,sBAAKkB,UAALQ,KAAiBK,WAEjB,EAAAC,QAAAA,SAAEpB,wBAAwBqB,OAEtB3C,OAAO4C,KAAKN,eAAezB,OAAS,EAAG,CACvC,IAAIgC,OAAQ,EACZ,IAAK,MAAMC,WAAWR,cAAe,CACjC,GAAIO,MACAA,OAAQ,EACRT,KAAKW,UAAUC,KAAK1B,4BAA4B2B,IAAIX,cAAcQ,SAAe,MACjFV,KAAKW,UAAUC,KAAK1B,uBAAuB2B,IAAIX,cAAcQ,SAAa,QACvE,CACH,IAAII,QAAUC,SAASC,cAAc,OACrCF,QAAQG,UAAUC,IAAI,aACtBlB,KAAKW,UAAUC,KAAK1B,wCAAwCiC,OAAOL,QACvE,CACA,IAAIM,OAASL,SAASC,cAAc,OACpC,IAAIK,KAAON,SAASO,eAAepB,cAAcQ,SAAe,MAChEU,OAAOG,YAAYF,MACnBD,OAAOI,aAAa,aAActB,cAAcQ,SAAa,IAC7DU,OAAOH,UAAUC,IAAI,sBACrBlB,KAAKW,UAAUC,KAAK1B,wCAAwCiC,OAAOC,OACvE,CACApB,KAAKW,UAAUC,KAAK1B,4BAA4BuC,YAAY,UAC5DzB,KAAKW,UAAUC,KAAK1B,sBAAsBuC,YAAY,UACtDzB,KAAKW,UAAUC,KAAK1B,2BAA2BuC,YAAY,SAC/D,CACJ,CAEAC,sBAAAA,GACI3B,MAAM2B,uBAAuB1B,MAE7BA,KAAK2B,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAU7C,4BAA4B,MACrE,EAAAoB,QAAAA,SAAEpB,gCAAgC8C,SAAS,WAC3C,EAAA1B,QAAAA,SAAEpB,2BAA2B+C,IAAI,CAACC,UAAa,oCAE/C,EAAA5B,QAAAA,SAAEpB,2BAA2B+C,IAAI,CAACC,UAAa,mBAEnD,EAAA5B,QAAAA,SAAEpB,gCAAgCiD,YAAY,SAAS,IAG3DnC,KAAK2B,WAAWC,GAAGC,2BAAYnF,QAACoF,OAAOC,SAAU7C,8BAA+B1C,IAC5EwD,KAAKW,UAAUC,KAAK1B,4BAA4B2B,IAAIrE,EAAE4F,OAAOC,YAC7D,EAAA/B,QAAAA,SAAEpB,8BAA8BuC,YAAY,YAC5CjF,EAAE4F,OAAOnB,UAAUC,IAAI,YACvBlB,KAAKW,UAAUC,KAAK1B,uBAAuB2B,IAAIrE,EAAE4F,OAAOE,aAAa,gBACrE,EAAAhC,QAAAA,SAAEpB,gCAAgCqD,SAAS,WAC3C,EAAAjC,QAAAA,SAAEpB,2BAA2B+C,IAAI,CAACC,UAAa,iBAAiB,IAGpElC,KAAK2B,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAU7C,mBAAmB,KAChEc,KAAKW,UAAUc,YAAY,SAC3B,EAAAnB,QAAAA,SAAE,QAAQmB,YAAY,eACtB,EAAAnB,QAAC5D,SAACwC,iBAAiBuC,YAAY,QAAQc,SAAS,SAChD,EAAAjC,QAAC5D,SAACwC,yBAAyBuC,YAAY,QAAQc,SAAS,QACxD,IAAIC,aAAe,IAAIC,gBAAgBC,OAAOC,SAASC,QACnDC,SAAWL,aAAapE,IAAI,MAAQoE,aAAapE,IAAI,MAAQ,KAC7D0E,OAAS9C,KAAKW,UAAUC,KAAK1B,uBAAuB2B,MAAQb,KAAKW,UAAUC,KAAK1B,uBAAuB2B,MAAQ,KACnHb,KAAK+C,SAASF,SAAUC,OAAO,IAGnC9C,KAAK2B,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAU7C,sBAAsB,KAC/DhB,sBAAKmB,mBAALW,MACA0C,OAAOC,SAASK,KAAO9E,sBAAKqB,YAALS,MAEvB0C,OAAOC,SAASK,KAAO9E,sBAAKsB,UAALQ,KAC3B,IAGJA,KAAK2B,WAAWC,GAAGC,2BAAYnF,QAACoF,OAAOC,SAAU7C,iBAAkB1C,IAC/DwD,KAAKiD,WACLzG,EAAE0G,iBACFlD,KAAKmD,MAAM,IAGfnD,KAAK2B,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAU7C,4BAA4B,KACzEc,KAAKiD,WACL3E,sBAAKoB,cAALM,MAAqB,GACrBA,KAAKoD,UAAU,IAGnBpD,KAAK2B,WAAWC,GAAGC,2BAAAA,QAAaC,OAAOC,SAAU7C,wBAAwB,KACrEc,KAAKoD,UAAU,KAGnB,EAAA9C,QAAAA,SAAEoC,QAAQd,GAAG,WAAYyB,QACH,WAAdA,MAAMC,OACN,EAAAhD,QAAAA,SAAEpB,yBAAyBuC,YAAY,QAAQc,SAAS,QAAQN,IAAI,UAAW,SAC/E,EAAA3B,QAAC5D,SAACwC,iBAAiB+C,IAAI,UAAW,QACtC,GAER,CAEAmB,QAAAA,GACQlF,sBAAKwB,cAALM,QACA,EAAAM,QAAAA,SAAEpB,oBAAoBiE,OACtB7E,sBAAKoB,cAALM,MAAqB,MAErB,EAAAM,QAAAA,SAAEpB,oBAAoBqB,OACtBjC,sBAAKoB,cAALM,MAAqB,GAE7B,CAEA+C,QAAAA,CAASF,SAAUC,QACES,MAAAA,QAAKhG,KAAK,CAAC,CACxBiG,WAAYrE,uBACZsE,KAAM,CACFZ,SAAUA,SACVC,OAAQA,WAIP,GAAGY,MAAMC,WACd3D,KAAK4D,WAAWD,SAAS,IAC1BE,MAAMC,KACL9D,KAAK+D,UAAUD,GAAG,GAE1B,CAEAF,UAAAA,GACII,eAAYtH,QAACuH,OAAO,CAACC,KAAMC,mCAA2BC,OAAOV,MAAMW,QAC/DA,MAAMlB,MAAM,GAEpB,CAEAY,SAAAA,CAAUD,IACNE,eAAYtH,QAACuH,OAAO,CAACC,KAAMI,gCAAwBF,OAAOV,MAAMW,QAC5DA,MAAMlB,MAAM,IAEhBT,OAAO6B,QAAQC,IAAIV,GACvB,CAEAb,QAAAA,GACQ/E,sBAAKuB,UAALO,QACA,EAAAM,QAAAA,SAAEpB,iBAAiBqB,QACnB,EAAAD,QAAAA,SAAEpB,oBAAoBqB,QACtB,EAAAD,QAAAA,SAAEpB,wBAAwBiE,QAC1B,EAAA7C,QAAAA,SAAEpB,uBAAuBqB,QACzB,EAAAD,QAAAA,SAAEpB,wBAAwBqB,QAC1B,EAAAD,QAAAA,SAAEpB,yBAAyBiE,OAC3B7E,sBAAKmB,UAALO,MAAiB,MAEjB,EAAAM,QAAAA,SAAEpB,oBAAoBiE,QACtB,EAAA7C,QAAAA,SAAEpB,iBAAiBiE,QACnB,EAAA7C,QAAAA,SAAEpB,wBAAwBqB,QAC1B,EAAAD,QAAAA,SAAEpB,uBAAuBiE,QACzB,EAAA7C,QAAAA,SAAEpB,wBAAwBiE,QAC1B,EAAA7C,QAAAA,SAAEpB,yBAAyBqB,OAC3BjC,sBAAKmB,UAALO,MAAiB,GAEzB,EAMH,OALAyE,SAAA/H,QAAAiD,cAAAzC,gBAzKoByC,cAAa,OAEhB,6BAA2BzC,gBAFxByC,cAAa,WAGZ,wCAwKjBP,aACDsF,gBAAAA,QAAcC,SAAShF,cAAcyE,KAAMzE,cAAeA,cAAciF,UACxExF,YAAa,GAChBqF,SAAA/H,OAAA"}
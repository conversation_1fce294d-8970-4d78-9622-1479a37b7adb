define(['jquery', 'jqueryui', 'local_npe/datepicker', 'local_npe/event_listener', 'local_npe/date_validator', "core/str"],
    function ($, jqueryui, DatePicker, EventListener, DateValidator, Str) {

        let SELECTORS = {
            ROOT: '.myassignments',
            ADD_BUTTON: '.add',
            CANCEL_BUTTON: '.cancel',
            NAME_ERROR: '.invalid-feedback',
            ACTIVITYLIST: '.activities-list',
            INFOBUTTON: '.info_drop_button',
            SEARCH: '#search',
            CLEAN: '.clean',
            ICON_SECTION: '.icon_menu.section',
            HOUR: '.hour-begining',
            HOUREND: '.hour-end',
            ERROR_HOUR: '#errorhourend',
            BACKTOSEQUENCE: 'ul.npe-linkcontainer li'
        };

        let EVENTS = {
            LISTCHANGE: 'list-changed',
            TEAMWITHCONFLICT: 'team-with-conflict',
            TEAMSWITHCONFLICTS: 'teams-with-conflicts',
        };

        /**
         *
         * @param {*} resourcesaved
         * @param {*} activitysaved
         * @param {*} examsaved
         * @param {*} assignmentsurl
         * @param {*} urltoviewer
         * @param {*} isassignmentfilter
         * @param {*} txtSequence
         */
        function ManageAssignment(resourcesaved = '',
            activitysaved = '',
            examsaved = '',
            assignmentsurl = '',
            urltoviewer = '',
            isassignmentfilter = false,
            txtSequence = '') {
            ManageAssignment.prototype.constructor = ManageAssignment;
            ManageAssignment.prototype.root = null;
            ManageAssignment.prototype.inputdescription = null;
            ManageAssignment.prototype.form = null;
            ManageAssignment.prototype.resourcesaved = resourcesaved;
            ManageAssignment.prototype.activitysaved = activitysaved;
            ManageAssignment.prototype.examsaved = examsaved;
            ManageAssignment.prototype.assignmentsurl = assignmentsurl;
            ManageAssignment.prototype.urltoviewer = urltoviewer;
            ManageAssignment.prototype.isassignmentfilter = isassignmentfilter;
            ManageAssignment.prototype.txtSequence = txtSequence;

            // Prepare view first.
            setTemplate(txtSequence);

            this.init();
            this.isAssignmentSaved();
            this.changeView();
        }

        ManageAssignment.prototype.init = function () {
            this.root = $(SELECTORS.ROOT);

            EventListener.hearEvent(EVENTS.LISTCHANGE, function (e) {
                if (e.detail.data.response.haselements) {
                    $(SELECTORS.ADD_BUTTON).prop('disabled', false);
                } else {
                    $(SELECTORS.ADD_BUTTON).prop('disabled', true);
                }
            }.bind(this));

            EventListener.hearEvent(EVENTS.TEAMWITHCONFLICT, function () {
                require(['local_npe/modal_team_conflict', 'core/modal_factory'],
                    function (ModalTeamConflict, ModalFactory) {
                        ModalFactory.create({ type: ModalTeamConflict.TYPE }, $("#success")).done(function (modal) {
                            modal.setData('single');
                            modal.show();
                        });
                    });
            }.bind(this));

            EventListener.hearEvent(EVENTS.TEAMSWITHCONFLICTS, function () {
                require(['local_npe/modal_team_conflict', 'core/modal_factory'],
                    function (ModalTeamConflict, ModalFactory) {
                        ModalFactory.create({ type: ModalTeamConflict.TYPE }).done(function (modal) {
                            modal.setData('multiple');
                            modal.show();
                        });
                    });
            }.bind(this));

            if ($(SELECTORS.HOUR).length) {
                $(SELECTORS.ROOT).ready(function () {
                    DateValidator.setFields(
                        '#userdate[name="user_date"]',
                        SELECTORS.HOUR,
                        SELECTORS.HOUREND,
                        SELECTORS.ADD_BUTTON
                    );
                });
            }

            this.prepareView();
            this.searchInList();
        };

        ManageAssignment.prototype.prepareView = function () {
            $('#collapse-icon').on('click', function () {
                if ($(this).hasClass('collapse-right')) {
                    $('.list-group-item').show();
                    $('#collapse-icon').removeClass('collapse-right').addClass('collapse-left');
                    $('.assignments-sidebar').removeClass('collapse-width');
                    $('#separator').removeClass('separator-collapse').addClass('separator');
                } else {
                    $('.list-group-item').hide();
                    $('#collapse-icon').removeClass('collapse-left').addClass('collapse-right');
                    $('.assignments-sidebar').addClass('collapse-width');
                    $('#separator').removeClass('separator').addClass('separator-collapse');
                }
            });
        };

        ManageAssignment.prototype.isAssignmentSaved = function () {
            if (window.performance && window.performance.navigation.type === window.performance.navigation.TYPE_BACK_FORWARD) {
                this.resourcesaved = '';
                this.activitysaved = '';
                this.examsaved = '';
            }
            var that = this;

            if (this.resourcesaved !== '' && this.resourcesaved !== '0') {
                require(['local_npe/modal_success_assign', 'core/modal_factory'],
                    function (ModalSuccessAssign, ModalFactory) {
                        ModalFactory.create({ type: ModalSuccessAssign.TYPE }, $("#success")).done(function (modal) {
                            if (that.isassignmentfilter) {
                                modal.setData('newresourceassignment', that.urltoviewer, that.isassignmentfilter, that.txtSequence);
                                modal.show();
                            } else {
                                modal.setData('newresourceassignment', that.assignmentsurl);
                                modal.show();
                            }
                        });
                    });
            }

            if (this.activitysaved !== '' && this.resourcesaved !== '0') {
                require(['local_npe/modal_success_assign', 'core/modal_factory'],
                    function (ModalSuccessAssign, ModalFactory) {
                        ModalFactory.create({ type: ModalSuccessAssign.TYPE }, $("#success")).done(function (modal) {
                            if (that.isassignmentfilter) {
                                modal.setData('newactivityassignment', that.urltoviewer, that.isassignmentfilter, that.txtSequence);
                                modal.show();
                            } else {
                                modal.setData('newactivityassignment', that.assignmentsurl);
                                modal.show();
                            }
                        });
                    });
            }

            if (this.examsaved !== '' && this.resourcesaved !== '0') {
                require(['local_npe/modal_success_assign', 'core/modal_factory'],
                    function (ModalSuccessAssign, ModalFactory) {
                        ModalFactory.create({ type: ModalSuccessAssign.TYPE }, $("#success")).done(function (modal) {
                            if (that.isassignmentfilter) {
                                modal.setData('newactivityassignment', that.urltoviewer, that.isassignmentfilter, that.txtSequence);
                                modal.show();
                            } else {
                                modal.setData('newactivityassignment', that.assignmentsurl);
                                modal.show();
                            }
                        });
                    });
            }
        };

        ManageAssignment.prototype.searchInList = function () {
            $('.icon-lupa').on('click', function() {
                $('.icon-lupa').toggleClass('active-icon');
                $('.assignments-search').toggleClass('active-input');
                if ($(window).width() < 799) {
                    $('.npe-page-header-title').toggle();
                }
                $('#search').focus();
            });

            if (!$(SELECTORS.SEARCH).val()) {
                $(SELECTORS.ACTIVITYLIST).each(function () {
                    $(this).find('.count').html('(' + $(this).find('.activity-item').length + ')');
                });
            }

            $('#search, .clean').on('keyup click', function () {
                if (!$(SELECTORS.SEARCH).val()) {
                    $(SELECTORS.ACTIVITYLIST).each(function () {
                        $(this).find('.count').html('(' + $(this).find('.activity-item:not(.hidden)').length + ')');
                    });
                }

                var searching = $(this).val();

                $(SELECTORS.CLEAN).on('click', function () {
                    $(SELECTORS.SEARCH).val('');
                    $('.icon-lupa').removeClass('active-icon');
                    $('.assignments-search').removeClass('active-input');
                });

                $(SELECTORS.ACTIVITYLIST).each(function () {
                    var filtercounter = 0;
                    var activities = $(this);
                    $(activities).find('.activity-item:not(.hidden)').each(function () {
                        let item = $(this).find('.activity-name');
                        if (searching.length > 0 && $(item[0]).data('name').toLowerCase().indexOf(searching.toLowerCase()) > -1) {
                            filtercounter++;
                            $(this).show();
                        } else if (searching.length > 0) {
                            $(this).hide();
                        } else if (searching.length === 0) {
                            $(this).show();
                            filtercounter++;
                        }
                    });

                    // Cambiar el total de actividades en función del filtro realizado.
                    $(this).find('.count').html('(' + filtercounter + ')');
                });

                $(SELECTORS.ACTIVITYLIST).each(function () {
                    let name = $(this).data("name");
                    let sec = $(this).data("section");
                    let list = $(`[data-section='${sec}'][data-name="${name}"]`);
                    let numberofelements = $(list).find('.activity-item').length;

                    var elementshidden = $(list).find('.activity-item').filter(function () {
                        return $(this).css('display') == 'none';
                    }).length;

                    if (elementshidden === numberofelements) {
                        $(list).hide();
                    } else {
                        $(list).show();
                    }
                    // Count del numero de subcategorias al buscar por el search
                    if (!$('.showmore[data-section="' + sec + '"]').is(':visible')) {
                        $('.activities-list[data-section="' + sec + '"]').filter(':visible').each(function () {
                            var section = this;
                            var cat = $(this).data('category');
                            $('#subcatsdropdown-' + sec + '-' + cat + ' .subcat').filter(':visible').each(function () {
                                var subcat = $(this).data('subcategory');
                                var counter = $(section).find('.activity-item')
                                    .filter('[data-subcategoryid="' + subcat + '"]')
                                    .filter(':visible').length;
                                var count = $(this).find('counter');
                                if (counter === 0) {
                                    $(this).hide();
                                } else {
                                    count.html('(' + counter + ')');
                                    $(this).show();
                                }
                            });
                        });
                    }
                });

                var isVisible = 0;
                $('.activity-item').each(function () {
                    if ($(this).is(":visible")) {
                        isVisible++;
                    }
                });

                if (isVisible === 0 && searching.length > 0 && !($('.assignments-filter-selected').length > 0)) {
                    $('.noresultsassignments').hide();
                    $('.noresultsfilter').hide();
                    $('.noresultssearch').show();
                } else if (isVisible === 0 && !(searching.length > 0) && !($('.assignments-filter-selected').length > 0)) {
                    $('.noresultssearch').hide();
                    $('.noresultsfilter').hide();
                    $('.noresultsassignments').show();
                } else if (isVisible === 0 && !(searching.length > 0) && ($('.assignments-filter-selected').length > 0)) {
                    $('.noresultssearch').hide();
                    $('.noresultsfilter').show();
                    $('.noresultsassignments').hide();
                } else if (isVisible === 0 && (searching.length > 0) && ($('.assignments-filter-selected').length > 0)) {
                    $('.noresultssearch').show();
                    $('.noresultsfilter').hide();
                    $('.noresultsassignments').hide();
                } else if (isVisible !== 0) {
                    $('.noresultsassignments').hide();
                    $('.noresultsfilter').hide();
                    $('.noresultssearch').hide();
                }
            });

            $(SELECTORS.CLEAN).on('click', function () {
                $(SELECTORS.ACTIVITYLIST).each(function () {
                    $(this).find('.count').html('(' + $(this).find('.activity-item:not(.hidden)').length + ')');
                    var section = this;
                    let sec = $(this).data("section");
                    var cat = $(this).data('category');
                    $('#subcatsdropdown-' + sec + '-' + cat + ' .subcat').each(function () {
                        var subcat = $(this).data('subcategory');
                        var counter = $(section).find('.activity-item')
                            .filter('[data-subcategoryid="' + subcat + '"]').length;
                        var count = $(this).find('counter');
                        count.html('(' + counter + ')');
                        $(this).show();
                    });
                });
            });

            $('#search').click();
        };

        ManageAssignment.prototype.changeView = function () {
            $('.sidebar-menu a').on('click', function () {
                let section = $(this).data('section');
                setTemplate(this.txtSequence, section);
            });
        };

        /**
         *
         * @param {*} section
         */
        function session_storage_history(section) {
            let urlParams = new URLSearchParams(window.location.search);
            urlParams.set('section', section);
            const urltogo = window.location.origin + window.location.pathname + '?' + urlParams;
            window.history.pushState({}, "", urltogo);

            // Notificación a navigation_history.js de cambio de url.
            $(window).trigger('OnHistoryUrlStateUpdated', [urltogo]);
        }

        /**
         *
         * @param {*} txtSequence
         * @param {*} section
         * @param {*} noChangeText
         */
        function setTemplate(txtSequence, section = null, noChangeText = false) {
            var textoBreadcrumbs = '';
            let urlParams = new URLSearchParams(window.location.search);
            if (section === null) {
                section = urlParams.get('section') ?? 'actAndRec';
            }
            $('.activities-container')
                .hide()
                .addClass('hidden');
            $('.activities-container.' + section)
                .removeClass('hidden')
                .show();
            if (!noChangeText) {
                const sections = $("a.section");
                let fromviewer = false;
                let urlfromviewer = false;
                let last_url = sessionStorage.getItem('urlhistory');
                let last_url_array = last_url.split(',');
                if (last_url_array.length >= 2) {
                    let viewer_url_session = last_url_array[last_url_array.length - 2];
                    fromviewer = viewer_url_session.includes("viewer/index.php");
                    urlfromviewer = urlParams.get('fromviewer') ?? false;
                    if (fromviewer && urlfromviewer == true) { window.console.log('dentro');
                        $('ul.npe-linkcontainer li').css('display', 'none');
                        $("ul.npe-linkcontainer li:last-child").css('content', '');
                        let a = { 'txtSequence': txtSequence };
                        Str.get_string(
                            'backtosequence', 'local_npe', a
                        ).done(function (string) {
                            $("ul.npe-linkcontainer li:last-child").text(string);
                        });
                        $("ul.npe-linkcontainer li:last-child").addClass('noslash');
                        $("ul.npe-linkcontainer li:last-child").css('display', 'inline');
                        // UPDATE SESSION STORAGE HISTORY
                        session_storage_history(section);
                    }
                }
                if (sections.length && (!fromviewer || !urlfromviewer)) {
                    textoBreadcrumbs = sections.filter("." + section).text();
                    $("ul.npe-linkcontainer li:last-child").text(textoBreadcrumbs);
                    $("ul.npe-linkcontainer li:last-child").removeClass('noslash');
                    $('ul.npe-linkcontainer li').css('display', 'inline');
                    //UPDATE SESSION STORAGE HISTORY
                    session_storage_history(section);
                }
            }
        }
        return ManageAssignment;
    });

import $ from 'jquery';
import CustomEvents from 'core/custom_interaction_events';
import Modal from 'core/modal';
import ModalRegistry from 'core/modal_registry';
import EventListener from 'local_npe/event_listener';

const SELECTORS = {
    HIDE: '.close',
    CONFIRM: '[data-action="submit-grade"]',
};

const EVENTS = {
    CONFIRMSUBMIT: 'npe:confirm-multigrading-submit'
};

let registered = false;

export default class ModalConfirmMultigrading extends Modal {

    static TYPE = 'local_npe/modal_confirm_multigrading';
    static TEMPLATE = 'local_npe/commons/modal_confirm_multigrading';

    constructor(root) {
        super(root);
    }

    setData(random) {
        this.random = random;
    }

    registerEventListeners() {
        super.registerEventListeners(this);

        this.getModal().on(CustomEvents.events.activate, SELECTORS.HIDE, () => {
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.success-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
        });

        this.getModal().on(CustomEvents.events.activate, SELECTORS.CONFIRM, () => {
            EventListener.shoutEvent(EVENTS.CONFIRMSUBMIT, {name: 'confirmmultigradingsubmit-' + this.random});
            this.getRoot().removeClass('show');
            $('body').removeClass('modal-open');
            $('.success-modal').remove();
            $('.modal-backdrop').removeClass('show').addClass('hide');
        });
    }
}

if (!registered) {
    ModalRegistry.register(ModalConfirmMultigrading.TYPE, ModalConfirmMultigrading, ModalConfirmMultigrading.TEMPLATE);
    registered = true;
}

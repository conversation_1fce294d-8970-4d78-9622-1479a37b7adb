define("local_npe/modal_error",["exports","jquery","core/notification","core/custom_interaction_events","core/modal","core/modal_registry","core/str"],(function(_exports,_jquery,_notification,_custom_interaction_events,_modal,_modal_registry,_str){function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _defineProperty(e,r,t){return(r=function(t){var i=function(t,r){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var i=e.call(t,r||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==typeof i?i:i+""}(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_notification=_interopRequireDefault(_notification),_custom_interaction_events=_interopRequireDefault(_custom_interaction_events),_modal=_interopRequireDefault(_modal),_modal_registry=_interopRequireDefault(_modal_registry),_str=_interopRequireDefault(_str);const SELECTORS_MODALTITLE=".error-title",SELECTORS_MODALDESCRIPTION=".error-description",SELECTORS_HIDE=".close",SELECTORS_FINISH='[data-action="finish"]';let registered=!1;class ModalError extends _modal.default{constructor(root){super(root)}setData(titleKey,descriptionKey){let descriptionParam=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;const description=null!==descriptionParam?{key:descriptionKey,component:"local_npe",param:JSON.parse(descriptionParam)}:{key:descriptionKey,component:"local_npe"};let requeststrings;if(null===titleKey)requeststrings=[description];else{const title={key:titleKey,component:"local_npe"};requeststrings=null===descriptionKey?[title]:[title,description]}_str.default.get_strings(requeststrings).done((strings=>{let descriptionstring="",titlestring=strings[0];2==strings.length&&(titlestring=strings[0],descriptionstring=strings[1]),this.getRoot().find(SELECTORS_MODALTITLE).html(titlestring),this.getRoot().find(SELECTORS_MODALDESCRIPTION).html(descriptionstring)})).fail(_notification.default.exception)}registerEventListeners(){super.registerEventListeners(this),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_FINISH,(()=>{this.getRoot().removeClass("show"),(0,_jquery.default)("body").removeClass("modal-open"),(0,_jquery.default)(".success-modal").remove(),(0,_jquery.default)(".modal-backdrop").removeClass("show").addClass("hide")})),this.getModal().on(_custom_interaction_events.default.events.activate,SELECTORS_HIDE,(()=>{this.getRoot().removeClass("show"),(0,_jquery.default)("body").removeClass("modal-open"),(0,_jquery.default)(".success-modal").remove(),(0,_jquery.default)(".modal-backdrop").removeClass("show").addClass("hide")}))}}return _exports.default=ModalError,_defineProperty(ModalError,"TYPE","local_npe/modal_error"),_defineProperty(ModalError,"TEMPLATE","local_npe/commons/modal_error"),registered||(_modal_registry.default.register(ModalError.TYPE,ModalError,ModalError.TEMPLATE),registered=!0),_exports.default}));

//# sourceMappingURL=modal_error.min.js.map